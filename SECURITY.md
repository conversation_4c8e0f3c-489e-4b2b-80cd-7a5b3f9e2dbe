# Security Framework Documentation

## Overview

The E-Procurement System implements a comprehensive, enterprise-grade security framework designed to protect against modern cyber threats while ensuring compliance with Indonesian government procurement regulations and data protection laws.

## Security Components

### 1. Role-Based Access Control (RBAC)

#### Features
- Hierarchical permission system with granular access controls
- Dynamic role assignment with custom roles and permissions
- Resource-level permissions with conditions and inheritance
- Permission caching for performance optimization

#### Usage
```typescript
import { hasPermission, assignRole } from "@/lib/security/rbac";

// Check permission
const canCreate = await hasPermission(userId, {
  resource: "procurement",
  action: "create"
});

// Assign role
await assignRole(userId, roleId, assignedBy);
```

#### Default Roles
- **Super Admin**: Full system access
- **Admin**: System administration
- **Procurement Manager**: Senior procurement operations
- **Procurement Officer**: Operational procurement tasks
- **Finance Officer**: Financial approval and oversight
- **Evaluator**: Technical evaluation of offers
- **Vendor**: Procurement participation

### 2. Audit Trail System

#### Features
- Immutable audit logging with cryptographic integrity verification
- Security event tracking and automated threat detection
- Comprehensive metadata capture (IP, user agent, timing)
- Suspicious activity pattern detection and alerting

#### Usage
```typescript
import { auditHelpers, createAuditLog } from "@/lib/security/audit-trail";

// Log vendor approval
await auditHelpers.vendorApproval(userId, vendorId, true, "All documents verified");

// Custom audit log
await createAuditLog({
  userId,
  action: "CREATE",
  resource: "procurement",
  description: "New procurement created",
  severity: "MEDIUM",
});
```

### 3. Security Best Practices

#### Rate Limiting
```typescript
import { rateLimit, rateLimitConfigs } from "@/lib/security/rate-limiting";

// Apply rate limiting to API route
export const POST = rateLimit(rateLimitConfigs.auth)(handler);
```

#### Security Headers
```typescript
import { withSecurity } from "@/lib/security/security-headers";

// Apply security headers and CSRF protection
export const POST = withSecurity({
  csrf: true,
  sanitizeInput: true,
})(handler);
```

#### Two-Factor Authentication
```typescript
import { setupTwoFactor, verifyTwoFactor } from "@/lib/security/two-factor-auth";

// Setup 2FA
const setup = await setupTwoFactor(userId, userEmail);

// Verify 2FA token
const result = await verifyTwoFactor(userId, token);
```

#### Password Security
```typescript
import { validatePassword, hashPassword } from "@/lib/security/password-security";

// Validate password strength
const validation = validatePassword(password, userInfo);

// Hash password securely
const hashedPassword = await hashPassword(password);
```

#### File Upload Security
```typescript
import { validateFileUpload, scanFileForThreats } from "@/lib/upload";

// Validate file
const validation = validateFileUpload(file);

// Scan for threats
const scanResult = await scanFileForThreats(file);
```

## Security Middleware

### Combining Security Layers

```typescript
import { 
  rateLimit, 
  withSecurity, 
  requirePermissions, 
  withAudit 
} from "@/lib/security";

export const POST = 
  rateLimit(rateLimitConfigs.admin)(
    withSecurity({ csrf: true })(
      requirePermissions([{ resource: "admin", action: "create" }])(
        withAudit({ action: "CREATE", resource: "admin" })(
          handler
        )
      )
    )
  );
```

## Environment Configuration

### Required Environment Variables

```bash
# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Redis Configuration (for rate limiting and queues)
REDIS_URL=redis://localhost:6379

# Security Configuration
CSRF_SECRET=your-csrf-secret-key
JWT_SECRET=your-jwt-secret-key

# Storage Configuration
STORAGE_PROVIDER=local
LOCAL_STORAGE_PATH=./uploads
```

## Security Policies

### Password Policy
- Minimum 8 characters
- Must contain uppercase, lowercase, numbers, and special characters
- Cannot contain user information
- Cannot be a common password
- Expires after 90 days
- Cannot reuse last 5 passwords

### Account Lockout Policy
- Maximum 5 failed login attempts
- 30-minute lockout duration
- Reset on successful login

### File Upload Policy
- Maximum file size: 10MB
- Allowed types: PDF, DOC, DOCX, XLS, XLSX, images
- Virus scanning and threat detection
- File name validation and sanitization

### Rate Limiting
- Authentication: 5 attempts per 15 minutes
- API endpoints: 100 requests per minute
- File uploads: 10 uploads per minute
- Admin operations: 50 requests per minute

## Threat Detection

### Automated Detection
- Failed login attempts
- Unusual activity patterns
- Rate limit violations
- File upload threats
- Script injection attempts
- CSRF violations

### Security Events
All security events are logged with severity levels:
- **LOW**: Normal operations
- **MEDIUM**: Suspicious activity
- **HIGH**: Security violations
- **CRITICAL**: Active threats

## Compliance

### Indonesian Regulations
- Data protection compliance
- Government procurement regulations
- Audit trail requirements
- Access control standards

### Security Standards
- OWASP Top 10 protection
- ISO 27001 alignment
- SOC 2 Type II controls
- GDPR compliance features

## Monitoring and Alerting

### Real-time Monitoring
- Security event dashboard
- Failed authentication tracking
- Rate limiting violations
- File upload threats

### Automated Alerts
- Critical security events
- Account lockouts
- Suspicious activity patterns
- System security violations

## Incident Response

### Security Incident Workflow
1. **Detection**: Automated monitoring and alerting
2. **Assessment**: Security event analysis
3. **Containment**: Automatic account lockout and rate limiting
4. **Investigation**: Audit trail analysis
5. **Recovery**: Account unlock and system restoration
6. **Lessons Learned**: Security policy updates

### Emergency Procedures
- Immediate account lockout for critical threats
- Automatic rate limiting escalation
- Security team notification
- Audit trail preservation

## Testing

### Security Test Suite
```bash
# Run security tests
npm test -- __tests__/security.test.ts

# Run all tests including security
npm test
```

### Test Coverage
- Password validation and strength
- CSRF token generation and verification
- File upload security validation
- Threat scanning functionality
- Input sanitization
- Rate limiting behavior

## Best Practices for Developers

### API Endpoint Security
1. Always apply rate limiting
2. Use security headers middleware
3. Implement RBAC permission checks
4. Add audit logging
5. Validate and sanitize inputs
6. Use CSRF protection for state-changing operations

### Frontend Security
1. Sanitize user inputs
2. Use HTTPS only
3. Implement CSP headers
4. Validate file uploads client-side
5. Handle security errors gracefully

### Database Security
1. Use parameterized queries
2. Implement proper access controls
3. Encrypt sensitive data
4. Regular security audits
5. Backup and recovery procedures

## Security Updates

### Regular Maintenance
- Dependency security updates
- Security policy reviews
- Threat model updates
- Penetration testing
- Security training

### Version Control
All security changes are tracked in git with detailed commit messages and proper review processes.

## Contact

For security issues or questions:
- Security Team: <EMAIL>
- Emergency: +62-xxx-xxx-xxxx
- Internal: Slack #security-alerts
