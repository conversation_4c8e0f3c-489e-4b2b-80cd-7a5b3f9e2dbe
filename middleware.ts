import { NextRequest, NextResponse } from "next/server";
import { createPublicAssetMiddleware } from "@/lib/security/public-asset-security";

// Create the public asset middleware
const publicAssetMiddleware = createPublicAssetMiddleware();

export function middleware(request: NextRequest) {
  const url = new URL(request.url);
  
  // Apply public asset security middleware
  if (url.pathname.startsWith("/assets/public/")) {
    return publicAssetMiddleware(request);
  }
  
  // Apply other security headers for all requests
  const response = NextResponse.next();
  
  // General security headers
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  
  // CSP for the main application
  if (!url.pathname.startsWith("/api/")) {
    response.headers.set(
      "Content-Security-Policy",
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-eval' 'unsafe-inline'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: blob: https:; " +
      "font-src 'self'; " +
      "connect-src 'self'; " +
      "frame-ancestors 'none';"
    );
  }
  
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
