// Simple test script to check authentication
async function testAuth() {
  try {
    console.log('Testing authentication...');
    
    // Test workflow-stats endpoint
    const response = await fetch('http://localhost:3000/api/admin/dashboard/workflow-stats', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    } else {
      const data = await response.json();
      console.log('Success response:', data);
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testAuth();
