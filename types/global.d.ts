/// <reference types="node" />

declare global {
  interface Promise<T> {}
  interface PromiseConstructor {}
  
  namespace NodeJS {
    interface Process {
      env: ProcessEnv;
      exit(code?: number): never;
      argv: string[];
    }
    
    interface ProcessEnv {
      [key: string]: string | undefined;
    }
  }
  
  var process: NodeJS.Process;
  var Buffer: BufferConstructor;
  var console: Console;
  
  interface Console {
    log(...data: any[]): void;
    error(...data: any[]): void;
    warn(...data: any[]): void;
    info(...data: any[]): void;
  }
}

// Module declarations for packages without types
declare module 'bcryptjs' {
  export function hash(data: string, saltOrRounds: string | number): Promise<string>;
  export function compare(data: string, encrypted: string): Promise<boolean>;
  export function genSalt(rounds?: number): Promise<string>;
  export function hashSync(data: string, saltOrRounds: string | number): string;
  export function compareSync(data: string, encrypted: string): boolean;
  export function genSaltSync(rounds?: number): string;
}

declare module 'jsonwebtoken' {
  export interface SignOptions {
    expiresIn?: string | number;
    algorithm?: string;
    [key: string]: any;
  }
  
  export interface VerifyOptions {
    algorithms?: string[];
    [key: string]: any;
  }
  
  export function sign(payload: any, secretOrPrivateKey: string, options?: SignOptions): string;
  export function verify(token: string, secretOrPublicKey: string, options?: VerifyOptions): any;
  export function decode(token: string): any;
}

declare module '@jest/globals' {
  export const describe: any;
  export const it: any;
  export const test: any;
  export const expect: any;
  export const beforeEach: any;
  export const afterEach: any;
  export const beforeAll: any;
  export const afterAll: any;
  export const jest: any;
}

export {};
