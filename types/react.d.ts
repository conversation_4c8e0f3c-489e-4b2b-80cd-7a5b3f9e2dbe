/// <reference types="react" />
/// <reference types="react-dom" />

declare namespace React {
  interface Component<P = {}, S = {}, SS = any> {}
  interface ComponentClass<P = {}, S = {}> {}
  interface FunctionComponent<P = {}> {}
  interface ReactElement<P = any, T extends string | JSXElementConstructor<any> = string | JSXElementConstructor<any>> {}
  interface ReactNode {}
  interface CSSProperties {}

  // Element reference types
  type ElementRef<T extends keyof JSX.IntrinsicElements | React.ComponentType<any>> =
    T extends keyof JSX.IntrinsicElements
      ? JSX.IntrinsicElements[T] extends React.DetailedHTMLProps<infer P, infer E>
        ? E
        : never
      : T extends React.ComponentType<infer P>
        ? T extends React.ForwardRefExoticComponent<infer P>
          ? P extends React.RefAttributes<infer E>
            ? E
            : never
          : never
        : never;

  // Component props without ref
  type ComponentPropsWithoutRef<T extends keyof JSX.IntrinsicElements | React.ComponentType<any>> =
    T extends keyof JSX.IntrinsicElements
      ? JSX.IntrinsicElements[T] extends React.DetailedHTMLProps<infer P, any>
        ? Omit<P, 'ref'>
        : never
      : T extends React.ComponentType<infer P>
        ? Omit<P, 'ref'>
        : never;

  // Component props with ref
  type ComponentPropsWithRef<T extends keyof JSX.IntrinsicElements | React.ComponentType<any>> =
    T extends keyof JSX.IntrinsicElements
      ? JSX.IntrinsicElements[T]
      : T extends React.ComponentType<infer P>
        ? P
        : never;
}

declare namespace JSX {
  interface Element extends React.ReactElement<any, any> {}
  interface ElementClass extends React.Component<any> {}
  interface ElementAttributesProperty { props: {}; }
  interface ElementChildrenAttribute { children: {}; }
  interface IntrinsicElements {
    [elemName: string]: any;
  }
}


