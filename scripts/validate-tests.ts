#!/usr/bin/env tsx

/**
 * Test Validation Script
 * 
 * This script validates the test suite structure and ensures:
 * - All test files are properly structured
 * - Test coverage meets minimum requirements
 * - Critical functionality is tested
 * - Test files follow naming conventions
 * - Required test utilities are available
 */

import { existsSync, readdirSync, readFileSync, statSync } from 'fs';
import { join } from 'path';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step: string) {
  log(`\n🔄 ${step}`, 'cyan');
}

function logSuccess(message: string) {
  log(`✅ ${message}`, 'green');
}

function logError(message: string) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message: string) {
  log(`⚠️  ${message}`, 'yellow');
}

interface ValidationResult {
  category: string;
  passed: boolean;
  issues: string[];
  suggestions: string[];
}

interface TestFile {
  path: string;
  name: string;
  size: number;
  hasDescribe: boolean;
  hasTest: boolean;
  hasBeforeEach: boolean;
  hasAfterEach: boolean;
  testCount: number;
  imports: string[];
}

function getAllFiles(dir: string, extension: string = '.test.ts'): string[] {
  const files: string[] = [];
  
  if (!existsSync(dir)) {
    return files;
  }
  
  const items = readdirSync(dir);
  
  for (const item of items) {
    const fullPath = join(dir, item);
    const stat = statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getAllFiles(fullPath, extension));
    } else if (item.endsWith(extension)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function analyzeTestFile(filePath: string): TestFile {
  const content = readFileSync(filePath, 'utf-8') as string;
  const stat = statSync(filePath);

  return {
    path: filePath,
    name: filePath.split('/').pop() || '',
    size: stat.size,
    hasDescribe: /describe\s*\(/.test(content),
    hasTest: /(?:test|it)\s*\(/.test(content),
    hasBeforeEach: /beforeEach\s*\(/.test(content),
    hasAfterEach: /afterEach\s*\(/.test(content),
    testCount: (content.match(/(?:test|it)\s*\(/g) || []).length,
    imports: (content.match(/import\s+.*?from\s+['"]([^'"]+)['"]/g) || [])
      .map((imp: string) => imp.match(/from\s+['"]([^'"]+)['"]/)?.[1] || '')
      .filter(Boolean)
  };
}

function validateTestStructure(): ValidationResult {
  logStep('Validating test structure...');
  
  const issues: string[] = [];
  const suggestions: string[] = [];
  
  // Check if test directories exist
  const requiredDirs = ['__tests__'];
  const optionalDirs = ['__tests__/unit', '__tests__/integration', '__tests__/e2e'];
  
  for (const dir of requiredDirs) {
    if (!existsSync(dir)) {
      issues.push(`Required test directory missing: ${dir}`);
    }
  }
  
  for (const dir of optionalDirs) {
    if (!existsSync(dir)) {
      suggestions.push(`Consider creating ${dir} for organized test types`);
    }
  }
  
  // Check if Jest configuration exists
  if (!existsSync('jest.config.js') && !existsSync('jest.config.ts')) {
    issues.push('Jest configuration file missing (jest.config.js or jest.config.ts)');
  }
  
  // Check if test setup file exists
  if (!existsSync('jest.setup.js') && !existsSync('jest.setup.ts')) {
    suggestions.push('Consider creating jest.setup.js for test configuration');
  }
  
  return {
    category: 'Test Structure',
    passed: issues.length === 0,
    issues,
    suggestions
  };
}

function validateTestFiles(): ValidationResult {
  logStep('Validating test files...');
  
  const issues: string[] = [];
  const suggestions: string[] = [];
  
  const testFiles = getAllFiles('__tests__');
  
  if (testFiles.length === 0) {
    issues.push('No test files found');
    return {
      category: 'Test Files',
      passed: false,
      issues,
      suggestions
    };
  }
  
  log(`   Found ${testFiles.length} test files`, 'blue');
  
  for (const filePath of testFiles) {
    const testFile = analyzeTestFile(filePath);
    
    // Check file naming convention
    if (!testFile.name.endsWith('.test.ts') && !testFile.name.endsWith('.test.js')) {
      issues.push(`Test file ${testFile.name} doesn't follow naming convention (*.test.ts)`);
    }
    
    // Check if file has test structure
    if (!testFile.hasDescribe && !testFile.hasTest) {
      issues.push(`Test file ${testFile.name} has no test cases`);
    }
    
    // Check if file is too small (might be empty)
    if (testFile.size < 100) {
      issues.push(`Test file ${testFile.name} is very small (${testFile.size} bytes)`);
    }
    
    // Check test count
    if (testFile.testCount === 0) {
      issues.push(`Test file ${testFile.name} has no test cases`);
    } else if (testFile.testCount < 3) {
      suggestions.push(`Test file ${testFile.name} has only ${testFile.testCount} test(s), consider adding more`);
    }
  }
  
  return {
    category: 'Test Files',
    passed: issues.length === 0,
    issues,
    suggestions
  };
}

function validateCriticalTests(): ValidationResult {
  logStep('Validating critical functionality tests...');
  
  const issues: string[] = [];
  const suggestions: string[] = [];
  
  const criticalTests = [
    { name: 'auth.test.ts', description: 'Authentication tests' },
    { name: 'security.test.ts', description: 'Security tests' },
    { name: 'procurement.test.ts', description: 'Procurement tests' },
    { name: 'storage.test.ts', description: 'Storage tests' },
  ];
  
  for (const test of criticalTests) {
    const testPath = join('__tests__', test.name);
    if (!existsSync(testPath)) {
      issues.push(`Critical test missing: ${test.description} (${test.name})`);
    } else {
      const testFile = analyzeTestFile(testPath);
      if (testFile.testCount === 0) {
        issues.push(`Critical test ${test.name} has no test cases`);
      }
    }
  }
  
  // Check for validation tests
  const validationTests = getAllFiles('__tests__').filter(f => f.includes('validation'));
  if (validationTests.length === 0) {
    suggestions.push('Consider adding validation tests for input validation');
  }
  
  return {
    category: 'Critical Tests',
    passed: issues.length === 0,
    issues,
    suggestions
  };
}

function validateTestUtilities(): ValidationResult {
  logStep('Validating test utilities...');
  
  const issues: string[] = [];
  const suggestions: string[] = [];
  
  // Check if package.json has required test dependencies
  if (existsSync('package.json')) {
    const packageJson = JSON.parse(readFileSync('package.json', 'utf-8') as string);
    const devDeps = packageJson.devDependencies || {};
    
    const requiredTestDeps = [
      'jest',
      '@testing-library/react',
      '@testing-library/jest-dom'
    ];
    
    for (const dep of requiredTestDeps) {
      if (!devDeps[dep]) {
        issues.push(`Missing test dependency: ${dep}`);
      }
    }
    
    // Check for optional but recommended dependencies
    const recommendedDeps = [
      '@testing-library/user-event',
      'jest-environment-jsdom'
    ];
    
    for (const dep of recommendedDeps) {
      if (!devDeps[dep]) {
        suggestions.push(`Consider adding test dependency: ${dep}`);
      }
    }
  }
  
  return {
    category: 'Test Utilities',
    passed: issues.length === 0,
    issues,
    suggestions
  };
}

function generateValidationReport(results: ValidationResult[]): void {
  logStep('Generating validation report...');
  
  const totalCategories = results.length;
  const passedCategories = results.filter(r => r.passed).length;
  const failedCategories = totalCategories - passedCategories;
  
  log('\n📊 Test Validation Summary', 'bright');
  log('==========================', 'bright');
  log(`Total categories: ${totalCategories}`, 'blue');
  log(`Passed: ${passedCategories}`, passedCategories === totalCategories ? 'green' : 'yellow');
  log(`Failed: ${failedCategories}`, failedCategories === 0 ? 'green' : 'red');
  
  for (const result of results) {
    log(`\n📋 ${result.category}`, 'cyan');
    log(`Status: ${result.passed ? '✅ PASSED' : '❌ FAILED'}`, result.passed ? 'green' : 'red');
    
    if (result.issues.length > 0) {
      log('Issues:', 'red');
      result.issues.forEach(issue => log(`   - ${issue}`, 'red'));
    }
    
    if (result.suggestions.length > 0) {
      log('Suggestions:', 'yellow');
      result.suggestions.forEach(suggestion => log(`   - ${suggestion}`, 'yellow'));
    }
  }
  
  // Overall recommendations
  log('\n💡 General Recommendations', 'cyan');
  log('===========================', 'cyan');
  log('1. Maintain at least 80% test coverage', 'blue');
  log('2. Write tests for all critical user flows', 'blue');
  log('3. Include both positive and negative test cases', 'blue');
  log('4. Mock external dependencies in unit tests', 'blue');
  log('5. Use integration tests for complex workflows', 'blue');
  log('6. Keep tests fast and independent', 'blue');
  log('7. Use descriptive test names and organize with describe blocks', 'blue');
}

async function main(): Promise<void> {
  log('\n🔍 E-Procurement Test Validation', 'bright');
  log('=================================', 'bright');
  
  const results: ValidationResult[] = [];
  
  try {
    // Run all validation checks
    results.push(validateTestStructure());
    results.push(validateTestFiles());
    results.push(validateCriticalTests());
    results.push(validateTestUtilities());
    
    generateValidationReport(results);
    
    const failedCategories = results.filter(r => !r.passed).length;
    
    if (failedCategories === 0) {
      log('\n🎉 Test validation passed successfully!', 'green');
      log('=====================================', 'green');
    } else {
      log(`\n⚠️  ${failedCategories} validation category(ies) failed!`, 'yellow');
      log('Please address the issues above to improve test quality.', 'yellow');
      log('===============================================', 'yellow');
    }
    
  } catch (error) {
    log('\n💥 Test validation failed!', 'red');
    log('==========================', 'red');
    logError('Error details:');
    console.error(error);
    process.exit(1);
  }
}

// Show usage information
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  log('\n🔍 E-Procurement Test Validator', 'bright');
  log('===============================', 'bright');
  log('\nThis script validates the test suite structure and quality.', 'blue');
  log('\nUsage: npm run test:validate', 'blue');
  log('\nWhat it checks:', 'cyan');
  log('  ✓ Test directory structure', 'blue');
  log('  ✓ Test file naming conventions', 'blue');
  log('  ✓ Critical functionality coverage', 'blue');
  log('  ✓ Test utilities and dependencies', 'blue');
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  logError('Fatal error during test validation:');
  console.error(error);
  process.exit(1);
});
