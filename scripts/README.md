# E-Procurement Scripts

This directory contains utility scripts for the E-Procurement system. These scripts help with database initialization, testing, and maintenance tasks.

## Available Scripts

### 🗄️ Database Initialization (`init-database.ts`)

**Command:** `npm run db:init`

Performs complete database initialization for the E-Procurement system:

- ✅ Generates Prisma client
- ✅ Applies database migrations
- ✅ Sets up database partitioning for performance
- ✅ Seeds the database with initial data
- ✅ Creates default approval workflows
- ✅ Creates default document templates
- ✅ Optimizes database indexes
- ✅ Sets up maintenance jobs

**Usage:**
```bash
# Make sure your .env file is configured with DATABASE_URL
npm run db:init
```

**What it creates:**
- Admin user: `<EMAIL>` / `admin123`
- Default roles (ADMIN, PROCUREMENT_USER, VENDOR, etc.)
- Sample vendors and departments
- Default document templates
- Approval workflows
- Tax types and categories

### 🧪 Test Runner (`run-tests.ts`)

**Command:** `npm run test:all`

Runs comprehensive test suites for the E-Procurement system:

- ✅ TypeScript type checking
- ✅ ESLint code quality checks
- ✅ Unit tests
- ✅ Integration tests
- ✅ Security tests
- ✅ Authentication tests
- ✅ Procurement workflow tests
- ✅ Validation tests
- ✅ Storage tests

**Usage:**
```bash
# Run all tests
npm run test:all

# Run specific test suites
npm run test:all -- --unit
npm run test:all -- --integration
npm run test:all -- --security
npm run test:all -- --auth

# Run multiple specific suites
npm run test:all -- --unit --integration --security

# Show help
npm run test:all -- --help
```

**Available Options:**
- `--all` - Run all test suites (default)
- `--unit` - Run unit tests only
- `--integration` - Run integration tests only
- `--security` - Run security tests only
- `--auth` - Run authentication tests only
- `--procurement` - Run procurement tests only
- `--validation` - Run validation tests only
- `--storage` - Run storage tests only
- `--type-check` - Run TypeScript type checking only
- `--lint` - Run ESLint checks only

### 🔍 Test Validator (`validate-tests.ts`)

**Command:** `npm run test:validate`

Validates the test suite structure and ensures quality standards:

- ✅ Test directory structure validation
- ✅ Test file naming convention checks
- ✅ Critical functionality test coverage
- ✅ Test utilities and dependencies validation
- ✅ Test quality recommendations

**Usage:**
```bash
# Validate test suite
npm run test:validate

# Show help
npm run test:validate -- --help
```

**What it checks:**
- Required test directories exist
- Test files follow naming conventions
- Critical tests are present (auth, security, procurement, storage)
- Test dependencies are installed
- Test files have proper structure

## Prerequisites

Before running these scripts, ensure you have:

1. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Configure your DATABASE_URL and other variables
   ```

2. **Dependencies Installed**
   ```bash
   npm install
   ```

3. **PostgreSQL Database**
   - PostgreSQL 14+ running
   - Database created and accessible via DATABASE_URL

## Script Dependencies

These scripts use the following tools:

- **tsx** - TypeScript execution
- **Prisma** - Database ORM and migrations
- **Jest** - Testing framework
- **ESLint** - Code linting
- **TypeScript** - Type checking

## Development Workflow

### Initial Setup
```bash
# 1. Clone and install dependencies
git clone <repository>
cd e-procurement-system
npm install

# 2. Configure environment
cp .env.example .env
# Edit .env with your database credentials

# 3. Initialize database
npm run db:init

# 4. Start development server
npm run dev
```

### Testing Workflow
```bash
# 1. Validate test structure
npm run test:validate

# 2. Run all tests
npm run test:all

# 3. Run specific tests during development
npm run test:all -- --unit --auth
```

### Database Management
```bash
# Initialize complete database
npm run db:init

# Seed database only
npm run db:seed

# Run migrations only
npm run db:migrate

# Reset database (development only)
npm run db:reset

# Open Prisma Studio
npm run db:studio
```

## Troubleshooting

### Database Connection Issues
```bash
# Check your DATABASE_URL in .env
echo $DATABASE_URL

# Test database connection
npx prisma db pull
```

### Migration Issues
```bash
# Reset and reinitialize database
npm run db:reset
npm run db:init
```

### Test Issues
```bash
# Validate test structure first
npm run test:validate

# Run tests with verbose output
npm run test:all -- --verbose
```

### Permission Issues
```bash
# Make scripts executable
chmod +x scripts/*.ts
```

## Script Customization

You can modify these scripts to fit your specific needs:

1. **Database Initialization**: Edit `init-database.ts` to add custom seed data
2. **Test Runner**: Modify `run-tests.ts` to add new test suites
3. **Test Validator**: Update `validate-tests.ts` to add custom validation rules

## Production Considerations

### Database Initialization
- Use `npm run db:deploy` instead of `npm run db:init` in production
- Set up proper backup strategies
- Configure connection pooling
- Enable SSL connections

### Testing
- Run tests in CI/CD pipelines
- Set up test databases separate from production
- Configure coverage thresholds
- Use test parallelization for faster execution

### Monitoring
- Set up database performance monitoring
- Configure log aggregation
- Enable error tracking
- Set up automated alerts

## Support

For issues with these scripts:

1. Check the console output for detailed error messages
2. Verify your environment configuration
3. Ensure all dependencies are installed
4. Check database connectivity
5. Review the script source code for customization options

## Contributing

When adding new scripts:

1. Follow the existing naming convention
2. Add proper error handling and logging
3. Include help documentation
4. Update this README file
5. Add appropriate npm scripts to package.json
