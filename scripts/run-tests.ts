#!/usr/bin/env tsx

/**
 * Test Runner Script
 * 
 * This script runs all tests in the E-Procurement system:
 * - Unit tests
 * - Integration tests
 * - Security tests
 * - Performance tests
 * - Generates coverage reports
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step: string) {
  log(`\n🔄 ${step}`, 'cyan');
}

function logSuccess(message: string) {
  log(`✅ ${message}`, 'green');
}

function logError(message: string) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message: string) {
  log(`⚠️  ${message}`, 'yellow');
}

interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  coverage?: number;
  error?: string;
}

async function runCommand(command: string, description: string): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    log(`   Running: ${command}`, 'blue');
    execSync(command, { stdio: 'inherit' });
    const duration = Date.now() - startTime;
    logSuccess(`${description} (${duration}ms)`);
    
    return {
      name: description,
      passed: true,
      duration
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    logError(`${description} failed (${duration}ms)`);
    
    return {
      name: description,
      passed: false,
      duration,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

async function checkTestEnvironment(): Promise<void> {
  logStep('Checking test environment...');
  
  // Check if test database is configured
  if (!process.env.TEST_DATABASE_URL && !process.env.DATABASE_URL) {
    logWarning('No test database configured. Using default database.');
    logWarning('Consider setting TEST_DATABASE_URL for isolated testing.');
  }
  
  // Check if required test directories exist
  const testDirs = ['__tests__', '__tests__/unit', '__tests__/integration'];
  for (const dir of testDirs) {
    if (!existsSync(dir)) {
      logWarning(`Test directory ${dir} not found`);
    }
  }
  
  logSuccess('Test environment checked');
}

async function runUnitTests(): Promise<TestResult> {
  logStep('Running unit tests...');
  return await runCommand(
    'npx jest __tests__/unit --coverage --passWithNoTests',
    'Unit tests'
  );
}

async function runIntegrationTests(): Promise<TestResult> {
  logStep('Running integration tests...');
  return await runCommand(
    'npx jest __tests__/integration --coverage --passWithNoTests',
    'Integration tests'
  );
}

async function runSecurityTests(): Promise<TestResult> {
  logStep('Running security tests...');
  return await runCommand(
    'npx jest __tests__/security.test.ts --coverage --passWithNoTests',
    'Security tests'
  );
}

async function runAuthTests(): Promise<TestResult> {
  logStep('Running authentication tests...');
  return await runCommand(
    'npx jest __tests__/auth.test.ts --coverage --passWithNoTests',
    'Authentication tests'
  );
}

async function runProcurementTests(): Promise<TestResult> {
  logStep('Running procurement tests...');
  return await runCommand(
    'npx jest __tests__/procurement.test.ts --coverage --passWithNoTests',
    'Procurement tests'
  );
}

async function runValidationTests(): Promise<TestResult> {
  logStep('Running validation tests...');
  return await runCommand(
    'npx jest __tests__/*validation*.test.ts --coverage --passWithNoTests',
    'Validation tests'
  );
}

async function runStorageTests(): Promise<TestResult> {
  logStep('Running storage tests...');
  return await runCommand(
    'npx jest __tests__/storage.test.ts --coverage --passWithNoTests',
    'Storage tests'
  );
}

async function runTypeChecking(): Promise<TestResult> {
  logStep('Running TypeScript type checking...');
  return await runCommand(
    'npx tsc --noEmit',
    'TypeScript type checking'
  );
}

async function runLinting(): Promise<TestResult> {
  logStep('Running ESLint...');
  return await runCommand(
    'npx next lint',
    'ESLint checks'
  );
}

function generateReport(results: TestResult[]): void {
  logStep('Generating test report...');
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = totalTests - passedTests;
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
  
  log('\n📊 Test Results Summary', 'bright');
  log('========================', 'bright');
  log(`Total tests: ${totalTests}`, 'blue');
  log(`Passed: ${passedTests}`, passedTests === totalTests ? 'green' : 'yellow');
  log(`Failed: ${failedTests}`, failedTests === 0 ? 'green' : 'red');
  log(`Total duration: ${totalDuration}ms`, 'blue');
  
  if (failedTests > 0) {
    log('\n❌ Failed tests:', 'red');
    results.filter(r => !r.passed).forEach(result => {
      log(`   - ${result.name}`, 'red');
      if (result.error) {
        log(`     Error: ${result.error}`, 'red');
      }
    });
  }
  
  log('\n✅ Passed tests:', 'green');
  results.filter(r => r.passed).forEach(result => {
    log(`   - ${result.name} (${result.duration}ms)`, 'green');
  });
  
  // Check if coverage directory exists
  if (existsSync('coverage')) {
    log('\n📈 Coverage report generated in ./coverage directory', 'cyan');
    log('   Open ./coverage/lcov-report/index.html to view detailed coverage', 'blue');
  }
}

async function main(): Promise<void> {
  log('\n🧪 E-Procurement Test Suite', 'bright');
  log('============================', 'bright');
  
  const results: TestResult[] = [];
  
  try {
    await checkTestEnvironment();
    
    // Run different test suites
    const testSuites = [
      runTypeChecking,
      runLinting,
      runUnitTests,
      runIntegrationTests,
      runSecurityTests,
      runAuthTests,
      runProcurementTests,
      runValidationTests,
      runStorageTests,
    ];
    
    // Run tests based on command line arguments
    const args = process.argv.slice(2);
    
    if (args.includes('--all') || args.length === 0) {
      log('\n🚀 Running all test suites...', 'cyan');
      for (const testSuite of testSuites) {
        const result = await testSuite();
        results.push(result);
      }
    } else {
      // Run specific test suites based on arguments
      if (args.includes('--unit')) {
        results.push(await runUnitTests());
      }
      if (args.includes('--integration')) {
        results.push(await runIntegrationTests());
      }
      if (args.includes('--security')) {
        results.push(await runSecurityTests());
      }
      if (args.includes('--auth')) {
        results.push(await runAuthTests());
      }
      if (args.includes('--procurement')) {
        results.push(await runProcurementTests());
      }
      if (args.includes('--validation')) {
        results.push(await runValidationTests());
      }
      if (args.includes('--storage')) {
        results.push(await runStorageTests());
      }
      if (args.includes('--type-check')) {
        results.push(await runTypeChecking());
      }
      if (args.includes('--lint')) {
        results.push(await runLinting());
      }
    }
    
    generateReport(results);
    
    const failedTests = results.filter(r => !r.passed).length;
    
    if (failedTests === 0) {
      log('\n🎉 All tests passed successfully!', 'green');
      log('============================', 'green');
    } else {
      log(`\n💥 ${failedTests} test suite(s) failed!`, 'red');
      log('============================', 'red');
      process.exit(1);
    }
    
  } catch (error) {
    log('\n💥 Test execution failed!', 'red');
    log('========================', 'red');
    logError('Error details:');
    console.error(error);
    process.exit(1);
  }
}

// Show usage information
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  log('\n🧪 E-Procurement Test Runner', 'bright');
  log('============================', 'bright');
  log('\nUsage: npm run test:all [options]', 'blue');
  log('\nOptions:', 'cyan');
  log('  --all          Run all test suites (default)', 'blue');
  log('  --unit         Run unit tests only', 'blue');
  log('  --integration  Run integration tests only', 'blue');
  log('  --security     Run security tests only', 'blue');
  log('  --auth         Run authentication tests only', 'blue');
  log('  --procurement  Run procurement tests only', 'blue');
  log('  --validation   Run validation tests only', 'blue');
  log('  --storage      Run storage tests only', 'blue');
  log('  --type-check   Run TypeScript type checking only', 'blue');
  log('  --lint         Run ESLint checks only', 'blue');
  log('  --help, -h     Show this help message', 'blue');
  log('\nExamples:', 'cyan');
  log('  npm run test:all', 'blue');
  log('  npm run test:all -- --unit --integration', 'blue');
  log('  npm run test:all -- --security --auth', 'blue');
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  logError('Fatal error during test execution:');
  console.error(error);
  process.exit(1);
});
