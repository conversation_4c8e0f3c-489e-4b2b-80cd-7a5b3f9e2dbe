import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    // Handle handlebars compatibility issues
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        handlebars: 'commonjs handlebars'
      });
    }

    // Ignore require.extensions warnings from handlebars
    config.ignoreWarnings = [
      ...(config.ignoreWarnings || []),
      {
        module: /node_modules\/handlebars/,
        message: /require\.extensions/,
      },
    ];

    return config;
  },
  // External packages for server components
  serverExternalPackages: ['handlebars'],
};

export default nextConfig;
