{"name": "e-proc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:unit": "vitest __tests__/unit", "test:integration": "vitest __tests__/integration", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:ci": "npm run type-check && npm run test:coverage && npm run test:e2e", "test:all": "tsx scripts/run-tests.ts", "test:validate": "tsx scripts/validate-tests.ts", "db:init": "tsx scripts/init-database.ts", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:reset": "prisma migrate reset", "db:studio": "prisma studio", "db:generate": "prisma generate", "type-check": "tsc --noEmit", "queue:start": "tsx scripts/start-queue-workers.ts", "queue:dev": "tsx --watch scripts/start-queue-workers.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-react": "^9.5.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@google-cloud/storage": "^7.16.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.9.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@types/jsonwebtoken": "^9.0.9", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "handlebars": "^4.7.8", "helmet": "^8.1.0", "html2canvas": "^1.4.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.514.0", "next": "15.3.3", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "qrcode": "^1.5.4", "react": "^19.0.0", "react-color": "^2.19.3", "react-dom": "^19.0.0", "react-to-print": "^3.1.0", "reactflow": "^11.11.4", "recharts": "^2.15.3", "sonner": "^2.0.5", "speakeasy": "^2.0.0", "zod": "^3.25.58"}, "devDependencies": {"@eslint/eslintrc": "^3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4", "@playwright/test": "^1.48.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@vitejs/plugin-react": "^4.3.3", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/puppeteer": "^5.4.7", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19", "@types/speakeasy": "^2.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "prisma": "^6.9.0", "puppeteer": "^24.10.0", "react-hook-form": "^7.57.0", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4", "tsx": "^4.20.1", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vitest": "^2.1.8"}}