// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// -----------------------------
// CORE ENUMS
// -----------------------------

enum UserRoleType {
  ADMIN
  PROCUREMENT_USER
  APPROVER
  VENDOR
  COMMITTEE
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED

  // Extended evaluation statuses
  EVALUATING_ADMIN
  PASSED_ADMIN
  FAILED_ADMIN
  EVALUATING_TECH
  PASSED_TECH
  FAILED_TECH
  EVALUATING_PRICE
  PASSED_PRICE
  FAILED_PRICE
  EVALUATING_COMMERCIAL
  PASSED_COMMERCIAL
  FAILED_COMMERCIAL
  NEGOTIATING
  BACKUP_1
  BACKUP_2
  SUBMITTED

  // Additional status values
  WINNER
  LOSER
}

enum ProcurementStatus {
  DRAFT
  PUBLISHED
  SUBMISSION_OPEN
  SUBMISSION_CLOSED
  EVALUATION
  AWARDED
  COMPLETED
  CANCELLED
  AANWIJZING
  SUBMISSION
  NEGOTIATION
  WINNER_ANNOUNCEMENT
}

enum VendorStatus {
  PENDING_VERIFICATION
  VERIFIED
  REJECTED
  SUSPENDED
  BLACKLISTED
}

// Purchase Requisition enums
enum PurchaseRequisitionStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  REJECTED
  CONSOLIDATED
  CONVERTED
  CANCELLED
}

enum PurchaseRequisitionType {
  INTERNAL
  EXTERNAL_ROUTINE
  EXTERNAL_NON_ROUTINE
}

// Good Receipt enums
enum GoodReceiptStatus {
  DRAFT
  COMPLETED
  CANCELLED
}

// BAST enums
enum BastStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  REJECTED
}

// -----------------------------
// USER & ROLE MODELS
// -----------------------------

model User {
  id              String    @id @default(cuid())
  email           String    @unique
  name            String
  password        String
  phone           String?
  isActive        Boolean   @default(true)
  emailVerified   Boolean   @default(false)
  emailVerifiedAt DateTime?
  lastLoginAt     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Legacy roles field
  roles UserRoleType[]

  // Relations
  vendor               Vendor?
  userRoles            UserRole[]
  committeeAssignments ProcurementCommitteeMember[]
  approvals            Approval[]
  notifications        Notification[]

  // Department relation
  departmentId String?
  department   Department? @relation(fields: [departmentId], references: [id])

  // Content management
  createdArticles         NewsArticle[]
  contentApprovalRequests ContentApproval[] @relation("ContentApprovalRequester")
  contentApprovalReviews  ContentApproval[] @relation("ContentApprovalReviewer")

  // Document templates
  createdTemplates   DocumentTemplate[]        @relation("TemplateCreator")
  updatedTemplates   DocumentTemplate[]        @relation("TemplateUpdater")
  approvedTemplates  DocumentTemplate[]        @relation("TemplateApprover")
  templateVersions   DocumentTemplateVersion[]
  generatedDocuments GeneratedDocument[]

  // Approval workflows
  createdWorkflows   ApprovalWorkflow[] @relation("WorkflowCreator")
  initiatedApprovals ApprovalInstance[] @relation("ApprovalInitiator")
  approvalActions    ApprovalAction[]   @relation("ApprovalActionPerformer")
  delegatedApprovals ApprovalAction[]   @relation("ApprovalActionDelegate")
  approvalComments   ApprovalComment[]  @relation("ApprovalCommentAuthor")

  // Post-award workflow relations
  createdPOs          PurchaseOrder[]    @relation("POCreator")
  approvedPOs         PurchaseOrder[]    @relation("POApprover")
  createdDeliveries   Delivery[]         @relation("DeliveryCreator")
  inspectedDeliveries Delivery[]         @relation("DeliveryInspector")
  createdGRNs         GoodsReceiptNote[] @relation("GRNCreator")
  approvedGRNs        GoodsReceiptNote[] @relation("GRNApprover")
  submittedInvoices   Invoice[]          @relation("InvoiceSubmitter")
  approvedInvoices    Invoice[]          @relation("InvoiceApprover")

  // Discussion & negotiation relations
  createdDiscussions         ProcurementDiscussion[] @relation("DiscussionCreator")
  discussionParticipations   DiscussionParticipant[] @relation("DiscussionParticipants")
  discussionMessages         DiscussionMessage[]     @relation("MessageAuthor")
  messageReactions           MessageReaction[]       @relation("MessageReactions")
  uploadedAttachments        DiscussionAttachment[]  @relation("AttachmentUploader")
  uploadedMessageAttachments MessageAttachment[]     @relation("MessageAttachmentUploader")
  initiatedNegotiations      NegotiationSession[]    @relation("NegotiationInitiator")
  proposedRounds             NegotiationRound[]      @relation("RoundProposer")
  respondedRounds            NegotiationRound[]      @relation("RoundResponder")

  // KPI & Performance relations
  createdKpiTemplates    VendorKpiTemplate[]   @relation("KpiTemplateCreator")
  kpiEvaluations         VendorKpiEvaluation[] @relation("KpiEvaluator")
  approvedKpiEvaluations VendorKpiEvaluation[] @relation("KpiEvaluationApprover")
  recordedMetrics        VendorKpiMetric[]     @relation("MetricRecorder")
  createdBlacklists      BlacklistEntry[]      @relation("BlacklistCreator")
  appealDecisions        BlacklistEntry[]      @relation("AppealDecider")

  // Tax exemptions
  taxExemptions TaxExemption[] @relation("TaxExemptionCreator")

  // Audit trail
  auditLogs AuditLog[]

  // Procurement relations
  createdProcurements Procurement[]

  // Purchase Requisition relations
  requestedPRs           PurchaseRequisition[]    @relation("PRRequester")
  createdPackages        ProcurementPackage[]
  createdGRs             GoodReceipt[]            @relation("GRCreator")
  receiptLogs            ReceiptLog[]
  createdBASTs           BAST[]                   @relation("BASTCreator")
  priceCorrections       PriceCorrectionLog[]
  verifiedVendorSteps    VendorVerificationStep[]
  vendorOfferCorrections VendorOfferItem[]        @relation("PriceCorrector")
  delegationsFrom        ApprovalDelegation[]     @relation("DelegationFrom")
  delegationsTo          ApprovalDelegation[]     @relation("DelegationTo")

  // Evaluation relations
  offerEvaluations  OfferEvaluation[]  @relation("OfferEvaluator")
  vendorEvaluations VendorEvaluation[]

  // Vendor issue relations
  reportedVendorIssues VendorIssue[] @relation("VendorIssueReporter")
  assignedVendorIssues VendorIssue[] @relation("VendorIssueAssignee")

  // Notification relations
  createdNotificationTemplates NotificationTemplate[]

  // Public asset relations
  uploadedPublicAssets PublicAsset[] @relation("PublicAssetUploader")

  // Content management relations
  content            Content[]            @relation("ContentAuthor")
  procurementContent ProcurementContent[] @relation("ProcurementContentAuthor")
  aboutPageContent   AboutPageContent[]   @relation("AboutPageContentUpdater")

  // Procurement workflow template relations
  workflowTemplatesCreated    ProcurementWorkflowTemplate[] @relation("WorkflowTemplateCreator")
  vendorRequirementsCreated   VendorRequirementTemplate[]   @relation("VendorRequirementCreator")
  scheduleTemplatesCreated    ProcurementScheduleTemplate[] @relation("ScheduleTemplateCreator")
  procurementSchedulesCreated ProcurementSchedule[]         @relation("ProcurementScheduleCreator")

  // Document management relations
  uploadedDocuments          Document[]           @relation("DocumentUploader")
  approvedDocuments          Document[]           @relation("DocumentApprover")
  documentPermissionsGranted DocumentPermission[] @relation("DocumentPermissionGrantor")
  documentPermissionsUser    DocumentPermission[] @relation("DocumentPermissionUser")
  documentAuditLogs          DocumentAuditLog[]   @relation("DocumentAuditUser")
  documentShares             DocumentShare[]      @relation("DocumentShareCreator")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  permissions Json // Array of permission strings
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  userRoles           UserRole[]
  documentPermissions DocumentPermission[] @relation("DocumentPermissionRole")
}

// Resource permission model for RBAC
model ResourcePermission {
  id         String   @id @default(cuid())
  resource   String
  action     String
  roleId     String?
  userId     String?
  conditions Json?
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([resource, action])
  @@index([roleId])
  @@index([userId])
}

model UserRole {
  id         String    @id @default(cuid())
  userId     String
  roleId     String
  assignedAt DateTime  @default(now())
  assignedBy String?
  expiresAt  DateTime?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Restrict)

  @@unique([userId, roleId])
}

// -----------------------------
// DEPARTMENT MODEL
// -----------------------------

model Department {
  id    String @id @default(cuid())
  name  String @unique
  users User[]
}

// -----------------------------
// VENDOR MODELS
// -----------------------------

model Vendor {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Company information
  companyName     String
  companyType     String
  businessLicense String @unique
  taxId           String @unique
  address         String
  city            String
  province        String
  postalCode      String
  country         String @default("Indonesia")

  // Contact information
  contactPerson String
  picName       String? // Alias for contactPerson for compatibility
  contactPhone  String
  contactEmail  String
  website       String?

  // Business details
  businessCategory    String
  businessDescription String?
  establishedYear     Int?
  employeeCount       Int?
  annualRevenue       Float?

  // Verification
  status             VendorStatus @default(PENDING_VERIFICATION)
  verifiedAt         DateTime?
  verificationNotes  String?
  verificationStatus String? // Additional verification status field

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  documents        Document[]
  offers           VendorOffer[]
  simpleOffers     Offer[] // For compatibility
  evaluations      VendorEvaluation[] // For compatibility
  kpis             VendorKpi[]
  purchaseOrders   PurchaseOrder[]
  blacklistEntries BlacklistEntry[]
  negotiations     NegotiationSession[]

  // KPI & Performance relations
  kpiEvaluations     VendorKpiEvaluation[]
  performanceHistory VendorPerformanceHistory[]
  kpiMetrics         VendorKpiMetric[]
  issues             VendorIssue[]

  // Purchase Requisition relations
  contracts Contract[]

  // Enhanced vendor verification
  isLocked          Boolean                  @default(true)
  unlockExpiryDate  DateTime?
  verificationSteps VendorVerificationStep[]
}

model VendorOffer {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  vendorId      String
  vendor        Vendor      @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  // Offer details
  totalPrice  Float
  totalAmount Float? // Additional field for compatibility
  currency    String   @default("IDR")
  validUntil  DateTime
  notes       String?

  // Status
  status         ApprovalStatus @default(PENDING)
  submittedAt    DateTime       @default(now())
  submissionDate DateTime? // For compatibility with existing code

  // Negotiation fields
  negotiatedPrice     Float?
  negotiationNotes    String?
  negotiationComments String? // Additional field for compatibility

  // Evaluation fields
  evaluationComments String?
  adminScore         Float?
  techScore          Float?
  priceScore         Float?
  evaluatedAt        DateTime?
  evaluatedBy        String?

  // Relations
  documents   Document[]
  items       VendorOfferItem[]
  evaluations OfferEvaluation[]

  @@unique([procurementId, vendorId])
  @@index([procurementId])
  @@index([vendorId])
  @@index([status])
}

model VendorOfferItem {
  id      String          @id @default(cuid())
  offerId String
  offer   VendorOffer     @relation(fields: [offerId], references: [id], onDelete: Cascade)
  itemId  String
  item    ProcurementItem @relation(fields: [itemId], references: [id], onDelete: Restrict)
  price   Float

  // Price correction fields
  correctedPrice   Float?
  correctionReason String?
  correctedAt      DateTime?
  correctedById    String?
  correctedBy      User?                @relation("PriceCorrector", fields: [correctedById], references: [id])
  correctionLogs   PriceCorrectionLog[]
}

model OfferEvaluation {
  id            String      @id @default(cuid())
  offerId       String
  offer         VendorOffer @relation(fields: [offerId], references: [id], onDelete: Cascade)
  evaluatedById String
  evaluatedBy   User        @relation("OfferEvaluator", fields: [evaluatedById], references: [id], onDelete: Restrict)

  // Evaluation scores
  technicalScore  Float // 0-100
  commercialScore Float // 0-100
  totalScore      Float // Calculated weighted score

  // Evaluation details
  notes       String?  @db.Text
  evaluatedAt DateTime @default(now())

  // Evaluation criteria breakdown
  criteriaScores Json? // Detailed scores for each criterion

  @@unique([offerId, evaluatedById])
  @@index([offerId])
  @@index([evaluatedById])
  @@index([totalScore])
}

model VendorVerificationStep {
  id           String         @id @default(cuid())
  vendorId     String
  vendor       Vendor         @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  stepName     String
  status       ApprovalStatus @default(PENDING)
  verifiedById String?
  verifiedBy   User?          @relation(fields: [verifiedById], references: [id])
  verifiedAt   DateTime?
  comments     String?

  @@unique([vendorId, stepName])
}

// -----------------------------
// PURCHASE REQUISITION MODELS
// -----------------------------

model ItemMaster {
  id             String  @id @default(cuid())
  itemCode       String  @unique
  name           String
  description    String?
  unit           String
  category       String
  specifications Json?

  prItems PurchaseRequisitionItem[]

  @@index([category])
}

model PurchaseRequisition {
  id               String                    @id @default(cuid())
  prNumber         String                    @unique @default(cuid())
  title            String
  type             PurchaseRequisitionType
  status           PurchaseRequisitionStatus @default(DRAFT)
  requesterId      String
  requester        User                      @relation("PRRequester", fields: [requesterId], references: [id])
  totalValue       Float                     @default(0)
  isConsolidated   Boolean                   @default(false)
  packageId        String?
  package          ProcurementPackage?       @relation(fields: [packageId], references: [id])
  procurementId    String?                   @unique
  procurement      Procurement?              @relation(fields: [procurementId], references: [id])
  sourceContractId String?
  sourceContract   Contract?                 @relation(fields: [sourceContractId], references: [id])
  consolidatedAt   DateTime?
  createdAt        DateTime                  @default(now())
  updatedAt        DateTime                  @updatedAt

  items            PurchaseRequisitionItem[]
  documents        Document[]                @relation("PRDocuments")
  approvalInstance ApprovalInstance?         @relation("PRApprovalInstance")
}

model PurchaseRequisitionItem {
  id             String              @id @default(cuid())
  prId           String
  pr             PurchaseRequisition @relation(fields: [prId], references: [id], onDelete: Cascade)
  itemMasterId   String? // Link to standardized item
  itemMaster     ItemMaster?         @relation(fields: [itemMasterId], references: [id])
  name           String // Name if not from master
  description    String?
  quantity       Float
  unit           String
  estimatedPrice Float
}

model ProcurementPackage {
  id            String                @id @default(cuid())
  packageNumber String?               @unique
  name          String
  description   String?
  status        String?
  createdById   String
  createdBy     User                  @relation(fields: [createdById], references: [id])
  procurementId String?               @unique
  procurement   Procurement?          @relation(fields: [procurementId], references: [id])
  requisitions  PurchaseRequisition[]
  createdAt     DateTime              @default(now())
  updatedAt     DateTime              @updatedAt
}

model Contract {
  id                   String                @id @default(cuid())
  contractNumber       String                @unique
  vendorId             String
  vendor               Vendor                @relation(fields: [vendorId], references: [id])
  title                String
  startDate            DateTime
  endDate              DateTime
  totalValue           Float
  status               String?
  purchaseRequisitions PurchaseRequisition[]
}

model PriceCorrectionLog {
  id             String          @id @default(cuid())
  offerItemId    String
  offerItem      VendorOfferItem @relation(fields: [offerItemId], references: [id])
  originalPrice  Float
  correctedPrice Float
  reason         String
  correctedAt    DateTime        @default(now())
  correctedById  String
  correctedBy    User            @relation(fields: [correctedById], references: [id])
}

model SanctionedIndividual {
  id                String          @id @default(cuid())
  name              String
  identityNumber    String          @unique
  reason            String
  sourceBlacklistId String?
  sourceBlacklist   BlacklistEntry? @relation(fields: [sourceBlacklistId], references: [id])
}

model EvaluationTemplate {
  id           String        @id @default(cuid())
  name         String        @unique
  method       String
  passGrade    Float?
  criteria     Json
  procurements Procurement[]
}

// -----------------------------
// PROCUREMENT MODELS
// -----------------------------

model Procurement {
  id                String @id @default(cuid())
  procurementNumber String @unique
  title             String
  description       String @db.Text
  category          String
  estimatedValue    Float
  currency          String @default("IDR")

  // Timing
  publishDate        DateTime?
  submissionDeadline DateTime
  evaluationDate     DateTime?
  awardDate          DateTime?

  // Status
  status ProcurementStatus @default(DRAFT)

  // Requirements
  requirements       Json? // Vendor requirements and criteria
  evaluationCriteria Json? // Evaluation criteria and weights

  // Enhanced procurement fields from gap analysis
  workTimeUnit              String?
  hpsIncludesVat            Boolean             @default(false)
  vatRate                   Float?
  evaluationTemplateId      String?
  evaluationTemplate        EvaluationTemplate? @relation(fields: [evaluationTemplateId], references: [id])
  type                      String? // RFQ or TENDER
  showOwnerEstimateToVendor Boolean             @default(false)
  ownerEstimate             Float?
  notes                     String?

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdById String
  createdBy   User   @relation(fields: [createdById], references: [id], onDelete: Restrict)

  items             ProcurementItem[]
  stages            ProcurementStage[]
  committee         ProcurementCommitteeMember[]
  offers            VendorOffer[]
  simpleOffers      Offer[] // For compatibility
  vendorEvaluations VendorEvaluation[] // For compatibility
  documents         Document[]
  discussions       ProcurementDiscussion[]
  purchaseOrders    PurchaseOrder[]
  negotiations      NegotiationSession[]
  vendorIssues      VendorIssue[]

  // Purchase Requisition relations
  purchaseRequisition PurchaseRequisition?
  procurementPackage  ProcurementPackage?

  // Procurement workflow relations
  workflowTemplate   ProcurementWorkflowTemplate?   @relation("ProcurementWorkflowTemplate", fields: [workflowTemplateId], references: [id])
  workflowTemplateId String?
  vendorRequirements ProcurementVendorRequirement[] @relation("ProcurementVendorRequirements")
  schedule           ProcurementSchedule?           @relation("ProcurementSchedule")

  @@index([procurementNumber])
  @@index([status])
  @@index([category])
  @@index([createdById])
}

model ProcurementItem {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)

  name           String
  description    String?
  quantity       Float
  unit           String
  estimatedPrice Float
  ownerEstimate  Float? // Owner's estimate for comparison
  specifications Json?

  // Relations
  purchaseOrderItems PurchaseOrderItem[]
  vendorOfferItems   VendorOfferItem[]
  item               ProcurementItemInclude? // For compatibility

  @@index([procurementId])
}

// Compatibility model for item includes
model ProcurementItemInclude {
  id     String          @id @default(cuid())
  itemId String          @unique
  item   ProcurementItem @relation(fields: [itemId], references: [id])
}

// Offer model for compatibility with document generation
model Offer {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  vendorId      String
  vendor        Vendor      @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  totalAmount   Float
  status        String
  submittedAt   DateTime    @default(now())

  @@unique([procurementId, vendorId])
  @@index([procurementId])
  @@index([vendorId])
}

// Vendor evaluation model for compatibility
model VendorEvaluation {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  vendorId      String
  vendor        Vendor      @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  score         Float
  notes         String?
  evaluatedAt   DateTime    @default(now())
  evaluatedById String
  evaluatedBy   User        @relation(fields: [evaluatedById], references: [id])

  @@unique([procurementId, vendorId])
  @@index([procurementId])
  @@index([vendorId])
}

model ProcurementStage {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)

  name        String
  description String?
  sequence    Int
  startDate   DateTime?
  endDate     DateTime?
  status      String    @default("PENDING")

  // Relations
  discussionThread DiscussionThread?

  @@unique([procurementId, sequence])
  @@index([procurementId])
}

model ProcurementCommitteeMember {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  userId        String
  user          User        @relation(fields: [userId], references: [id], onDelete: Restrict)

  role       String // "CHAIRMAN", "MEMBER", "SECRETARY", etc.
  assignedAt DateTime @default(now())

  @@unique([procurementId, userId])
  @@index([procurementId])
  @@index([userId])
}

// -----------------------------
// POST-AWARD WORKFLOW MODELS
// -----------------------------

model PurchaseOrder {
  id              String         @id @default(cuid())
  poNumber        String         @unique
  procurementId   String         @unique
  procurement     Procurement    @relation(fields: [procurementId], references: [id], onDelete: Restrict)
  vendorId        String
  vendor          Vendor         @relation(fields: [vendorId], references: [id], onDelete: Restrict)
  status          ApprovalStatus @default(PENDING)
  poDate          DateTime
  totalValue      Float
  deliveryAddress String
  termsOfPayment  String

  // Approval
  approvedAt   DateTime?
  approvedById String?
  approvedBy   User?     @relation("POApprover", fields: [approvedById], references: [id], onDelete: Restrict)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdById String
  createdBy   User   @relation("POCreator", fields: [createdById], references: [id], onDelete: Restrict)

  documents    Document[]
  items        PurchaseOrderItem[]
  grn          GoodsReceiptNote?
  goodReceipts GoodReceipt[]
  invoices     Invoice[]
  basts        BAST[]
  approvals    Approval[]
  deliveries   Delivery[]
  vendorIssues VendorIssue[]

  @@index([vendorId, status])
}

model PurchaseOrderItem {
  id       String          @id @default(cuid())
  poId     String
  po       PurchaseOrder   @relation(fields: [poId], references: [id], onDelete: Cascade)
  itemId   String
  item     ProcurementItem @relation(fields: [itemId], references: [id], onDelete: Restrict)
  quantity Float
  price    Float

  receivedItems GoodReceiptItem[]
  deliveryItems DeliveryItem[]
}

model GoodsReceiptNote {
  id           String        @id @default(cuid())
  grnNumber    String        @unique
  poId         String        @unique
  po           PurchaseOrder @relation(fields: [poId], references: [id], onDelete: Cascade)
  receivedDate DateTime
  notes        String?

  // Status
  status ApprovalStatus @default(PENDING)

  // Approval
  approvedAt   DateTime?
  approvedById String?
  approvedBy   User?     @relation("GRNApprover", fields: [approvedById], references: [id], onDelete: Restrict)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdById String
  createdBy   User   @relation("GRNCreator", fields: [createdById], references: [id], onDelete: Restrict)

  receivedItems GoodReceiptItem[]
}

// Enhanced Good Receipt models for the gap analysis
model GoodReceipt {
  id           String            @id @default(cuid())
  grNumber     String            @unique
  poId         String
  po           PurchaseOrder     @relation(fields: [poId], references: [id])
  status       GoodReceiptStatus @default(DRAFT)
  receivedDate DateTime
  createdById  String
  createdBy    User              @relation("GRCreator", fields: [createdById], references: [id])
  items        GoodReceiptItem[]
}

model ReceiptLog {
  id                String          @id @default(cuid())
  goodReceiptItemId String
  goodReceiptItem   GoodReceiptItem @relation(fields: [goodReceiptItemId], references: [id])
  quantityChange    Float
  logDate           DateTime        @default(now())
  notes             String?
  loggedById        String
  loggedBy          User            @relation(fields: [loggedById], references: [id])
}

model GoodReceiptItem {
  id                  String            @id @default(cuid())
  grnId               String?
  grn                 GoodsReceiptNote? @relation(fields: [grnId], references: [id], onDelete: Cascade)
  grId                String?
  gr                  GoodReceipt?      @relation(fields: [grId], references: [id], onDelete: Cascade)
  purchaseOrderItemId String
  purchaseOrderItem   PurchaseOrderItem @relation(fields: [purchaseOrderItemId], references: [id], onDelete: Restrict)
  receivedQuantity    Float // This will be a calculated field (sum of logs)
  notes               String?
  receiptLogs         ReceiptLog[]

  @@unique([grnId, purchaseOrderItemId])
}

model Invoice {
  id            String         @id @default(cuid())
  invoiceNumber String         @unique
  poId          String
  po            PurchaseOrder  @relation(fields: [poId], references: [id], onDelete: Restrict)
  invoiceDate   DateTime
  amount        Float
  status        ApprovalStatus @default(PENDING)

  // Approval
  approvedAt   DateTime?
  approvedById String?
  approvedBy   User?     @relation("InvoiceApprover", fields: [approvedById], references: [id], onDelete: Restrict)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  submittedById String
  submittedBy   User   @relation("InvoiceSubmitter", fields: [submittedById], references: [id], onDelete: Restrict)

  documents Document[]
  approvals Approval[]

  @@index([poId, status])
}

model BAST {
  id           String        @id @default(cuid())
  bastNumber   String        @unique
  poId         String
  po           PurchaseOrder @relation(fields: [poId], references: [id], onDelete: Restrict)
  handoverDate DateTime
  status       BastStatus    @default(DRAFT)
  summary      String?       @db.Text
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  createdById  String
  createdBy    User          @relation("BASTCreator", fields: [createdById], references: [id])

  documents Document[]
  checklist BastChecklistItem[]
  approvals Approval[]

  @@index([poId, status])
  @@index([createdAt])
}

model BastChecklistItem {
  id          String    @id @default(cuid())
  bastId      String
  bast        BAST      @relation(fields: [bastId], references: [id], onDelete: Cascade)
  description String
  defectNotes String?
  targetDate  DateTime?
}

// Enhanced delivery tracking
model Delivery {
  id             String        @id @default(cuid())
  deliveryNumber String        @unique
  poId           String
  po             PurchaseOrder @relation(fields: [poId], references: [id], onDelete: Restrict)

  // Delivery details
  deliveryDate DateTime
  receivedDate DateTime?

  // Status
  status DeliveryStatus @default(SCHEDULED)

  // Location
  deliveryAddress String
  receivedBy      String?

  // Quality control
  inspectionDate   DateTime?
  inspectionResult QualityResult?
  inspectionNotes  String?

  // BAST (Berita Acara Serah Terima)
  bastNumber   String?
  bastDate     DateTime?
  bastSignedBy String?
  bastDocument String? // File path to BAST document

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdById   String
  createdBy     User    @relation("DeliveryCreator", fields: [createdById], references: [id], onDelete: Restrict)
  inspectedById String?
  inspectedBy   User?   @relation("DeliveryInspector", fields: [inspectedById], references: [id], onDelete: Restrict)

  items DeliveryItem[]

  @@index([deliveryNumber])
  @@index([poId])
  @@index([status])
  @@index([deliveryDate])
}

enum DeliveryStatus {
  SCHEDULED
  IN_TRANSIT
  DELIVERED
  INSPECTING
  ACCEPTED
  REJECTED
  PARTIALLY_ACCEPTED
}

enum QualityResult {
  PASSED
  FAILED
  CONDITIONAL
}

model DeliveryItem {
  id         String            @id @default(cuid())
  deliveryId String
  delivery   Delivery          @relation(fields: [deliveryId], references: [id], onDelete: Cascade)
  poItemId   String
  poItem     PurchaseOrderItem @relation(fields: [poItemId], references: [id], onDelete: Restrict)

  // Delivered quantities
  deliveredQty Float
  acceptedQty  Float @default(0)
  rejectedQty  Float @default(0)

  // Quality notes
  qualityNotes    String?
  rejectionReason String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([deliveryId])
  @@index([poItemId])
}

// -----------------------------
// GENERIC & SUPPORTING MODELS
// -----------------------------

model Approval {
  id          String         @id @default(cuid())
  sequence    Int
  status      ApprovalStatus @default(PENDING)
  approverId  String
  approver    User           @relation(fields: [approverId], references: [id], onDelete: Restrict)
  comments    String?
  processedAt DateTime?

  // Polymorphic relation fields
  poId          String?
  purchaseOrder PurchaseOrder? @relation(fields: [poId], references: [id], onDelete: Cascade)
  invoiceId     String?
  invoice       Invoice?       @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  bastId        String?
  bast          BAST?          @relation(fields: [bastId], references: [id], onDelete: Cascade)

  @@index([approverId, status])
}

model Document {
  id           String   @id @default(cuid())
  fileName     String
  fileUrl      String   @unique
  fileType     String // Mime type
  documentType String? // Document category/type
  description  String?
  uploadedAt   DateTime @default(now())

  // Enhanced document management fields
  fileSize       BigInt?
  checksum       String? // For integrity verification
  status         DocumentStatus @default(ACTIVE)
  isConfidential Boolean        @default(false)
  retentionDate  DateTime? // When document should be archived/deleted
  tags           String[] // Document tags for categorization

  // Version control
  version  Int        @default(1)
  parentId String? // For document versions
  parent   Document?  @relation("DocumentVersions", fields: [parentId], references: [id])
  versions Document[] @relation("DocumentVersions")

  // Access control
  uploadedById String
  uploadedBy   User   @relation("DocumentUploader", fields: [uploadedById], references: [id])

  // Approval workflow
  requiresApproval Boolean                @default(false)
  approvalStatus   DocumentApprovalStatus @default(NOT_REQUIRED)
  approvedAt       DateTime?
  approvedById     String?
  approvedBy       User?                  @relation("DocumentApprover", fields: [approvedById], references: [id])

  // Metadata
  metadata  Json     @default("{}")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Polymorphic relation fields
  vendorId      String?
  vendor        Vendor?              @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorOfferId String?
  vendorOffer   VendorOffer?         @relation(fields: [vendorOfferId], references: [id], onDelete: Cascade)
  poId          String?
  purchaseOrder PurchaseOrder?       @relation(fields: [poId], references: [id], onDelete: Cascade)
  invoiceId     String?
  invoice       Invoice?             @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  bastId        String?
  bast          BAST?                @relation(fields: [bastId], references: [id], onDelete: Cascade)
  procurementId String?
  procurement   Procurement?         @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  prId          String?
  pr            PurchaseRequisition? @relation("PRDocuments", fields: [prId], references: [id], onDelete: Cascade)

  // Enhanced relations
  permissions DocumentPermission[]
  auditLogs   DocumentAuditLog[]
  shares      DocumentShare[]

  @@index([documentType])
  @@index([status])
  @@index([uploadedById])
  @@index([approvalStatus])
  @@index([createdAt])
  @@index([tags])
}

// Document management supporting models
model DocumentPermission {
  id         String                 @id @default(cuid())
  documentId String
  document   Document               @relation(fields: [documentId], references: [id], onDelete: Cascade)
  userId     String?
  user       User?                  @relation("DocumentPermissionUser", fields: [userId], references: [id], onDelete: Cascade)
  roleId     String?
  role       Role?                  @relation("DocumentPermissionRole", fields: [roleId], references: [id], onDelete: Cascade)
  permission DocumentPermissionType
  grantedAt  DateTime               @default(now())
  grantedBy  String
  grantor    User                   @relation("DocumentPermissionGrantor", fields: [grantedBy], references: [id])
  expiresAt  DateTime?

  @@unique([documentId, userId, permission])
  @@unique([documentId, roleId, permission])
  @@index([documentId])
  @@index([userId])
  @@index([roleId])
}

model DocumentAuditLog {
  id         String              @id @default(cuid())
  documentId String
  document   Document            @relation(fields: [documentId], references: [id], onDelete: Cascade)
  action     DocumentAuditAction
  userId     String
  user       User                @relation("DocumentAuditUser", fields: [userId], references: [id])
  details    Json?
  ipAddress  String?
  userAgent  String?
  timestamp  DateTime            @default(now())

  @@index([documentId])
  @@index([userId])
  @@index([timestamp])
  @@index([action])
}

model DocumentShare {
  id          String            @id @default(cuid())
  documentId  String
  document    Document          @relation(fields: [documentId], references: [id], onDelete: Cascade)
  shareToken  String            @unique
  shareType   DocumentShareType @default(VIEW)
  expiresAt   DateTime?
  password    String? // Optional password protection
  createdBy   String
  creator     User              @relation("DocumentShareCreator", fields: [createdBy], references: [id])
  createdAt   DateTime          @default(now())
  accessedAt  DateTime? // Last accessed time
  accessCount Int               @default(0)

  @@index([shareToken])
  @@index([documentId])
  @@index([expiresAt])
}

model DiscussionThread {
  id                 String           @id @default(cuid())
  procurementStageId String           @unique
  procurementStage   ProcurementStage @relation(fields: [procurementStageId], references: [id], onDelete: Cascade)
  type               String // "AANWIJZING", "NEGOTIATION", "SANGGAHAN"
}

model VendorKpi {
  id            String   @id @default(cuid())
  vendorId      String
  vendor        Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  procurementId String // The procurement this KPI relates to
  period        String
  qualityScore  Int
  timeScore     Int
  costScore     Int
  serviceScore  Int
  overallScore  Float
  remarks       String?
  createdAt     DateTime @default(now())

  @@index([vendorId])
}

// -----------------------------
// DOCUMENT TEMPLATE MODELS
// -----------------------------

model DocumentTemplate {
  id          String                 @id @default(cuid())
  name        String
  description String?
  type        DocumentTemplateType
  category    String
  content     Json // Template content with components and layout
  variables   Json // Available variables/placeholders
  version     Int                    @default(1)
  status      DocumentTemplateStatus @default(DRAFT)
  isActive    Boolean                @default(false)
  createdBy   String
  updatedBy   String?
  approvedBy  String?
  approvedAt  DateTime?
  createdAt   DateTime               @default(now())
  updatedAt   DateTime               @updatedAt

  creator  User  @relation("TemplateCreator", fields: [createdBy], references: [id])
  updater  User? @relation("TemplateUpdater", fields: [updatedBy], references: [id])
  approver User? @relation("TemplateApprover", fields: [approvedBy], references: [id])

  versions DocumentTemplateVersion[]
  usages   GeneratedDocument[]

  @@map("document_templates")
}

model DocumentTemplateVersion {
  id         String   @id @default(cuid())
  templateId String
  version    Int
  content    Json
  variables  Json
  changelog  String?
  createdBy  String
  createdAt  DateTime @default(now())

  template DocumentTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  creator  User             @relation(fields: [createdBy], references: [id])

  @@unique([templateId, version])
  @@map("document_template_versions")
}

model GeneratedDocument {
  id           String        @id @default(cuid())
  templateId   String
  templateName String?
  name         String
  documentType DocumentType?
  entityType   String?
  entityId     String?
  data         Json // Data used to populate the template
  content      Json // Final rendered content
  pdfUrl       String?
  generatedAt  DateTime?
  status       String?
  version      Int           @default(1)
  metadata     Json?
  createdBy    String
  createdAt    DateTime      @default(now())

  template DocumentTemplate @relation(fields: [templateId], references: [id])
  creator  User             @relation(fields: [createdBy], references: [id])

  @@map("generated_documents")
}

enum DocumentTemplateType {
  RFQ
  CONTRACT
  PURCHASE_ORDER
  INVOICE
  BAST
  AANWIJZING
  EVALUATION_REPORT
  AWARD_LETTER
  CUSTOM
  PURCHASE_REQUISITION
  DELIVERY_NOTE
}

enum DocumentType {
  RFQ
  CONTRACT
  PURCHASE_ORDER
  INVOICE
  BAST
  AANWIJZING
  EVALUATION_REPORT
  AWARD_LETTER
  CUSTOM
  PURCHASE_REQUISITION
  DELIVERY_NOTE
}

enum DocumentTemplateStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  REJECTED
  ARCHIVED
}

// Document management enums
enum DocumentStatus {
  ACTIVE
  ARCHIVED
  DELETED
  QUARANTINED
}

enum DocumentApprovalStatus {
  NOT_REQUIRED
  PENDING
  APPROVED
  REJECTED
  EXPIRED
}

enum DocumentPermissionType {
  VIEW
  EDIT
  DELETE
  SHARE
  APPROVE
  ADMIN
}

enum DocumentAuditAction {
  CREATED
  VIEWED
  DOWNLOADED
  EDITED
  DELETED
  SHARED
  APPROVED
  REJECTED
  PERMISSION_GRANTED
  PERMISSION_REVOKED
}

enum DocumentShareType {
  VIEW
  EDIT
  DOWNLOAD
}

// -----------------------------
// PROCUREMENT WORKFLOW TEMPLATE MODELS
// -----------------------------

model ProcurementWorkflowTemplate {
  id          String  @id @default(cuid())
  name        String
  description String?
  type        String // "TENDER", "RFQ", "DIRECT_PURCHASE", "FRAMEWORK"
  category    String // "GOODS", "SERVICES", "CONSTRUCTION"
  isDefault   Boolean @default(false)
  isActive    Boolean @default(true)

  // Template configuration
  config Json // Workflow configuration including stages, rules, conditions

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String
  creator   User     @relation("WorkflowTemplateCreator", fields: [createdBy], references: [id], onDelete: Restrict)

  // Relations
  stages             ProcurementWorkflowStageTemplate[]
  vendorRequirements VendorRequirementTemplate[]
  scheduleTemplates  ProcurementScheduleTemplate[]
  procurements       Procurement[]                      @relation("ProcurementWorkflowTemplate")

  @@index([type, category])
  @@index([isDefault, isActive])
}

model ProcurementWorkflowStageTemplate {
  id         String                      @id @default(cuid())
  templateId String
  template   ProcurementWorkflowTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  name        String
  description String?
  sequence    Int
  type        String // "ANNOUNCEMENT", "SUBMISSION", "EVALUATION", "AWARD", "CONTRACT"
  isRequired  Boolean @default(true)

  // Stage configuration
  config Json // Stage-specific configuration, rules, and conditions

  // Duration settings
  minDuration Int? // Minimum duration in hours
  maxDuration Int? // Maximum duration in hours

  // Dependencies
  dependencies Json? // Dependencies on other stages

  // Approval requirements
  requiresApproval Boolean @default(false)
  approvalConfig   Json? // Approval configuration for this stage

  // Document requirements
  requiredDocuments Json? // Required documents for this stage

  @@unique([templateId, sequence])
  @@index([templateId])
}

// -----------------------------
// VENDOR REQUIREMENTS MODELS
// -----------------------------

model VendorRequirementTemplate {
  id         String                       @id @default(cuid())
  templateId String?
  template   ProcurementWorkflowTemplate? @relation(fields: [templateId], references: [id], onDelete: Cascade)

  name        String
  description String?
  category    String // "LEGAL", "FINANCIAL", "TECHNICAL", "EXPERIENCE"
  type        String // "MANDATORY", "PREFERRED", "SCORING"

  // Requirement configuration
  criteria Json // Detailed criteria and scoring rules
  weight   Float? // Weight for scoring requirements

  // Validation rules
  validationRules Json? // Rules for validating vendor compliance

  // Document requirements
  requiredDocuments Json? // Required supporting documents

  // Metadata
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String
  creator   User     @relation("VendorRequirementCreator", fields: [createdBy], references: [id], onDelete: Restrict)

  // Relations
  procurementRequirements ProcurementVendorRequirement[]

  @@index([category, type])
  @@index([templateId])
}

model ProcurementVendorRequirement {
  id            String                    @id @default(cuid())
  procurementId String
  procurement   Procurement               @relation("ProcurementVendorRequirements", fields: [procurementId], references: [id], onDelete: Cascade)
  requirementId String
  requirement   VendorRequirementTemplate @relation(fields: [requirementId], references: [id], onDelete: Restrict)

  // Customized values for this procurement
  customCriteria Json? // Procurement-specific criteria overrides
  customWeight   Float? // Procurement-specific weight override
  isRequired     Boolean @default(true)

  @@unique([procurementId, requirementId])
  @@index([procurementId])
}

// -----------------------------
// PROCUREMENT SCHEDULE MODELS
// -----------------------------

model ProcurementScheduleTemplate {
  id         String                      @id @default(cuid())
  templateId String
  template   ProcurementWorkflowTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  name        String
  description String?

  // Schedule configuration
  stages     Json // Stage timing and dependencies
  milestones Json // Key milestones and deadlines
  buffers    Json // Buffer times between stages

  // Working time configuration
  workingDays Json // Working days configuration
  holidays    Json? // Holiday calendar

  // Metadata
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String
  creator   User     @relation("ScheduleTemplateCreator", fields: [createdBy], references: [id], onDelete: Restrict)

  // Relations
  procurementSchedules ProcurementSchedule[]

  @@index([templateId])
}

model ProcurementSchedule {
  id            String                       @id @default(cuid())
  procurementId String                       @unique
  procurement   Procurement                  @relation("ProcurementSchedule", fields: [procurementId], references: [id], onDelete: Cascade)
  templateId    String?
  template      ProcurementScheduleTemplate? @relation(fields: [templateId], references: [id], onDelete: SetNull)

  // Schedule data
  schedule Json // Actual schedule with dates and milestones

  // Status tracking
  currentStage String?
  progress     Float   @default(0) // Progress percentage

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String
  creator   User     @relation("ProcurementScheduleCreator", fields: [createdBy], references: [id], onDelete: Restrict)

  // Relations
  milestones ProcurementMilestone[]

  @@index([procurementId])
}

model ProcurementMilestone {
  id         String              @id @default(cuid())
  scheduleId String
  schedule   ProcurementSchedule @relation(fields: [scheduleId], references: [id], onDelete: Cascade)

  name        String
  description String?
  type        String // "STAGE_START", "STAGE_END", "DEADLINE", "REVIEW"

  // Timing
  plannedDate DateTime
  actualDate  DateTime?

  // Status
  status String @default("PENDING") // "PENDING", "IN_PROGRESS", "COMPLETED", "OVERDUE"

  // Dependencies
  dependencies Json? // Dependencies on other milestones

  // Notifications
  notifyBefore Int? // Hours before to send notification

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([scheduleId])
  @@index([plannedDate])
}

// -----------------------------
// NOTIFICATION MODELS
// -----------------------------

model Notification {
  id               String           @id @default(cuid())
  userId           String
  title            String
  message          String
  type             NotificationType @default(INFO)
  notificationType String? // e.g., "BAST_APPROVAL_REQUEST"
  read             Boolean          @default(false)
  metadata         Json? // Additional data for the notification
  createdAt        DateTime         @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
}

// -----------------------------
// CONTENT MANAGEMENT MODELS
// -----------------------------

model NewsCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  articles NewsArticle[]

  @@index([slug])
  @@index([isActive])
}

model NewsArticle {
  id            String        @id @default(cuid())
  title         String
  slug          String        @unique
  excerpt       String?
  content       String        @db.Text
  featuredImage String?
  status        ArticleStatus @default(DRAFT)
  publishedAt   DateTime?
  scheduledAt   DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  authorId   String
  author     User         @relation(fields: [authorId], references: [id], onDelete: Restrict)
  categoryId String
  category   NewsCategory @relation(fields: [categoryId], references: [id], onDelete: Restrict)

  // Content approval workflow
  approvals ContentApproval[]

  @@index([slug])
  @@index([status])
  @@index([publishedAt])
  @@index([authorId])
  @@index([categoryId])
}

enum ArticleStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  PUBLISHED
  ARCHIVED
}

model ContentApproval {
  id          String         @id @default(cuid())
  contentType String // "news_article", "announcement", etc.
  contentId   String
  status      ApprovalStatus @default(PENDING)
  comments    String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  requestedById String
  requestedBy   User    @relation("ContentApprovalRequester", fields: [requestedById], references: [id], onDelete: Restrict)
  reviewedById  String?
  reviewedBy    User?   @relation("ContentApprovalReviewer", fields: [reviewedById], references: [id], onDelete: Restrict)

  // Polymorphic relations
  newsArticle NewsArticle? @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@index([contentType, contentId])
  @@index([status])
  @@index([requestedById])
  @@index([reviewedById])
}

// Comprehensive Content Management Model
model Content {
  id          String        @id @default(cuid())
  type        String // "news", "announcement", "banner", "logo", "document", "page", "hero_section", "footer_content"
  title       String
  slug        String? // For content types that need URLs
  content     String?       @db.Text // Main content/body
  excerpt     String? // Short description/summary
  imageUrl    String? // For banners, logos, featured images
  fileUrl     String? // For documents, downloads
  status      ContentStatus @default(DRAFT)
  publishedAt DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  metadata    Json          @default("{}") // Flexible metadata storage

  // Relations
  authorId String
  author   User   @relation("ContentAuthor", fields: [authorId], references: [id], onDelete: Restrict)

  @@unique([slug, type]) // Ensure unique slugs per content type
  @@index([type])
  @@index([status])
  @@index([slug])
  @@index([publishedAt])
  @@index([authorId])
}

enum ContentStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Procurement Content Management
model ProcurementContent {
  id            String                 @id @default(cuid())
  title         String
  slug          String                 @unique
  excerpt       String?
  content       String                 @db.Text
  type          ProcurementContentType
  status        ContentStatus          @default(DRAFT)
  featuredImage String?
  publishedAt   DateTime?
  createdAt     DateTime               @default(now())
  updatedAt     DateTime               @updatedAt

  // Relations
  authorId String
  author   User   @relation("ProcurementContentAuthor", fields: [authorId], references: [id], onDelete: Restrict)

  @@index([type])
  @@index([status])
  @@index([slug])
  @@index([publishedAt])
  @@index([authorId])
}

enum ProcurementContentType {
  GUIDELINE
  ANNOUNCEMENT
  PROCEDURE
  FAQ
  TERMS
}

// About Page Content Management (Structured)
model AboutPageContent {
  id String @id @default(cuid())

  // Hero Section
  heroTitle       String @default("Tentang E-Procurement")
  heroSubtitle    String @default("PT Bank BPD Sulteng")
  heroDescription String @default("Sistem pengadaan elektronik yang mendukung transparansi, efisiensi, dan akuntabilitas dalam proses pengadaan barang dan jasa") @db.Text

  // About E-Procurement Section
  aboutTitle       String @default("Apa itu E-Procurement?")
  aboutDescription String @default("E-Procurement adalah sistem pengadaan elektronik yang memungkinkan proses pengadaan barang dan jasa dilakukan secara digital, transparan, dan efisien.") @db.Text

  // Company Profile Section
  companyTitle       String @default("Tentang PT Bank BPD Sulteng")
  companyDescription String @default("Bank Pembangunan Daerah Sulawesi Tengah yang berkomitmen pada pelayanan terbaik") @db.Text
  companyName        String @default("PT Bank BPD Sulteng")
  companyFullName    String @default("Bank Pembangunan Daerah Sulawesi Tengah")
  operationalArea    String @default("Sulawesi Tengah dan sekitarnya")
  vision             String @default("Menjadi bank pilihan utama yang mendukung pembangunan ekonomi daerah") @db.Text

  // E-Procurement Features (JSON array)
  features Json @default("[\"Proses tender yang transparan dan fair\", \"Dokumentasi digital yang lengkap\", \"Evaluasi vendor yang objektif\", \"Monitoring real-time status pengadaan\", \"Integrasi dengan sistem keuangan\", \"Audit trail yang komprehensif\"]")

  // Contact Information
  contactTitle       String @default("Informasi Kontak")
  contactDescription String @default("Hubungi kami untuk informasi lebih lanjut tentang e-procurement")
  address            String @default("Jl. Contoh No. 123\nPalu, Sulawesi Tengah\n94111") @db.Text
  phones             Json   @default("[\"+62 **********\", \"+62 **********\"]")
  emails             Json   @default("[\"<EMAIL>\", \"<EMAIL>\"]")

  // CTA Section
  ctaTitle       String @default("Siap Bermitra dengan Kami?")
  ctaDescription String @default("Bergabunglah dengan vendor-vendor terpercaya lainnya dalam sistem e-procurement kami")

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  updatedById String
  updatedBy   User     @relation("AboutPageContentUpdater", fields: [updatedById], references: [id])

  @@map("about_page_content")
}

// -----------------------------
// ENHANCED DYNAMIC APPROVAL WORKFLOW MODELS
// -----------------------------

model ApprovalWorkflow {
  id          String   @id @default(cuid())
  name        String
  description String?
  entityType  String // "procurement", "vendor", "contract", "payment", etc.
  stage       String? // Specific stage like "vendor_registration", "rfq_creation", "offer_evaluation"
  isActive    Boolean  @default(true)
  isDefault   Boolean  @default(false)
  version     Int      @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Conditions for when this workflow applies
  conditions Json? // Dynamic conditions like value thresholds, categories, etc.

  // Relations
  createdById String
  createdBy   User   @relation("WorkflowCreator", fields: [createdById], references: [id], onDelete: Restrict)

  steps     ApprovalStep[]
  instances ApprovalInstance[]

  @@index([entityType])
  @@index([stage])
  @@index([isActive])
  @@index([isDefault])
}

// Workflow configuration model for compatibility
model ApprovalWorkflowConfig {
  id          String   @id @default(cuid())
  name        String
  description String?
  entityType  String
  stage       String?
  isActive    Boolean  @default(true)
  config      Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([entityType])
  @@index([isActive])
}

model ApprovalStep {
  id         String           @id @default(cuid())
  workflowId String
  workflow   ApprovalWorkflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  name        String
  description String?
  sequence    Int // Order of execution
  stepType    ApprovalStepType
  isRequired  Boolean          @default(true)

  // Dynamic configuration
  config Json? // Step-specific configuration

  // Approval requirements
  approverType    ApproverType
  approverConfig  Json? // Configuration for approver selection
  requiredCount   Int          @default(1) // Number of approvals needed
  allowDelegation Boolean      @default(false)
  timeoutHours    Int? // Auto-escalation timeout

  // Signature configuration for documents
  signatureConfig Json? // Position, size, and other signature settings

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  stepApprovals ApprovalStepInstance[]

  @@unique([workflowId, sequence])
  @@index([workflowId])
  @@index([sequence])
}

enum ApprovalStepType {
  APPROVAL // Standard approval
  REVIEW // Review only (no approval power)
  NOTIFICATION // Notification only
  CONDITIONAL // Conditional step based on data
  PARALLEL // Parallel approval (multiple approvers)
  SEQUENTIAL // Sequential approval (one after another)
  ESCALATION // Escalation step
  SIGNATURE // Digital signature step
}

enum ApproverType {
  SPECIFIC_USER // Specific user(s)
  ROLE_BASED // Users with specific role(s)
  DEPARTMENT // Department head/members
  HIERARCHY // Based on organizational hierarchy
  DYNAMIC // Dynamically determined based on conditions
  COMMITTEE // Committee-based approval
  EXTERNAL // External approver (email notification)
  VALUE_THRESHOLD // Based on procurement value thresholds
}

model ApprovalInstance {
  id         String           @id @default(cuid())
  workflowId String
  workflow   ApprovalWorkflow @relation(fields: [workflowId], references: [id], onDelete: Restrict)

  entityType String // Same as workflow entityType
  entityId   String // ID of the entity being approved
  stage      String? // Specific stage being approved

  status   ApprovalInstanceStatus @default(PENDING)
  priority ApprovalPriority       @default(NORMAL)

  // Metadata
  title            String?
  description      String?
  metadata         Json? // Additional data about the approval
  context          Json? // Additional context data
  startedBy        String? // User who started the approval
  workflowConfigId String? // Configuration ID for workflow

  // Timing
  startedAt   DateTime  @default(now())
  completedAt DateTime?
  dueDate     DateTime?

  // Relations
  initiatedById String
  initiatedBy   User   @relation("ApprovalInitiator", fields: [initiatedById], references: [id], onDelete: Restrict)

  stepInstances ApprovalStepInstance[]
  comments      ApprovalComment[]

  // Purchase Requisition relation
  purchaseRequisitionId String?              @unique
  purchaseRequisition   PurchaseRequisition? @relation("PRApprovalInstance", fields: [purchaseRequisitionId], references: [id])

  @@index([entityType, entityId])
  @@index([stage])
  @@index([status])
  @@index([initiatedById])
  @@index([dueDate])
}

enum ApprovalInstanceStatus {
  PENDING
  IN_PROGRESS
  APPROVED
  REJECTED
  CANCELLED
  EXPIRED
  TIMEOUT
}

enum ApprovalPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

model ApprovalStepInstance {
  id         String           @id @default(cuid())
  instanceId String
  instance   ApprovalInstance @relation(fields: [instanceId], references: [id], onDelete: Cascade)
  stepId     String
  step       ApprovalStep     @relation(fields: [stepId], references: [id], onDelete: Restrict)

  status   ApprovalStepStatus @default(PENDING)
  sequence Int // Copied from step for performance

  // Timing
  startedAt   DateTime?
  completedAt DateTime?
  dueDate     DateTime?

  // Results
  decision           ApprovalDecision?
  comments           String?
  metadata           Json?
  assignedTo         String? // User assigned to this step
  approvalInstanceId String? // Additional reference

  // Relations
  approvals ApprovalAction[]

  @@index([instanceId])
  @@index([stepId])
  @@index([status])
  @@index([sequence])
}

enum ApprovalStepStatus {
  PENDING
  IN_PROGRESS
  APPROVED
  REJECTED
  SKIPPED
  EXPIRED
  TIMEOUT
}

enum ApprovalDecision {
  APPROVED
  REJECTED
  DELEGATED
  ESCALATED
  RETURNED
}

model ApprovalAction {
  id             String               @id @default(cuid())
  stepInstanceId String
  stepInstance   ApprovalStepInstance @relation(fields: [stepInstanceId], references: [id], onDelete: Cascade)

  action     ApprovalActionType
  actionType String? // Additional action type field
  decision   ApprovalDecision?
  comments   String?

  // Delegation
  delegatedToId String?
  delegatedTo   User?   @relation("ApprovalActionDelegate", fields: [delegatedToId], references: [id], onDelete: Restrict)

  // Metadata
  metadata  Json?
  ipAddress String?
  userAgent String?

  createdAt DateTime @default(now())

  // Relations
  performedById String
  performedBy   User   @relation("ApprovalActionPerformer", fields: [performedById], references: [id], onDelete: Restrict)

  @@index([stepInstanceId])
  @@index([performedById])
  @@index([action])
  @@index([createdAt])
}

enum ApprovalActionType {
  APPROVE
  REJECT
  DELEGATE
  REQUEST_INFO
  REQUEST_CHANGES
  ESCALATE
  COMMENT
  SIGN
}

model ApprovalComment {
  id         String           @id @default(cuid())
  instanceId String
  instance   ApprovalInstance @relation(fields: [instanceId], references: [id], onDelete: Cascade)

  content    String  @db.Text
  isInternal Boolean @default(false) // Internal comments vs public

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  authorId String
  author   User   @relation("ApprovalCommentAuthor", fields: [authorId], references: [id], onDelete: Restrict)

  @@index([instanceId])
  @@index([authorId])
  @@index([createdAt])
}

// ApprovalDelegation model for handling delegation of approval authority
model ApprovalDelegation {
  id         String   @id @default(cuid())
  fromUserId String // User delegating their authority
  toUserId   String // User receiving the delegation
  entityType String // Type of entity this delegation applies to
  reason     String? // Reason for delegation
  startDate  DateTime // When delegation starts
  endDate    DateTime // When delegation ends
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  fromUser User @relation("DelegationFrom", fields: [fromUserId], references: [id], onDelete: Cascade)
  toUser   User @relation("DelegationTo", fields: [toUserId], references: [id], onDelete: Cascade)

  @@index([fromUserId])
  @@index([toUserId])
  @@index([entityType])
  @@index([isActive])
  @@index([startDate, endDate])
}

// -----------------------------
// DISCUSSION & NEGOTIATION MODELS
// -----------------------------

model ProcurementDiscussion {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)

  // Discussion details
  title       String
  description String?
  type        DiscussionType
  status      DiscussionStatus @default(ACTIVE)

  // Aanwijzing (Pre-bid conference) specific
  meetingDate     DateTime?
  meetingLocation String?
  meetingType     MeetingType?
  maxParticipants Int?

  // Access control
  isPublic       Boolean @default(true)
  allowAnonymous Boolean @default(false)

  // Timing
  startDate DateTime  @default(now())
  endDate   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdById String
  createdBy   User   @relation("DiscussionCreator", fields: [createdById], references: [id], onDelete: Restrict)

  messages     DiscussionMessage[]
  participants DiscussionParticipant[]
  attachments  DiscussionAttachment[]

  @@index([procurementId])
  @@index([type])
  @@index([status])
  @@index([createdById])
}

enum DiscussionType {
  AANWIJZING // Pre-bid conference
  QA_SESSION // Question & Answer
  CLARIFICATION // Clarification requests
  NEGOTIATION // Price/terms negotiation
  TECHNICAL_DISCUSSION // Technical discussions
  GENERAL // General discussion
}

enum DiscussionStatus {
  ACTIVE
  CLOSED
  ARCHIVED
}

enum MeetingType {
  PHYSICAL
  VIRTUAL
  HYBRID
}

model DiscussionMessage {
  id           String                @id @default(cuid())
  discussionId String
  discussion   ProcurementDiscussion @relation(fields: [discussionId], references: [id], onDelete: Cascade)

  // Message content
  content     String      @db.Text
  messageType MessageType @default(MESSAGE)

  // Threading
  parentId String?
  parent   DiscussionMessage?  @relation("MessageReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies  DiscussionMessage[] @relation("MessageReplies")

  // Status
  isEdited   Boolean @default(false)
  isDeleted  Boolean @default(false)
  isOfficial Boolean @default(false) // Official response from procurement team

  // Visibility
  isPublic    Boolean @default(true)
  isAnonymous Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  authorId String
  author   User   @relation("MessageAuthor", fields: [authorId], references: [id], onDelete: Restrict)

  attachments MessageAttachment[]
  reactions   MessageReaction[]

  @@index([discussionId])
  @@index([authorId])
  @@index([parentId])
  @@index([createdAt])
}

enum MessageType {
  MESSAGE
  QUESTION
  ANSWER
  CLARIFICATION
  ANNOUNCEMENT
  SYSTEM
}

model DiscussionParticipant {
  id           String                @id @default(cuid())
  discussionId String
  discussion   ProcurementDiscussion @relation(fields: [discussionId], references: [id], onDelete: Cascade)
  userId       String
  user         User                  @relation("DiscussionParticipants", fields: [userId], references: [id], onDelete: Cascade)

  // Participation details
  role       ParticipantRole @default(PARTICIPANT)
  joinedAt   DateTime        @default(now())
  lastSeenAt DateTime?

  // Permissions
  canPost     Boolean @default(true)
  canModerate Boolean @default(false)

  // Status
  isActive Boolean @default(true)

  @@unique([discussionId, userId])
  @@index([discussionId])
  @@index([userId])
}

enum ParticipantRole {
  MODERATOR
  FACILITATOR
  PARTICIPANT
  OBSERVER
}

model DiscussionAttachment {
  id           String                @id @default(cuid())
  discussionId String
  discussion   ProcurementDiscussion @relation(fields: [discussionId], references: [id], onDelete: Cascade)

  // File details
  fileName     String
  originalName String
  fileSize     Int
  mimeType     String
  filePath     String

  // Metadata
  description String?
  isPublic    Boolean @default(true)

  createdAt DateTime @default(now())

  // Relations
  uploadedById String
  uploadedBy   User   @relation("AttachmentUploader", fields: [uploadedById], references: [id], onDelete: Restrict)

  @@index([discussionId])
  @@index([uploadedById])
}

model MessageAttachment {
  id        String            @id @default(cuid())
  messageId String
  message   DiscussionMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)

  // File details
  fileName     String
  originalName String
  fileSize     Int
  mimeType     String
  filePath     String

  // Metadata
  description String?

  createdAt DateTime @default(now())

  // Relations
  uploadedById String
  uploadedBy   User   @relation("MessageAttachmentUploader", fields: [uploadedById], references: [id], onDelete: Restrict)

  @@index([messageId])
  @@index([uploadedById])
}

model MessageReaction {
  id        String            @id @default(cuid())
  messageId String
  message   DiscussionMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  userId    String
  user      User              @relation("MessageReactions", fields: [userId], references: [id], onDelete: Cascade)

  // Reaction details
  emoji     String // Unicode emoji
  createdAt DateTime @default(now())

  @@unique([messageId, userId, emoji])
  @@index([messageId])
  @@index([userId])
}

// Negotiation-specific models
model NegotiationSession {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Restrict)
  vendorId      String
  vendor        Vendor      @relation(fields: [vendorId], references: [id], onDelete: Restrict)

  // Session details
  title       String
  description String?
  status      NegotiationStatus @default(PENDING)

  // Timing
  startDate   DateTime?
  endDate     DateTime?
  scheduledAt DateTime?

  // Results
  finalPrice  Float?
  agreedTerms Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  initiatedById String
  initiatedBy   User   @relation("NegotiationInitiator", fields: [initiatedById], references: [id], onDelete: Restrict)

  rounds NegotiationRound[]

  @@index([procurementId])
  @@index([vendorId])
  @@index([status])
}

enum NegotiationStatus {
  PENDING
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

model NegotiationRound {
  id        String             @id @default(cuid())
  sessionId String
  session   NegotiationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  // Round details
  roundNumber   Int
  proposedPrice Float?
  proposedTerms Json?
  counterPrice  Float?
  counterTerms  Json?

  // Status
  status RoundStatus @default(PENDING)

  // Notes
  vendorNotes String?
  buyerNotes  String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  proposedById  String
  proposedBy    User    @relation("RoundProposer", fields: [proposedById], references: [id], onDelete: Restrict)
  respondedById String?
  respondedBy   User?   @relation("RoundResponder", fields: [respondedById], references: [id], onDelete: Restrict)

  @@index([sessionId])
  @@index([roundNumber])
}

enum RoundStatus {
  PENDING
  RESPONDED
  ACCEPTED
  REJECTED
  EXPIRED
}

// -----------------------------
// VENDOR KPI & PERFORMANCE MODELS
// -----------------------------

model VendorKpiTemplate {
  id          String      @id @default(cuid())
  name        String
  description String?
  category    KpiCategory
  isActive    Boolean     @default(true)

  // KPI Configuration
  metrics    Json // Array of metric definitions
  weights    Json // Weights for each metric
  thresholds Json // Performance thresholds (excellent, good, fair, poor)

  // Calculation settings
  calculationMethod CalculationMethod @default(WEIGHTED_AVERAGE)
  evaluationPeriod  EvaluationPeriod  @default(QUARTERLY)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdById String
  createdBy   User   @relation("KpiTemplateCreator", fields: [createdById], references: [id], onDelete: Restrict)

  evaluations VendorKpiEvaluation[]

  @@index([category])
  @@index([isActive])
}

enum KpiCategory {
  QUALITY
  DELIVERY
  COST
  SERVICE
  COMPLIANCE
  INNOVATION
  OVERALL
}

enum CalculationMethod {
  WEIGHTED_AVERAGE
  SIMPLE_AVERAGE
  MINIMUM_SCORE
  MAXIMUM_SCORE
  CUSTOM_FORMULA
}

enum EvaluationPeriod {
  MONTHLY
  QUARTERLY
  SEMI_ANNUAL
  ANNUAL
  PROJECT_BASED
}

model VendorKpiEvaluation {
  id         String            @id @default(cuid())
  vendorId   String
  vendor     Vendor            @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  templateId String
  template   VendorKpiTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)

  // Evaluation period
  evaluationPeriod String // e.g., "2024-Q1", "2024-01", "PROJECT-123"
  startDate        DateTime
  endDate          DateTime

  // Scores
  overallScore   Float
  categoryScores Json // Scores by category
  metricScores   Json // Individual metric scores

  // Performance rating
  rating PerformanceRating

  // Comments and feedback
  strengths       String?
  weaknesses      String?
  recommendations String?

  // Status
  status EvaluationStatus @default(DRAFT)

  // Approval
  approvedAt   DateTime?
  approvedById String?
  approvedBy   User?     @relation("KpiEvaluationApprover", fields: [approvedById], references: [id], onDelete: Restrict)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  evaluatedById String
  evaluatedBy   User             @relation("KpiEvaluator", fields: [evaluatedById], references: [id], onDelete: Restrict)
  scores        VendorKpiScore[]

  @@unique([vendorId, templateId, evaluationPeriod])
  @@index([vendorId])
  @@index([templateId])
  @@index([evaluationPeriod])
  @@index([rating])
}

enum PerformanceRating {
  EXCELLENT
  GOOD
  SATISFACTORY
  NEEDS_IMPROVEMENT
  POOR
}

enum EvaluationStatus {
  DRAFT
  PENDING_REVIEW
  APPROVED
  PUBLISHED
}

model VendorPerformanceHistory {
  id       String @id @default(cuid())
  vendorId String
  vendor   Vendor @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  // Performance data
  period          String // e.g., "2024-Q1"
  overallScore    Float
  qualityScore    Float?
  deliveryScore   Float?
  costScore       Float?
  serviceScore    Float?
  complianceScore Float?

  // Ranking
  rank         Int?
  totalVendors Int?

  // Trend analysis
  previousScore Float?
  scoreChange   Float?
  trend         PerformanceTrend?

  createdAt DateTime @default(now())

  @@unique([vendorId, period])
  @@index([vendorId])
  @@index([period])
  @@index([overallScore])
}

enum PerformanceTrend {
  IMPROVING
  STABLE
  DECLINING
}

model VendorKpiMetric {
  id       String @id @default(cuid())
  vendorId String
  vendor   Vendor @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  // Metric details
  metricName String
  metricType MetricType
  category   KpiCategory

  // Values
  targetValue     Float?
  actualValue     Float
  achievementRate Float? // Percentage of target achieved

  // Scoring
  score  Float // Normalized score (0-100)
  weight Float // Weight in overall calculation

  // Period
  evaluationPeriod String
  recordedDate     DateTime @default(now())

  // Source
  sourceType MetricSource
  sourceId   String? // ID of source record (procurement, contract, etc.)

  // Metadata
  notes            String?
  isAutoCalculated Boolean @default(false)

  createdAt DateTime @default(now())

  // Relations
  recordedById String
  recordedBy   User             @relation("MetricRecorder", fields: [recordedById], references: [id], onDelete: Restrict)
  scores       VendorKpiScore[]

  @@index([vendorId])
  @@index([metricName])
  @@index([category])
  @@index([evaluationPeriod])
}

model VendorKpiScore {
  id           String              @id @default(cuid())
  evaluationId String
  evaluation   VendorKpiEvaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)
  metricId     String
  metric       VendorKpiMetric     @relation(fields: [metricId], references: [id], onDelete: Cascade)
  score        Float

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([evaluationId, metricId])
  @@index([evaluationId])
  @@index([metricId])
}

model VendorIssue {
  id       String @id @default(cuid())
  vendorId String
  vendor   Vendor @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  title       String
  description String @db.Text
  category    String // DELIVERY, QUALITY, COMMUNICATION, COMPLIANCE, etc.
  severity    String // LOW, MEDIUM, HIGH, CRITICAL
  status      String @default("OPEN") // OPEN, IN_PROGRESS, RESOLVED, CLOSED

  reportedById String
  reportedBy   User    @relation("VendorIssueReporter", fields: [reportedById], references: [id])
  assignedToId String?
  assignedTo   User?   @relation("VendorIssueAssignee", fields: [assignedToId], references: [id])

  reportedAt DateTime  @default(now())
  resolvedAt DateTime?
  closedAt   DateTime?

  // Resolution details
  resolution     String? @db.Text
  resolutionTime Int? // in hours

  // Related entities
  procurementId   String?
  procurement     Procurement?   @relation(fields: [procurementId], references: [id])
  purchaseOrderId String?
  purchaseOrder   PurchaseOrder? @relation(fields: [purchaseOrderId], references: [id])

  metadata Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([vendorId])
  @@index([status])
  @@index([severity])
  @@index([reportedById])
  @@index([assignedToId])
}

enum MetricType {
  PERCENTAGE
  RATIO
  COUNT
  DURATION
  CURRENCY
  SCORE
  BOOLEAN
}

enum MetricSource {
  MANUAL_ENTRY
  PROCUREMENT_DATA
  CONTRACT_DATA
  DELIVERY_DATA
  INVOICE_DATA
  SURVEY_DATA
  SYSTEM_CALCULATED
}

// Vendor blacklist management
model BlacklistEntry {
  id       String @id @default(cuid())
  vendorId String
  vendor   Vendor @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  // Blacklist details
  reason      String
  description String?
  severity    BlacklistSeverity
  category    BlacklistCategory

  // Duration
  startDate   DateTime  @default(now())
  endDate     DateTime?
  isPermanent Boolean   @default(false)

  // Status
  status BlacklistStatus @default(ACTIVE)

  // Appeal process
  appealSubmitted   Boolean       @default(false)
  appealDate        DateTime?
  appealReason      String?
  appealStatus      AppealStatus?
  appealDecision    String?
  appealDecidedAt   DateTime?
  appealDecidedById String?
  appealDecidedBy   User?         @relation("AppealDecider", fields: [appealDecidedById], references: [id], onDelete: Restrict)

  // Evidence and documentation
  evidenceFiles    Json? // Array of file paths
  relatedContracts Json? // Array of contract IDs

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdById           String
  createdBy             User                   @relation("BlacklistCreator", fields: [createdById], references: [id], onDelete: Restrict)
  sanctionedIndividuals SanctionedIndividual[]

  @@index([vendorId])
  @@index([status])
  @@index([severity])
  @@index([startDate])
  @@index([endDate])
}

enum BlacklistSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum BlacklistCategory {
  QUALITY_ISSUES
  DELIVERY_DELAYS
  CONTRACT_BREACH
  FRAUD
  CORRUPTION
  NON_COMPLIANCE
  POOR_PERFORMANCE
  LEGAL_ISSUES
  OTHER
}

enum BlacklistStatus {
  ACTIVE
  SUSPENDED
  EXPIRED
  APPEALED
  REVOKED
}

enum AppealStatus {
  PENDING
  UNDER_REVIEW
  APPROVED
  REJECTED
}

// -----------------------------
// TAX MANAGEMENT MODELS
// -----------------------------

model TaxType {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  rate        Float // Tax rate as percentage
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  exemptions TaxExemption[]
}

model TaxExemption {
  id         String    @id @default(cuid())
  taxTypeId  String
  taxType    TaxType   @relation(fields: [taxTypeId], references: [id], onDelete: Cascade)
  entityType String // "vendor", "procurement", "item"
  entityId   String
  reason     String
  validFrom  DateTime
  validUntil DateTime?
  isActive   Boolean   @default(true)
  createdBy  String
  createdAt  DateTime  @default(now())

  creator User @relation("TaxExemptionCreator", fields: [createdBy], references: [id])

  @@index([entityType, entityId])
  @@index([taxTypeId])
  @@index([isActive])
}

// -----------------------------
// COMPREHENSIVE AUDIT TRAIL SYSTEM
// -----------------------------

model AuditLog {
  id String @id @default(cuid())

  // User information
  userId    String?
  user      User?   @relation(fields: [userId], references: [id], onDelete: SetNull)
  userEmail String? // Backup in case user is deleted
  userRole  String?

  // Action details
  action     AuditAction
  resource   String // Table/entity name
  resourceId String? // ID of the affected record
  entityType String? // Additional entity type field
  entityId   String? // Additional entity ID field

  // Change tracking
  oldValues     Json? // Before values
  newValues     Json? // After values
  changedFields Json? // Array of changed field names
  details       Json? // Additional details field

  // Request metadata
  ipAddress String?
  userAgent String?
  sessionId String?
  requestId String?

  // Business context
  procurementId String? // Related procurement if applicable
  workflowStage String? // Current workflow stage
  approvalStep  String? // Current approval step

  // Classification
  severity AuditSeverity @default(LOW)
  category AuditCategory @default(GENERAL)

  // Additional metadata
  metadata    Json? // Additional context data
  description String? // Human-readable description

  // Timing
  timestamp DateTime @default(now())
  createdAt DateTime @default(now()) // Additional created at field

  // Partitioning field for performance
  partitionDate DateTime @default(now()) @db.Date

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([resourceId])
  @@index([timestamp])
  @@index([partitionDate])
  @@index([procurementId])
  @@index([severity])
  @@index([category])
  @@index([entityType, entityId])
  @@map("audit_logs")
}

enum AuditAction {
  CREATE
  READ
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  APPROVE
  REJECT
  SUBMIT
  CANCEL
  EXPORT
  IMPORT
  UPLOAD
  DOWNLOAD
  SEND_EMAIL
  GENERATE_DOCUMENT
  SIGN_DOCUMENT
  ESCALATE
  DELEGATE
  COMMENT
  BLACKLIST
  UNBLACKLIST
  VERIFY
  SUSPEND
  ACTIVATE
  ARCHIVE
  RESTORE
  NEGOTIATE
  EVALUATE
  AWARD
  PRICE_CORRECT
  STATUS_CHANGE
  WORKFLOW_TRANSITION
  VENDOR_REGISTER
  VENDOR_VERIFY
  OFFER_SUBMIT
  DISCUSSION_CREATE
  NOTIFICATION_SEND
  LOGIN_FAILED
  DATABASE_PERFORMANCE
  SLOW_QUERY_ANALYSIS
}

enum AuditSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AuditCategory {
  GENERAL
  AUTHENTICATION
  AUTHORIZATION
  DATA_CHANGE
  PROCUREMENT
  VENDOR_MANAGEMENT
  APPROVAL_WORKFLOW
  DOCUMENT_MANAGEMENT
  FINANCIAL
  SECURITY
  SYSTEM
  BAST_APPROVAL
}

// Partitioned table for high-volume audit data
model AuditLogArchive {
  id         String @id @default(cuid())
  originalId String @unique // Reference to original audit log

  // Same fields as AuditLog but for archived data
  userId        String?
  userEmail     String?
  userRole      String?
  action        AuditAction
  resource      String
  resourceId    String?
  entityType    String?
  entityId      String?
  oldValues     Json?
  newValues     Json?
  changedFields Json?
  details       Json?
  ipAddress     String?
  userAgent     String?
  sessionId     String?
  requestId     String?
  procurementId String?
  workflowStage String?
  approvalStep  String?
  severity      AuditSeverity
  category      AuditCategory
  metadata      Json?
  description   String?
  timestamp     DateTime
  partitionDate DateTime

  // Archive metadata
  archivedAt    DateTime @default(now())
  archiveReason String?

  @@index([originalId])
  @@index([timestamp])
  @@index([archivedAt])
  @@index([action])
  @@map("audit_logs_archive")
}

// Performance monitoring for database operations
model DatabasePerformanceLog {
  id            String   @id @default(cuid())
  operation     String // SELECT, INSERT, UPDATE, DELETE
  tableName     String
  queryHash     String // Hash of the query for grouping
  executionTime Float // Execution time in milliseconds
  rowsAffected  Int?
  timestamp     DateTime @default(now())

  // Partitioning field
  partitionDate DateTime @default(now()) @db.Date

  @@index([tableName])
  @@index([timestamp])
  @@index([partitionDate])
  @@index([queryHash])
  @@map("db_performance_logs")
}

// -----------------------------
// NOTIFICATION MODELS
// -----------------------------

model NotificationEscalation {
  id                 String               @id @default(cuid())
  entityType         String
  entityId           String
  templateId         String
  template           NotificationTemplate @relation(fields: [templateId], references: [id])
  escalateAt         DateTime
  recipients         Json // Array of recipient configurations
  escalationTemplate String? // Template to use for escalation
  status             EscalationStatus     @default(PENDING)
  processedAt        DateTime?
  metadata           Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([escalateAt])
  @@index([status])
  @@index([entityType, entityId])
}

model NotificationTemplate {
  id           String                @id @default(cuid())
  name         String                @unique
  description  String?
  category     String
  subject      String
  bodyTemplate String                @db.Text
  htmlTemplate String?               @db.Text
  channels     NotificationChannel[]
  priority     NotificationPriority  @default(MEDIUM)
  isActive     Boolean               @default(true)

  // Template variables
  variables Json? // Array of variable definitions

  // Conditions for when to use this template
  conditions Json?

  // Escalation rules
  escalationRules Json?

  // Versioning
  version  Int                    @default(1)
  parentId String?
  parent   NotificationTemplate?  @relation("TemplateVersions", fields: [parentId], references: [id])
  versions NotificationTemplate[] @relation("TemplateVersions")

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String
  createdBy   User     @relation(fields: [createdById], references: [id])

  // Relations
  escalations NotificationEscalation[]

  @@index([category])
  @@index([isActive])
}

// Queue priorities and statuses are now handled by Redis queue system

enum EscalationStatus {
  PENDING
  PROCESSED
  CANCELLED
}

enum NotificationChannel {
  IN_APP
  EMAIL
  SMS
  PUSH
  WEBHOOK
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// -----------------------------
// QUEUE SYSTEM MODELS
// -----------------------------

model EmailQueue {
  id           String               @id @default(cuid())
  to           String // Comma-separated for multiple recipients
  cc           String?
  bcc          String?
  subject      String
  body         String               @db.Text
  htmlBody     String?              @db.Text
  attachments  Json? // Array of attachment objects
  templateId   String?
  template     String? // Template field for compatibility
  templateData Json? // Data for template rendering
  priority     NotificationPriority @default(MEDIUM)
  status       QueueJobStatus       @default(PENDING)
  attempts     Int                  @default(0)
  maxAttempts  Int                  @default(3)
  retryCount   Int                  @default(0) // Additional retry count field
  maxRetries   Int                  @default(3) // Additional max retries field
  scheduledAt  DateTime             @default(now())
  processedAt  DateTime?
  sentAt       DateTime? // Additional sent at field
  failedAt     DateTime?
  error        String?              @db.Text
  metadata     Json?
  createdAt    DateTime             @default(now())
  updatedAt    DateTime             @updatedAt

  @@index([status])
  @@index([priority])
  @@index([scheduledAt])
  @@index([createdAt])
  @@index([sentAt])
  @@index([retryCount])
  @@map("email_queue")
}

model SmsQueue {
  id           String               @id @default(cuid())
  to           String // Phone number
  message      String               @db.Text
  templateId   String?
  templateData Json? // Data for template rendering
  priority     NotificationPriority @default(MEDIUM)
  status       QueueJobStatus       @default(PENDING)
  attempts     Int                  @default(0)
  maxAttempts  Int                  @default(3)
  scheduledAt  DateTime             @default(now())
  processedAt  DateTime?
  failedAt     DateTime?
  error        String?              @db.Text
  metadata     Json?
  createdAt    DateTime             @default(now())
  updatedAt    DateTime             @updatedAt

  @@index([status])
  @@index([priority])
  @@index([scheduledAt])
  @@index([createdAt])
  @@map("sms_queue")
}

model PushQueue {
  id          String               @id @default(cuid())
  userId      String // Target user ID
  title       String
  body        String               @db.Text
  data        Json? // Additional payload data
  badge       Int?
  sound       String?
  priority    NotificationPriority @default(MEDIUM)
  status      QueueJobStatus       @default(PENDING)
  attempts    Int                  @default(0)
  maxAttempts Int                  @default(3)
  scheduledAt DateTime             @default(now())
  processedAt DateTime?
  failedAt    DateTime?
  error       String?              @db.Text
  metadata    Json?
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt

  @@index([userId])
  @@index([status])
  @@index([priority])
  @@index([scheduledAt])
  @@index([createdAt])
  @@map("push_queue")
}

model WebhookQueue {
  id          String               @id @default(cuid())
  url         String
  method      String               @default("POST") // GET, POST, PUT, PATCH, DELETE
  headers     Json? // HTTP headers
  body        Json? // Request body
  payload     Json? // Additional payload field
  templateId  String? // Template ID field for compatibility
  priority    NotificationPriority @default(MEDIUM)
  status      QueueJobStatus       @default(PENDING)
  attempts    Int                  @default(0)
  maxAttempts Int                  @default(5)
  scheduledAt DateTime             @default(now())
  processedAt DateTime?
  failedAt    DateTime?
  error       String?              @db.Text
  metadata    Json?
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt

  @@index([status])
  @@index([priority])
  @@index([scheduledAt])
  @@index([createdAt])
  @@map("webhook_queue")
}

enum QueueJobStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  RETRYING
  CANCELLED
  DELAYED
  SENT
  PERMANENTLY_FAILED
}

// -----------------------------
// PUBLIC ASSET MANAGEMENT MODELS
// -----------------------------

model PublicAsset {
  id               String                   @id @default(cuid())
  fileName         String
  originalFileName String
  filePath         String                   @unique
  fileSize         Int?
  size             Int? // Additional size field for compatibility
  mimeType         String
  fileType         PublicAssetType
  category         String? // Additional category field
  type             PublicAssetType? // Additional type field for compatibility
  securityLevel    PublicAssetSecurityLevel @default(PUBLIC)
  url              String? // Additional URL field

  // Asset metadata
  title       String?
  description String?  @db.Text
  tags        String[] @default([])

  // Access control
  isActive      Boolean @default(true)
  isPublic      Boolean @default(true)
  allowDownload Boolean @default(true)

  // Versioning
  version  Int           @default(1)
  parentId String?
  parent   PublicAsset?  @relation("AssetVersions", fields: [parentId], references: [id])
  versions PublicAsset[] @relation("AssetVersions")

  // Lifecycle management
  publishedAt DateTime?
  expiresAt   DateTime?
  archivedAt  DateTime?

  // Usage tracking
  downloadCount  Int       @default(0)
  viewCount      Int       @default(0)
  lastAccessedAt DateTime?

  // Relations
  uploadedById String
  uploadedBy   User   @relation("PublicAssetUploader", fields: [uploadedById], references: [id])

  // Metadata
  metadata  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([fileType])
  @@index([securityLevel])
  @@index([isActive])
  @@index([isPublic])
  @@index([uploadedById])
  @@index([publishedAt])
  @@index([expiresAt])
  @@index([tags])
  @@index([category])
  @@index([size])
  @@map("public_assets")
}

enum PublicAssetType {
  PROCUREMENT_DOCUMENT
  TEMPLATE
  ANNOUNCEMENT
  NEWS_ARTICLE
  EVALUATION_RESULT
  AWARD_NOTICE
  TENDER_DOCUMENT
  SPECIFICATION
  TERMS_CONDITIONS
  USER_GUIDE
  FORM
  REPORT
  IMAGE
  VIDEO
  AUDIO
  OTHER
}

enum PublicAssetSecurityLevel {
  PUBLIC
  REGISTERED_USERS
  VERIFIED_VENDORS
  INTERNAL_ONLY
  CONFIDENTIAL
  RESTRICTED
}

// -----------------------------
// ENHANCED VENDOR OFFER FIELDS
// -----------------------------

// Add missing fields to existing models via direct schema updates
// Note: These fields should be added to existing VendorOffer model:
// - negotiatedPrice: Float?
// - negotiationNotes: String?

// Add missing fields to existing Vendor model:
// - picName: String? (already exists as alias)

// Add missing fields to existing ProcurementItem model:
// - ownerEstimate: Float? (already exists)

// Add missing fields to existing models:
// - notes field for various models

// -----------------------------
// NOTIFICATION ESCALATION MODEL (ALREADY EXISTS)
// -----------------------------

// NotificationEscalation model already exists in the schema
