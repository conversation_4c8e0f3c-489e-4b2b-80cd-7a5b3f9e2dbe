# E-Procurement Tech Context

## Technologies Used

- **Bun**: A fast JavaScript runtime built on top of JavaScriptCore, used for running the application and managing dependencies.
- **Next.js 15**: A React framework for building full-stack web applications with features like server-side rendering, static site generation, and API routes.
- **React**: The underlying library for building user interfaces, integrated within Next.js.
- **TypeScript**: A superset of JavaScript that adds static typing, used for type safety and improved developer experience.
- **Prisma**: An ORM (Object-Relational Mapping) tool for database access, providing type-safe queries and schema migrations.
- **PostgreSQL**: A powerful, open-source relational database system chosen for its robustness and support for complex queries.
- **TanStack Query (@tanstack/react-query)**: A library for fetching, caching, and updating asynchronous data in React applications.
- **Shadcn UI**: A collection of reusable UI components built on Radix UI and styled with Tailwind CSS, used for creating accessible and customizable interfaces.
- **Tailwind CSS**: A utility-first CSS framework for rapid UI development with a consistent design system.
- **ESLint**: A tool for identifying and fixing problems in JavaScript/TypeScript code, ensuring code quality and consistency.

## Development Setup

- **Project Directory**: Located at `/Users/<USER>/Documents/e-proc`.
- **Package Manager**: Bun is used for installing dependencies and running scripts (`bun install`, `bun run`).
- **Initialization**: The project was initialized with `bun create next-app` using TypeScript, ESLint, and Tailwind CSS configurations.
- **Prisma Setup**: Prisma is initialized with `prisma init`, creating a `prisma` directory with `schema.prisma` for database schema definitions.
- **Shadcn UI Integration**: Initialized with `bunx shadcn@latest init`, setting up CSS variables and utility functions for component usage.
- **Development Server**: Can be started with `bun run dev` to run the Next.js application locally.

## Technical Constraints

- **Database Connection**: Requires a PostgreSQL database URL to be set in `.env` file for Prisma to connect. The user will specify the hosting solution later.
- **Next.js App Router**: The project uses the App Router structure in Next.js 15, which differs from the Pages Router and requires specific conventions for routing and API endpoints.
- **Bun Compatibility**: While Bun offers performance benefits, some Node.js-specific packages or scripts might require compatibility checks or workarounds.

## Dependencies

- **Core Dependencies**: `next`, `react`, `react-dom`.
- **Development Dependencies**: `typescript`, `@types/node`, `@types/react`, `@types/react-dom`, `eslint`, `eslint-config-next`.
- **UI and Styling**: `tailwindcss`, `@tailwindcss/postcss`, `shadcn` (CLI for component setup).
- **Data Management**: `prisma`, `@prisma/client`, `@tanstack/react-query`.

Note: Additional dependencies may be added as the project evolves, especially for authentication, payment gateways, and other integrations.
