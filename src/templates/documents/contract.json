{"id": "contract", "name": "Contract Template", "type": "CONTRACT", "isActive": true, "variables": [{"name": "contractNumber", "type": "text", "required": true, "description": "Contract Number"}, {"name": "contractDate", "type": "date", "required": true, "description": "Contract Date"}, {"name": "contractTitle", "type": "text", "required": true, "description": "Contract Title"}, {"name": "buyerName", "type": "text", "required": true, "description": "Buyer Company Name"}, {"name": "buyerAddress", "type": "text", "required": true, "description": "Buyer Address"}, {"name": "buyerRepresentative", "type": "text", "required": true, "description": "Buyer Representative"}, {"name": "buyerTitle", "type": "text", "required": true, "description": "Buyer Representative Title"}, {"name": "vendorName", "type": "text", "required": true, "description": "Vendor Company Name"}, {"name": "vendorAddress", "type": "text", "required": true, "description": "<PERSON><PERSON>or Address"}, {"name": "vendorRepresentative", "type": "text", "required": true, "description": "Vendor Representative"}, {"name": "vendorTitle", "type": "text", "required": true, "description": "Vendor Representative Title"}, {"name": "contractValue", "type": "currency", "required": true, "description": "Contract Value"}, {"name": "workDescription", "type": "text", "required": true, "description": "Work Description"}, {"name": "startDate", "type": "date", "required": true, "description": "Start Date"}, {"name": "endDate", "type": "date", "required": true, "description": "End Date"}, {"name": "paymentSchedule", "type": "text", "required": false, "description": "Payment Schedule"}, {"name": "penalties", "type": "text", "required": false, "description": "Penalty Clauses"}, {"name": "warranties", "type": "text", "required": false, "description": "Warranty Terms"}], "htmlTemplate": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Kontrak - {{contractNumber}}</title>\n</head>\n<body>\n    <div class=\"letterhead\">\n        <div class=\"header\">\n            <h1>KONTRAK PENGADAAN</h1>\n            <h2>{{contractTitle}}</h2>\n            <p><strong>Nomor: {{contractNumber}}</strong></p>\n        </div>\n    </div>\n    \n    <p>Pada hari ini, {{contractDate}}, telah dibuat dan ditandatangani Kontrak Pengadaan antara:</p>\n    \n    <div style=\"margin: 20px 0;\">\n        <strong>PIHAK PERTAMA (PEMBERI KERJA):</strong><br>\n        {{buyerName}}<br>\n        {{buyerAddress}}<br>\n        Dalam hal ini diwakili oleh: {{buyerRepresentative}}<br>\n        Jabatan: {{buyerTitle}}<br>\n        Selanjutnya disebut sebagai <strong>\"PEMBERI KERJA\"</strong>\n    </div>\n    \n    <div style=\"margin: 20px 0;\">\n        <strong><PERSON><PERSON><PERSON><PERSON> KEDUA (PENYEDIA BARANG/JASA):</strong><br>\n        {{vendorName}}<br>\n        {{vendorAddress}}<br>\n        Dalam hal ini diwakili oleh: {{vendorRepresentative}}<br>\n        Jabatan: {{vendorTitle}}<br>\n        Selanjutnya disebut sebagai <strong>\"PENYEDIA\"</strong>\n    </div>\n    \n    <p>Kedua belah pihak sepakat untuk mengadakan kontrak dengan ketentuan sebagai berikut:</p>\n    \n    <div style=\"margin: 20px 0;\">\n        <h3>PASAL 1 - RUANG LINGKUP PEKERJAAN</h3>\n        <p>{{workDescription}}</p>\n    </div>\n    \n    <div style=\"margin: 20px 0;\">\n        <h3>PASAL 2 - NILAI KONTRAK</h3>\n        <p>Nilai kontrak ini adalah sebesar <strong>{{contractValue}}</strong> (sudah termasuk PPN).</p>\n    </div>\n    \n    <div style=\"margin: 20px 0;\">\n        <h3>PASAL 3 - JANGKA WAKTU PELAKSANAAN</h3>\n        <p>Pekerjaan dimulai pada tanggal {{startDate}} dan berakhir pada tanggal {{endDate}}.</p>\n    </div>\n    \n    {{#if paymentSchedule}}\n    <div style=\"margin: 20px 0;\">\n        <h3>PASAL 4 - JADWAL PEMBAYARAN</h3>\n        <p>{{paymentSchedule}}</p>\n    </div>\n    {{/if}}\n    \n    {{#if penalties}}\n    <div style=\"margin: 20px 0;\">\n        <h3>PASAL 5 - SANKSI DAN DENDA</h3>\n        <p>{{penalties}}</p>\n    </div>\n    {{/if}}\n    \n    {{#if warranties}}\n    <div style=\"margin: 20px 0;\">\n        <h3>PASAL 6 - JAMINAN</h3>\n        <p>{{warranties}}</p>\n    </div>\n    {{/if}}\n    \n    <div style=\"margin: 20px 0;\">\n        <h3>PASAL PENUTUP</h3>\n        <p>Kontrak ini dibuat dalam rangkap 2 (dua) yang masing-masing mempunyai kekuatan hukum yang sama. Kontrak ini mulai berlaku sejak ditandatangani oleh kedua belah pihak.</p>\n    </div>\n    \n    <div class=\"signature-section\">\n        <table class=\"no-border\" style=\"width: 100%; margin-top: 50px;\">\n            <tr>\n                <td style=\"width: 50%; border: none; text-align: center;\">\n                    <p><strong>PIHAK PERTAMA</strong></p>\n                    <p>{{buyerName}}</p>\n                    <div class=\"signature-box\"></div>\n                    <p><strong>{{buyerRepresentative}}</strong><br>\n                    {{buyerTitle}}</p>\n                </td>\n                <td style=\"width: 50%; border: none; text-align: center;\">\n                    <p><strong>PIHAK KEDUA</strong></p>\n                    <p>{{vendorName}}</p>\n                    <div class=\"signature-box\"></div>\n                    <p><strong>{{vendorRepresentative}}</strong><br>\n                    {{vendorTitle}}</p>\n                </td>\n            </tr>\n        </table>\n    </div>\n</body>\n</html>", "cssStyles": "body { font-family: 'Times New Roman', serif; font-size: 12pt; line-height: 1.6; margin: 30px; } .header { text-align: center; margin-bottom: 40px; } .letterhead { border-bottom: 3px solid #000; padding-bottom: 15px; margin-bottom: 30px; } h1 { font-size: 18pt; margin-bottom: 10px; } h2 { font-size: 16pt; margin-bottom: 10px; } h3 { font-size: 14pt; margin-top: 25px; margin-bottom: 10px; } .signature-section { margin-top: 60px; } .signature-box { width: 200px; height: 100px; border: 1px solid #000; margin: 20px auto; } .no-border td { border: none; } p { text-align: justify; margin-bottom: 10px; }"}