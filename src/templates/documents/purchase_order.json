{"id": "purchase_order", "name": "Purchase Order Template", "type": "PURCHASE_ORDER", "isActive": true, "variables": [{"name": "poNumber", "type": "text", "required": true, "description": "Purchase Order Number"}, {"name": "issueDate", "type": "date", "required": true, "description": "Issue Date"}, {"name": "deliveryDate", "type": "date", "required": true, "description": "Delivery Date"}, {"name": "vendorName", "type": "text", "required": true, "description": "Vendor Company Name"}, {"name": "vendorAddress", "type": "text", "required": true, "description": "<PERSON><PERSON>or Address"}, {"name": "vendorPhone", "type": "text", "required": false, "description": "Vendor Phone"}, {"name": "vendorEmail", "type": "text", "required": false, "description": "<PERSON><PERSON><PERSON>"}, {"name": "buyerName", "type": "text", "required": true, "description": "Buyer Company Name"}, {"name": "buyerAddress", "type": "text", "required": true, "description": "Buyer Address"}, {"name": "totalAmount", "type": "currency", "required": true, "description": "Total Amount"}, {"name": "items", "type": "array", "required": true, "description": "Purchase Order Items"}, {"name": "paymentTerms", "type": "text", "required": false, "description": "Payment Terms"}, {"name": "deliveryTerms", "type": "text", "required": false, "description": "Delivery Terms"}, {"name": "approver<PERSON><PERSON>", "type": "text", "required": true, "description": "Approver Name"}, {"name": "approver<PERSON><PERSON><PERSON>", "type": "text", "required": true, "description": "Approver Title"}, {"name": "notes", "type": "text", "required": false, "description": "Additional Notes"}], "htmlTemplate": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Purchase Order - {{poNumber}}</title>\n</head>\n<body>\n    <div class=\"letterhead\">\n        <div class=\"header\">\n            <h1>{{buyerName}}</h1>\n            <p>{{buyerAddress}}</p>\n        </div>\n    </div>\n    \n    <div class=\"document-title\">\n        <h2>PURCHASE ORDER</h2>\n        <p><strong>No: {{poNumber}}</strong></p>\n    </div>\n    \n    <div class=\"po-details\">\n        <table class=\"no-border\" style=\"width: 100%; margin-bottom: 20px;\">\n            <tr>\n                <td style=\"width: 50%; border: none; vertical-align: top;\">\n                    <strong>Kepada:</strong><br>\n                    {{vendorName}}<br>\n                    {{vendorAddress}}<br>\n                    {{#if vendorPhone}}Tel: {{vendorPhone}}<br>{{/if}}\n                    {{#if vendorEmail}}Email: {{vendorEmail}}{{/if}}\n                </td>\n                <td style=\"width: 50%; border: none; vertical-align: top;\">\n                    <strong>Tanggal:</strong> {{issueDate}}<br>\n                    <strong>Tanggal Pengiriman:</strong> {{deliveryDate}}<br>\n                </td>\n            </tr>\n        </table>\n    </div>\n    \n    <p>Dengan ini kami memesan barang/jasa sebagai berikut:</p>\n    \n    <table>\n        <thead>\n            <tr>\n                <th style=\"width: 5%;\">No</th>\n                <th style=\"width: 40%;\">Nama Barang/Jasa</th>\n                <th style=\"width: 15%;\">Kuantitas</th>\n                <th style=\"width: 10%;\">Satuan</th>\n                <th style=\"width: 15%;\">Harga Satuan</th>\n                <th style=\"width: 15%;\">Total</th>\n            </tr>\n        </thead>\n        <tbody>\n            {{#each items}}\n            <tr>\n                <td class=\"center\">{{@index}}</td>\n                <td>{{this.name}}\n                    {{#if this.description}}<br><small>{{this.description}}</small>{{/if}}\n                </td>\n                <td class=\"center\">{{this.quantity}}</td>\n                <td class=\"center\">{{this.unit}}</td>\n                <td class=\"currency\">{{this.unitPrice}}</td>\n                <td class=\"currency\">{{this.totalPrice}}</td>\n            </tr>\n            {{/each}}\n        </tbody>\n        <tfoot>\n            <tr>\n                <td colspan=\"5\" class=\"bold\">TOTAL</td>\n                <td class=\"currency bold\">{{totalAmount}}</td>\n            </tr>\n        </tfoot>\n    </table>\n    \n    {{#if paymentTerms}}\n    <div style=\"margin-top: 20px;\">\n        <strong>Syarat Pembayaran:</strong><br>\n        {{paymentTerms}}\n    </div>\n    {{/if}}\n    \n    {{#if deliveryTerms}}\n    <div style=\"margin-top: 10px;\">\n        <strong>Syarat Pengiriman:</strong><br>\n        {{deliveryTerms}}\n    </div>\n    {{/if}}\n    \n    {{#if notes}}\n    <div style=\"margin-top: 10px;\">\n        <strong>Catatan:</strong><br>\n        {{notes}}\n    </div>\n    {{/if}}\n    \n    <div class=\"signature-section\">\n        <table class=\"no-border\" style=\"width: 100%; margin-top: 50px;\">\n            <tr>\n                <td style=\"width: 50%; border: none;\"></td>\n                <td style=\"width: 50%; border: none; text-align: center;\">\n                    <p>{{buyerName}}</p>\n                    <div class=\"signature-box\"></div>\n                    <p><strong>{{approverName}}</strong><br>\n                    {{approverTitle}}</p>\n                </td>\n            </tr>\n        </table>\n    </div>\n</body>\n</html>", "cssStyles": "body { font-family: 'Times New Roman', serif; font-size: 12pt; line-height: 1.5; margin: 20px; } .header { text-align: center; margin-bottom: 30px; } .letterhead { border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; } .document-title { text-align: center; margin: 20px 0; } .signature-section { margin-top: 50px; } .signature-box { width: 200px; height: 80px; border: 1px solid #000; margin: 10px auto; } .currency { text-align: right; } .center { text-align: center; } .bold { font-weight: bold; } table { width: 100%; border-collapse: collapse; margin: 10px 0; } th, td { border: 1px solid #000; padding: 8px; text-align: left; } th { background-color: #f0f0f0; font-weight: bold; } .no-border td { border: none; }"}