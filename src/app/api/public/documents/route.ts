import { NextRequest } from "next/server";
import { createSuccessResponse, handleApiError } from "@/lib/errors";
import { publicAssetStorage } from "@/lib/storage/public-assets-storage";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";

    // Fetch documents from public assets
    const allDocuments = await publicAssetStorage.listPublicAssets("documents");

    // Filter only active documents
    let activeDocuments = allDocuments.filter(asset => asset.isActive);

    // Apply search filter if provided
    if (search) {
      const searchLower = search.toLowerCase();
      activeDocuments = activeDocuments.filter(doc => 
        doc.originalName.toLowerCase().includes(searchLower) ||
        (doc.title && doc.title.toLowerCase().includes(searchLower)) ||
        (doc.description && doc.description.toLowerCase().includes(searchLower))
      );
    }

    // Sort by upload date (newest first)
    activeDocuments.sort((a, b) => 
      new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()
    );

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedDocuments = activeDocuments.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalDocuments = activeDocuments.length;
    const totalPages = Math.ceil(totalDocuments / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return createSuccessResponse({
      documents: paginatedDocuments,
      pagination: {
        currentPage: page,
        totalPages,
        totalDocuments,
        hasNextPage,
        hasPrevPage,
        limit,
      },
    });
  } catch (error) {
    console.error("Error fetching public documents:", error);
    return handleApiError(error);
  }
}
