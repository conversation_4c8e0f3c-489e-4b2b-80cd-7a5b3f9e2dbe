import { NextRequest } from "next/server";
import { createSuccessResponse, handleApiError } from "@/lib/errors";
import { publicAssetStorage } from "@/lib/storage/public-assets-storage";

export async function GET(request: NextRequest) {
  try {
    // Fetch assets by category for landing page
    const [banners, logos] = await Promise.all([
      publicAssetStorage.listPublicAssets("banners"),
      publicAssetStorage.listPublicAssets("logos"),
    ]);

    // Filter only active assets and sort by upload date
    const activeBanners = banners
      .filter(asset => asset.isActive)
      .sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());

    const activeLogos = logos
      .filter(asset => asset.isActive)
      .sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());

    return createSuccessResponse({
      banners: activeBanners,
      logos: activeLogos,
      primaryLogo: activeLogos[0] || null, // Most recent logo as primary
      primaryBanner: activeBanners[0] || null, // Most recent banner as primary
    });
  } catch (error) {
    console.error("Error fetching landing page assets:", error);
    return handleApiError(error);
  }
}
