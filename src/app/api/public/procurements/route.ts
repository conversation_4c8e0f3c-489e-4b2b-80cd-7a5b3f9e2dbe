import { NextRequest } from "next/server";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search") || "";
    const type = searchParams.get("type") || "";
    const status = searchParams.get("status") || "";
    const minValue = searchParams.get("minValue");
    const maxValue = searchParams.get("maxValue");
    const sortBy = searchParams.get("sortBy") || "newest";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      // Only show published procurements
      status: {
        in: ["PUBLISHED", "SUBMISSION", "EVALUATION", "NEGOTIATION", "WINNER_ANNOUNCEMENT"]
      }
    };

    // Add search filter
    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { procurementNumber: { contains: search, mode: "insensitive" } },
      ];
    }

    // Add type filter
    if (type) {
      where.type = type;
    }

    // Add status filter
    if (status === "active") {
      where.status = {
        in: ["PUBLISHED", "SUBMISSION"]
      };
    } else if (status) {
      where.status = status;
    }

    // Add value filters
    if (minValue || maxValue) {
      where.ownerEstimate = {};
      if (minValue) {
        where.ownerEstimate.gte = parseFloat(minValue);
      }
      if (maxValue) {
        where.ownerEstimate.lte = parseFloat(maxValue);
      }
      // Only show procurements where owner estimate is visible to vendors
      where.showOwnerEstimateToVendor = true;
    }

    // Build order by clause
    let orderBy: any = {};
    switch (sortBy) {
      case "oldest":
        orderBy = { createdAt: "asc" };
        break;
      case "value-high":
        orderBy = { ownerEstimate: "desc" };
        break;
      case "value-low":
        orderBy = { ownerEstimate: "asc" };
        break;
      case "deadline":
        // This would require a more complex query with stages
        orderBy = { createdAt: "desc" };
        break;
      default: // newest
        orderBy = { createdAt: "desc" };
    }

    // Fetch procurements
    const [procurements, totalCount] = await Promise.all([
      prisma.procurement.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        select: {
          id: true,
          title: true,
          procurementNumber: true,
          type: true,
          status: true,
          ownerEstimate: true,
          showOwnerEstimateToVendor: true,

          createdAt: true,
          updatedAt: true,
          stages: {
            select: {
              id: true,
              name: true,
              startDate: true,
              endDate: true,
              status: true,
              sequence: true,
            },
            orderBy: {
              sequence: "asc",
            },
          },
          _count: {
            select: {
              items: true,
              offers: true,
            },
          },
        },
      }),
      prisma.procurement.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return createSuccessResponse({
      procurements,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}
