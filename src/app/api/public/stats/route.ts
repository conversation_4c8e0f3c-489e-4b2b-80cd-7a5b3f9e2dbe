import { NextRequest } from "next/server";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    // Get public statistics (no authentication required)
    const [
      totalProcurements,
      activeProcurements,
      totalVendors,
      totalOffers,
      totalValue
    ] = await Promise.all([
      // Total procurements (published only)
      prisma.procurement.count({
        where: {
          status: {
            in: ["PUBLISHED", "SUBMISSION", "EVALUATION", "NEGOTIATION", "WINNER_ANNOUNCEMENT", "AWARDED", "COMPLETED"]
          }
        }
      }),

      // Active procurements
      prisma.procurement.count({
        where: {
          status: {
            in: ["PUBLISHED", "SUBMISSION"]
          }
        }
      }),

      // Total verified vendors
      prisma.vendor.count({
        where: {
          verificationStatus: "VERIFIED"
        }
      }),

      // Total offers
      prisma.vendorOffer.count(),

      // Total value (sum of owner estimates for completed procurements)
      prisma.procurement.aggregate({
        where: {
          status: {
            in: ["AWARDED", "COMPLETED"]
          },
          ownerEstimate: {
            not: null
          }
        },
        _sum: {
          ownerEstimate: true
        }
      })
    ]);

    const stats = {
      totalProcurements,
      activeProcurements,
      totalVendors,
      totalOffers,
      totalValue: totalValue._sum.ownerEstimate || 0,
    };

    return createSuccessResponse(stats);
  } catch (error) {
    return handleApiError(error);
  }
}
