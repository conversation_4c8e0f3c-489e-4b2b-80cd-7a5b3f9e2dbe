import { NextRequest } from "next/server";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { getCurrentTaxRates } from "@/lib/tax/tax-service";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    // Check if user has permission to view tax rates
    if (!user.roles.some((role: any) => ["ADMIN", "COMMITTEE", "VENDOR"].includes(role))) {
      return handleApiError(new Error("Access denied. Insufficient permissions."));
    }

    const taxRates = await getCurrentTaxRates();

    return createSuccessResponse(taxRates, "Tax rates retrieved successfully");
  } catch (error) {
    return handleApiError(error);
  }
}
