import { NextRequest } from "next/server";
import { z } from "zod";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { calculateTaxes, calculateSpecificTax } from "@/lib/tax/tax-service";

const taxCalculationSchema = z.object({
  baseAmount: z.number().positive("Base amount must be positive"),
  vendorType: z.string().optional(),
  procurementCategory: z.string().optional(),
  vendorAnnualTurnover: z.number().optional(),
  exemptionCodes: z.array(z.string()).optional(),
  taxTypeCode: z.string().optional(), // For calculating specific tax only
});

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();
    const validatedData = taxCalculationSchema.parse(body);

    // Check if user has permission to calculate taxes
    if (!user.roles.some((role: any) => ["ADMIN", "COMMITTEE", "VENDOR"].includes(role))) {
      return handleApiError(new Error("Access denied. Insufficient permissions."));
    }

    let result;

    if (validatedData.taxTypeCode) {
      // Calculate specific tax
      result = await calculateSpecificTax(validatedData.taxTypeCode, {
        baseAmount: validatedData.baseAmount,
        vendorType: validatedData.vendorType,
        procurementCategory: validatedData.procurementCategory,
        vendorAnnualTurnover: validatedData.vendorAnnualTurnover,
        exemptionCodes: validatedData.exemptionCodes,
      });

      if (!result) {
        return handleApiError(new Error(`Tax type ${validatedData.taxTypeCode} not found or not active`));
      }
    } else {
      // Calculate all applicable taxes
      result = await calculateTaxes({
        baseAmount: validatedData.baseAmount,
        vendorType: validatedData.vendorType,
        procurementCategory: validatedData.procurementCategory,
        vendorAnnualTurnover: validatedData.vendorAnnualTurnover,
        exemptionCodes: validatedData.exemptionCodes,
      });
    }

    return createSuccessResponse(result, "Tax calculation completed successfully");
  } catch (error) {
    return handleApiError(error);
  }
}
