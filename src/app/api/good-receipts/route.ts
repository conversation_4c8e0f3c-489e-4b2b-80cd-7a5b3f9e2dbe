import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, ValidationError } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { goodReceiptSchema } from "@/lib/validations/good-receipt";
import { auditCreate } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Create good receipt
const createHandler = requireRoles(["PROCUREMENT_USER"])(
  auditCreate("good_receipt_create", {
    description: "Create good receipt for purchase order",
    severity: "HIGH",
    category: "PROCUREMENT",
  })(async function POST(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = goodReceiptSchema.parse(body);

      // Get purchase order with items
      const purchaseOrder = await prisma.purchaseOrder.findUnique({
        where: { id: validatedData.poId },
        include: {
          items: {
            include: {
              item: true,
            },
          },
          vendor: {
            select: {
              id: true,
              companyName: true,
              userId: true,
            },
          },
        },
      });

      if (!purchaseOrder) {
        throw new ValidationError("Purchase order not found");
      }

      if (purchaseOrder.status !== "APPROVED") {
        throw new ValidationError("Can only create good receipt for approved purchase orders");
      }

      // Validate all items exist in PO
      const poItemIds = purchaseOrder.items.map((item: any) => item.id);
      const invalidItems = validatedData.items.filter(
        (item: any) => !poItemIds.includes(item.purchaseOrderItemId)
      );

      if (invalidItems.length > 0) {
        throw new ValidationError("Some items are not found in the purchase order");
      }

      // Create good receipt and related data in transaction
      const result = await prisma.$transaction(async (tx: any) => {
        // Generate GR number
        const grNumber = `GR-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}-${Date.now().toString().slice(-4)}`;

        // Create good receipt
        const goodReceipt = await tx.goodReceipt.create({
          data: {
            grNumber,
            poId: validatedData.poId,
            receivedDate: new Date(validatedData.receivedDate),
            status: "COMPLETED",
            createdById: user.id,
          },
        });

        // Create good receipt items with receipt logs
        for (const itemData of validatedData.items) {
          const grItem = await tx.goodReceiptItem.create({
            data: {
              grId: goodReceipt.id,
              purchaseOrderItemId: itemData.purchaseOrderItemId,
              receivedQuantity: itemData.receivedQuantity,
              notes: itemData.notes,
            },
          });

          // Create receipt logs if provided
          if (itemData.receiptLogs && itemData.receiptLogs.length > 0) {
            await Promise.all(
              itemData.receiptLogs.map((log: any) =>
                tx.receiptLog.create({
                  data: {
                    grItemId: grItem.id,
                    quantityChange: log.quantityChange,
                    notes: log.notes,
                    logDate: new Date(),
                    loggedById: user.id,
                  },
                })
              )
            );
          }
        }

        // Update purchase order status if all items received
        const totalOrdered = purchaseOrder.items.reduce(
          (sum: any, item: any) => sum + item.quantity,
          0
        );
        const totalReceived = validatedData.items.reduce(
          (sum: any, item: any) => sum + item.receivedQuantity,
          0
        );

        if (totalReceived >= totalOrdered) {
          await tx.purchaseOrder.update({
            where: { id: validatedData.poId },
            data: { status: "COMPLETED" },
          });
        }

        // Get vendor user for notification
        const vendorUser = await tx.user.findUnique({
          where: { id: purchaseOrder.vendor.userId },
        });

        if (vendorUser) {
          // Create notification for vendor about good receipt
          await tx.notification.create({
            data: {
              userId: vendorUser.id,
              type: "INFO",
              title: "Good Receipt Created",
              message: `Good receipt ${grNumber} has been created for purchase order ${purchaseOrder.poNumber}`,
              metadata: {
                entityType: "GOOD_RECEIPT",
                entityId: goodReceipt.id,
                poId: purchaseOrder.id,
                poNumber: purchaseOrder.poNumber,
              },
            },
          });
        }

        return goodReceipt;
      });

      return createSuccessResponse(
        {
          id: result.id,
          grNumber: result.grNumber,
          poId: result.poId,
          receivedDate: result.receivedDate,
          status: result.status,
        },
        "Good receipt created successfully"
      );
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { createHandler as POST };