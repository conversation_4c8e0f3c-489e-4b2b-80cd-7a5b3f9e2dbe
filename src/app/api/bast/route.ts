import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { bastSchema } from "@/lib/validations/bast";
import { auditCreate, auditRead } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { enhancedWorkflowEngine } from "@/lib/approval/enhanced-workflow-engine";

// List BASTs
const listHandler = requireRoles(["ADMIN", "PROCUREMENT_USER", "APPROVER", "VENDOR"])(
  auditRead("bast")(async function GET(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const { searchParams } = new URL(request.url);
      
      const page = parseInt(searchParams.get("page") || "1");
      const limit = parseInt(searchParams.get("limit") || "10");
      const status = searchParams.get("status");
      const search = searchParams.get("search");
      
      const skip = (page - 1) * limit;

      // Build where clause based on user role
      const where: any = {};
      
      if (user.roles.includes("VENDOR")) {
        // Vendors can only see their own BASTs
        where.po = {
          vendorId: user.vendor?.id,
        };
      }
      
      if (status) {
        where.status = status;
      }
      
      if (search) {
        where.OR = [
          { bastNumber: { contains: search, mode: "insensitive" } },
          { po: { poNumber: { contains: search, mode: "insensitive" } } },
        ];
      }

      // Get BASTs with pagination
      const [basts, total] = await Promise.all([
        prisma.bAST.findMany({
          where,
          include: {
            po: {
              select: {
                id: true,
                poNumber: true,
                vendor: {
                  select: {
                    id: true,
                    companyName: true,
                  },
                },
              },
            },
            checklist: true,
            createdBy: {
              select: {
                id: true,
                name: true,
              },
            },
            approvals: {
              include: {
                approver: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
              orderBy: { sequence: "asc" },
            },
          },
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
        }),
        prisma.bAST.count({ where }),
      ]);

      return createSuccessResponse({
        data: basts,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Create BAST (vendor-facing)
const createHandler = requireRoles(["VENDOR"])(
  auditCreate("bast", {
    description: "Create new BAST",
    severity: "HIGH",
    category: "PROCUREMENT",
  })(async function POST(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = bastSchema.parse(body);

      // Validate purchase order exists and belongs to vendor
      const purchaseOrder = await prisma.purchaseOrder.findUnique({
        where: { id: validatedData.poId },
        include: {
          items: true,
          vendor: {
            select: {
              id: true,
              companyName: true,
              user: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      if (!purchaseOrder) {
        throw new Error("Purchase order not found");
      }

      if (purchaseOrder.vendor.user?.id !== user.id) {
        throw new Error("Access denied. You can only create BAST for your own purchase orders");
      }

      if (!["APPROVED", "PARTIALLY_DELIVERED"].includes(purchaseOrder.status)) {
        throw new Error("Can only create BAST for approved or partially delivered purchase orders");
      }

      // Generate BAST number
      const bastNumber = `BAST-${Date.now()}`;

      // Create BAST and start approval workflow in transaction
      const result = await prisma.$transaction(async (tx: any) => {
        // Create the BAST
        const bast = await tx.bAST.create({
          data: {
            bastNumber,
            poId: validatedData.poId,
            handoverDate: new Date(validatedData.handoverDate),
            status: "PENDING_APPROVAL",
            summary: validatedData.summary,
            createdById: user.id,
          },
        });

        // Create BAST checklist items
        const checklistItems = await Promise.all(
          validatedData.checklist.map((item: any) =>
            tx.bastChecklistItem.create({
              data: {
                bastId: bast.id,
                description: item.description,
                defectNotes: item.defectNotes,
                targetDate: item.targetDate ? new Date(item.targetDate) : undefined,
              },
            })
          )
        );

        // Calculate total value for workflow conditions (from PO)
        const totalValue = purchaseOrder.items.reduce((sum: any, item: any) => {
          return sum + (item.quantity * item.price);
        }, 0);

        // Prepare entity data for workflow engine
        const entityData = {
          id: bast.id,
          bastNumber: bast.bastNumber,
          poNumber: purchaseOrder.poNumber,
          vendorName: purchaseOrder.vendor.companyName,
          totalValue,
          itemCount: purchaseOrder.items.length,
          handoverDate: bast.handoverDate,
          summary: bast.summary,
        };

        // Start BAST approval workflow
        const approvalInstance = await enhancedWorkflowEngine.startStageApproval({
          entityType: "BAST",
          entityId: bast.id,
          stage: "bast_approval", // BAST approval stage
          entityData,
          initiatedById: user.id,
          title: `BAST Approval: ${bast.bastNumber}`,
          description: `Approval request for BAST: ${purchaseOrder.poNumber}`,
          priority: totalValue > ********* ? "HIGH" : totalValue > 50000000 ? "NORMAL" : "LOW",
        });

        // Get the first step instance for notifications
        const stepInstances = await tx.approvalStepInstance.findMany({
          where: { instanceId: approvalInstance.id },
          orderBy: { sequence: 'asc' },
          take: 1,
        });

        const firstStep = stepInstances.length > 0 ? stepInstances[0] : null;
        if (firstStep) {
          // Get approver user IDs from the first step
          const approverIds = await tx.approvalStepInstance.findUnique({
            where: { id: firstStep.id },
            select: { assignedTo: true },
          });

          if (approverIds?.assignedTo) {
            // Create notifications for each approver
            await Promise.all(
              (approverIds.assignedTo as string[]).map(approverId =>
                tx.notification.create({
                  data: {
                    userId: approverId,
                    type: "APPROVAL_REQUEST",
                    title: "BAST Approval Required",
                    message: `BAST ${bast.bastNumber} for PO ${purchaseOrder.poNumber} requires your approval. Total value: ${totalValue.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}`,
                    metadata: {
                      entityType: "BAST",
                      entityId: bast.id,
                      approvalInstanceId: approvalInstance.id,
                      stepInstanceId: firstStep.id,
                      bastNumber: bast.bastNumber,
                      poNumber: purchaseOrder.poNumber,
                      totalValue,
                      vendorName: purchaseOrder.vendor.companyName,
                    },
                  },
                })
              )
            );
          }
        }

        return {
          bast,
          checklist: checklistItems,
          approvalInstance,
        };
      });

      return createSuccessResponse(
        {
          id: result.bast.id,
          bastNumber: result.bast.bastNumber,
          poNumber: purchaseOrder.poNumber,
          status: result.bast.status,
          createdAt: result.bast.createdAt,
          approvalInstanceId: result.approvalInstance.id,
          checklistCount: result.checklist.length,
        },
        "BAST created and submitted for approval successfully"
      );
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { listHandler as GET, createHandler as POST };
