import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError, ValidationError } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { enhancedWorkflowEngine } from "@/lib/approval/enhanced-workflow-engine";
import { auditCreate } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Approve BAST
const approveHandler = requireRoles(["ADMIN", "APPROVER"])(
  auditCreate("bast_approve", {
    description: "Approve BAST",
    severity: "HIGH",
    category: "PROCUREMENT",
  })(async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const bastId = params.id;
      const body = await request.json();
      const { comments } = body;

      // Get BAST with related data
      const bast = await prisma.bAST.findUnique({
        where: { id: bastId },
        include: {
          po: {
            include: {
              vendor: {
                include: {
                  user: true,
                },
              },
            },
          },
        },
      });

      if (!bast) {
        throw new NotFoundError("BAST not found");
      }

      if (bast.status !== "PENDING_APPROVAL") {
        throw new ValidationError("BAST is not pending approval");
      }

      // Get approval instance for this BAST
      const approvalInstance = await prisma.approvalInstance.findFirst({
        where: {
          entityType: "BAST",
          entityId: bastId,
        },
        include: {
          stepInstances: {
            include: {
              step: true,
              approvals: true,
            },
            orderBy: { sequence: "asc" },
          },
        },
      });

      if (!approvalInstance) {
        throw new ValidationError("No approval workflow found for this BAST");
      }

      // Find current pending step that user can approve
      const currentStep = approvalInstance.stepInstances.find(
        (step: any) => step.status === "IN_PROGRESS" || step.status === "PENDING"
      );

      if (!currentStep) {
        throw new ValidationError("No pending approval step found");
      }

      // Check if user is assigned to approve this step
      const assignedUsers = Array.isArray(currentStep.assignedTo)
        ? currentStep.assignedTo
        : currentStep.assignedTo ? [currentStep.assignedTo] : [];
      const userCanApprove = assignedUsers.includes(user.id);

      if (!userCanApprove) {
        throw new ValidationError("You are not authorized to approve this step");
      }

      // Process approval action through workflow engine
      const workflowResult = await enhancedWorkflowEngine.processApprovalAction({
        instanceId: approvalInstance.id,
        stepInstanceId: currentStep.id,
        userId: user.id,
        action: "APPROVE",
        comments,
        metadata: {
          bastId: bast.id,
          bastNumber: bast.bastNumber,
          poNumber: bast.po.poNumber,
        },
      });

      // Update BAST status based on workflow completion
      const result = await prisma.$transaction(async (tx: any) => {
        let bastStatus = "PENDING_APPROVAL";
        let completedAt = null;

        if (workflowResult.workflowCompleted) {
          bastStatus = workflowResult.finalStatus === "APPROVED" ? "APPROVED" : "REJECTED";
          completedAt = new Date();
        }

        // Update BAST status
        const updatedBast = await tx.bAST.update({
          where: { id: bastId },
          data: {
            status: bastStatus,
            approvedAt: bastStatus === "APPROVED" ? completedAt : null,
          },
        });

        // If BAST is fully approved, update PO status
        if (bastStatus === "APPROVED") {
          await tx.purchaseOrder.update({
            where: { id: bast.poId },
            data: { 
              status: "DELIVERED",
              deliveredAt: new Date(),
            },
          });

          // Create notification for vendor
          await tx.notification.create({
            data: {
              userId: bast.po.vendor.user.id,
              type: "SUCCESS",
              title: "BAST Approved",
              message: `Your BAST ${bast.bastNumber} for PO ${bast.po.poNumber} has been approved.`,
              metadata: {
                entityType: "BAST",
                entityId: bast.id,
                bastNumber: bast.bastNumber,
                poNumber: bast.po.poNumber,
                status: "APPROVED",
              },
            },
          });
        }

        // Create notification for other stakeholders if workflow continues
        if (!workflowResult.workflowCompleted && workflowResult.nextStep) {
          // Get next step approvers and notify them
          const nextStepInstance = await tx.approvalStepInstance.findUnique({
            where: { id: workflowResult.nextStep },
          });

          if (nextStepInstance && nextStepInstance.assignedTo) {
            await Promise.all(
              (nextStepInstance.assignedTo as string[]).map(approverId =>
                tx.notification.create({
                  data: {
                    userId: approverId,
                    type: "APPROVAL_REQUEST",
                    title: "BAST Approval Required",
                    message: `BAST ${bast.bastNumber} requires your approval at the next step.`,
                    metadata: {
                      entityType: "BAST",
                      entityId: bast.id,
                      approvalInstanceId: approvalInstance.id,
                      stepInstanceId: nextStepInstance.id,
                      bastNumber: bast.bastNumber,
                      poNumber: bast.po.poNumber,
                    },
                  },
                })
              )
            );
          }
        }

        return updatedBast;
      });

      return createSuccessResponse(
        {
          id: result.id,
          bastNumber: result.bastNumber,
          status: result.status,
          approvedAt: result.approvedAt,
          workflowCompleted: workflowResult.workflowCompleted,
          nextStep: workflowResult.nextStep,
        },
        `BAST ${workflowResult.workflowCompleted ? 'approved' : 'approval processed'} successfully`
      );
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { approveHandler as PUT };
