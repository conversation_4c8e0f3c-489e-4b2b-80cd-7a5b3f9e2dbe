import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { purchaseRequisitionSchema } from "@/lib/validations/purchase-requisition";
import { auditCreate, auditRead } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Generate unique PR number
function generatePRNumber(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const timestamp = now.getTime().toString().slice(-4);
  
  return `PR-${year}${month}${day}-${timestamp}`;
}

// List purchase requisitions
const listHandler = requireRoles(["ADMIN", "PROCUREMENT_USER", "APPROVER"])(
  auditRead("purchase_requisitions")(async function GET(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const { searchParams } = new URL(request.url);
      
      const page = parseInt(searchParams.get("page") || "1");
      const limit = parseInt(searchParams.get("limit") || "10");
      const status = searchParams.get("status");
      const search = searchParams.get("search");
      const type = searchParams.get("type");
      
      const skip = (page - 1) * limit;

      // Build where clause based on user role
      const where: any = {};
      
      // Non-admin users can only see their own PRs
      if (!user.roles.includes("ADMIN") && !user.roles.includes("APPROVER")) {
        where.requesterId = user.id;
      }
      
      // Filter by status
      if (status) {
        where.status = status;
      }
      
      // Filter by type
      if (type) {
        where.type = type;
      }
      
      // Search functionality
      if (search) {
        where.OR = [
          { prNumber: { contains: search, mode: "insensitive" } },
          { title: { contains: search, mode: "insensitive" } },
        ];
      }

      const [purchaseRequisitions, total] = await Promise.all([
        prisma.purchaseRequisition.findMany({
          where,
          include: {
            requester: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            items: {
              select: {
                id: true,
                name: true,
                quantity: true,
                unit: true,
                estimatedPrice: true,
              },
            },
            sourceContract: {
              select: {
                id: true,
                contractNumber: true,
                title: true,
              },
            },
            _count: {
              select: {
                items: true,
                documents: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
        }),
        prisma.purchaseRequisition.count({ where }),
      ]);

      // Calculate total amounts for each PR
      const enrichedPRs = purchaseRequisitions.map((pr: any) => ({
        ...pr,
        totalAmount: pr.items.reduce((sum: any, item: any) => sum + (item.quantity * item.estimatedPrice), 0),
      }));

      return createSuccessResponse({
        data: enrichedPRs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Create purchase requisition
const createHandler = requireRoles(["PROCUREMENT_USER"])(
  auditCreate("purchase_requisitions", {
    description: "Create new purchase requisition",
    severity: "MEDIUM",
    category: "PROCUREMENT",
  })(async function POST(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = purchaseRequisitionSchema.parse(body);

      // Validate source contract if provided
      if (validatedData.type === "EXTERNAL_ROUTINE" && validatedData.sourceContractId) {
        const contract = await prisma.contract.findUnique({
          where: { id: validatedData.sourceContractId },
        });

        if (!contract) {
          throw new Error("Source contract not found");
        }

        if (contract.status !== "ACTIVE") {
          throw new Error("Source contract is not active");
        }
      }

      // Create PR and items in transaction
      const result = await prisma.$transaction(async (tx: any) => {
        // Generate unique PR number
        const prNumber = generatePRNumber();

        // Create the purchase requisition
        const purchaseRequisition = await tx.purchaseRequisition.create({
          data: {
            prNumber,
            title: validatedData.title,
            type: validatedData.type,
            status: "DRAFT",
            requesterId: user.id,
            sourceContractId: validatedData.sourceContractId,
          },
        });

        // Create PR items
        const items = await Promise.all(
          validatedData.items.map((item: any) =>
            tx.purchaseRequisitionItem.create({
              data: {
                prId: purchaseRequisition.id,
                itemMasterId: item.itemMasterId,
                name: item.name,
                description: item.description,
                quantity: item.quantity,
                unit: item.unit,
                estimatedPrice: item.estimatedPrice,
              },
            })
          )
        );

        return {
          ...purchaseRequisition,
          items,
        };
      });

      return createSuccessResponse(result, "Purchase requisition created successfully");
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { listHandler as GET, createHandler as POST };
