import { NextRequest } from "next/server";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get("token");

    if (!token) {
      return handleApiError(new Error("Reset token is required"));
    }

    // Find valid reset token from memory storage
    const passwordResetTokens = (global as any).passwordResetTokens || new Map();
    const tokenData = passwordResetTokens.get(token);

    if (!tokenData || tokenData.expiresAt < new Date()) {
      return handleApiError(new Error("Invalid or expired reset token"));
    }

    // Get user information
    const user = await prisma.user.findUnique({
      where: { id: tokenData.userId },
      select: {
        id: true,
        email: true,
        name: true,
      },
    });

    if (!user) {
      return handleApiError(new Error("User not found"));
    }

    return createSuccessResponse(
      {
        valid: true,
        user: {
          email: user.email,
          name: user.name,
        },
      },
      "Token is valid"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
