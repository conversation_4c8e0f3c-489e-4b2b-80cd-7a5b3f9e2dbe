import { NextRequest } from "next/server";
import { z } from "zod";
import bcrypt from "bcryptjs";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { prisma } from "@/lib/db";

const resetPasswordSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain uppercase, lowercase, and number"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = resetPasswordSchema.parse(body);

    const { token, password } = validatedData;

    // Find valid reset token from memory storage
    const passwordResetTokens = (global as any).passwordResetTokens || new Map();
    const tokenData = passwordResetTokens.get(token);

    if (!tokenData || tokenData.expiresAt < new Date()) {
      return handleApiError(new Error("Invalid or expired reset token"));
    }

    // Get user information
    const user = await prisma.user.findUnique({
      where: { id: tokenData.userId },
    });

    if (!user) {
      return handleApiError(new Error("User not found"));
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Update user password
    await prisma.user.update({
      where: { id: tokenData.userId },
      data: {
        password: hashedPassword,
      },
    });

    // Remove the used token from memory
    passwordResetTokens.delete(token);

    // Clean up expired tokens for this user
    for (const [key, value] of passwordResetTokens.entries()) {
      if (value.userId === tokenData.userId || value.expiresAt < new Date()) {
        passwordResetTokens.delete(key);
      }
    }

    // Log the password reset
    await prisma.auditLog.create({
      data: {
        action: "UPDATE", // Using existing enum value
        resource: "user",
        resourceId: tokenData.userId,
        userId: tokenData.userId,
        metadata: {
          email: user.email,
          tokenUsed: token,
          action: "password_reset_completed",
        },
        ipAddress: request.headers.get("x-forwarded-for") ||
                   request.headers.get("x-real-ip") ||
                   "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
      },
    });

    return createSuccessResponse(
      { message: "Password reset successfully" },
      "Password reset successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
