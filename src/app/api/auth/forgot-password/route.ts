import { NextRequest } from "next/server";
import { z } from "zod";
import crypto from "crypto";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { prisma } from "@/lib/db";
import { sendEmailNotification } from "@/lib/notifications/notification-service";

const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email format"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = forgotPasswordSchema.parse(body);

    const { email } = validatedData;

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    // Always return success to prevent email enumeration
    // Don't reveal whether the email exists or not
    if (!user) {
      return createSuccessResponse(
        { message: "If the email exists, a reset link has been sent" },
        "Reset link sent successfully"
      );
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString("hex");
    const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    // Save reset token to user record (since passwordResetToken model doesn't exist)
    await prisma.user.update({
      where: { id: user.id },
      data: {
        // Store reset token in a JSON field or use a simple approach
        // For now, we'll store it in memory or use a simple file-based approach
        // In production, you should add resetToken and resetTokenExpiry fields to User model
      },
    });

    // For now, store tokens in memory (not recommended for production)
    if (typeof global !== 'undefined') {
      (global as any).passwordResetTokens = (global as any).passwordResetTokens || new Map();
      (global as any).passwordResetTokens.set(resetToken, {
        userId: user.id,
        expiresAt: resetTokenExpiry,
      });
    }

    // Send password reset email
    const resetUrl = `${process.env.NEXTAUTH_URL || "http://localhost:3000"}/reset-password?token=${resetToken}`;

    console.log(`Password reset URL for ${email}: ${resetUrl}`);

    // Send email with reset link
    await sendEmailNotification({
      to: user.email,
      subject: "Reset Password - E-Procurement System",
      template: "password-reset",
      data: {
        userName: user.name,
        resetUrl: resetUrl,
      },
    });

    // Log the password reset request
    await prisma.auditLog.create({
      data: {
        action: "UPDATE", // Using existing enum value
        resource: "user",
        resourceId: user.id,
        userId: user.id,
        metadata: {
          email: user.email,
          resetTokenGenerated: true,
        },
        ipAddress: request.headers.get("x-forwarded-for") || 
                   request.headers.get("x-real-ip") || 
                   "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
      },
    });

    return createSuccessResponse(
      { message: "If the email exists, a reset link has been sent" },
      "Reset link sent successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
