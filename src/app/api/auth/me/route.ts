import { NextRequest } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { createSuccessResponse, handleApiError } from "@/lib/errors";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    return createSuccessResponse({
      id: user.id,
      email: user.email,
      name: user.name,
      roles: user.roles,
      vendor: user.vendor ? {
        id: user.vendor.id,
        companyName: user.vendor.companyName,
        status: user.vendor.status,
      } : null,
    });
  } catch (error) {
    return handleApiError(error);
  }
}
