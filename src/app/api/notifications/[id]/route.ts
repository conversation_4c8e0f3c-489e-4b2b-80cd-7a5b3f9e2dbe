import { NextRequest } from "next/server";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { markNotificationAsRead, deleteNotification } from "@/lib/notifications/notification-service";

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const notificationId = params.id;

    await markNotificationAsRead(notificationId, user.id);

    return createSuccessResponse(
      { message: "Notification marked as read" },
      "Notification marked as read"
    );
  } catch (error) {
    return handleApiError(error);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const notificationId = params.id;

    await deleteNotification(notificationId, user.id);

    return createSuccessResponse(
      { message: "Notification deleted" },
      "Notification deleted"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
