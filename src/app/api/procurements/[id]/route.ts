import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const procurement = await prisma.procurement.findUnique({
      where: { id: params.id },
      include: {
        items: {
          orderBy: { name: "asc" },
        },
        stages: {
          orderBy: { sequence: "asc" },
          include: {
            discussionThread: true,
          },
        },
        committee: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        offers: {
          include: {
            vendor: {
              select: {
                id: true,
                companyName: true,
                picName: true,
              },
            },
            items: {
              include: {
                item: true,
              },
            },
            documents: true,
          },
          orderBy: { submissionDate: "desc" },
        },
        purchaseOrders: {
          include: {
            vendor: {
              select: {
                id: true,
                companyName: true,
              },
            },
          },
        },
      },
    });

    if (!procurement) {
      throw new NotFoundError("Procurement not found");
    }

    // Check access permissions
    if (user.roles.includes("VENDOR")) {
      // Vendors can only see published procurements
      if (!["PUBLISHED", "SUBMISSION", "EVALUATION", "AWARDED", "COMPLETED"].includes(procurement.status)) {
        throw new NotFoundError("Procurement not found");
      }
    }

    // Filter sensitive information for vendors
    if (user.roles.includes("VENDOR") && !user.roles.includes("ADMIN")) {
      // Hide other vendors' offer details
      if (procurement.offers) {
        procurement.offers = procurement.offers.filter(
          (offer: any) => offer.vendorId === user.vendor?.id
        );
      }

      // Hide owner estimate if not configured to show
      if (!procurement.showOwnerEstimateToVendor && procurement.items) {
        procurement.items.forEach((item: any) => {
          (item as any).ownerEstimate = undefined;
        });
      }
    }

    return createSuccessResponse(procurement);
  } catch (error) {
    return handleApiError(error);
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    // Check permissions
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER")) {
      return handleApiError(new Error("Unauthorized to update procurements"));
    }

    const body = await request.json();
    const { status, ...updateData } = body;

    // Find existing procurement
    const existingProcurement = await prisma.procurement.findUnique({
      where: { id: params.id },
    });

    if (!existingProcurement) {
      throw new NotFoundError("Procurement not found");
    }

    // Validate status transitions
    const validTransitions: Record<string, string[]> = {
      DRAFT: ["PUBLISHED", "CANCELED"],
      PUBLISHED: ["AANWIJZING", "SUBMISSION", "CANCELED"],
      AANWIJZING: ["SUBMISSION", "CANCELED"],
      SUBMISSION: ["EVALUATION", "CANCELED"],
      EVALUATION: ["NEGOTIATION", "WINNER_ANNOUNCEMENT", "CANCELED"],
      NEGOTIATION: ["WINNER_ANNOUNCEMENT", "CANCELED"],
      WINNER_ANNOUNCEMENT: ["AWARDED", "CANCELED"],
      AWARDED: ["COMPLETED"],
      COMPLETED: [],
      CANCELED: [],
    };

    if (status && !validTransitions[existingProcurement.status]?.includes(status)) {
      return handleApiError(
        new Error(`Invalid status transition from ${existingProcurement.status} to ${status}`)
      );
    }

    // Update procurement
    const updatedProcurement = await prisma.procurement.update({
      where: { id: params.id },
      data: {
        ...updateData,
        ...(status && { status }),
      },
      include: {
        items: true,
        stages: {
          orderBy: { sequence: "asc" },
        },
        committee: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    return createSuccessResponse(
      updatedProcurement,
      "Procurement updated successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
