import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { awardSchema } from "@/lib/validations/procurement";
import { sendCombinedNotification } from "@/lib/notifications/notification-service";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to award procurements
    if (!user.roles.includes("ADMIN") && !user.roles.includes("APPROVER")) {
      return handleApiError(new Error("Unauthorized to award procurements"));
    }

    const body = await request.json();
    const validatedData = awardSchema.parse(body);

    // Find the procurement
    const procurement = await prisma.procurement.findUnique({
      where: { id: params.id },
      include: {
        offers: {
          include: {
            vendor: {
              select: {
                id: true,
                companyName: true,
                userId: true,
                user: {
                  select: {
                    id: true,
                    email: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
        committee: {
          where: { userId: user.id },
        },
      },
    });

    if (!procurement) {
      throw new NotFoundError("Procurement not found");
    }

    // Check if user is authorized (admin or committee member)
    if (!user.roles.includes("ADMIN") && procurement.committee.length === 0) {
      return handleApiError(new Error("You are not authorized to award this procurement"));
    }

    // Check if procurement is in the right status
    if (!["EVALUATION", "NEGOTIATION", "WINNER_ANNOUNCEMENT"].includes(procurement.status)) {
      return handleApiError(new Error("Procurement is not ready for award"));
    }

    // Validate that the winning offer exists
    const winningOffer = procurement.offers.find((offer: any) => offer.id === validatedData.winningOfferId);
    if (!winningOffer) {
      return handleApiError(new Error("Winning offer not found"));
    }

    // Validate backup offers if provided
    if (validatedData.backupOfferIds) {
      const backupOffers = procurement.offers.filter((offer: any) =>
        validatedData.backupOfferIds!.includes(offer.id)
      );
      if (backupOffers.length !== validatedData.backupOfferIds.length) {
        return handleApiError(new Error("One or more backup offers not found"));
      }
    }

    // Award the procurement in transaction
    const result = await prisma.$transaction(async (tx: any) => {
      // Update procurement status
      const updatedProcurement = await tx.procurement.update({
        where: { id: params.id },
        data: {
          status: "AWARDED",
          awardedAt: new Date(),
          awardComments: validatedData.comments,
        },
      });

      // Update winning offer status
      await tx.vendorOffer.update({
        where: { id: validatedData.winningOfferId },
        data: {
          status: "WINNER",
          awardedAt: new Date(),
        },
      });

      // Update backup offers status
      if (validatedData.backupOfferIds && validatedData.backupOfferIds.length > 0) {
        for (let i = 0; i < validatedData.backupOfferIds.length; i++) {
          const backupStatus = i === 0 ? "BACKUP_1" : "BACKUP_2";
          await tx.vendorOffer.update({
            where: { id: validatedData.backupOfferIds[i] },
            data: {
              status: backupStatus,
            },
          });
        }
      }

      // Update losing offers status
      const losingOfferIds = procurement.offers
        .filter((offer: any) =>
          offer.id !== validatedData.winningOfferId &&
          !validatedData.backupOfferIds?.includes(offer.id)
        )
        .map((offer: any) => offer.id);

      if (losingOfferIds.length > 0) {
        await tx.vendorOffer.updateMany({
          where: {
            id: { in: losingOfferIds },
          },
          data: {
            status: "LOSER",
          },
        });
      }

      return updatedProcurement;
    });

    // Send notifications to all vendors
    const winningVendor = winningOffer.vendor;
    await sendCombinedNotification(
      {
        userId: winningVendor.userId,
        title: "Selamat! Anda Memenangkan Pengadaan",
        message: `Penawaran Anda untuk ${procurement.title} terpilih sebagai pemenang.`,
        type: "SUCCESS",
      },
      {
        to: winningVendor.user.email,
        subject: "Selamat! Penawaran Anda Menang",
        template: "offer-evaluation",
        data: {
          vendorName: winningVendor.user.name,
          procurementTitle: procurement.title,
          status: "PEMENANG",
        },
      }
    );

    // Notify losing vendors
    const losingVendors = procurement.offers.filter((offer: any) =>
      offer.id !== validatedData.winningOfferId &&
      !validatedData.backupOfferIds?.includes(offer.id)
    );

    for (const offer of losingVendors) {
      await sendCombinedNotification(
        {
          userId: offer.vendor.userId,
          title: "Hasil Pengadaan",
          message: `Terima kasih atas partisipasi Anda dalam ${procurement.title}. Penawaran lain terpilih sebagai pemenang.`,
          type: "INFO",
        },
        {
          to: offer.vendor.user.email,
          subject: "Hasil Pengadaan",
          template: "offer-evaluation",
          data: {
            vendorName: offer.vendor.user.name,
            procurementTitle: procurement.title,
            status: "TIDAK TERPILIH",
          },
        }
      );
    }

    return createSuccessResponse(
      result,
      "Procurement awarded successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
