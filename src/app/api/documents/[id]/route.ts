import { NextRequest, NextResponse } from 'next/server';
import { documentManager } from '@/lib/documents/document-manager';
import { getCurrentUser } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const document = await documentManager.getDocument(params.id, user.id);
    
    return NextResponse.json(document);
  } catch (error) {
    console.error('Error fetching document:', error);
    
    if (error instanceof Error && error.message === 'Document not found') {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }
    
    if (error instanceof Error && error.message === 'Access denied') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    return NextResponse.json(
      { error: 'Failed to fetch document' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const { description, tags, isConfidential, requiresApproval } = body;

    // TODO: Implement document update once schema is fixed
    console.log(`Update document ${params.id}:`, {
      description,
      tags,
      isConfidential,
      requiresApproval,
      updatedBy: user.id,
    });

    return NextResponse.json({
      success: true,
      message: 'Document updated successfully',
    });
  } catch (error) {
    console.error('Error updating document:', error);
    return NextResponse.json(
      { error: 'Failed to update document' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    // TODO: Implement document deletion once schema is fixed
    console.log(`Delete document ${params.id} by user ${user.id}`);

    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting document:', error);
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    );
  }
}
