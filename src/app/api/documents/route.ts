import { NextRequest, NextResponse } from 'next/server';
import { documentService } from '@/lib/documents/document-service';
import { getCurrentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query') || '';
    const documentType = searchParams.get('documentType') || '';
    const status = searchParams.get('status') || '';
    const procurementId = searchParams.get('procurementId') || '';
    const stageId = searchParams.get('stageId') || '';
    const sortBy = searchParams.get('sortBy') || 'uploadedAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const result = await documentService.searchDocuments(user.id, {
      query,
      documentType,
      status: status as any,
      procurementId,
      sortBy: sortBy as any,
      sortOrder: sortOrder as any,
      limit,
      offset,
      includeStats: true,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching documents:', error);
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const documentType = formData.get('documentType') as string;
    const description = formData.get('description') as string;
    const tags = JSON.parse(formData.get('tags') as string || '[]');
    const isConfidential = formData.get('isConfidential') === 'true';
    const requiresApproval = formData.get('requiresApproval') === 'true';
    const procurementId = formData.get('procurementId') as string;
    const stageId = formData.get('stageId') as string;

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    if (!documentType) {
      return NextResponse.json(
        { error: 'Document type is required' },
        { status: 400 }
      );
    }

    const uploadedDocuments = [];

    for (const file of files) {
      const buffer = Buffer.from(await file.arrayBuffer());
      
      const document = await documentService.uploadDocument(user.id, {
        buffer,
        originalname: file.name,
        mimetype: file.type,
      }, {
        documentType,
        description,
        tags,
        isConfidential,
        requiresApproval,
        procurementId,
        metadata: {
          stageId,
          uploadedVia: 'web',
        },
      });

      uploadedDocuments.push(document);
    }

    return NextResponse.json({
      success: true,
      documents: uploadedDocuments,
    });
  } catch (error) {
    console.error('Error uploading documents:', error);
    return NextResponse.json(
      { error: 'Failed to upload documents' },
      { status: 500 }
    );
  }
}
