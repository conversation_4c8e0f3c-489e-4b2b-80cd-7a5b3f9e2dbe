import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { auditRead, auditUpdate } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Get procurement package by ID
const getHandler = requireRoles(["ADMIN", "PROCUREMENT_USER"])(
  auditRead("procurement_packages")(async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const packageId = params.id;

      const procurementPackage = await prisma.procurementPackage.findUnique({
        where: { id: packageId },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          requisitions: {
            include: {
              requester: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              items: {
                include: {
                  itemMaster: {
                    select: {
                      id: true,
                      name: true,
                      category: true,
                      unit: true,
                    },
                  },
                },
              },
              sourceContract: {
                select: {
                  id: true,
                  contractNumber: true,
                  title: true,
                },
              },
            },
          },
          procurement: {
            select: {
              id: true,
              procurementNumber: true,
              title: true,
              status: true,
              createdAt: true,
            },
          },
        },
      });

      if (!procurementPackage) {
        throw new NotFoundError("Procurement package not found");
      }

      // Check access permissions
      if (!user.roles.includes("ADMIN") && procurementPackage.createdById !== user.id) {
        throw new Error("Access denied. You can only view packages you created.");
      }

      // Calculate totals
      const totalAmount = procurementPackage.requisitions.reduce((sum: any, pr: any) => {
        return sum + pr.items.reduce((prSum: any, item: any) => prSum + (item.quantity * item.estimatedPrice), 0);
      }, 0);

      const totalItems = procurementPackage.requisitions.reduce((sum: any, pr: any) => sum + pr.items.length, 0);

      // Group items by category for better organization
      const itemsByCategory: Record<string, any[]> = {};
      procurementPackage.requisitions.forEach((pr: any) => {
        pr.items.forEach((item: any) => {
          const category = item.itemMaster?.category || "Uncategorized";
          if (!itemsByCategory[category]) {
            itemsByCategory[category] = [];
          }
          itemsByCategory[category].push({
            ...item,
            prNumber: pr.prNumber,
            prTitle: pr.title,
            requesterName: pr.requester.name,
          });
        });
      });

      return createSuccessResponse({
        ...procurementPackage,
        totalAmount,
        totalItems,
        totalPRs: procurementPackage.requisitions.length,
        itemsByCategory,
        summary: {
          totalAmount,
          totalItems,
          totalPRs: procurementPackage.requisitions.length,
          categories: Object.keys(itemsByCategory).length,
          canConvert: procurementPackage.status === "DRAFT" && !procurementPackage.procurement,
        },
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Update procurement package
const updateHandler = requireRoles(["PROCUREMENT_USER"])(
  auditUpdate("procurement_packages", {
    description: "Update procurement package",
    severity: "MEDIUM",
    category: "PROCUREMENT",
  })(async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const packageId = params.id;
      const body = await request.json();

      // Check if package exists and user has permission
      const existingPackage = await prisma.procurementPackage.findUnique({
        where: { id: packageId },
      });

      if (!existingPackage) {
        throw new NotFoundError("Procurement package not found");
      }

      if (!user.roles.includes("ADMIN") && existingPackage.createdById !== user.id) {
        throw new Error("Access denied. You can only update packages you created.");
      }

      // Check if package can be updated
      if (existingPackage.status !== "DRAFT") {
        throw new Error("Only draft packages can be updated");
      }

      if (existingPackage.procurementId) {
        throw new Error("Package has already been converted to procurement and cannot be updated");
      }

      // Update the package
      const updatedPackage = await prisma.procurementPackage.update({
        where: { id: packageId },
        data: {
          name: body.name || existingPackage.name,
          description: body.description,
        },
      });

      return createSuccessResponse(updatedPackage, "Procurement package updated successfully");
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { getHandler as GET, updateHandler as PUT };
