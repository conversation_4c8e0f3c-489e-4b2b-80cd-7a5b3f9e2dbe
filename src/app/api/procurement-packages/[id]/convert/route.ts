import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { procurementPackageConvertSchema } from "@/lib/validations/purchase-requisition";
import { auditCreate } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Generate unique procurement number
function generateProcurementNumber(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const timestamp = now.getTime().toString().slice(-4);
  
  return `PROC-${year}${month}${day}-${timestamp}`;
}

// Convert procurement package to procurement
const convertHandler = requireRoles(["PROCUREMENT_USER"])(
  auditCreate("procurement_package_convert", {
    description: "Convert procurement package to procurement",
    severity: "HIGH",
    category: "PROCUREMENT",
  })(async function POST(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const packageId = params.id;
      const body = await request.json();
      
      // Validate request data
      const validatedData = procurementPackageConvertSchema.parse(body);

      // Get package with all related data
      const procurementPackage = await prisma.procurementPackage.findUnique({
        where: { id: packageId },
        include: {
          requisitions: { // Using correct field name from schema
            include: {
              items: {
                include: {
                  itemMaster: {
                    select: {
                      id: true,
                      name: true,
                      category: true,
                      unit: true,
                      specifications: true,
                    },
                  },
                },
              },
              requester: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      if (!procurementPackage) {
        throw new NotFoundError("Procurement package not found");
      }

      // Check access permissions
      if (!user.roles.includes("ADMIN") && procurementPackage.createdById !== user.id) {
        throw new Error("Access denied. You can only convert packages you created.");
      }

      // Check if package can be converted
      if (procurementPackage.status !== "DRAFT") {
        throw new Error("Only draft packages can be converted");
      }

      if (procurementPackage.procurementId) {
        throw new Error("Package has already been converted to procurement");
      }

      if (procurementPackage.requisitions.length === 0) {
        throw new Error("Package has no purchase requisitions to convert");
      }

      // Validate evaluation template if provided
      if (validatedData.evaluationTemplateId) {
        const template = await prisma.evaluationTemplate.findUnique({
          where: { id: validatedData.evaluationTemplateId },
        });

        if (!template) {
          throw new Error("Evaluation template not found");
        }
      }

      // Calculate total estimated value
      const totalEstimatedValue = procurementPackage.requisitions.reduce((sum: any, pr: any) => {
        return sum + pr.items.reduce((prSum: any, item: any) => prSum + (item.quantity * item.estimatedPrice), 0);
      }, 0);

      // Create procurement and related data in transaction
      const result = await prisma.$transaction(async (tx: any) => {
        // Generate unique procurement number
        const procurementNumber = generateProcurementNumber();

        // Create the procurement
        const procurement = await tx.procurement.create({
          data: {
            procurementNumber,
            title: validatedData.title,
            description: validatedData.description,
            category: validatedData.category,
            type: "GOODS", // Default type, can be made configurable
            method: "TENDER", // Default method, can be made configurable
            status: "DRAFT",
            ownerEstimate: totalEstimatedValue,
            showOwnerEstimateToVendor: false, // Default setting
            evaluationMethod: "LOWEST_PRICE", // Default method
            submissionDeadline: new Date(validatedData.submissionDeadline),
            workTimeUnit: validatedData.workTimeUnit,
            hpsIncludesVat: validatedData.hpsIncludesVat,
            vatRate: validatedData.vatRate,
            evaluationTemplateId: validatedData.evaluationTemplateId,
            createdById: user.id,
          },
        });

        // Aggregate items from all PRs
        const aggregatedItems: Record<string, any> = {};
        
        procurementPackage.requisitions.forEach((pr: any) => {
          pr.items.forEach((item: any) => {
            const key = `${item.name}_${item.unit}`;
            
            if (aggregatedItems[key]) {
              // Aggregate quantities and calculate weighted average price
              const existingItem = aggregatedItems[key];
              const totalQuantity = existingItem.quantity + item.quantity;
              const weightedPrice = (
                (existingItem.estimatedPrice * existingItem.quantity) + 
                (item.estimatedPrice * item.quantity)
              ) / totalQuantity;
              
              aggregatedItems[key] = {
                ...existingItem,
                quantity: totalQuantity,
                estimatedPrice: weightedPrice,
                description: existingItem.description + (item.description ? `; ${item.description}` : ''),
                specifications: {
                  ...existingItem.specifications,
                  ...item.itemMaster?.specifications,
                },
                sourceItems: [...existingItem.sourceItems, {
                  prNumber: pr.prNumber,
                  prTitle: pr.title,
                  requester: pr.requester.name,
                  quantity: item.quantity,
                  estimatedPrice: item.estimatedPrice,
                }],
              };
            } else {
              aggregatedItems[key] = {
                name: item.name,
                description: item.description || '',
                quantity: item.quantity,
                unit: item.unit,
                estimatedPrice: item.estimatedPrice,
                specifications: item.itemMaster?.specifications || {},
                sourceItems: [{
                  prNumber: pr.prNumber,
                  prTitle: pr.title,
                  requester: pr.requester.name,
                  quantity: item.quantity,
                  estimatedPrice: item.estimatedPrice,
                }],
              };
            }
          });
        });

        // Create procurement items
        const procurementItems = await Promise.all(
          Object.values(aggregatedItems).map((item: any) =>
            tx.procurementItem.create({
              data: {
                procurementId: procurement.id,
                name: item.name,
                description: item.description,
                quantity: item.quantity,
                unit: item.unit,
                estimatedPrice: item.estimatedPrice,
                specifications: item.specifications,
                metadata: {
                  sourceItems: item.sourceItems,
                },
              },
            })
          )
        );

        // Create committee members
        await Promise.all(
          validatedData.committee.map((member: any) =>
            tx.procurementCommitteeMember.create({
              data: {
                procurementId: procurement.id,
                userId: member.userId,
                role: member.role,
                assignedAt: new Date(),
              },
            })
          )
        );

        // Update package status and link to procurement
        await tx.procurementPackage.update({
          where: { id: packageId },
          data: {
            status: "CONVERTED",
            procurementId: procurement.id,
            convertedAt: new Date(),
          },
        });

        // Create notifications for committee members
        await Promise.all(
          validatedData.committee.map((member: any) =>
            tx.notification.create({
              data: {
                userId: member.userId,
                type: "INFO",
                title: "Anda Ditugaskan ke Panitia Pengadaan",
                message: `Anda telah ditugaskan sebagai ${member.role} untuk pengadaan "${validatedData.title}".`,
                metadata: {
                  entityType: "PROCUREMENT",
                  entityId: procurement.id,
                  procurementNumber: procurement.procurementNumber,
                  role: member.role,
                },
              },
            })
          )
        );

        return {
          procurement,
          procurementItems,
          aggregatedItemsCount: Object.keys(aggregatedItems).length,
          totalSourcePRs: procurementPackage.requisitions.length,
        };
      });

      return createSuccessResponse(
        {
          procurementId: result.procurement.id,
          procurementNumber: result.procurement.procurementNumber,
          title: result.procurement.title,
          totalEstimatedValue,
          itemsCount: result.aggregatedItemsCount,
          sourcePRsCount: result.totalSourcePRs,
          committeeSize: validatedData.committee.length,
        },
        "Procurement package converted to procurement successfully"
      );
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { convertHandler as POST };
