import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { procurementPackageSchema } from "@/lib/validations/purchase-requisition";
import { auditCreate, auditRead } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { generateUniquePackageNumber } from "@/lib/utils/package-number";



// List procurement packages
const listHandler = requireRoles(["ADMIN", "PROCUREMENT_USER"])(
  auditRead("procurement_packages")(async function GET(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const { searchParams } = new URL(request.url);
      
      const page = parseInt(searchParams.get("page") || "1");
      const limit = parseInt(searchParams.get("limit") || "10");
      const status = searchParams.get("status");
      const search = searchParams.get("search");
      
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};
      
      // Non-admin users can only see packages they created
      if (!user.roles.includes("ADMIN")) {
        where.createdById = user.id;
      }
      
      // Filter by status
      if (status) {
        where.status = status;
      }
      
      // Search functionality
      if (search) {
        where.OR = [
          { packageNumber: { contains: search, mode: "insensitive" } },
          { name: { contains: search, mode: "insensitive" } },
        ];
      }

      const [packages, total] = await Promise.all([
        prisma.procurementPackage.findMany({
          where,
          include: {
            createdBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            requisitions: {
              include: {
                requester: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                items: {
                  select: {
                    quantity: true,
                    estimatedPrice: true,
                  },
                },
                _count: {
                  select: {
                    items: true,
                  },
                },
              },
            },
            procurement: {
              select: {
                id: true,
                procurementNumber: true,
                title: true,
                status: true,
              },
            },
            _count: {
              select: {
                requisitions: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
        }),
        prisma.procurementPackage.count({ where }),
      ]);

      // Calculate total amounts for each package
      const enrichedPackages = packages.map((pkg: any) => {
        const totalAmount = pkg.requisitions.reduce((sum: any, pr: any) => {
          return sum + pr.items.reduce((prSum: any, item: any) => prSum + (item.quantity * item.estimatedPrice), 0);
        }, 0);

        return {
          ...pkg,
          totalAmount,
          totalPRs: pkg._count.requisitions,
          totalItems: pkg.requisitions.reduce((sum: any, pr: any) => sum + pr._count.items, 0),
        };
      });

      return createSuccessResponse({
        data: enrichedPackages,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Create procurement package
const createHandler = requireRoles(["PROCUREMENT_USER"])(
  auditCreate("procurement_packages", {
    description: "Create procurement package from approved PRs",
    severity: "MEDIUM",
    category: "PROCUREMENT",
  })(async function POST(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = procurementPackageSchema.parse(body);

      // Validate that all PRs exist and are approved
      const purchaseRequisitions = await prisma.purchaseRequisition.findMany({
        where: {
          id: { in: validatedData.requisitionIds },
        },
        include: {
          items: true,
          requester: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Check if all PRs were found
      if (purchaseRequisitions.length !== validatedData.requisitionIds.length) {
        const foundIds = purchaseRequisitions.map((pr: any) => pr.id);
        const missingIds = validatedData.requisitionIds.filter((id: any) => !foundIds.includes(id));
        throw new Error(`Purchase requisitions not found: ${missingIds.join(", ")}`);
      }

      // Check if all PRs are approved
      const nonApprovedPRs = purchaseRequisitions.filter((pr: any) => pr.status !== "APPROVED");
      if (nonApprovedPRs.length > 0) {
        const nonApprovedNumbers = nonApprovedPRs.map((pr: any) => pr.prNumber);
        throw new Error(`The following PRs are not approved: ${nonApprovedNumbers.join(", ")}`);
      }

      // Check if any PR is already consolidated
      const consolidatedPRs = purchaseRequisitions.filter((pr: any) => pr.status === "CONSOLIDATED");
      if (consolidatedPRs.length > 0) {
        const consolidatedNumbers = consolidatedPRs.map((pr: any) => pr.prNumber);
        throw new Error(`The following PRs are already consolidated: ${consolidatedNumbers.join(", ")}`);
      }

      // Create package and update PR statuses in transaction
      const result = await prisma.$transaction(async (tx: any) => {
        // Generate unique package number
        const packageNumber = await generateUniquePackageNumber();

        // Create the procurement package
        const procurementPackage = await tx.procurementPackage.create({
          data: {
            packageNumber,
            name: validatedData.name,
            status: "DRAFT",
            createdById: user.id,
          },
        });

        // Update PR statuses to CONSOLIDATED and link to package
        await tx.purchaseRequisition.updateMany({
          where: {
            id: { in: validatedData.requisitionIds },
          },
          data: {
            status: "CONSOLIDATED",
            packageId: procurementPackage.id,
            consolidatedAt: new Date(),
          },
        });

        // Create notifications for PR requesters
        const uniqueRequesterIds = Array.from(new Set(purchaseRequisitions.map((pr: any) => pr.requesterId)));
        await Promise.all(
          (uniqueRequesterIds as string[]).map((requesterId: string) =>
            tx.notification.create({
              data: {
                userId: requesterId,
                type: "INFO",
                title: "PR Anda Telah Dikonsolidasi",
                message: `Purchase requisition Anda telah dikonsolidasi ke dalam paket pengadaan "${validatedData.name}".`,
                metadata: {
                  entityType: "PROCUREMENT_PACKAGE",
                  entityId: procurementPackage.id,
                  packageNumber: procurementPackage.packageNumber,
                  packageName: validatedData.name,
                },
              },
            })
          )
        );

        return procurementPackage;
      });

      return createSuccessResponse(
        {
          ...result,
          consolidatedPRs: purchaseRequisitions.length,
          totalAmount: purchaseRequisitions.reduce((sum: any, pr: any) => {
            return sum + pr.items.reduce((prSum: any, item: any) => prSum + (item.quantity * item.estimatedPrice), 0);
          }, 0),
        },
        "Procurement package created successfully"
      );
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { listHandler as GET, createHandler as POST };
