import { NextRequest, NextResponse } from 'next/server';
import { workflowAutomation, WorkflowEvent } from '@/lib/documents/workflow-automation';
import { getCurrentUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const { type, procurementId, stageId, stageType, metadata } = body;

    if (!type || !procurementId || !stageId || !stageType) {
      return NextResponse.json(
        { error: 'Missing required fields: type, procurementId, stageId, stageType' },
        { status: 400 }
      );
    }

    // Validate event type
    const validEventTypes = [
      'STAGE_STARTED',
      'STAGE_COMPLETED',
      'DOCUMENT_UPLOADED',
      'DOCUMENT_APPROVED',
      'DOCUMENT_REJECTED'
    ];
    
    if (!validEventTypes.includes(type)) {
      return NextResponse.json(
        { error: `Invalid event type. Must be one of: ${validEventTypes.join(', ')}` },
        { status: 400 }
      );
    }

    const event: WorkflowEvent = {
      type,
      procurementId,
      stageId,
      stageType,
      userId: user.id,
      metadata: metadata || {},
    };

    // Process workflow event
    await workflowAutomation.processWorkflowEvent(event);

    return NextResponse.json({
      success: true,
      message: 'Workflow event processed successfully',
    });
  } catch (error) {
    console.error('Error processing workflow event:', error);
    return NextResponse.json(
      { error: 'Failed to process workflow event' },
      { status: 500 }
    );
  }
}
