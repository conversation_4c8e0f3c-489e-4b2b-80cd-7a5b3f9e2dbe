import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { updateTemplateSchema } from "@/lib/validations/template";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to view templates
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER")) {
      return handleApiError(new Error("Unauthorized to view templates"));
    }

    const template = await prisma.documentTemplate.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        approver: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        versions: {
          include: {
            creator: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            version: "desc",
          },
        },
        _count: {
          select: {
            usages: true,
          },
        },
      },
    });

    if (!template) {
      throw new NotFoundError("Template not found");
    }

    return createSuccessResponse(template);
  } catch (error) {
    return handleApiError(error);
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to update templates
    if (!user.roles.includes("ADMIN") && !user.roles.includes("PROCUREMENT_USER")) {
      return handleApiError(new Error("Unauthorized to update templates"));
    }

    const body = await request.json();
    const validatedData = updateTemplateSchema.parse({ ...body, id: params.id });

    // Find existing template
    const existingTemplate = await prisma.documentTemplate.findUnique({
      where: { id: params.id },
    });

    if (!existingTemplate) {
      throw new NotFoundError("Template not found");
    }

    // Check if template can be updated (not approved unless user is admin)
    if (existingTemplate.status === "APPROVED" && !user.roles.includes("ADMIN")) {
      return handleApiError(new Error("Cannot update approved template"));
    }

    // Update template in transaction
    const updatedTemplate = await prisma.$transaction(async (tx: any) => {
      // Update the main template
      const template = await tx.documentTemplate.update({
        where: { id: params.id },
        data: {
          name: validatedData.name,
          description: validatedData.description,
          type: validatedData.type,
          category: validatedData.category,
          content: validatedData.content,
          variables: validatedData.variables,
          version: { increment: 1 },
          status: "DRAFT", // Reset to draft when updated
          updatedBy: user.id,
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // Create new version if content changed
      if (validatedData.content || validatedData.variables) {
        await tx.documentTemplateVersion.create({
          data: {
            templateId: params.id,
            version: template.version,
            content: validatedData.content || existingTemplate.content,
            variables: validatedData.variables || existingTemplate.variables,
            changelog: validatedData.changelog || "Template updated",
            createdBy: user.id,
          },
        });
      }

      return template;
    });

    return createSuccessResponse(
      updatedTemplate,
      "Template updated successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    
    // Check if user has permission to delete templates
    if (!user.roles.includes("ADMIN")) {
      return handleApiError(new Error("Unauthorized to delete templates"));
    }

    const template = await prisma.documentTemplate.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            usages: true,
          },
        },
      },
    });

    if (!template) {
      throw new NotFoundError("Template not found");
    }

    // Check if template has been used
    if (template._count.usages > 0) {
      return handleApiError(new Error("Cannot delete template that has been used"));
    }

    // Delete template (cascade will handle versions)
    await prisma.documentTemplate.delete({
      where: { id: params.id },
    });

    return createSuccessResponse(
      null,
      "Template deleted successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}
