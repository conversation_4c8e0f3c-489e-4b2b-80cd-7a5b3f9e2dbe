import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/db';

const approveStepSchema = z.object({
  stepId: z.string().min(1, "Step ID is required"),
  action: z.enum(['APPROVE', 'REJECT']),
  comment: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { instanceId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const instance = await prisma.approvalInstance.findUnique({
      where: { id: params.instanceId },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
            entityType: true,
            steps: {
              orderBy: { sequence: 'asc' },
              select: {
                id: true,
                name: true,
                description: true,
                sequence: true,
                isRequired: true,
                timeoutHours: true,
                stepType: true,
                approverType: true,
              },
            },
          },
        },
        initiatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        stepInstances: {
          include: {
            step: {
              select: {
                id: true,
                name: true,
                description: true,
                sequence: true,
                isRequired: true,
                timeoutHours: true,
                stepType: true,
                approverType: true,
              },
            },
          },
          orderBy: { sequence: 'asc' },
        },
      },
    });

    if (!instance) {
      return NextResponse.json(
        { error: 'Workflow instance not found' },
        { status: 404 }
      );
    }

    // Calculate progress
    const totalSteps = instance.stepInstances.length;
    const completedSteps = instance.stepInstances.filter(stepInstance =>
      stepInstance.status === 'APPROVED' || stepInstance.status === 'REJECTED'
    ).length;
    const progress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

    // Get current step
    const currentStep = instance.stepInstances.find(stepInstance => stepInstance.status === 'PENDING');

    // Calculate estimated completion
    let estimatedCompletion = null;
    if (instance.dueDate && instance.status === 'IN_PROGRESS') {
      const remainingSteps = instance.stepInstances.filter(stepInstance =>
        stepInstance.status === 'PENDING'
      );

      if (remainingSteps.length > 0) {
        const avgStepTime = 24; // hours
        const estimatedHours = remainingSteps.length * avgStepTime;
        estimatedCompletion = new Date(Date.now() + estimatedHours * 60 * 60 * 1000);
      }
    }

    const statusInfo = {
      ...instance,
      progress,
      currentStep,
      estimatedCompletion,
      isOverdue: instance.dueDate ? new Date() > instance.dueDate : false,
      timeRemaining: instance.dueDate 
        ? Math.max(0, instance.dueDate.getTime() - Date.now())
        : null,
    };

    return NextResponse.json(statusInfo);
  } catch (error) {
    console.error('Error fetching workflow status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow status' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { instanceId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = approveStepSchema.parse(body);

    // Check if instance exists
    const instance = await prisma.approvalInstance.findUnique({
      where: { id: params.instanceId },
      include: {
        stepInstances: { orderBy: { sequence: 'asc' } },
      },
    });

    if (!instance) {
      return NextResponse.json(
        { error: 'Workflow instance not found' },
        { status: 404 }
      );
    }

    // Check if step instance exists and user is authorized
    const stepInstance = await prisma.approvalStepInstance.findUnique({
      where: { id: validatedData.stepId },
      include: {
        step: true,
      },
    });

    if (!stepInstance || stepInstance.instanceId !== params.instanceId) {
      return NextResponse.json(
        { error: 'Approval step not found' },
        { status: 404 }
      );
    }

    if (stepInstance.assignedTo !== user.id) {
      return NextResponse.json(
        { error: 'Not authorized to approve this step' },
        { status: 403 }
      );
    }

    if (stepInstance.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Step has already been processed' },
        { status: 400 }
      );
    }

    // Update step status
    const updatedStepInstance = await prisma.approvalStepInstance.update({
      where: { id: validatedData.stepId },
      data: {
        status: validatedData.action === 'APPROVE' ? 'APPROVED' : 'REJECTED',
        completedAt: new Date(),
        comments: validatedData.comment,
        metadata: validatedData.metadata || {},
        decision: validatedData.action === 'APPROVE' ? 'APPROVED' : 'REJECTED',
      },
    });

    // Update instance status and handle workflow progression
    let instanceStatus = instance.status;
    let nextStepId = null;

    if (validatedData.action === 'REJECT') {
      // If rejected, mark instance as rejected
      instanceStatus = 'REJECTED';
    } else {
      // If approved, check if there are more steps
      const nextStepInstance = instance.stepInstances.find(s =>
        s.sequence > stepInstance.sequence && s.status === 'PENDING'
      );

      if (nextStepInstance) {
        // Activate next step
        await prisma.approvalStepInstance.update({
          where: { id: nextStepInstance.id },
          data: {
            status: 'IN_PROGRESS',
            startedAt: new Date(),
          },
        });
        nextStepId = nextStepInstance.id;
        instanceStatus = 'IN_PROGRESS';
      } else {
        // All steps completed
        instanceStatus = 'APPROVED';
      }
    }

    // Update instance
    const updatedInstance = await prisma.approvalInstance.update({
      where: { id: params.instanceId },
      data: {
        status: instanceStatus,
        completedAt: instanceStatus === 'APPROVED' || instanceStatus === 'REJECTED'
          ? new Date()
          : null,
      },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
            entityType: true,
          },
        },
        stepInstances: {
          include: {
            step: {
              select: {
                id: true,
                name: true,
                description: true,
                sequence: true,
                stepType: true,
              },
            },
          },
          orderBy: { sequence: 'asc' },
        },
      },
    });

    return NextResponse.json({
      instance: updatedInstance,
      updatedStep: updatedStepInstance,
      nextStepId,
      message: `Step ${validatedData.action.toLowerCase()}d successfully`,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error processing approval step:', error);
    return NextResponse.json(
      { error: 'Failed to process approval step' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { instanceId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, comment } = body;

    if (!['CANCEL', 'RESTART'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be CANCEL or RESTART' },
        { status: 400 }
      );
    }

    // Check if instance exists
    const instance = await prisma.approvalInstance.findUnique({
      where: { id: params.instanceId },
      include: {
        stepInstances: true,
      },
    });

    if (!instance) {
      return NextResponse.json(
        { error: 'Workflow instance not found' },
        { status: 404 }
      );
    }

    if (action === 'CANCEL') {
      // Cancel the workflow
      await prisma.approvalInstance.update({
        where: { id: params.instanceId },
        data: {
          status: 'CANCELLED',
          completedAt: new Date(),
          metadata: {
            ...(instance.metadata as object || {}),
            cancelledBy: user.id,
            cancelledAt: new Date().toISOString(),
            cancelReason: comment,
          },
        },
      });

      // Cancel all pending steps
      await prisma.approvalStepInstance.updateMany({
        where: {
          instanceId: params.instanceId,
          status: { in: ['PENDING', 'IN_PROGRESS'] },
        },
        data: {
          status: 'REJECTED',
          completedAt: new Date(),
        },
      });
    } else if (action === 'RESTART') {
      // Restart the workflow
      await prisma.approvalInstance.update({
        where: { id: params.instanceId },
        data: {
          status: 'PENDING',
          completedAt: null,
          metadata: {
            ...(instance.metadata as object || {}),
            restartedBy: user.id,
            restartedAt: new Date().toISOString(),
            restartReason: comment,
          },
        },
      });

      // Reset all steps
      await prisma.approvalStepInstance.updateMany({
        where: { instanceId: params.instanceId },
        data: {
          status: 'PENDING',
          startedAt: null,
          completedAt: null,
          comments: null,
          decision: null,
        },
      });

      // Activate first step
      const firstStepInstance = instance.stepInstances.find(s => s.sequence === 1);
      if (firstStepInstance) {
        await prisma.approvalStepInstance.update({
          where: { id: firstStepInstance.id },
          data: {
            status: 'IN_PROGRESS',
            startedAt: new Date(),
          },
        });
      }
    }

    // Return updated instance
    const updatedInstance = await prisma.approvalInstance.findUnique({
      where: { id: params.instanceId },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
            entityType: true,
          },
        },
        stepInstances: {
          include: {
            step: {
              select: {
                id: true,
                name: true,
                description: true,
                sequence: true,
                stepType: true,
              },
            },
          },
          orderBy: { sequence: 'asc' },
        },
      },
    });

    return NextResponse.json({
      instance: updatedInstance,
      message: `Workflow ${action.toLowerCase()}ed successfully`,
    });
  } catch (error) {
    console.error('Error updating workflow instance:', error);
    return NextResponse.json(
      { error: 'Failed to update workflow instance' },
      { status: 500 }
    );
  }
}
