import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
// import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';

const updateWorkflowTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  type: z.enum(['TENDER', 'RFQ', 'DIRECT_PURCHASE', 'FRAMEWORK']).optional(),
  category: z.enum(['GOODS', 'SERVICES', 'CONSTRUCTION']).optional(),
  isDefault: z.boolean().optional(),
  isActive: z.boolean().optional(),
  config: z.object({
    stages: z.array(z.object({
      name: z.string(),
      type: z.string(),
      sequence: z.number(),
      duration: z.number().optional(),
      requirements: z.array(z.string()).optional(),
    })),
    rules: z.object({
      minValue: z.number().optional(),
      maxValue: z.number().optional(),
      requiresCommittee: z.boolean().default(false),
      autoApproval: z.boolean().default(false),
    }).optional(),
    conditions: z.array(z.object({
      field: z.string(),
      operator: z.string(),
      value: z.any(),
    })).optional(),
  }).optional(),
});

// GET /api/admin/procurement-workflow-templates/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const template = await prisma.procurementWorkflowTemplate.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        stages: {
          orderBy: { sequence: 'asc' },
        },
        vendorRequirements: {
          where: { isActive: true },
          include: {
            creator: {
              select: { id: true, name: true, email: true },
            },
          },
        },
        scheduleTemplates: {
          where: { isActive: true },
          include: {
            creator: {
              select: { id: true, name: true, email: true },
            },
          },
        },
        procurements: {
          select: {
            id: true,
            procurementNumber: true,
            title: true,
            status: true,
            createdAt: true,
          },
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            procurements: true,
            stages: true,
            vendorRequirements: true,
            scheduleTemplates: true,
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error('Error fetching workflow template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow template' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/procurement-workflow-templates/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateWorkflowTemplateSchema.parse(body);

    // Check if template exists
    const existingTemplate = await prisma.procurementWorkflowTemplate.findUnique({
      where: { id: params.id },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    // If setting as default, unset other defaults for the same type/category
    if (validatedData.isDefault) {
      const type = validatedData.type || existingTemplate.type;
      const category = validatedData.category || existingTemplate.category;
      
      await prisma.procurementWorkflowTemplate.updateMany({
        where: {
          type,
          category,
          isDefault: true,
          id: { not: params.id },
        },
        data: { isDefault: false },
      });
    }

    const template = await prisma.procurementWorkflowTemplate.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        stages: {
          orderBy: { sequence: 'asc' },
        },
        vendorRequirements: {
          where: { isActive: true },
        },
        scheduleTemplates: {
          where: { isActive: true },
        },
        _count: {
          select: {
            procurements: true,
            stages: true,
            vendorRequirements: true,
          },
        },
      },
    });

    return NextResponse.json(template);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating workflow template:', error);
    return NextResponse.json(
      { error: 'Failed to update workflow template' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/procurement-workflow-templates/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if template exists and is not being used
    const template = await prisma.procurementWorkflowTemplate.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { procurements: true },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    if (template._count.procurements > 0) {
      return NextResponse.json(
        { error: 'Cannot delete template that is being used by procurements' },
        { status: 400 }
      );
    }

    await prisma.procurementWorkflowTemplate.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'Workflow template deleted successfully' });
  } catch (error) {
    console.error('Error deleting workflow template:', error);
    return NextResponse.json(
      { error: 'Failed to delete workflow template' },
      { status: 500 }
    );
  }
}
