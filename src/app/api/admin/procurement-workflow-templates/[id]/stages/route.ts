import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
// import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';

const createStageSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  sequence: z.number().min(1),
  type: z.enum(['ANNOUNCEMENT', 'SUBMISSION', 'EVALUATION', 'AWARD', 'CONTRACT']),
  isRequired: z.boolean().default(true),
  config: z.object({
    rules: z.array(z.object({
      type: z.string(),
      condition: z.string(),
      value: z.any(),
    })).optional(),
    validations: z.array(z.object({
      field: z.string(),
      rule: z.string(),
      message: z.string(),
    })).optional(),
    notifications: z.object({
      onStart: z.boolean().default(false),
      onComplete: z.boolean().default(false),
      recipients: z.array(z.string()).optional(),
    }).optional(),
  }),
  minDuration: z.number().optional(),
  maxDuration: z.number().optional(),
  dependencies: z.array(z.string()).optional(),
  requiresApproval: z.boolean().default(false),
  approvalConfig: z.object({
    approverType: z.string(),
    approvers: z.array(z.string()).optional(),
    requiredCount: z.number().default(1),
    timeoutHours: z.number().optional(),
  }).optional(),
  requiredDocuments: z.array(z.object({
    name: z.string(),
    type: z.string(),
    required: z.boolean().default(true),
    template: z.string().optional(),
  })).optional(),
});

const updateStageSchema = createStageSchema.partial();

// GET /api/admin/procurement-workflow-templates/[id]/stages
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify template exists
    const template = await prisma.procurementWorkflowTemplate.findUnique({
      where: { id: params.id },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    const stages = await prisma.procurementWorkflowStageTemplate.findMany({
      where: { templateId: params.id },
      orderBy: { sequence: 'asc' },
    });

    return NextResponse.json(stages);
  } catch (error) {
    console.error('Error fetching workflow stages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow stages' },
      { status: 500 }
    );
  }
}

// POST /api/admin/procurement-workflow-templates/[id]/stages
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createStageSchema.parse(body);

    // Verify template exists
    const template = await prisma.procurementWorkflowTemplate.findUnique({
      where: { id: params.id },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    // Check if sequence already exists
    const existingStage = await prisma.procurementWorkflowStageTemplate.findFirst({
      where: {
        templateId: params.id,
        sequence: validatedData.sequence,
      },
    });

    if (existingStage) {
      return NextResponse.json(
        { error: 'Stage with this sequence already exists' },
        { status: 400 }
      );
    }

    const stage = await prisma.procurementWorkflowStageTemplate.create({
      data: {
        ...validatedData,
        templateId: params.id,
      },
    });

    return NextResponse.json(stage, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating workflow stage:', error);
    return NextResponse.json(
      { error: 'Failed to create workflow stage' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/procurement-workflow-templates/[id]/stages/reorder
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { stages } = z.object({
      stages: z.array(z.object({
        id: z.string(),
        sequence: z.number(),
      })),
    }).parse(body);

    // Verify template exists
    const template = await prisma.procurementWorkflowTemplate.findUnique({
      where: { id: params.id },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    // Update sequences in a transaction
    await prisma.$transaction(
      stages.map(stage =>
        prisma.procurementWorkflowStageTemplate.update({
          where: { id: stage.id },
          data: { sequence: stage.sequence },
        })
      )
    );

    const updatedStages = await prisma.procurementWorkflowStageTemplate.findMany({
      where: { templateId: params.id },
      orderBy: { sequence: 'asc' },
    });

    return NextResponse.json(updatedStages);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error reordering workflow stages:', error);
    return NextResponse.json(
      { error: 'Failed to reorder workflow stages' },
      { status: 500 }
    );
  }
}
