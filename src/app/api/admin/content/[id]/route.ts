import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { prisma } from "@/lib/db";
import { z } from "zod";

const updateContentSchema = z.object({
  type: z.string().optional(),
  title: z.string().optional(),
  content: z.string().optional(),
  excerpt: z.string().optional(),
  imageUrl: z.string().optional(),
  fileUrl: z.string().optional(),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional(),
  metadata: z.record(z.any()).optional(),
});

// GET /api/admin/content/[id] - Get single content
const getHandler = requireRoles(["ADMIN"])(async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const content = await prisma.content.findUnique({
      where: { id: params.id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!content) {
      return NextResponse.json({ error: "Content not found" }, { status: 404 });
    }

    return NextResponse.json(content);
  } catch (error) {
    console.error("Get content error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// PUT /api/admin/content/[id] - Update content
const putHandler = requireRoles(["ADMIN"])(async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const validatedData = updateContentSchema.parse(body);

    // Check if content exists
    const existingContent = await prisma.content.findUnique({
      where: { id: params.id },
    });

    if (!existingContent) {
      return NextResponse.json({ error: "Content not found" }, { status: 404 });
    }

    const updateData: any = {};

    // Update fields if provided
    if (validatedData.title) {
      updateData.title = validatedData.title;
      
      // Generate new slug if title changed and content type supports slugs
      if (validatedData.title !== existingContent.title && 
          ["news", "announcement", "page"].includes(existingContent.type)) {
        const slug = validatedData.title
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, "")
          .replace(/\s+/g, "-")
          .replace(/-+/g, "-")
          .replace(/^-+|-+$/g, "");

        // Ensure slug is unique
        let uniqueSlug = slug;
        let counter = 1;
        while (await prisma.content.findFirst({ 
          where: { 
            slug: uniqueSlug,
            type: existingContent.type,
            id: { not: params.id }
          } 
        })) {
          uniqueSlug = `${slug}-${counter}`;
          counter++;
        }
        updateData.slug = uniqueSlug;
      }
    }

    if (validatedData.content !== undefined) updateData.content = validatedData.content;
    if (validatedData.excerpt !== undefined) updateData.excerpt = validatedData.excerpt;
    if (validatedData.imageUrl !== undefined) updateData.imageUrl = validatedData.imageUrl;
    if (validatedData.fileUrl !== undefined) updateData.fileUrl = validatedData.fileUrl;
    if (validatedData.metadata !== undefined) updateData.metadata = validatedData.metadata;
    
    if (validatedData.status) {
      updateData.status = validatedData.status;
      
      // Set publishedAt if status changed to PUBLISHED
      if (validatedData.status === "PUBLISHED" && existingContent.status !== "PUBLISHED") {
        updateData.publishedAt = new Date();
      }
    }

    updateData.updatedAt = new Date();

    const content = await prisma.content.update({
      where: { id: params.id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(content);
  } catch (error) {
    console.error("Update content error:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// DELETE /api/admin/content/[id] - Delete content
const deleteHandler = requireRoles(["ADMIN"])(async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    // Check if content exists
    const existingContent = await prisma.content.findUnique({
      where: { id: params.id },
    });

    if (!existingContent) {
      return NextResponse.json({ error: "Content not found" }, { status: 404 });
    }

    await prisma.content.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: "Content deleted successfully" });
  } catch (error) {
    console.error("Delete content error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

export const GET = getHandler;
export const PUT = putHandler;
export const DELETE = deleteHandler;