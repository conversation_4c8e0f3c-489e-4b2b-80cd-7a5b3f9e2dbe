import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { prisma } from "@/lib/db";
import { z } from "zod";

const createArticleSchema = z.object({
  title: z.string().min(1, "Title is required"),
  excerpt: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  categoryId: z.string().min(1, "Category is required"),
  featuredImage: z.string().optional(),
  status: z.enum(["DRAFT", "PENDING_APPROVAL", "PUBLISHED"]),
  scheduledAt: z.string().optional(),
});

// GET /api/admin/news - List all articles for admin
const getHandler = requireRoles(["ADMIN"])(async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const categoryId = searchParams.get("categoryId");

    const where: any = {};
    if (status) where.status = status;
    if (categoryId) where.categoryId = categoryId;

    const [articles, totalCount] = await Promise.all([
      prisma.newsArticle.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.newsArticle.count({ where }),
    ]);

    return NextResponse.json({
      articles,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Admin news API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// POST /api/admin/news - Create new article
const postHandler = requireRoles(["ADMIN"])(async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const validatedData = createArticleSchema.parse(body);

    // Generate slug from title
    const slug = validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-+|-+$/g, "");

    // Ensure slug is unique
    let uniqueSlug = slug;
    let counter = 1;
    while (await prisma.newsArticle.findUnique({ where: { slug: uniqueSlug } })) {
      uniqueSlug = `${slug}-${counter}`;
      counter++;
    }

    const articleData: any = {
      title: validatedData.title,
      slug: uniqueSlug,
      excerpt: validatedData.excerpt,
      content: validatedData.content,
      featuredImage: validatedData.featuredImage,
      status: validatedData.status,
      authorId: user.id,
      categoryId: validatedData.categoryId,
    };

    // Handle scheduling
    if (validatedData.scheduledAt) {
      articleData.scheduledAt = new Date(validatedData.scheduledAt);
    }

    // Set publishedAt if status is PUBLISHED
    if (validatedData.status === "PUBLISHED") {
      articleData.publishedAt = new Date();
    }

    const article = await prisma.newsArticle.create({
      data: articleData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    return NextResponse.json(article, { status: 201 });
  } catch (error) {
    console.error("Create article error:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

export const GET = getHandler;
export const POST = postHandler;