import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { prisma } from "@/lib/db";
import { z } from "zod";

const updateArticleSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  excerpt: z.string().optional(),
  content: z.string().min(1, "Content is required").optional(),
  categoryId: z.string().min(1, "Category is required").optional(),
  featuredImage: z.string().optional(),
  status: z.enum(["DRAFT", "PENDING_APPROVAL", "APPROVED", "PUBLISHED", "ARCHIVED"]).optional(),
  scheduledAt: z.string().optional(),
});

// GET /api/admin/news/[id] - Get single article
const getHandler = requireRoles(["ADMIN"])(async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const article = await prisma.newsArticle.findUnique({
      where: { id: params.id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!article) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    return NextResponse.json(article);
  } catch (error) {
    console.error("Get article error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// PUT /api/admin/news/[id] - Update article
const putHandler = requireRoles(["ADMIN"])(async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const validatedData = updateArticleSchema.parse(body);

    // Check if article exists
    const existingArticle = await prisma.newsArticle.findUnique({
      where: { id: params.id },
    });

    if (!existingArticle) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    const updateData: any = {};

    // Update fields if provided
    if (validatedData.title) {
      updateData.title = validatedData.title;
      
      // Generate new slug if title changed
      if (validatedData.title !== existingArticle.title) {
        const slug = validatedData.title
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, "")
          .replace(/\s+/g, "-")
          .replace(/-+/g, "-")
          .replace(/^-+|-+$/g, "");

        // Ensure slug is unique
        let uniqueSlug = slug;
        let counter = 1;
        while (await prisma.newsArticle.findFirst({ 
          where: { 
            slug: uniqueSlug,
            id: { not: params.id }
          } 
        })) {
          uniqueSlug = `${slug}-${counter}`;
          counter++;
        }
        updateData.slug = uniqueSlug;
      }
    }

    if (validatedData.excerpt !== undefined) updateData.excerpt = validatedData.excerpt;
    if (validatedData.content) updateData.content = validatedData.content;
    if (validatedData.categoryId) updateData.categoryId = validatedData.categoryId;
    if (validatedData.featuredImage !== undefined) updateData.featuredImage = validatedData.featuredImage;
    
    if (validatedData.status) {
      updateData.status = validatedData.status;
      
      // Set publishedAt if status changed to PUBLISHED
      if (validatedData.status === "PUBLISHED" && existingArticle.status !== "PUBLISHED") {
        updateData.publishedAt = new Date();
      }
    }

    // Handle scheduling
    if (validatedData.scheduledAt !== undefined) {
      updateData.scheduledAt = validatedData.scheduledAt ? new Date(validatedData.scheduledAt) : null;
    }

    updateData.updatedAt = new Date();

    const article = await prisma.newsArticle.update({
      where: { id: params.id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    return NextResponse.json(article);
  } catch (error) {
    console.error("Update article error:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// DELETE /api/admin/news/[id] - Delete article
const deleteHandler = requireRoles(["ADMIN"])(async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    // Check if article exists
    const existingArticle = await prisma.newsArticle.findUnique({
      where: { id: params.id },
    });

    if (!existingArticle) {
      return NextResponse.json({ error: "Article not found" }, { status: 404 });
    }

    await prisma.newsArticle.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: "Article deleted successfully" });
  } catch (error) {
    console.error("Delete article error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

export const GET = getHandler;
export const PUT = putHandler;
export const DELETE = deleteHandler;