import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { prisma } from "@/lib/db";
import { z } from "zod";
import { createSuccessResponse, handleApiError } from "@/lib/errors";

const updateProcurementContentSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  content: z.string().min(1, "Content is required").optional(),
  excerpt: z.string().optional(),
  type: z.enum(["GUIDELINE", "ANNOUNCEMENT", "PROCEDURE", "FAQ", "TERMS"]).optional(),
  status: z.enum(["DRAFT", "PUBLISHED"]).optional(),
  featuredImage: z.string().optional(),
});

// GET /api/admin/procurement-content/[id] - Get specific procurement content
const getHandler = requireRoles(["ADMIN"])(async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    const procurementContent = await prisma.procurementContent.findUnique({
      where: { id: params.id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!procurementContent) {
      return NextResponse.json(
        { error: "Procurement content not found" },
        { status: 404 }
      );
    }

    return createSuccessResponse(procurementContent);
  } catch (error) {
    console.error("Error fetching procurement content:", error);
    return handleApiError(error);
  }
});

// PUT /api/admin/procurement-content/[id] - Update procurement content
const putHandler = requireRoles(["ADMIN"])(async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();

    const validatedData = updateProcurementContentSchema.parse(body);

    // Check if procurement content exists
    const existingContent = await prisma.procurementContent.findUnique({
      where: { id: params.id },
    });

    if (!existingContent) {
      return NextResponse.json(
        { error: "Procurement content not found" },
        { status: 404 }
      );
    }

    const updateData: any = { ...validatedData };

    // Update slug if title changed
    if (validatedData.title && validatedData.title !== existingContent.title) {
      const slug = validatedData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();

      // Ensure slug is unique
      let finalSlug = slug;
      let counter = 1;
      while (await prisma.procurementContent.findFirst({ 
        where: { 
          slug: finalSlug,
          id: { not: params.id }
        } 
      })) {
        finalSlug = `${slug}-${counter}`;
        counter++;
      }
      updateData.slug = finalSlug;
    }

    // Set publishedAt if status changed to PUBLISHED
    if (validatedData.status === "PUBLISHED" && existingContent.status !== "PUBLISHED") {
      updateData.publishedAt = new Date();
    }

    const updatedContent = await prisma.procurementContent.update({
      where: { id: params.id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return createSuccessResponse(updatedContent);
  } catch (error) {
    console.error("Error updating procurement content:", error);
    return handleApiError(error);
  }
});

// DELETE /api/admin/procurement-content/[id] - Delete procurement content
const deleteHandler = requireRoles(["ADMIN"])(async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);

    // Check if procurement content exists
    const existingContent = await prisma.procurementContent.findUnique({
      where: { id: params.id },
    });

    if (!existingContent) {
      return NextResponse.json(
        { error: "Procurement content not found" },
        { status: 404 }
      );
    }

    await prisma.procurementContent.delete({
      where: { id: params.id },
    });

    return createSuccessResponse({ message: "Procurement content deleted successfully" });
  } catch (error) {
    console.error("Error deleting procurement content:", error);
    return handleApiError(error);
  }
});

export const GET = getHandler;
export const PUT = putHandler;
export const DELETE = deleteHandler;
