import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError, ValidationError } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { auditCreate } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";
import { z } from "zod";

const verifyStepSchema = z.object({
  stepName: z.string().min(1, "Step name is required"),
  status: z.enum(["APPROVED", "REJECTED"]),
  comments: z.string().optional(),
});

// Verify specific vendor verification step
const verifyStepHandler = requireRoles(["ADMIN", "APPROVER"])(
  auditCreate("vendor_verify_step", {
    description: "Verify specific vendor verification step",
    severity: "HIGH",
    category: "APPROVAL_WORKFLOW",
  })(async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const vendorId = params.id;
      const body = await request.json();
      
      // Validate request data
      const validatedData = verifyStepSchema.parse(body);

      // Get vendor with verification steps
      const vendor = await prisma.vendor.findUnique({
        where: { id: vendorId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verificationSteps: true,
        },
      });

      if (!vendor) {
        throw new NotFoundError("Vendor not found");
      }

      if (vendor.status !== "PENDING_VERIFICATION") {
        throw new ValidationError("Vendor is not pending verification");
      }

      // Check if the verification step exists
      const existingStep = vendor.verificationSteps.find(
        (step: any) => step.stepName === validatedData.stepName
      );

      const result = await prisma.$transaction(async (tx: any) => {
        let verificationStep;

        if (existingStep) {
          // Update existing step
          verificationStep = await tx.vendorVerificationStep.update({
            where: { id: existingStep.id },
            data: {
              status: validatedData.status,
              verifiedById: user.id,
              verifiedAt: new Date(),
              comments: validatedData.comments,
            },
          });
        } else {
          // Create new verification step
          verificationStep = await tx.vendorVerificationStep.create({
            data: {
              vendorId,
              stepName: validatedData.stepName,
              status: validatedData.status,
              verifiedById: user.id,
              verifiedAt: new Date(),
              comments: validatedData.comments,
            },
          });
        }

        // Check if all required verification steps are completed
        const requiredSteps = [
          "ADMINISTRASI",
          "PAJAK", 
          "TEKNIS",
          "KEUANGAN",
          "LEGALITAS",
        ];

        const allVerificationSteps = await tx.vendorVerificationStep.findMany({
          where: { vendorId },
        });

        const completedSteps = allVerificationSteps.filter(
          (step: any) => step.status === "APPROVED"
        );

        const rejectedSteps = allVerificationSteps.filter(
          (step: any) => step.status === "REJECTED"
        );

        let vendorStatus = vendor.status;
        let statusChangeMessage = "";

        // If any step is rejected, mark vendor as rejected
        if (rejectedSteps.length > 0) {
          vendorStatus = "REJECTED";
          statusChangeMessage = "Vendor verification rejected due to failed verification steps";
          
          await tx.vendor.update({
            where: { id: vendorId },
            data: {
              status: vendorStatus,
              verificationNotes: `Verification failed at step: ${rejectedSteps.map((s: any) => s.stepName).join(", ")}`,
            },
          });
        }
        // If all required steps are approved, mark vendor as verified
        else if (completedSteps.length >= requiredSteps.length && 
                 requiredSteps.every(step => 
                   allVerificationSteps.some((vs: any) => vs.stepName === step && vs.status === "APPROVED")
                 )) {
          vendorStatus = "VERIFIED";
          statusChangeMessage = "Vendor verification completed successfully";
          
          await tx.vendor.update({
            where: { id: vendorId },
            data: {
              status: vendorStatus,
              verifiedAt: new Date(),
              verificationNotes: "All verification steps completed successfully",
              isLocked: false, // Unlock vendor data for editing
            },
          });
        }

        // Create notification for vendor about step verification
        await tx.notification.create({
          data: {
            userId: vendor.user.id,
            type: validatedData.status === "APPROVED" ? "SUCCESS" : "ERROR",
            title: `Verification Step ${validatedData.status === "APPROVED" ? "Approved" : "Rejected"}`,
            message: `Your ${validatedData.stepName.toLowerCase()} verification step has been ${validatedData.status.toLowerCase()}.${validatedData.comments ? ` Reason: ${validatedData.comments}` : ""}`,
            metadata: {
              entityType: "VENDOR_VERIFICATION",
              entityId: vendorId,
              stepName: validatedData.stepName,
              status: validatedData.status,
              verifiedBy: user.name,
              comments: validatedData.comments,
            },
          },
        });

        // If vendor status changed, create additional notification
        if (vendorStatus !== vendor.status) {
          await tx.notification.create({
            data: {
              userId: vendor.user.id,
              type: vendorStatus === "VERIFIED" ? "SUCCESS" : "ERROR",
              title: `Vendor ${vendorStatus === "VERIFIED" ? "Verified" : "Rejected"}`,
              message: statusChangeMessage,
              metadata: {
                entityType: "VENDOR",
                entityId: vendorId,
                previousStatus: vendor.status,
                newStatus: vendorStatus,
                verifiedBy: user.name,
              },
            },
          });

          // Notify admin team about vendor status change
          const adminUsers = await tx.user.findMany({
            where: {
              roles: {
                hasSome: ["ADMIN"],
              },
            },
          });

          await Promise.all(
            adminUsers.map((admin: any) =>
              tx.notification.create({
                data: {
                  userId: admin.id,
                  type: "INFO",
                  title: "Vendor Status Updated",
                  message: `Vendor ${vendor.companyName} has been ${vendorStatus.toLowerCase()}.`,
                  metadata: {
                    entityType: "VENDOR",
                    entityId: vendorId,
                    vendorName: vendor.companyName,
                    newStatus: vendorStatus,
                    verifiedBy: user.name,
                  },
                },
              })
            )
          );
        }

        return {
          verificationStep,
          vendorStatus,
          allSteps: allVerificationSteps,
          statusChanged: vendorStatus !== vendor.status,
        };
      });

      return createSuccessResponse(
        {
          stepName: result.verificationStep.stepName,
          status: result.verificationStep.status,
          verifiedAt: result.verificationStep.verifiedAt,
          comments: result.verificationStep.comments,
          vendorStatus: result.vendorStatus,
          statusChanged: result.statusChanged,
          completedSteps: result.allSteps.filter((s: any) => s.status === "APPROVED").length,
          totalSteps: result.allSteps.length,
        },
        `Verification step ${validatedData.status.toLowerCase()} successfully`
      );
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { verifyStepHandler as PUT };