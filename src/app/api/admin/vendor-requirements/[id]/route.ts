import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
// import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';

const updateVendorRequirementSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  category: z.enum(['LEGAL', 'FINANCIAL', 'TECHNICAL', 'EXPERIENCE']).optional(),
  type: z.enum(['MANDATORY', 'PREFERRED', 'SCORING']).optional(),
  templateId: z.string().optional(),
  isActive: z.boolean().optional(),
  criteria: z.object({
    description: z.string(),
    requirements: z.array(z.object({
      item: z.string(),
      description: z.string(),
      required: z.boolean().default(true),
      scoringCriteria: z.object({
        minScore: z.number().optional(),
        maxScore: z.number().optional(),
        weightage: z.number().optional(),
      }).optional(),
    })),
    evaluation: z.object({
      method: z.enum(['PASS_FAIL', 'SCORING', 'RANKING']),
      passingScore: z.number().optional(),
      maxScore: z.number().optional(),
    }),
  }).optional(),
  weight: z.number().min(0).max(100).optional(),
  validationRules: z.array(z.object({
    field: z.string(),
    rule: z.string(),
    value: z.any(),
    message: z.string(),
  })).optional(),
  requiredDocuments: z.array(z.object({
    name: z.string(),
    type: z.string(),
    required: z.boolean().default(true),
    format: z.array(z.string()).optional(),
    maxSize: z.number().optional(),
    description: z.string().optional(),
  })).optional(),
});

// GET /api/admin/vendor-requirements/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const requirement = await prisma.vendorRequirementTemplate.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        template: {
          select: { id: true, name: true, type: true, category: true },
        },
        procurementRequirements: {
          include: {
            procurement: {
              select: {
                id: true,
                procurementNumber: true,
                title: true,
                status: true,
                createdAt: true,
              },
            },
          },
          take: 10,
          orderBy: { procurement: { createdAt: 'desc' } },
        },
        _count: {
          select: {
            procurementRequirements: true,
          },
        },
      },
    });

    if (!requirement) {
      return NextResponse.json(
        { error: 'Vendor requirement not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(requirement);
  } catch (error) {
    console.error('Error fetching vendor requirement:', error);
    return NextResponse.json(
      { error: 'Failed to fetch vendor requirement' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/vendor-requirements/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateVendorRequirementSchema.parse(body);

    // Check if requirement exists
    const existingRequirement = await prisma.vendorRequirementTemplate.findUnique({
      where: { id: params.id },
    });

    if (!existingRequirement) {
      return NextResponse.json(
        { error: 'Vendor requirement not found' },
        { status: 404 }
      );
    }

    // If templateId is provided, verify it exists
    if (validatedData.templateId) {
      const template = await prisma.procurementWorkflowTemplate.findUnique({
        where: { id: validatedData.templateId },
      });

      if (!template) {
        return NextResponse.json(
          { error: 'Workflow template not found' },
          { status: 404 }
        );
      }
    }

    const requirement = await prisma.vendorRequirementTemplate.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        template: {
          select: { id: true, name: true, type: true, category: true },
        },
        _count: {
          select: {
            procurementRequirements: true,
          },
        },
      },
    });

    return NextResponse.json(requirement);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating vendor requirement:', error);
    return NextResponse.json(
      { error: 'Failed to update vendor requirement' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/vendor-requirements/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if requirement exists and is not being used
    const requirement = await prisma.vendorRequirementTemplate.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { procurementRequirements: true },
        },
      },
    });

    if (!requirement) {
      return NextResponse.json(
        { error: 'Vendor requirement not found' },
        { status: 404 }
      );
    }

    if (requirement._count.procurementRequirements > 0) {
      return NextResponse.json(
        { error: 'Cannot delete requirement that is being used by procurements' },
        { status: 400 }
      );
    }

    await prisma.vendorRequirementTemplate.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'Vendor requirement deleted successfully' });
  } catch (error) {
    console.error('Error deleting vendor requirement:', error);
    return NextResponse.json(
      { error: 'Failed to delete vendor requirement' },
      { status: 500 }
    );
  }
}
