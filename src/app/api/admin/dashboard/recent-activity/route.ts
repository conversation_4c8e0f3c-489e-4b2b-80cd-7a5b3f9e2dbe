import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');

    // Get recent workflow activities
    const recentActivities: any[] = [];

    // Recent workflow creations
    const recentWorkflows = await prisma.approvalWorkflow.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    recentWorkflows.forEach(workflow => {
      recentActivities.push({
        id: `workflow-${workflow.id}`,
        type: 'workflow_created',
        title: 'New Workflow Created',
        description: `Created workflow "${workflow.name}" for ${workflow.entityType}`,
        timestamp: workflow.createdAt.toISOString(),
        user: workflow.createdBy,
        metadata: {
          workflowId: workflow.id,
          entityType: workflow.entityType,
        },
      });
    });

    // Recent template updates
    const recentTemplates = await prisma.procurementWorkflowTemplate.findMany({
      take: 5,
      orderBy: { updatedAt: 'desc' },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    recentTemplates.forEach(template => {
      recentActivities.push({
        id: `template-${template.id}`,
        type: 'template_updated',
        title: 'Template Updated',
        description: `Updated template "${template.name}" (${template.type})`,
        timestamp: template.updatedAt.toISOString(),
        user: template.creator,
        metadata: {
          templateId: template.id,
          type: template.type,
        },
      });
    });

    // Recent schedule completions
    const recentCompletions = await prisma.approvalInstance.findMany({
      take: 5,
      where: {
        status: 'APPROVED',
        completedAt: { not: null },
      },
      orderBy: { completedAt: 'desc' },
      include: {
        workflow: {
          select: { name: true, entityType: true },
        },
        initiatedBy: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    recentCompletions.forEach(completion => {
      recentActivities.push({
        id: `completion-${completion.id}`,
        type: 'schedule_completed',
        title: 'Workflow Completed',
        description: `Completed ${completion.workflow.name} workflow`,
        timestamp: completion.completedAt!.toISOString(),
        user: completion.initiatedBy,
        metadata: {
          instanceId: completion.id,
          entityType: completion.workflow.entityType,
        },
      });
    });

    // Recent requirement additions
    const recentRequirements = await prisma.vendorRequirementTemplate.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    recentRequirements.forEach(requirement => {
      recentActivities.push({
        id: `requirement-${requirement.id}`,
        type: 'requirement_added',
        title: 'Vendor Requirement Added',
        description: `Added ${requirement.category} requirement "${requirement.name}"`,
        timestamp: requirement.createdAt.toISOString(),
        user: requirement.creator,
        metadata: {
          requirementId: requirement.id,
          category: requirement.category,
          type: requirement.type,
        },
      });
    });

    // Sort all activities by timestamp and limit
    const sortedActivities = recentActivities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);

    return NextResponse.json(sortedActivities);
  } catch (error) {
    console.error('Error fetching recent activity:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recent activity' },
      { status: 500 }
    );
  }
}
