import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    // Fetch stats in parallel for better performance
    const [vendorStats, procurementStats, offerStats] = await Promise.all([
      // Vendor statistics
      prisma.vendor.groupBy({
        by: ['verificationStatus'],
        _count: {
          verificationStatus: true,
        },
      }),
      
      // Procurement statistics  
      prisma.procurement.groupBy({
        by: ['status'],
        _count: {
          status: true,
        },
      }),
      
      // Offer statistics
      prisma.offer.groupBy({
        by: ['status'],
        _count: {
          status: true,
        },
      }),
    ]);

    // Process vendor stats
    const vendors = {
      verified: vendorStats.find(v => v.verificationStatus === 'VERIFIED')?._count.verificationStatus || 0,
      pending: vendorStats.find(v => v.verificationStatus === 'PENDING')?._count.verificationStatus || 0,
      total: vendorStats.reduce((sum, v) => sum + v._count.verificationStatus, 0),
    };

    // Process procurement stats
    const procurements = {
      active: procurementStats.filter(p => 
        ['PUBLISHED', 'SUBMISSION_PERIOD', 'EVALUATION'].includes(p.status)
      ).reduce((sum, p) => sum + p._count.status, 0),
      completed: procurementStats.filter(p => 
        ['AWARDED', 'CANCELLED'].includes(p.status)
      ).reduce((sum, p) => sum + p._count.status, 0),
      total: procurementStats.reduce((sum, p) => sum + p._count.status, 0),
    };

    // Process offer stats
    const offers = {
      submitted: offerStats.filter(o => 
        ['SUBMITTED', 'UNDER_EVALUATION'].includes(o.status)
      ).reduce((sum, o) => sum + o._count.status, 0),
      evaluated: offerStats.filter(o => 
        ['ACCEPTED', 'REJECTED'].includes(o.status)
      ).reduce((sum, o) => sum + o._count.status, 0),
      total: offerStats.reduce((sum, o) => sum + o._count.status, 0),
    };

    const stats = {
      vendors,
      procurements,
      offers,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch dashboard statistics",
      },
      { status: 500 }
    );
  }
}