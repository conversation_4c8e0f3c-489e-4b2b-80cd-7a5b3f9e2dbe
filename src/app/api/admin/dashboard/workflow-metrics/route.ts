import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { subMonths, format } from 'date-fns';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get workflows by type
    const workflowsByType = await prisma.approvalWorkflow.groupBy({
      by: ['entityType'],
      _count: { id: true },
    });

    const totalWorkflows = workflowsByType.reduce((sum, item) => sum + item._count.id, 0);
    const workflowTypeData = workflowsByType.map(item => ({
      type: item.entityType,
      count: item._count.id,
      percentage: Math.round((item._count.id / totalWorkflows) * 100),
    }));

    // Get workflows by status
    const workflowsByStatus = await prisma.approvalInstance.groupBy({
      by: ['status'],
      _count: { id: true },
    });

    const statusColors = {
      PENDING: '#FFA500',
      IN_PROGRESS: '#0088FE',
      COMPLETED: '#00C49F',
      REJECTED: '#FF8042',
      CANCELLED: '#8884D8',
    };

    const workflowStatusData = workflowsByStatus.map(item => ({
      status: item.status,
      count: item._count.id,
      color: statusColors[item.status as keyof typeof statusColors] || '#8884D8',
    }));

    // Get processing times for the last 12 months
    const processingTimes = [];
    for (let i = 11; i >= 0; i--) {
      const monthStart = subMonths(new Date(), i);
      const monthEnd = subMonths(new Date(), i - 1);
      
      const monthInstances = await prisma.approvalInstance.findMany({
        where: {
          status: 'APPROVED',
          completedAt: {
            gte: monthStart,
            lt: monthEnd,
          },
        },
        select: {
          startedAt: true,
          completedAt: true,
        },
      });

      let avgTime = 0;
      if (monthInstances.length > 0) {
        const totalTime = monthInstances.reduce((total, instance) => {
          const processingTime = instance.completedAt!.getTime() - instance.startedAt.getTime();
          return total + processingTime;
        }, 0);
        
        avgTime = Math.round(
          totalTime / monthInstances.length / (1000 * 60 * 60 * 24)
        );
      }

      processingTimes.push({
        month: format(monthStart, 'MMM yyyy'),
        avgTime,
        completedCount: monthInstances.length,
      });
    }

    // Get template usage statistics
    const templateUsage = await prisma.procurementWorkflowTemplate.findMany({
      include: {
        _count: {
          select: {
            procurements: true,
          },
        },
      },
    });

    const templateUsageData = await Promise.all(
      templateUsage.map(async (template) => {
        // Calculate success rate based on completed procurements
        const completedProcurements = await prisma.procurement.count({
          where: {
            workflowTemplateId: template.id,
            status: 'COMPLETED',
          },
        });

        const totalProcurements = template._count.procurements;
        const successRate = totalProcurements > 0 
          ? Math.round((completedProcurements / totalProcurements) * 100)
          : 0;

        return {
          templateName: template.name,
          usageCount: totalProcurements,
          successRate,
        };
      })
    );

    const metrics = {
      workflowsByType: workflowTypeData,
      workflowsByStatus: workflowStatusData,
      processingTimes,
      templateUsage: templateUsageData,
    };

    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Error fetching workflow metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow metrics' },
      { status: 500 }
    );
  }
}
