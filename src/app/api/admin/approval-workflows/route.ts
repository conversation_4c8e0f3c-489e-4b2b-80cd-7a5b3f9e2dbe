import { NextRequest } from "next/server";
import { z } from "zod";

import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { getCurrentUser } from "@/lib/auth";
import { enhancedWorkflowEngine } from "@/lib/approval/enhanced-workflow-engine";
import { auditCreate, auditRead } from "@/lib/audit/audit-middleware";
import { requireRoles } from "@/lib/security/rbac-middleware";

const createWorkflowSchema = z.object({
  name: z.string().min(1, "Workflow name is required"),
  description: z.string().optional(),
  entityType: z.string().min(1, "Entity type is required"),
  stage: z.enum([
    "vendor_registration",
    "rfq_creation", 
    "rfq_publication",
    "offer_submission",
    "offer_evaluation",
    "contract_award",
    "po_creation",
    "po_approval",
    "delivery_confirmation",
    "invoice_processing",
    "payment_approval",
    "contract_completion"
  ]),
  steps: z.array(z.object({
    name: z.string().min(1, "Step name is required"),
    description: z.string().optional(),
    sequence: z.number().min(1),
    stepType: z.enum(["APPROVAL", "REVIEW", "NOTIFICATION", "CONDITIONAL", "PARALLEL", "SEQUENTIAL", "ESCALATION", "SIGNATURE"]),
    isRequired: z.boolean(),
    approverAssignment: z.object({
      type: z.enum(["SPECIFIC_USER", "ROLE_BASED", "DEPARTMENT", "HIERARCHY", "DYNAMIC", "COMMITTEE", "VALUE_THRESHOLD"]),
      config: z.object({
        userIds: z.array(z.string()).optional(),
        roleNames: z.array(z.string()).optional(),
        departmentIds: z.array(z.string()).optional(),
        hierarchyLevel: z.number().optional(),
        dynamicRule: z.string().optional(),
        committeeType: z.string().optional(),
        valueThresholds: z.array(z.object({
          minValue: z.number(),
          maxValue: z.number().optional(),
          approverIds: z.array(z.string()),
        })).optional(),
      }),
    }),
    requiredCount: z.number().min(1),
    allowDelegation: z.boolean(),
    timeoutHours: z.number().optional(),
    escalationConfig: z.object({
      escalateToIds: z.array(z.string()),
      escalationMessage: z.string(),
    }).optional(),
    signatureConfig: z.object({
      position: z.object({
        x: z.number(),
        y: z.number(),
      }),
      size: z.object({
        width: z.number(),
        height: z.number(),
      }),
      page: z.number().optional(),
      required: z.boolean(),
    }).optional(),
  })).min(1, "At least one step is required"),
  conditions: z.array(z.object({
    field: z.string(),
    operator: z.enum(["eq", "gt", "gte", "lt", "lte", "in", "contains"]),
    value: z.any(),
    logicalOperator: z.enum(["AND", "OR"]).optional(),
  })).optional(),
  isDefault: z.boolean().default(false),
});

// List approval workflows
const listHandler = requireRoles(["ADMIN", "COMMITTEE"])(
  auditRead("approval_workflows")(async function GET(request: NextRequest) {
    try {
      const { searchParams } = new URL(request.url);
      const entityType = searchParams.get("entityType");
      const stage = searchParams.get("stage");
      const isActive = searchParams.get("isActive");

      const workflows = await enhancedWorkflowEngine.getWorkflowConfigurations();

      // Apply filters
      let filteredWorkflows = workflows;
      
      if (entityType) {
        filteredWorkflows = filteredWorkflows.filter((w: any) => w.entityType === entityType);
      }

      if (stage) {
        filteredWorkflows = filteredWorkflows.filter((w: any) => w.stage === stage);
      }

      if (isActive !== null) {
        const activeFilter = isActive === "true";
        filteredWorkflows = filteredWorkflows.filter((w: any) => w.isActive === activeFilter);
      }

      return createSuccessResponse({
        workflows: filteredWorkflows,
        totalCount: filteredWorkflows.length,
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Create approval workflow
const createHandler = requireRoles(["ADMIN"])(
  auditCreate("approval_workflows", {
    description: "Create new approval workflow",
    severity: "HIGH",
    category: "APPROVAL_WORKFLOW",
  })(async function POST(request: NextRequest) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = createWorkflowSchema.parse(body);

      // Transform steps to match ApprovalStepConfig interface
      const transformedSteps = validatedData.steps.map((step: any) => ({
        name: step.name,
        description: step.description,
        sequence: step.sequence,
        stepType: step.stepType as any,
        isRequired: step.isRequired,
        approverAssignment: step.approverAssignment,
        requiredCount: step.requiredCount,
        allowDelegation: step.allowDelegation,
        timeoutHours: step.timeoutHours,
        escalationConfig: step.escalationConfig,
        signatureConfig: step.signatureConfig,
      }));

      // Create workflow
      const workflow = await enhancedWorkflowEngine.createStageWorkflow({
        name: validatedData.name,
        description: validatedData.description,
        entityType: validatedData.entityType,
        stage: validatedData.stage as any, // Type assertion for enum compatibility
        steps: transformedSteps,
        conditions: validatedData.conditions as any,
        isDefault: validatedData.isDefault,
        createdById: user.id,
      });

      // Log the creation
      console.log(`Approval workflow created by ${user.email}:`, {
        workflowId: workflow.id,
        name: workflow.name,
        entityType: workflow.entityType,
        stage: workflow.stage,
        stepsCount: validatedData.steps.length,
      });

      return createSuccessResponse({
        workflow,
        message: "Approval workflow created successfully",
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { listHandler as GET, createHandler as POST };
