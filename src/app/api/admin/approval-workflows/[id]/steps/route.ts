import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { handleApiError, createSuccessResponse } from '@/lib/errors';
import { getCurrentUser } from '@/lib/auth';
import { enhancedWorkflowEngine } from '@/lib/approval/enhanced-workflow-engine';
import { auditCreate, auditRead } from '@/lib/audit/audit-middleware';
import { requireRoles } from '@/lib/security/rbac-middleware';

const createStepSchema = z.object({
  name: z.string().min(1, 'Step name is required'),
  description: z.string().optional(),
  sequence: z.number().min(1),
  stepType: z.enum(['APPROVAL', 'REVIEW', 'NOTIFICATION', 'CONDITIONAL', 'PARALLEL', 'SEQUENTIAL', 'ESCALATION', 'SIGNATURE']),
  isRequired: z.boolean().default(true),
  approverAssignment: z.object({
    type: z.enum(['SPECIFIC_USER', 'ROLE_BASED', 'DEPARTMENT', 'HIERARCHY', 'DYNAMIC', 'COMMITTEE', 'VALUE_THRESHOLD']),
    config: z.object({
      userIds: z.array(z.string()).optional(),
      roleNames: z.array(z.string()).optional(),
      departmentIds: z.array(z.string()).optional(),
      hierarchyLevel: z.number().optional(),
      dynamicRule: z.string().optional(),
      committeeType: z.string().optional(),
      valueThresholds: z.array(z.object({
        minValue: z.number(),
        maxValue: z.number().optional(),
        approverIds: z.array(z.string()),
      })).optional(),
    }),
  }),
  requiredCount: z.number().min(1).default(1),
  allowDelegation: z.boolean().default(false),
  timeoutHours: z.number().optional(),
  escalationConfig: z.object({
    escalateToIds: z.array(z.string()),
    escalationMessage: z.string(),
  }).optional(),
  signatureConfig: z.object({
    position: z.object({
      x: z.number(),
      y: z.number(),
    }),
    size: z.object({
      width: z.number(),
      height: z.number(),
    }),
    page: z.number().optional(),
    required: z.boolean(),
  }).optional(),
});

const reorderStepsSchema = z.object({
  steps: z.array(z.object({
    id: z.string(),
    sequence: z.number(),
  })),
});

// Get workflow steps
const getHandler = requireRoles(['ADMIN', 'COMMITTEE'])(
  auditRead('approval_workflow_steps')(async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      // Verify workflow exists
      const workflow = await enhancedWorkflowEngine.getWorkflowById(params.id);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      const steps = await enhancedWorkflowEngine.getWorkflowSteps(params.id);

      return createSuccessResponse({
        steps: steps.sort((a, b) => a.sequence - b.sequence),
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Create workflow step
const createHandler = requireRoles(['ADMIN'])(
  auditCreate('approval_workflow_steps', {
    description: 'Create new approval workflow step',
    severity: 'MEDIUM',
    category: 'APPROVAL_WORKFLOW',
  })(async function POST(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = createStepSchema.parse(body);

      // Verify workflow exists
      const workflow = await enhancedWorkflowEngine.getWorkflowById(params.id);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      // Check if sequence already exists
      const existingSteps = await enhancedWorkflowEngine.getWorkflowSteps(params.id);
      const sequenceExists = existingSteps.some(step => step.sequence === validatedData.sequence);
      
      if (sequenceExists) {
        return NextResponse.json(
          { error: 'Step with this sequence already exists' },
          { status: 400 }
        );
      }

      // Create step
      const step = await enhancedWorkflowEngine.createWorkflowStep(params.id, {
        name: validatedData.name,
        description: validatedData.description,
        sequence: validatedData.sequence,
        stepType: validatedData.stepType as any,
        isRequired: validatedData.isRequired,
        approverAssignment: validatedData.approverAssignment,
        requiredCount: validatedData.requiredCount,
        allowDelegation: validatedData.allowDelegation,
        timeoutHours: validatedData.timeoutHours,
        escalationConfig: validatedData.escalationConfig,
        signatureConfig: validatedData.signatureConfig,
      });

      // Log the creation
      console.log(`Approval workflow step created by ${user.email}:`, {
        workflowId: params.id,
        stepId: step.id,
        stepName: step.name,
        sequence: step.sequence,
      });

      return createSuccessResponse({
        step,
        message: 'Approval workflow step created successfully',
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

// Reorder workflow steps
const reorderHandler = requireRoles(['ADMIN'])(
  auditCreate('approval_workflow_steps', {
    description: 'Reorder approval workflow steps',
    severity: 'MEDIUM',
    category: 'APPROVAL_WORKFLOW',
  })(async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      const body = await request.json();
      
      // Validate request data
      const validatedData = reorderStepsSchema.parse(body);

      // Verify workflow exists
      const workflow = await enhancedWorkflowEngine.getWorkflowById(params.id);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Approval workflow not found' },
          { status: 404 }
        );
      }

      // Verify all step IDs belong to this workflow
      const existingSteps = await enhancedWorkflowEngine.getWorkflowSteps(params.id);
      const existingStepIds = existingSteps.map(step => step.id);
      const providedStepIds = validatedData.steps.map(step => step.id);
      
      const invalidStepIds = providedStepIds.filter(id => !existingStepIds.includes(id));
      if (invalidStepIds.length > 0) {
        return NextResponse.json(
          { error: `Invalid step IDs: ${invalidStepIds.join(', ')}` },
          { status: 400 }
        );
      }

      // Reorder steps
      const updatedSteps = await enhancedWorkflowEngine.reorderWorkflowSteps(
        params.id,
        validatedData.steps
      );

      // Log the reordering
      console.log(`Approval workflow steps reordered by ${user.email}:`, {
        workflowId: params.id,
        stepCount: validatedData.steps.length,
      });

      return createSuccessResponse({
        steps: updatedSteps.sort((a, b) => a.sequence - b.sequence),
        message: 'Approval workflow steps reordered successfully',
      });
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { getHandler as GET, createHandler as POST, reorderHandler as PUT };
