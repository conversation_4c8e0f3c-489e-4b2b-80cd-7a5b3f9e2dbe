import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");

    // For now, return mock activities since we don't have an activities table
    // In a real application, you would fetch from an audit_logs or activities table
    const mockActivities = [
      {
        id: "1",
        type: "USER_LOGIN",
        description: "Admin user logged into the system",
        timestamp: new Date().toISOString(),
        severity: "LOW",
      },
      {
        id: "2", 
        type: "VENDOR_VERIFICATION",
        description: "New vendor registration pending verification",
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        severity: "MEDIUM",
      },
      {
        id: "3",
        type: "PROCUREMENT_CREATED",
        description: "New procurement package created",
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        severity: "LOW",
      },
      {
        id: "4",
        type: "SYSTEM_BACKUP",
        description: "Daily system backup completed successfully",
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        severity: "LOW",
      },
      {
        id: "5",
        type: "SECURITY_ALERT",
        description: "Multiple failed login attempts detected",
        timestamp: new Date(Date.now() - 172800000).toISOString(),
        severity: "HIGH",
      },
    ];

    const activities = mockActivities.slice(0, limit);

    return NextResponse.json({
      success: true,
      data: activities,
    });

  } catch (error) {
    console.error("Error fetching admin activities:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch activities",
      },
      { status: 500 }
    );
  }
}