import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
// import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';

const createScheduleTemplateSchema = z.object({
  templateId: z.string(),
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  stages: z.array(z.object({
    stageId: z.string(),
    stageName: z.string(),
    duration: z.number().min(1), // Duration in hours
    bufferTime: z.number().default(0), // Buffer time in hours
    dependencies: z.array(z.string()).optional(),
    workingDaysOnly: z.boolean().default(true),
    notifications: z.object({
      beforeStart: z.number().optional(), // Hours before start
      beforeEnd: z.number().optional(), // Hours before end
      onOverdue: z.boolean().default(true),
    }).optional(),
  })),
  milestones: z.array(z.object({
    name: z.string(),
    type: z.enum(['STAGE_START', 'STAGE_END', 'DEADLINE', 'REVIEW']),
    stageId: z.string().optional(),
    offsetDays: z.number().default(0), // Days offset from stage start/end
    isRequired: z.boolean().default(true),
    notifications: z.object({
      beforeDue: z.number().optional(), // Hours before due
      onOverdue: z.boolean().default(true),
    }).optional(),
  })),
  buffers: z.object({
    betweenStages: z.number().default(24), // Default buffer between stages in hours
    beforeDeadlines: z.number().default(48), // Buffer before critical deadlines
    contingency: z.number().default(10), // Contingency percentage
  }),
  workingDays: z.object({
    monday: z.boolean().default(true),
    tuesday: z.boolean().default(true),
    wednesday: z.boolean().default(true),
    thursday: z.boolean().default(true),
    friday: z.boolean().default(true),
    saturday: z.boolean().default(false),
    sunday: z.boolean().default(false),
    workingHours: z.object({
      start: z.string().default('08:00'),
      end: z.string().default('17:00'),
    }),
  }),
  holidays: z.array(z.object({
    date: z.string(),
    name: z.string(),
    recurring: z.boolean().default(false),
  })).optional(),
});

const updateScheduleTemplateSchema = createScheduleTemplateSchema.partial();

// GET /api/admin/procurement-schedule-templates
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const templateId = searchParams.get('templateId');
    const isActive = searchParams.get('isActive');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (templateId) where.templateId = templateId;
    if (isActive !== null) where.isActive = isActive === 'true';
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [scheduleTemplates, total] = await Promise.all([
      prisma.procurementScheduleTemplate.findMany({
        where,
        include: {
          creator: {
            select: { id: true, name: true, email: true },
          },
          template: {
            select: { id: true, name: true, type: true, category: true },
          },
          _count: {
            select: {
              procurementSchedules: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.procurementScheduleTemplate.count({ where }),
    ]);

    return NextResponse.json({
      scheduleTemplates,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching schedule templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch schedule templates' },
      { status: 500 }
    );
  }
}

// POST /api/admin/procurement-schedule-templates
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createScheduleTemplateSchema.parse(body);

    // Verify workflow template exists
    const workflowTemplate = await prisma.procurementWorkflowTemplate.findUnique({
      where: { id: validatedData.templateId },
      include: {
        stages: true,
      },
    });

    if (!workflowTemplate) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    // Validate that all stage IDs exist in the workflow template
    const workflowStageIds = workflowTemplate.stages.map(stage => stage.id);
    const scheduleStageIds = validatedData.stages.map(stage => stage.stageId);
    const invalidStageIds = scheduleStageIds.filter(id => !workflowStageIds.includes(id));

    if (invalidStageIds.length > 0) {
      return NextResponse.json(
        { error: `Invalid stage IDs: ${invalidStageIds.join(', ')}` },
        { status: 400 }
      );
    }

    const scheduleTemplate = await prisma.procurementScheduleTemplate.create({
      data: {
        templateId: validatedData.templateId,
        name: validatedData.name,
        description: validatedData.description,
        stages: validatedData.stages,
        milestones: validatedData.milestones,
        buffers: validatedData.buffers,
        workingDays: validatedData.workingDays,
        holidays: validatedData.holidays || [],
        createdBy: user.id,
      },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        template: {
          select: { id: true, name: true, type: true, category: true },
        },
      },
    });

    return NextResponse.json(scheduleTemplate, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating schedule template:', error);
    return NextResponse.json(
      { error: 'Failed to create schedule template' },
      { status: 500 }
    );
  }
}
