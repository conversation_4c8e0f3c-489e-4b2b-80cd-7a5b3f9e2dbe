import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
// import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';

const updateScheduleTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  stages: z.array(z.object({
    stageId: z.string(),
    stageName: z.string(),
    duration: z.number().min(1), // Duration in hours
    bufferTime: z.number().default(0), // Buffer time in hours
    dependencies: z.array(z.string()).optional(),
    workingDaysOnly: z.boolean().default(true),
    notifications: z.object({
      beforeStart: z.number().optional(), // Hours before start
      beforeEnd: z.number().optional(), // Hours before end
      onOverdue: z.boolean().default(true),
    }).optional(),
  })).optional(),
  milestones: z.array(z.object({
    name: z.string(),
    type: z.enum(['STAGE_START', 'STAGE_END', 'DEADLINE', 'REVIEW']),
    stageId: z.string().optional(),
    offsetDays: z.number().default(0), // Days offset from stage start/end
    isRequired: z.boolean().default(true),
    notifications: z.object({
      beforeDue: z.number().optional(), // Hours before due
      onOverdue: z.boolean().default(true),
    }).optional(),
  })).optional(),
  buffers: z.object({
    betweenStages: z.number().default(24), // Default buffer between stages in hours
    beforeDeadlines: z.number().default(48), // Buffer before critical deadlines
    contingency: z.number().default(10), // Contingency percentage
  }).optional(),
  workingDays: z.object({
    monday: z.boolean().default(true),
    tuesday: z.boolean().default(true),
    wednesday: z.boolean().default(true),
    thursday: z.boolean().default(true),
    friday: z.boolean().default(true),
    saturday: z.boolean().default(false),
    sunday: z.boolean().default(false),
    workingHours: z.object({
      start: z.string().default('08:00'),
      end: z.string().default('17:00'),
    }),
  }).optional(),
  holidays: z.array(z.object({
    date: z.string(),
    name: z.string(),
    recurring: z.boolean().default(false),
  })).optional(),
});

// GET /api/admin/procurement-schedule-templates/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const scheduleTemplate = await prisma.procurementScheduleTemplate.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        template: {
          select: { id: true, name: true, type: true, category: true },
          include: {
            stages: {
              orderBy: { sequence: 'asc' },
            },
          },
        },
        procurementSchedules: {
          include: {
            procurement: {
              select: {
                id: true,
                procurementNumber: true,
                title: true,
                status: true,
                createdAt: true,
              },
            },
          },
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            procurementSchedules: true,
          },
        },
      },
    });

    if (!scheduleTemplate) {
      return NextResponse.json(
        { error: 'Schedule template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(scheduleTemplate);
  } catch (error) {
    console.error('Error fetching schedule template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch schedule template' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/procurement-schedule-templates/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateScheduleTemplateSchema.parse(body);

    // Check if schedule template exists
    const existingTemplate = await prisma.procurementScheduleTemplate.findUnique({
      where: { id: params.id },
      include: {
        template: {
          include: {
            stages: true,
          },
        },
      },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Schedule template not found' },
        { status: 404 }
      );
    }

    // If stages are being updated, validate stage IDs
    if (validatedData.stages) {
      const workflowStageIds = existingTemplate.template.stages.map(stage => stage.id);
      const scheduleStageIds = validatedData.stages.map(stage => stage.stageId);
      const invalidStageIds = scheduleStageIds.filter(id => !workflowStageIds.includes(id));

      if (invalidStageIds.length > 0) {
        return NextResponse.json(
          { error: `Invalid stage IDs: ${invalidStageIds.join(', ')}` },
          { status: 400 }
        );
      }
    }

    const scheduleTemplate = await prisma.procurementScheduleTemplate.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        template: {
          select: { id: true, name: true, type: true, category: true },
        },
        _count: {
          select: {
            procurementSchedules: true,
          },
        },
      },
    });

    return NextResponse.json(scheduleTemplate);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating schedule template:', error);
    return NextResponse.json(
      { error: 'Failed to update schedule template' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/procurement-schedule-templates/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if schedule template exists and is not being used
    const scheduleTemplate = await prisma.procurementScheduleTemplate.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { procurementSchedules: true },
        },
      },
    });

    if (!scheduleTemplate) {
      return NextResponse.json(
        { error: 'Schedule template not found' },
        { status: 404 }
      );
    }

    if (scheduleTemplate._count.procurementSchedules > 0) {
      return NextResponse.json(
        { error: 'Cannot delete schedule template that is being used by procurements' },
        { status: 400 }
      );
    }

    await prisma.procurementScheduleTemplate.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'Schedule template deleted successfully' });
  } catch (error) {
    console.error('Error deleting schedule template:', error);
    return NextResponse.json(
      { error: 'Failed to delete schedule template' },
      { status: 500 }
    );
  }
}
