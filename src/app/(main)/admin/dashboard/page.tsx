"use client";

import { useEffect, useState } from "react";
import { AdminDashboard } from "@/components/dashboard/admin-dashboard";

interface DashboardStats {
   vendors: {
      verified: number;
      pending: number;
      total: number;
   };
   procurements: {
      active: number;
      completed: number;
      total: number;
   };
   offers: {
      submitted: number;
      evaluated: number;
      total: number;
   };
}

async function fetchDashboardStats(): Promise<DashboardStats> {
   try {
      const response = await fetch("/api/admin/dashboard/stats");
      if (!response.ok) {
         throw new Error("Failed to fetch dashboard stats");
      }
      const result = await response.json();
      return result.data;
   } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      // Return default stats if API fails
      return {
         vendors: { verified: 0, pending: 0, total: 0 },
         procurements: { active: 0, completed: 0, total: 0 },
         offers: { submitted: 0, evaluated: 0, total: 0 },
      };
   }
}

export default function AdminDashboardPage() {
   const [stats, setStats] = useState<DashboardStats | null>(null);
   const [isLoading, setIsLoading] = useState(true);

   useEffect(() => {
      const loadStats = async () => {
         try {
            const dashboardStats = await fetchDashboardStats();
            setStats(dashboardStats);
         } catch (error) {
            console.error("Failed to load dashboard stats:", error);
         } finally {
            setIsLoading(false);
         }
      };

      loadStats();
   }, []);

   if (isLoading) {
      return (
         <div className="container mx-auto py-8">
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
               <p className="text-gray-600 mt-2">
                  Panel administrasi sistem e-procurement
               </p>
            </div>
            <div className="animate-pulse space-y-6">
               <div className="h-32 bg-gray-200 rounded"></div>
               <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="h-64 bg-gray-200 rounded"></div>
                  <div className="h-64 bg-gray-200 rounded"></div>
               </div>
               <div className="h-48 bg-gray-200 rounded"></div>
            </div>
         </div>
      );
   }

   return (
      <div className="container mx-auto py-8">
         <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-600 mt-2">
               Panel administrasi sistem e-procurement
            </p>
         </div>

         <AdminDashboard stats={stats} />
      </div>
   );
}