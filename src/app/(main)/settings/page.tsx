"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
   Settings,
   User,
   Bell,
   Shield,
   Database,
   Mail,
   Key,
   Globe,
   Save
} from "lucide-react";

interface UserSettings {
   name: string;
   email: string;
   phone: string;
   roles: string[];
}

export default function SettingsPage() {
   const [userSettings, setUserSettings] = useState<UserSettings>({
      name: "",
      email: "",
      phone: "",
      roles: [],
   });
   const [isLoading, setIsLoading] = useState(true);

   useEffect(() => {
      // Load user data from localStorage
      const userData = localStorage.getItem("user");
      if (userData) {
         const user = JSON.parse(userData);
         setUserSettings({
            name: user.name || "",
            email: user.email || "",
            phone: user.phone || "",
            roles: user.roles || [],
         });
      }
      setIsLoading(false);
   }, []);

   const handleSave = () => {
      // TODO: Implement save functionality
      console.log("Saving settings:", userSettings);
   };

   if (isLoading) {
      return (
         <div className="container mx-auto py-8">
            <div className="animate-pulse space-y-6">
               <div className="h-8 bg-gray-200 rounded w-1/4"></div>
               <div className="h-64 bg-gray-200 rounded"></div>
            </div>
         </div>
      );
   }

   return (
      <div className="container mx-auto py-8">
         <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
               <Settings className="h-8 w-8" />
               Pengaturan Sistem
            </h1>
            <p className="text-gray-600 mt-2">
               Kelola pengaturan akun dan sistem e-procurement
            </p>
         </div>

         <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
               <TabsTrigger value="profile" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Profil
               </TabsTrigger>
               <TabsTrigger value="notifications" className="flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  Notifikasi
               </TabsTrigger>
               <TabsTrigger value="security" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Keamanan
               </TabsTrigger>
               <TabsTrigger value="system" className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Sistem
               </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
               <Card>
                  <CardHeader>
                     <CardTitle>Informasi Profil</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                           <Label htmlFor="name">Nama Lengkap</Label>
                           <Input
                              id="name"
                              value={userSettings.name}
                              onChange={(e) => setUserSettings(prev => ({ ...prev, name: e.target.value }))}
                              placeholder="Masukkan nama lengkap"
                           />
                        </div>
                        <div className="space-y-2">
                           <Label htmlFor="email">Email</Label>
                           <Input
                              id="email"
                              type="email"
                              value={userSettings.email}
                              onChange={(e) => setUserSettings(prev => ({ ...prev, email: e.target.value }))}
                              placeholder="Masukkan email"
                           />
                        </div>
                        <div className="space-y-2">
                           <Label htmlFor="phone">Nomor Telepon</Label>
                           <Input
                              id="phone"
                              value={userSettings.phone}
                              onChange={(e) => setUserSettings(prev => ({ ...prev, phone: e.target.value }))}
                              placeholder="Masukkan nomor telepon"
                           />
                        </div>
                        <div className="space-y-2">
                           <Label>Role</Label>
                           <div className="flex gap-2">
                              {userSettings.roles.map((role) => (
                                 <Badge key={role} variant="secondary">
                                    {role}
                                 </Badge>
                              ))}
                           </div>
                        </div>
                     </div>
                     <Separator />
                     <div className="flex justify-end">
                        <Button onClick={handleSave} className="flex items-center gap-2">
                           <Save className="h-4 w-4" />
                           Simpan Perubahan
                        </Button>
                     </div>
                  </CardContent>
               </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <Mail className="h-5 w-5" />
                        Pengaturan Notifikasi
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="space-y-4">
                        <div className="flex items-center justify-between">
                           <div>
                              <h4 className="font-medium">Email Notifikasi</h4>
                              <p className="text-sm text-gray-600">Terima notifikasi melalui email</p>
                           </div>
                           <Button variant="outline" size="sm">
                              Aktif
                           </Button>
                        </div>
                        <Separator />
                        <div className="flex items-center justify-between">
                           <div>
                              <h4 className="font-medium">Notifikasi Browser</h4>
                              <p className="text-sm text-gray-600">Terima notifikasi di browser</p>
                           </div>
                           <Button variant="outline" size="sm">
                              Aktif
                           </Button>
                        </div>
                        <Separator />
                        <div className="flex items-center justify-between">
                           <div>
                              <h4 className="font-medium">SMS Notifikasi</h4>
                              <p className="text-sm text-gray-600">Terima notifikasi melalui SMS</p>
                           </div>
                           <Button variant="outline" size="sm">
                              Nonaktif
                           </Button>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <Key className="h-5 w-5" />
                        Keamanan Akun
                     </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                     <div className="space-y-4">
                        <div>
                           <h4 className="font-medium mb-2">Ubah Password</h4>
                           <div className="space-y-2">
                              <Input type="password" placeholder="Password saat ini" />
                              <Input type="password" placeholder="Password baru" />
                              <Input type="password" placeholder="Konfirmasi password baru" />
                           </div>
                           <Button className="mt-2" size="sm">
                              Ubah Password
                           </Button>
                        </div>
                        <Separator />
                        <div>
                           <h4 className="font-medium">Aktivitas Login Terakhir</h4>
                           <p className="text-sm text-gray-600 mt-1">
                              Terakhir login: {new Date().toLocaleString('id-ID')}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </TabsContent>

            <TabsContent value="system" className="space-y-6">
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <Globe className="h-5 w-5" />
                        Pengaturan Sistem
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                           <div className="space-y-2">
                              <Label>Zona Waktu</Label>
                              <Input value="Asia/Jakarta (WIB)" readOnly />
                           </div>
                           <div className="space-y-2">
                              <Label>Bahasa</Label>
                              <Input value="Bahasa Indonesia" readOnly />
                           </div>
                           <div className="space-y-2">
                              <Label>Format Tanggal</Label>
                              <Input value="DD/MM/YYYY" readOnly />
                           </div>
                           <div className="space-y-2">
                              <Label>Format Mata Uang</Label>
                              <Input value="IDR (Rupiah)" readOnly />
                           </div>
                        </div>
                        <Separator />
                        <div className="text-sm text-gray-600">
                           <p><strong>Versi Sistem:</strong> 1.0.0</p>
                           <p><strong>Terakhir Update:</strong> {new Date().toLocaleDateString('id-ID')}</p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </TabsContent>
         </Tabs>
      </div>
   );
}