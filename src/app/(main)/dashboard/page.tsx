"use client";

import { useEffect, useState } from "react";
import { DashboardOverview } from "@/components/dashboard/dashboard-overview";

export default function DashboardPage() {
  const [userRoles, setUserRoles] = useState<string[]>([]);

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem("user");
    if (userData) {
      const user = JSON.parse(userData);
      setUserRoles(user.roles || []);
    }
  }, []);

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Ringkasan aktivitas dan informasi penting sistem e-procurement
        </p>
      </div>
      
      <DashboardOverview userRoles={userRoles} />
    </div>
  );
}
