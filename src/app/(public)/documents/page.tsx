"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Search, Download, FileText, Calendar, Filter } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface PublicDocument {
  id: string;
  filename: string;
  originalName: string;
  url: string;
  title?: string;
  description?: string;
  size: number;
  uploadedAt: string;
  mimeType: string;
}

interface DocumentsResponse {
  documents: PublicDocument[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalDocuments: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    limit: number;
  };
}

async function fetchPublicDocuments(page: number, search: string): Promise<DocumentsResponse> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: "12",
    ...(search && { search }),
  });

  const response = await fetch(`/api/public/documents?${params}`);
  if (!response.ok) {
    throw new Error("Failed to fetch documents");
  }
  const result = await response.json();
  return result.data;
}

export default function PublicDocumentsPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchInput, setSearchInput] = useState("");

  const { data, isLoading, error } = useQuery({
    queryKey: ["public-documents", currentPage, searchQuery],
    queryFn: () => fetchPublicDocuments(currentPage, searchQuery),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const handleSearch = () => {
    setSearchQuery(searchInput);
    setCurrentPage(1);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileTypeFromMime = (mimeType: string) => {
    if (mimeType.includes("pdf")) return "PDF";
    if (mimeType.includes("word")) return "DOC";
    if (mimeType.includes("excel")) return "XLS";
    if (mimeType.includes("powerpoint")) return "PPT";
    return "FILE";
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Dokumen Publik
          </h1>
          <p className="text-gray-600">
            Unduh dokumen, panduan, dan formulir yang tersedia untuk umum
          </p>
        </div>

        {/* Search */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cari dokumen..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button onClick={handleSearch}>
                <Filter className="h-4 w-4 mr-2" />
                Cari
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-full"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card>
            <CardContent className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Gagal memuat dokumen
              </h3>
              <p className="text-gray-600">
                Terjadi kesalahan saat memuat dokumen. Silakan coba lagi.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Documents Grid */}
        {data && (
          <>
            {data.documents.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {searchQuery ? "Tidak ada dokumen yang ditemukan" : "Belum ada dokumen"}
                  </h3>
                  <p className="text-gray-600">
                    {searchQuery
                      ? "Coba gunakan kata kunci yang berbeda"
                      : "Dokumen akan muncul di sini setelah diunggah oleh admin"
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                  {data.documents.map((document) => (
                    <Card key={document.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex items-start justify-between mb-2">
                          <Badge variant="outline">
                            {getFileTypeFromMime(document.mimeType)}
                          </Badge>
                          <div className="text-xs text-gray-500">
                            {formatFileSize(document.size)}
                          </div>
                        </div>
                        <CardTitle className="text-lg line-clamp-2">
                          {document.title || document.originalName}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {document.description && (
                            <p className="text-sm text-gray-600 line-clamp-3">
                              {document.description}
                            </p>
                          )}

                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Calendar className="h-3 w-3" />
                            <span>
                              {format(new Date(document.uploadedAt), "dd MMM yyyy", { locale: id })}
                            </span>
                          </div>

                          <Button
                            className="w-full"
                            onClick={() => {
                              const link = window.document.createElement("a");
                              link.href = document.url;
                              link.download = document.originalName;
                              link.click();
                            }}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Unduh
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Pagination */}
                {data.pagination.totalPages > 1 && (
                  <div className="flex justify-center items-center gap-2">
                    <Button
                      variant="outline"
                      disabled={!data.pagination.hasPrevPage}
                      onClick={() => setCurrentPage(currentPage - 1)}
                    >
                      Sebelumnya
                    </Button>

                    <span className="text-sm text-gray-600 mx-4">
                      Halaman {data.pagination.currentPage} dari {data.pagination.totalPages}
                    </span>

                    <Button
                      variant="outline"
                      disabled={!data.pagination.hasNextPage}
                      onClick={() => setCurrentPage(currentPage + 1)}
                    >
                      Selanjutnya
                    </Button>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
}
