import Link from "next/link";
import { Building2, Shield, Users, Target, CheckCircle, Award, Globe, Mail, Phone, MapPin } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { fetchAboutPageContent } from "@/lib/api/about-content";

export default async function AboutPage() {
  const content = await fetchAboutPageContent();
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {content.heroTitle}
            </h1>
            <h2 className="text-xl md:text-2xl mb-4 text-blue-100">
              {content.heroSubtitle}
            </h2>
            <p className="text-lg text-blue-100">
              {content.heroDescription}
            </p>
          </div>
        </div>
      </section>

      {/* About E-Procurement */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {content.aboutTitle}
              </h2>
              <p className="text-lg text-gray-600">
                {content.aboutDescription}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="text-center">
                <CardHeader>
                  <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <CardTitle>Transparansi</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Semua proses pengadaan dapat dimonitor dan diaudit dengan mudah
                  </p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <Target className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <CardTitle>Efisiensi</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Proses pengadaan menjadi lebih cepat dan mengurangi biaya operasional
                  </p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <Users className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <CardTitle>Aksesibilitas</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Vendor dapat mengakses informasi pengadaan kapan saja dan dimana saja
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Bank Profile */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {content.companyTitle}
              </h2>
              <p className="text-lg text-gray-600">
                {content.companyDescription}
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Profil Perusahaan</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Building2 className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900">{content.companyName}</h4>
                      <p className="text-gray-600">{content.companyFullName}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Globe className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Wilayah Operasional</h4>
                      <p className="text-gray-600">{content.operationalArea}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Award className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-gray-900">Visi</h4>
                      <p className="text-gray-600">
                        {content.vision}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Keunggulan E-Procurement</h3>
                <div className="space-y-3">
                  {content.features.map((item, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                      <span className="text-gray-700">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {content.contactTitle}
              </h2>
              <p className="text-lg text-gray-600">
                {content.contactDescription}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="text-center">
                <CardHeader>
                  <MapPin className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <CardTitle className="text-lg">Alamat</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 whitespace-pre-line">
                    {content.address}
                  </p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <Phone className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <CardTitle className="text-lg">Telepon</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-gray-600">
                    {content.phones.map((phone, index) => (
                      <div key={index}>{phone}</div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <Mail className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <CardTitle className="text-lg">Email</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-gray-600">
                    {content.emails.map((email, index) => (
                      <div key={index}>{email}</div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">
              {content.ctaTitle}
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              {content.ctaDescription}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/register">
                <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                  Daftar Sebagai Vendor
                </Button>
              </Link>
              <Link href="/procurements">
                <Button size="lg" variant="outline" className="w-full sm:w-auto text-white border-white hover:bg-white hover:text-blue-600">
                  Lihat Pengadaan Aktif
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}