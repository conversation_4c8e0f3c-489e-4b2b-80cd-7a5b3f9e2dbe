"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Search, Filter, Calendar, DollarSign, FileText, Clock } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface PublicProcurement {
  id: string;
  title: string;
  procurementNumber: string;
  type: string;
  status: string;
  ownerEstimate?: number;
  showOwnerEstimateToVendor: boolean;
  createdAt: string;
  stages: Array<{
    name: string;
    startDate: string;
    endDate: string;
    status: string;
  }>;
  _count: {
    items: number;
    offers: number;
  };
}

interface ProcurementFilters {
  search: string;
  type: string;
  status: string;
  minValue: string;
  maxValue: string;
  sortBy: string;
}

async function fetchPublicProcurements(
  filters: ProcurementFilters,
  page: number = 1,
  limit: number = 12
): Promise<{
  procurements: PublicProcurement[];
  totalCount: number;
  totalPages: number;
}> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value)),
  });

  const response = await fetch(`/api/public/procurements?${params}`);
  if (!response.ok) {
    throw new Error("Failed to fetch procurements");
  }
  const result = await response.json();
  return result.data;
}

export default function PublicProcurementsPage() {
  const [filters, setFilters] = useState<ProcurementFilters>({
    search: "",
    type: "",
    status: "",
    minValue: "",
    maxValue: "",
    sortBy: "newest",
  });
  const [currentPage, setCurrentPage] = useState(1);

  const { data, isLoading, error } = useQuery({
    queryKey: ["public-procurements", filters, currentPage],
    queryFn: () => fetchPublicProcurements(filters, currentPage),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PUBLISHED: { variant: "default" as const, label: "Terbuka" },
      SUBMISSION: { variant: "default" as const, label: "Penawaran" },
      EVALUATION: { variant: "secondary" as const, label: "Evaluasi" },
      NEGOTIATION: { variant: "secondary" as const, label: "Negosiasi" },
      WINNER_ANNOUNCEMENT: { variant: "default" as const, label: "Pengumuman" },
      AWARDED: { variant: "outline" as const, label: "Dikontrakkan" },
      COMPLETED: { variant: "outline" as const, label: "Selesai" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getActiveStage = (stages: PublicProcurement["stages"]) => {
    const activeStage = stages.find(stage => stage.status === "ONGOING");
    if (activeStage) {
      return {
        name: activeStage.name,
        endDate: activeStage.endDate,
      };
    }

    const nextStage = stages.find(stage => stage.status === "PENDING");
    if (nextStage) {
      return {
        name: `Akan datang: ${nextStage.name}`,
        endDate: nextStage.startDate,
      };
    }

    return null;
  };

  const handleFilterChange = (key: keyof ProcurementFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Pengadaan Aktif
          </h1>
          <p className="text-gray-600">
            Temukan peluang bisnis dengan PT Bank BPD Sulteng
          </p>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter & Pencarian
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              <div className="xl:col-span-2">
                <Input
                  placeholder="Cari pengadaan..."
                  value={filters.search}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleFilterChange("search", e.target.value)}
                  className="w-full"
                />
              </div>

              <Select value={filters.type} onValueChange={(value: string) => handleFilterChange("type", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  <SelectItem value="RFQ">RFQ</SelectItem>
                  <SelectItem value="TENDER">Tender</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.status} onValueChange={(value: string) => handleFilterChange("status", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="PUBLISHED">Terbuka</SelectItem>
                  <SelectItem value="SUBMISSION">Penawaran</SelectItem>
                  <SelectItem value="EVALUATION">Evaluasi</SelectItem>
                </SelectContent>
              </Select>

              <Input
                placeholder="Nilai min (Rp)"
                type="number"
                value={filters.minValue}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleFilterChange("minValue", e.target.value)}
              />

              <Select value={filters.sortBy} onValueChange={(value: string) => handleFilterChange("sortBy", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Urutkan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Terbaru</SelectItem>
                  <SelectItem value="oldest">Terlama</SelectItem>
                  <SelectItem value="value-high">Nilai Tertinggi</SelectItem>
                  <SelectItem value="value-low">Nilai Terendah</SelectItem>
                  <SelectItem value="deadline">Deadline Terdekat</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-20 bg-gray-200 rounded w-full"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-red-500 mb-2">Error loading procurements</div>
              <p className="text-gray-600">Please try again later</p>
            </CardContent>
          </Card>
        ) : data?.procurements.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Tidak ada pengadaan ditemukan
              </h3>
              <p className="text-gray-600">
                Coba ubah filter pencarian atau periksa kembali nanti
              </p>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Results Count */}
            <div className="mb-6">
              <p className="text-gray-600">
                Menampilkan {data?.procurements.length} dari {data?.totalCount} pengadaan
              </p>
            </div>

            {/* Procurement Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {data?.procurements.map((procurement) => {
                const activeStage = getActiveStage(procurement.stages);

                return (
                  <Card key={procurement.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex justify-between items-start mb-2">
                        <Badge variant="outline">{procurement.type}</Badge>
                        {getStatusBadge(procurement.status)}
                      </div>
                      <CardTitle className="text-lg line-clamp-2">
                        {procurement.title}
                      </CardTitle>
                      <p className="text-sm text-gray-600">
                        {procurement.procurementNumber}
                      </p>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {procurement.showOwnerEstimateToVendor && procurement.ownerEstimate && (
                          <div className="flex items-center gap-2 text-sm">
                            <DollarSign className="h-4 w-4 text-gray-400" />
                            <span className="font-medium">
                              {formatCurrency(procurement.ownerEstimate)}
                            </span>
                          </div>
                        )}

                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <FileText className="h-4 w-4" />
                          <span>{procurement._count.items} item</span>
                          <span>•</span>
                          <span>{procurement._count.offers} penawaran</span>
                        </div>

                        {activeStage && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Clock className="h-4 w-4" />
                            <div>
                              <div>{activeStage.name}</div>
                              <div className="text-xs">
                                Hingga {format(new Date(activeStage.endDate), "dd MMM yyyy HH:mm", { locale: id })}
                              </div>
                            </div>
                          </div>
                        )}

                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <span>
                            Dipublikasi {format(new Date(procurement.createdAt), "dd MMM yyyy", { locale: id })}
                          </span>
                        </div>
                      </div>

                      <div className="mt-4">
                        <Link href={`/procurements/${procurement.id}`}>
                          <Button className="w-full">
                            Lihat Detail
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Pagination */}
            {data && data.totalPages > 1 && (
              <div className="flex justify-center gap-2">
                <Button
                  variant="outline"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => prev - 1)}
                >
                  Sebelumnya
                </Button>

                {[...Array(Math.min(5, data.totalPages))].map((_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  );
                })}

                <Button
                  variant="outline"
                  disabled={currentPage === data.totalPages}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                >
                  Selanjutnya
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
