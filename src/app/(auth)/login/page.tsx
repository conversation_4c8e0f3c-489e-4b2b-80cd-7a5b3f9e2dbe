"use client";

import { LoginForm } from "@/components/auth/login-form";
import { DynamicLogo } from "@/components/public/dynamic-logo";

export default function LoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <DynamicLogo size="lg" className="mx-auto" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            E-Procurement System
          </h1>
          <p className="text-gray-600">PT Bank BPD Sulteng</p>
        </div>
        <LoginForm />
      </div>
    </div>
  );
}
