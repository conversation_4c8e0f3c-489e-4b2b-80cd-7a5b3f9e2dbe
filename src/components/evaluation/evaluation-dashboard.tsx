"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Eye, FileText, BarChart3, CheckCircle } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ProcurementForEvaluation {
  id: string;
  title: string;
  procurementNumber: string;
  type: "TENDER" | "RFQ";
  status: string;
  evaluationMethod: string;
  createdAt: string;
  _count: {
    offers: number;
  };
  committee: Array<{
    id: string;
    committeeRole: string;
    user: {
      id: string;
      name: string;
    };
  }>;
}

interface OfferForEvaluation {
  id: string;
  offerNumber: string;
  totalOfferedPrice: number;
  status: string;
  submissionDate: string;
  vendor: {
    id: string;
    companyName: string;
    picName: string;
  };
  items: Array<{
    id: string;
    offeredPrice: number;
    item: {
      id: string;
      name: string;
      quantity: number;
      unit: string;
      ownerEstimate: number;
    };
  }>;
  documents: Array<{
    id: string;
    fileName: string;
    fileUrl: string;
    documentType: string;
  }>;
  evaluations: Array<{
    id: string;
    technicalScore: number;
    commercialScore: number;
    totalScore: number;
    notes: string;
    evaluatedBy: string;
    evaluatedAt: string;
  }>;
}

async function fetchProcurementsForEvaluation(): Promise<ProcurementForEvaluation[]> {
  const response = await fetch("/api/evaluations/procurements");
  if (!response.ok) {
    throw new Error("Failed to fetch procurements for evaluation");
  }
  const result = await response.json();
  return result.data;
}

async function fetchOffersForEvaluation(procurementId: string): Promise<OfferForEvaluation[]> {
  const response = await fetch(`/api/evaluations/procurements/${procurementId}/offers`);
  if (!response.ok) {
    throw new Error("Failed to fetch offers for evaluation");
  }
  const result = await response.json();
  return result.data;
}

async function submitEvaluation(data: {
  offerId: string;
  technicalScore: number;
  commercialScore: number;
  notes: string;
}) {
  const response = await fetch(`/api/evaluations/offers/${data.offerId}/evaluate`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to submit evaluation");
  }

  return response.json();
}

export function EvaluationDashboard() {
  const [selectedProcurement, setSelectedProcurement] = useState<string | null>(null);
  const [selectedOffer, setSelectedOffer] = useState<OfferForEvaluation | null>(null);
  const [showEvaluationDialog, setShowEvaluationDialog] = useState(false);
  const [evaluationForm, setEvaluationForm] = useState({
    technicalScore: 0,
    commercialScore: 0,
    notes: "",
  });
  const [error, setError] = useState<string | null>(null);

  const queryClient = useQueryClient();

  const { data: procurements, isLoading: procurementsLoading } = useQuery({
    queryKey: ["procurements-for-evaluation"],
    queryFn: fetchProcurementsForEvaluation,
  });

  const { data: offers, isLoading: offersLoading } = useQuery({
    queryKey: ["offers-for-evaluation", selectedProcurement],
    queryFn: () => fetchOffersForEvaluation(selectedProcurement!),
    enabled: !!selectedProcurement,
  });

  const evaluationMutation = useMutation({
    mutationFn: submitEvaluation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["offers-for-evaluation"] });
      setShowEvaluationDialog(false);
      setSelectedOffer(null);
      setEvaluationForm({ technicalScore: 0, commercialScore: 0, notes: "" });
      setError(null);
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const handleEvaluateOffer = (offer: OfferForEvaluation) => {
    setSelectedOffer(offer);
    setShowEvaluationDialog(true);
    setEvaluationForm({ technicalScore: 0, commercialScore: 0, notes: "" });
    setError(null);
  };

  const handleSubmitEvaluation = () => {
    if (!selectedOffer) return;

    if (evaluationForm.technicalScore < 0 || evaluationForm.technicalScore > 100) {
      setError("Skor teknis harus antara 0-100");
      return;
    }

    if (evaluationForm.commercialScore < 0 || evaluationForm.commercialScore > 100) {
      setError("Skor komersial harus antara 0-100");
      return;
    }

    evaluationMutation.mutate({
      offerId: selectedOffer.id,
      technicalScore: evaluationForm.technicalScore,
      commercialScore: evaluationForm.commercialScore,
      notes: evaluationForm.notes,
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      EVALUATION: { variant: "default" as const, label: "Evaluasi" },
      NEGOTIATION: { variant: "secondary" as const, label: "Negosiasi" },
      WINNER_ANNOUNCEMENT: { variant: "default" as const, label: "Pengumuman Pemenang" },
      AWARDED: { variant: "default" as const, label: "Dikontrakkan" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateTotalScore = (technicalScore: number, commercialScore: number) => {
    // Weighted scoring: 70% technical, 30% commercial
    return (technicalScore * 0.7) + (commercialScore * 0.3);
  };

  if (procurementsLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Procurement Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Pengadaan untuk Evaluasi</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Judul Pengadaan</TableHead>
                <TableHead>Nomor</TableHead>
                <TableHead>Jenis</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Penawaran</TableHead>
                <TableHead>Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {procurements?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    Tidak ada pengadaan yang perlu dievaluasi
                  </TableCell>
                </TableRow>
              ) : (
                procurements?.map((procurement) => (
                  <TableRow key={procurement.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{procurement.title}</p>
                        <p className="text-sm text-gray-600">
                          {format(new Date(procurement.createdAt), "dd MMM yyyy", { locale: id })}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-mono text-sm">{procurement.procurementNumber}</span>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{procurement.type}</Badge>
                    </TableCell>
                    <TableCell>{getStatusBadge(procurement.status)}</TableCell>
                    <TableCell>
                      <span className="font-semibold">{procurement._count.offers}</span> penawaran
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedProcurement(procurement.id)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Evaluasi
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Offers for Selected Procurement */}
      {selectedProcurement && (
        <Card>
          <CardHeader>
            <CardTitle>Daftar Penawaran untuk Evaluasi</CardTitle>
          </CardHeader>
          <CardContent>
            {offersLoading ? (
              <div className="text-center py-8">Loading offers...</div>
            ) : offers?.length === 0 ? (
              <div className="text-center py-8">Tidak ada penawaran untuk dievaluasi</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vendor</TableHead>
                    <TableHead>Nomor Penawaran</TableHead>
                    <TableHead>Total Harga</TableHead>
                    <TableHead>Status Evaluasi</TableHead>
                    <TableHead>Skor</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {offers?.map((offer) => {
                    const hasEvaluation = offer.evaluations.length > 0;
                    const avgScore = hasEvaluation
                      ? offer.evaluations.reduce((sum, evaluation) => sum + evaluation.totalScore, 0) / offer.evaluations.length
                      : 0;

                    return (
                      <TableRow key={offer.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{offer.vendor.companyName}</p>
                            <p className="text-sm text-gray-600">{offer.vendor.picName}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-mono text-sm">{offer.offerNumber}</span>
                        </TableCell>
                        <TableCell>
                          <span className="font-semibold">{formatCurrency(offer.totalOfferedPrice)}</span>
                        </TableCell>
                        <TableCell>
                          {hasEvaluation ? (
                            <Badge variant="default">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Sudah Dievaluasi
                            </Badge>
                          ) : (
                            <Badge variant="secondary">Belum Dievaluasi</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {hasEvaluation ? (
                            <span className="font-semibold">{avgScore.toFixed(1)}</span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedOffer(offer)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              Detail
                            </Button>
                            {!hasEvaluation && (
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleEvaluateOffer(offer)}
                              >
                                <BarChart3 className="h-4 w-4 mr-1" />
                                Evaluasi
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      )}

      {/* Offer Detail Dialog */}
      {selectedOffer && !showEvaluationDialog && (
        <Dialog open={!!selectedOffer} onOpenChange={() => setSelectedOffer(null)}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Detail Penawaran - {selectedOffer.vendor.companyName}</DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              {/* Offer Summary */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Ringkasan Penawaran</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Nomor Penawaran</Label>
                    <p className="text-sm font-mono">{selectedOffer.offerNumber}</p>
                  </div>
                  <div>
                    <Label>Total Harga</Label>
                    <p className="text-sm font-semibold">{formatCurrency(selectedOffer.totalOfferedPrice)}</p>
                  </div>
                  <div>
                    <Label>Tanggal Submit</Label>
                    <p className="text-sm">
                      {format(new Date(selectedOffer.submissionDate), "dd MMMM yyyy HH:mm", { locale: id })}
                    </p>
                  </div>
                  <div>
                    <Label>Status</Label>
                    <Badge variant="outline">{selectedOffer.status}</Badge>
                  </div>
                </div>
              </div>

              {/* Offer Items */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Detail Item</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Kuantitas</TableHead>
                      <TableHead>Harga Penawaran</TableHead>
                      <TableHead>Estimasi Owner</TableHead>
                      <TableHead>Selisih</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedOffer.items.map((item) => {
                      const difference = item.offeredPrice - item.item.ownerEstimate;
                      const percentageDiff = ((difference / item.item.ownerEstimate) * 100);

                      return (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{item.item.name}</p>
                              <p className="text-sm text-gray-600">
                                {item.item.quantity} {item.item.unit}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>{item.item.quantity.toLocaleString("id-ID")}</TableCell>
                          <TableCell>{formatCurrency(item.offeredPrice)}</TableCell>
                          <TableCell>{formatCurrency(item.item.ownerEstimate)}</TableCell>
                          <TableCell>
                            <span className={difference < 0 ? "text-green-600" : "text-red-600"}>
                              {formatCurrency(Math.abs(difference))} ({percentageDiff.toFixed(1)}%)
                            </span>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {/* Documents */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Dokumen Penawaran</h3>
                <div className="space-y-2">
                  {selectedOffer.documents.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-gray-500" />
                        <div>
                          <p className="font-medium">{doc.fileName}</p>
                          <p className="text-sm text-gray-600">{doc.documentType}</p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(doc.fileUrl, "_blank")}
                      >
                        Lihat
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Existing Evaluations */}
              {selectedOffer.evaluations.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Hasil Evaluasi</h3>
                  <div className="space-y-3">
                    {selectedOffer.evaluations.map((evaluation) => (
                      <div key={evaluation.id} className="p-4 border rounded">
                        <div className="grid grid-cols-3 gap-4 mb-2">
                          <div>
                            <Label>Skor Teknis</Label>
                            <p className="text-sm font-semibold">{evaluation.technicalScore}</p>
                          </div>
                          <div>
                            <Label>Skor Komersial</Label>
                            <p className="text-sm font-semibold">{evaluation.commercialScore}</p>
                          </div>
                          <div>
                            <Label>Total Skor</Label>
                            <p className="text-sm font-semibold">{evaluation.totalScore.toFixed(1)}</p>
                          </div>
                        </div>
                        {evaluation.notes && (
                          <div>
                            <Label>Catatan</Label>
                            <p className="text-sm">{evaluation.notes}</p>
                          </div>
                        )}
                        <p className="text-xs text-gray-500 mt-2">
                          Dievaluasi pada {format(new Date(evaluation.evaluatedAt), "dd MMM yyyy HH:mm", { locale: id })}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedOffer(null)}>
                Tutup
              </Button>
              {selectedOffer.evaluations.length === 0 && (
                <Button onClick={() => handleEvaluateOffer(selectedOffer)}>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Evaluasi Penawaran
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Evaluation Dialog */}
      {showEvaluationDialog && selectedOffer && (
        <Dialog open={showEvaluationDialog} onOpenChange={setShowEvaluationDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Evaluasi Penawaran - {selectedOffer.vendor.companyName}</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="technicalScore">Skor Teknis (0-100) *</Label>
                  <Input
                    id="technicalScore"
                    type="number"
                    min="0"
                    max="100"
                    value={evaluationForm.technicalScore}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEvaluationForm(prev => ({
                      ...prev,
                      technicalScore: Number(e.target.value)
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="commercialScore">Skor Komersial (0-100) *</Label>
                  <Input
                    id="commercialScore"
                    type="number"
                    min="0"
                    max="100"
                    value={evaluationForm.commercialScore}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEvaluationForm(prev => ({
                      ...prev,
                      commercialScore: Number(e.target.value)
                    }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Total Skor (Weighted: 70% Teknis + 30% Komersial)</Label>
                <div className="text-2xl font-bold text-blue-600">
                  {calculateTotalScore(evaluationForm.technicalScore, evaluationForm.commercialScore).toFixed(1)}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Catatan Evaluasi</Label>
                <Textarea
                  id="notes"
                  placeholder="Masukkan catatan evaluasi..."
                  value={evaluationForm.notes}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setEvaluationForm(prev => ({
                    ...prev,
                    notes: e.target.value
                  }))}
                  rows={4}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEvaluationDialog(false)}>
                Batal
              </Button>
              <Button
                onClick={handleSubmitEvaluation}
                disabled={evaluationMutation.isPending}
              >
                {evaluationMutation.isPending ? "Menyimpan..." : "Simpan Evaluasi"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
