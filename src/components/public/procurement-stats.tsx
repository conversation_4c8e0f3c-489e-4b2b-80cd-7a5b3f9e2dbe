"use client";

import { useQuery } from "@tanstack/react-query";
import { ShoppingCart, Users, FileText, TrendingUp } from "lucide-react";

import { Card, CardContent } from "@/components/ui/card";

interface PublicStats {
  totalProcurements: number;
  activeProcurements: number;
  totalVendors: number;
  totalOffers: number;
  totalValue: number;
}

async function fetchPublicStats(): Promise<PublicStats> {
  const response = await fetch("/api/public/stats");
  if (!response.ok) {
    throw new Error("Failed to fetch public stats");
  }
  const result = await response.json();
  return result.data;
}

export function ProcurementStats() {
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ["public-stats"],
    queryFn: fetchPublicStats,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000000) {
      return `Rp ${(amount / 1000000000).toFixed(1)}M`;
    } else if (amount >= 1000000) {
      return `Rp ${(amount / 1000000).toFixed(1)}Jt`;
    } else {
      return new Intl.NumberFormat("id-ID", {
        style: "currency",
        currency: "IDR",
        minimumFractionDigits: 0,
      }).format(amount);
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6 text-center">
              <div className="h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return null; // Don't show error state for stats
  }

  const statItems = [
    {
      icon: ShoppingCart,
      value: stats.activeProcurements,
      label: "Pengadaan Aktif",
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      icon: Users,
      value: stats.totalVendors,
      label: "Vendor Terdaftar",
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      icon: FileText,
      value: stats.totalOffers,
      label: "Total Penawaran",
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      icon: TrendingUp,
      value: formatCurrency(stats.totalValue),
      label: "Total Nilai",
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      isValue: true,
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
      {statItems.map((item, index) => {
        const IconComponent = item.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center">
              <div className={`w-12 h-12 ${item.bgColor} rounded-lg flex items-center justify-center mx-auto mb-4`}>
                <IconComponent className={`h-6 w-6 ${item.color}`} />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {item.isValue ? item.value : item.value.toLocaleString("id-ID")}
              </div>
              <div className="text-sm text-gray-600">
                {item.label}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
