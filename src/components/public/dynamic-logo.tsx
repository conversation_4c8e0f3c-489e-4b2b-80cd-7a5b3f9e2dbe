"use client";

import { useQuery } from "@tanstack/react-query";
import { ShoppingCart } from "lucide-react";

interface LandingAsset {
  id: string;
  filename: string;
  originalName: string;
  url: string;
  title?: string;
  description?: string;
  category: string;
}

interface LandingAssetsResponse {
  banners: LandingAsset[];
  logos: LandingAsset[];
  primaryLogo: LandingAsset | null;
  primaryBanner: LandingAsset | null;
}

async function fetchLandingAssets(): Promise<LandingAssetsResponse> {
  const response = await fetch("/api/public/landing-assets");
  if (!response.ok) {
    throw new Error("Failed to fetch landing assets");
  }
  const result = await response.json();
  return result.data;
}

interface DynamicLogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  showFallback?: boolean;
}

export function DynamicLogo({ 
  className = "", 
  size = "md",
  showFallback = true 
}: DynamicLogoProps) {
  const { data: assets, isLoading, error } = useQuery({
    queryKey: ["landing-assets"],
    queryFn: fetchLandingAssets,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const logo = assets?.primaryLogo;

  // Size configurations
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-10 h-10", 
    lg: "w-16 h-16"
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  };

  // Show fallback if no logo available or loading/error
  if (isLoading || error || !logo) {
    if (!showFallback) return null;
    
    return (
      <div className={`${sizeClasses[size]} bg-blue-600 rounded-lg flex items-center justify-center ${className}`}>
        <ShoppingCart className={`${iconSizes[size]} text-white`} />
      </div>
    );
  }

  return (
    <div className={`${sizeClasses[size]} rounded-lg overflow-hidden ${className}`}>
      <img
        src={logo.url}
        alt={logo.title || logo.originalName || "Logo"}
        className="w-full h-full object-contain"
      />
    </div>
  );
}
