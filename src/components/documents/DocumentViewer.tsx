'use client';

import React, { useState, useEffect } from 'react';
import {
  X,
  Download,
  Share2,
  Edit,
  Trash2,
  ZoomIn,
  ZoomOut,
  RotateCw,
  FileText,
  Image,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  User,
  Calendar,
  Tag,
  Lock
} from 'lucide-react';

interface Document {
  id: string;
  fileName: string;
  fileType: string;
  documentType: string;
  description?: string;
  fileSize: number;
  fileUrl: string;
  uploadedAt: Date;
  uploadedBy: {
    id: string;
    name: string;
    email: string;
  };
  tags: string[];
  status: 'ACTIVE' | 'ARCHIVED' | 'DELETED';
  approvalStatus: 'NOT_REQUIRED' | 'PENDING' | 'APPROVED' | 'REJECTED';
  isConfidential: boolean;
  metadata?: Record<string, any>;
}

interface DocumentViewerProps {
  document: Document | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (document: Document) => void;
  onDelete?: (document: Document) => void;
  onShare?: (document: Document) => void;
  allowEdit?: boolean;
  allowDelete?: boolean;
  allowShare?: boolean;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  document,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onShare,
  allowEdit = true,
  allowDelete = false,
  allowShare = true,
}) => {
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (document) {
      setZoom(100);
      setRotation(0);
      setError(null);
    }
  }, [document]);

  if (!isOpen || !document) {
    return null;
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <FileText className="w-6 h-6 text-red-500" />;
    if (fileType.includes('image')) return <Image className="w-6 h-6 text-blue-500" />;
    if (fileType.includes('spreadsheet') || fileType.includes('excel')) return <FileSpreadsheet className="w-6 h-6 text-green-500" />;
    return <FileText className="w-6 h-6 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getApprovalStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'PENDING':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'REJECTED':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleDownload = () => {
    // Create a temporary link to download the file
    const link = window.document.createElement('a');
    link.href = document.fileUrl;
    link.download = document.fileName;
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const renderDocumentContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-gray-500">
          <AlertCircle className="w-12 h-12 mb-4" />
          <p className="text-lg font-medium">Error loading document</p>
          <p className="text-sm">{error}</p>
        </div>
      );
    }

    // Handle different file types
    if (document.fileType.includes('pdf')) {
      return (
        <div className="h-full">
          <iframe
            src={`${document.fileUrl}#zoom=${zoom}`}
            className="w-full h-full border-0"
            title={document.fileName}
            style={{ transform: `rotate(${rotation}deg)` }}
          />
        </div>
      );
    }

    if (document.fileType.includes('image')) {
      return (
        <div className="flex items-center justify-center h-full p-4">
          <img
            src={document.fileUrl}
            alt={document.fileName}
            className="max-w-full max-h-full object-contain"
            style={{
              transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
              transition: 'transform 0.2s ease-in-out'
            }}
          />
        </div>
      );
    }

    // For other file types, show a preview message
    return (
      <div className="flex flex-col items-center justify-center h-full text-gray-500">
        {getFileIcon(document.fileType)}
        <p className="text-lg font-medium mt-4">Preview not available</p>
        <p className="text-sm mb-4">This file type cannot be previewed in the browser</p>
        <button
          onClick={handleDownload}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Download to view
        </button>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />

      <div className="relative h-full flex">
        {/* Document Content */}
        <div className="flex-1 flex flex-col bg-white">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-3">
              {getFileIcon(document.fileType)}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  {document.fileName}
                  {document.isConfidential && (
                    <Lock className="w-4 h-4 ml-2 text-red-500" />
                  )}
                </h2>
                <p className="text-sm text-gray-500">
                  {document.documentType} • {formatFileSize(document.fileSize)}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Zoom Controls */}
              {(document.fileType.includes('pdf') || document.fileType.includes('image')) && (
                <>
                  <button
                    onClick={handleZoomOut}
                    className="p-2 text-gray-600 hover:text-gray-900"
                    title="Zoom Out"
                  >
                    <ZoomOut className="w-4 h-4" />
                  </button>
                  <span className="text-sm text-gray-600 min-w-[3rem] text-center">
                    {zoom}%
                  </span>
                  <button
                    onClick={handleZoomIn}
                    className="p-2 text-gray-600 hover:text-gray-900"
                    title="Zoom In"
                  >
                    <ZoomIn className="w-4 h-4" />
                  </button>
                  <button
                    onClick={handleRotate}
                    className="p-2 text-gray-600 hover:text-gray-900"
                    title="Rotate"
                  >
                    <RotateCw className="w-4 h-4" />
                  </button>
                  <div className="w-px h-6 bg-gray-300" />
                </>
              )}

              {/* Action Buttons */}
              <button
                onClick={handleDownload}
                className="p-2 text-gray-600 hover:text-gray-900"
                title="Download"
              >
                <Download className="w-4 h-4" />
              </button>

              {allowShare && onShare && (
                <button
                  onClick={() => onShare(document)}
                  className="p-2 text-gray-600 hover:text-gray-900"
                  title="Share"
                >
                  <Share2 className="w-4 h-4" />
                </button>
              )}

              {allowEdit && onEdit && (
                <button
                  onClick={() => onEdit(document)}
                  className="p-2 text-gray-600 hover:text-gray-900"
                  title="Edit"
                >
                  <Edit className="w-4 h-4" />
                </button>
              )}

              {allowDelete && onDelete && (
                <button
                  onClick={() => onDelete(document)}
                  className="p-2 text-gray-600 hover:text-red-600"
                  title="Delete"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              )}

              <button
                onClick={onClose}
                className="p-2 text-gray-600 hover:text-gray-900"
                title="Close"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Document Content */}
          <div className="flex-1 overflow-hidden">
            {renderDocumentContent()}
          </div>
        </div>

        {/* Sidebar */}
        <div className="w-80 bg-gray-50 border-l border-gray-200 overflow-y-auto">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Details</h3>

            {/* Basic Information */}
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Description</label>
                <p className="mt-1 text-sm text-gray-900">
                  {document.description || 'No description provided'}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Status</label>
                <div className="mt-1 flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${document.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                      document.status === 'ARCHIVED' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                    }`}>
                    {document.status}
                  </span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Approval Status</label>
                <div className="mt-1 flex items-center space-x-2">
                  {getApprovalStatusIcon(document.approvalStatus)}
                  <span className="text-sm text-gray-900">
                    {document.approvalStatus.replace('_', ' ')}
                  </span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Uploaded By</label>
                <div className="mt-1 flex items-center space-x-2">
                  <User className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-900">{document.uploadedBy.name}</p>
                    <p className="text-xs text-gray-500">{document.uploadedBy.email}</p>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Upload Date</label>
                <div className="mt-1 flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-900">
                    {document.uploadedAt.toLocaleDateString()} at {document.uploadedAt.toLocaleTimeString()}
                  </span>
                </div>
              </div>

              {document.tags.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Tags</label>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {document.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                      >
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {document.metadata && Object.keys(document.metadata).length > 0 && (
                <div>
                  <label className="text-sm font-medium text-gray-700">Metadata</label>
                  <div className="mt-1 space-y-1">
                    {Object.entries(document.metadata).map(([key, value]) => (
                      <div key={key} className="flex justify-between text-xs">
                        <span className="text-gray-500">{key}:</span>
                        <span className="text-gray-900">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;
