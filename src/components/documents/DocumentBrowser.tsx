'use client';

import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Upload, 
  Download, 
  Eye, 
  Edit, 
  Trash2, 
  FileText, 
  Image, 
  FileSpreadsheet,
  Calendar,
  User,
  Tag,
  MoreVertical
} from 'lucide-react';

interface Document {
  id: string;
  fileName: string;
  fileType: string;
  documentType: string;
  description?: string;
  fileSize: number;
  uploadedAt: Date;
  uploadedBy: {
    id: string;
    name: string;
    email: string;
  };
  tags: string[];
  status: 'ACTIVE' | 'ARCHIVED' | 'DELETED';
  approvalStatus: 'NOT_REQUIRED' | 'PENDING' | 'APPROVED' | 'REJECTED';
  isConfidential: boolean;
}

interface DocumentBrowserProps {
  procurementId?: string;
  stageId?: string;
  allowUpload?: boolean;
  allowEdit?: boolean;
  allowDelete?: boolean;
  onDocumentSelect?: (document: Document) => void;
  onDocumentUpload?: (files: FileList) => void;
}

const DocumentBrowser: React.FC<DocumentBrowserProps> = ({
  procurementId,
  stageId,
  allowUpload = true,
  allowEdit = true,
  allowDelete = false,
  onDocumentSelect,
  onDocumentUpload,
}) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDocumentType, setSelectedDocumentType] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [sortBy, setSortBy] = useState<'fileName' | 'uploadedAt' | 'fileSize'>('uploadedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);

  useEffect(() => {
    loadDocuments();
  }, [procurementId, stageId, searchQuery, selectedDocumentType, selectedStatus, sortBy, sortOrder]);

  const loadDocuments = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/documents', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     procurementId,
      //     stageId,
      //     query: searchQuery,
      //     documentType: selectedDocumentType,
      //     status: selectedStatus,
      //     sortBy,
      //     sortOrder,
      //   }),
      // });
      // const data = await response.json();
      
      // Mock data for now
      const mockDocuments: Document[] = [
        {
          id: '1',
          fileName: 'RFQ_Document.pdf',
          fileType: 'application/pdf',
          documentType: 'RFQ',
          description: 'Request for Quotation document',
          fileSize: 1024000,
          uploadedAt: new Date('2024-01-15'),
          uploadedBy: {
            id: 'user1',
            name: 'John Doe',
            email: '<EMAIL>',
          },
          tags: ['rfq', 'procurement'],
          status: 'ACTIVE',
          approvalStatus: 'APPROVED',
          isConfidential: false,
        },
        {
          id: '2',
          fileName: 'Contract_Template.docx',
          fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          documentType: 'CONTRACT',
          description: 'Standard contract template',
          fileSize: 512000,
          uploadedAt: new Date('2024-01-10'),
          uploadedBy: {
            id: 'user2',
            name: 'Jane Smith',
            email: '<EMAIL>',
          },
          tags: ['contract', 'template'],
          status: 'ACTIVE',
          approvalStatus: 'PENDING',
          isConfidential: true,
        },
      ];
      
      setDocuments(mockDocuments);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <FileText className="w-5 h-5 text-red-500" />;
    if (fileType.includes('image')) return <Image className="w-5 h-5 text-blue-500" />;
    if (fileType.includes('spreadsheet') || fileType.includes('excel')) return <FileSpreadsheet className="w-5 h-5 text-green-500" />;
    return <FileText className="w-5 h-5 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      ACTIVE: 'bg-green-100 text-green-800',
      ARCHIVED: 'bg-yellow-100 text-yellow-800',
      DELETED: 'bg-red-100 text-red-800',
    };
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colors[status as keyof typeof colors]}`}>
        {status}
      </span>
    );
  };

  const getApprovalStatusBadge = (status: string) => {
    const colors = {
      NOT_REQUIRED: 'bg-gray-100 text-gray-800',
      PENDING: 'bg-yellow-100 text-yellow-800',
      APPROVED: 'bg-green-100 text-green-800',
      REJECTED: 'bg-red-100 text-red-800',
    };
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colors[status as keyof typeof colors]}`}>
        {status.replace('_', ' ')}
      </span>
    );
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && onDocumentUpload) {
      onDocumentUpload(files);
    }
  };

  const handleDocumentAction = (action: string, documentId: string) => {
    switch (action) {
      case 'view':
        const document = documents.find(d => d.id === documentId);
        if (document && onDocumentSelect) {
          onDocumentSelect(document);
        }
        break;
      case 'download':
        // TODO: Implement download
        console.log('Download document:', documentId);
        break;
      case 'edit':
        // TODO: Implement edit
        console.log('Edit document:', documentId);
        break;
      case 'delete':
        // TODO: Implement delete
        console.log('Delete document:', documentId);
        break;
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = !searchQuery || 
      doc.fileName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesType = !selectedDocumentType || doc.documentType === selectedDocumentType;
    const matchesStatus = !selectedStatus || doc.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Documents</h2>
          {allowUpload && (
            <label className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 cursor-pointer">
              <Upload className="w-4 h-4 mr-2" />
              Upload Document
              <input
                type="file"
                multiple
                className="hidden"
                onChange={handleFileUpload}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
              />
            </label>
          )}
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search documents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          
          <select
            value={selectedDocumentType}
            onChange={(e) => setSelectedDocumentType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Types</option>
            <option value="RFQ">RFQ</option>
            <option value="CONTRACT">Contract</option>
            <option value="PURCHASE_ORDER">Purchase Order</option>
            <option value="INVOICE">Invoice</option>
            <option value="BAST">BAST</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="ARCHIVED">Archived</option>
          </select>

          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field as typeof sortBy);
              setSortOrder(order as typeof sortOrder);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="uploadedAt-desc">Newest First</option>
            <option value="uploadedAt-asc">Oldest First</option>
            <option value="fileName-asc">Name A-Z</option>
            <option value="fileName-desc">Name Z-A</option>
            <option value="fileSize-desc">Largest First</option>
            <option value="fileSize-asc">Smallest First</option>
          </select>
        </div>
      </div>

      {/* Document List */}
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredDocuments.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No documents</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery || selectedDocumentType || selectedStatus
                ? 'No documents match your search criteria.'
                : 'Get started by uploading a document.'}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredDocuments.map((document) => (
              <div
                key={document.id}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => onDocumentSelect?.(document)}
              >
                <div className="flex items-center space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    {getFileIcon(document.fileType)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {document.fileName}
                      </p>
                      {document.isConfidential && (
                        <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                          Confidential
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-sm text-gray-500">{document.documentType}</span>
                      <span className="text-sm text-gray-500">{formatFileSize(document.fileSize)}</span>
                      <div className="flex items-center text-sm text-gray-500">
                        <User className="w-3 h-3 mr-1" />
                        {document.uploadedBy.name}
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="w-3 h-3 mr-1" />
                        {document.uploadedAt.toLocaleDateString()}
                      </div>
                    </div>
                    
                    {document.tags.length > 0 && (
                      <div className="flex items-center space-x-1 mt-2">
                        <Tag className="w-3 h-3 text-gray-400" />
                        {document.tags.map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {getStatusBadge(document.status)}
                  {getApprovalStatusBadge(document.approvalStatus)}
                  
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDocumentAction('view', document.id);
                      }}
                      className="p-1 text-gray-400 hover:text-gray-600"
                      title="View"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDocumentAction('download', document.id);
                      }}
                      className="p-1 text-gray-400 hover:text-gray-600"
                      title="Download"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                    
                    {allowEdit && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDocumentAction('edit', document.id);
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Edit"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                    )}
                    
                    {allowDelete && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDocumentAction('delete', document.id);
                        }}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentBrowser;
