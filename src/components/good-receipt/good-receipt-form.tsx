"use client";

import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { Plus, Trash2, History, AlertTriangle, CheckCircle } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";

import { 
  goodReceiptSchema,
  type GoodReceiptInput 
} from "@/lib/validations/good-receipt";

interface PurchaseOrder {
  id: string;
  poNumber: string;
  vendor: {
    id: string;
    companyName: string;
  };
  items: Array<{
    id: string;
    item: {
      id: string;
      name: string;
      description?: string;
      unit: string;
    };
    quantity: number;
    price: number;
    receivedQuantity?: number;
  }>;
}

interface ReceiptLog {
  id?: string;
  quantityChange: number;
  notes?: string;
  logDate?: string;
  loggedBy?: {
    id: string;
    name: string;
  };
}

async function fetchPurchaseOrders(): Promise<PurchaseOrder[]> {
  const response = await fetch("/api/purchase-orders?status=APPROVED&needsGR=true");
  if (!response.ok) throw new Error("Failed to fetch purchase orders");
  const result = await response.json();
  return result.data.purchaseOrders;
}

async function fetchPurchaseOrder(id: string): Promise<PurchaseOrder> {
  const response = await fetch(`/api/purchase-orders/${id}`);
  if (!response.ok) throw new Error("Failed to fetch purchase order");
  const result = await response.json();
  return result.data;
}

async function createGoodReceipt(data: GoodReceiptInput) {
  const response = await fetch("/api/good-receipts", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to create good receipt");
  }
  
  return response.json();
}

interface GoodReceiptFormProps {
  initialPoId?: string;
  editMode?: boolean;
  goodReceiptId?: string;
  onSuccess?: () => void;
}

export function GoodReceiptForm({ initialPoId, editMode, goodReceiptId, onSuccess }: GoodReceiptFormProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [selectedPoId, setSelectedPoId] = useState<string>(initialPoId || "");
  const [logDialogOpen, setLogDialogOpen] = useState(false);
  const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null);
  const [logQuantity, setLogQuantity] = useState<number>(0);
  const [logNotes, setLogNotes] = useState<string>("");
  const [receiptLogs, setReceiptLogs] = useState<Record<string, ReceiptLog[]>>({});

  const { data: purchaseOrders, isLoading: posLoading } = useQuery({
    queryKey: ["purchase-orders-for-gr"],
    queryFn: fetchPurchaseOrders,
    enabled: !initialPoId && !editMode,
  });

  const { data: selectedPo, isLoading: poLoading } = useQuery({
    queryKey: ["purchase-order", selectedPoId],
    queryFn: () => fetchPurchaseOrder(selectedPoId),
    enabled: !!selectedPoId,
  });

  const form = useForm<GoodReceiptInput>({
    resolver: zodResolver(goodReceiptSchema),
    defaultValues: {
      poId: initialPoId || "",
      receivedDate: new Date().toISOString().split('T')[0],
      items: [],
    },
  });

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Initialize form with PO items when PO is selected
  React.useEffect(() => {
    if (selectedPo && selectedPoId) {
      form.setValue("poId", selectedPoId);
      
      // Clear existing items
      while (fields.length > 0) {
        remove(0);
      }
      
      // Add PO items
      selectedPo.items.forEach(item => {
        append({
          purchaseOrderItemId: item.id,
          receivedQuantity: 0,
          notes: "",
        });
        
        // Initialize receipt logs for this item
        setReceiptLogs(prev => ({
          ...prev,
          [item.id]: []
        }));
      });
    }
  }, [selectedPo, selectedPoId, append, remove, fields.length, form]);

  const createMutation = useMutation({
    mutationFn: createGoodReceipt,
    onSuccess: (data) => {
      toast.success("Good receipt created successfully");
      queryClient.invalidateQueries({ queryKey: ["good-receipts"] });
      queryClient.invalidateQueries({ queryKey: ["purchase-order", selectedPoId] });
      if (onSuccess) {
        onSuccess();
      } else {
        router.push(`/good-receipts/${data.data.id}`);
      }
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const onSubmit = (data: GoodReceiptInput) => {
    // Add receipt logs to the items
    const itemsWithLogs = data.items.map((item, index) => {
      const poItem = selectedPo?.items.find(i => i.id === item.purchaseOrderItemId);
      if (poItem && receiptLogs[poItem.id]) {
        return {
          ...item,
          receiptLogs: receiptLogs[poItem.id]
        };
      }
      return item;
    });

    createMutation.mutate({
      ...data,
      items: itemsWithLogs as any
    });
  };

  const handleAddLog = () => {
    if (selectedItemIndex === null) return;
    
    const item = fields[selectedItemIndex];
    const poItem = selectedPo?.items.find(i => i.id === item.purchaseOrderItemId);
    
    if (!poItem) return;
    
    // Add log to the receipt logs
    const newLog: ReceiptLog = {
      quantityChange: logQuantity,
      notes: logNotes,
    };
    
    setReceiptLogs(prev => ({
      ...prev,
      [poItem.id]: [...(prev[poItem.id] || []), newLog]
    }));
    
    // Update the total received quantity
    const currentQuantity = form.watch(`items.${selectedItemIndex}.receivedQuantity`) || 0;
    form.setValue(`items.${selectedItemIndex}.receivedQuantity`, currentQuantity + logQuantity);
    
    // Reset form
    setLogQuantity(0);
    setLogNotes("");
    setLogDialogOpen(false);
  };

  const getTotalReceivedQuantity = (poItemId: string) => {
    return (receiptLogs[poItemId] || []).reduce((sum, log) => sum + log.quantityChange, 0);
  };

  if (posLoading || (selectedPoId && poLoading)) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{editMode ? "Edit Good Receipt" : "Create New Good Receipt"}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Purchase Order Selection */}
            {!initialPoId && !editMode && (
              <div className="space-y-2">
                <Label htmlFor="poId">Purchase Order *</Label>
                <Select
                  value={selectedPoId}
                  onValueChange={(value: string) => setSelectedPoId(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select purchase order" />
                  </SelectTrigger>
                  <SelectContent>
                    {purchaseOrders?.map((po) => (
                      <SelectItem key={po.id} value={po.id}>
                        {po.poNumber} - {po.vendor.companyName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.poId && (
                  <p className="text-sm text-red-500">{form.formState.errors.poId.message}</p>
                )}
              </div>
            )}

            {/* Received Date */}
            <div className="space-y-2">
              <Label htmlFor="receivedDate">Received Date *</Label>
              <Input
                type="date"
                id="receivedDate"
                {...form.register("receivedDate")}
              />
              {form.formState.errors.receivedDate && (
                <p className="text-sm text-red-500">{form.formState.errors.receivedDate.message}</p>
              )}
            </div>

            {/* PO Details */}
            {selectedPo && (
              <div className="border rounded-lg p-4 bg-gray-50">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">PO Number:</p>
                    <p className="font-medium">{selectedPo.poNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Vendor:</p>
                    <p className="font-medium">{selectedPo.vendor.companyName}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Items Section */}
            {selectedPo && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-lg font-semibold">Items *</Label>
                </div>

                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item</TableHead>
                        <TableHead>Ordered Qty</TableHead>
                        <TableHead>Received Qty</TableHead>
                        <TableHead>Unit</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {fields.map((field, index) => {
                        const poItem = selectedPo.items.find(item => item.id === field.purchaseOrderItemId);
                        if (!poItem) return null;
                        
                        const receivedQty = form.watch(`items.${index}.receivedQuantity`) || 0;
                        const isComplete = receivedQty > 0;
                        const hasDiscrepancy = receivedQty !== poItem.quantity;
                        
                        return (
                          <TableRow key={field.id}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{poItem.item.name}</p>
                                <p className="text-sm text-gray-600">{poItem.item.description}</p>
                                <input
                                  type="hidden"
                                  {...form.register(`items.${index}.purchaseOrderItemId`)}
                                  value={poItem.id}
                                />
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="font-medium">{poItem.quantity}</span>
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                step="0.01"
                                {...form.register(`items.${index}.receivedQuantity`, { valueAsNumber: true })}
                                className="w-24"
                                readOnly
                              />
                            </TableCell>
                            <TableCell>
                              <span>{poItem.item.unit}</span>
                            </TableCell>
                            <TableCell>
                              {isComplete ? (
                                hasDiscrepancy ? (
                                  <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                                    {React.createElement(AlertTriangle as any, { className: "h-3 w-3 mr-1" })}
                                    Discrepancy
                                  </Badge>
                                ) : (
                                  <Badge variant="outline" className="bg-green-100 text-green-800">
                                    {React.createElement(CheckCircle as any, { className: "h-3 w-3 mr-1" })}
                                    Complete
                                  </Badge>
                                )
                              ) : (
                                <Badge variant="outline" className="bg-gray-100">
                                  Not Received
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <Dialog open={logDialogOpen && selectedItemIndex === index} onOpenChange={(open: boolean) => {
                                  setLogDialogOpen(open);
                                  if (open) setSelectedItemIndex(index);
                                  else setSelectedItemIndex(null);
                                }}>
                                  <DialogTrigger asChild>
                                    <Button variant="outline" size="sm">
                                      {React.createElement(Plus as any, { className: "h-4 w-4 mr-1" })}
                                      Add Log
                                    </Button>
                                  </DialogTrigger>
                                  <DialogContent>
                                    <DialogHeader>
                                      <DialogTitle>Add Receipt Log</DialogTitle>
                                    </DialogHeader>
                                    <div className="space-y-4 py-4">
                                      <div>
                                        <Label htmlFor="quantity">Quantity Change *</Label>
                                        <Input
                                          type="number"
                                          step="0.01"
                                          id="quantity"
                                          value={logQuantity}
                                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setLogQuantity(Number(e.target.value))}
                                          min="0"
                                        />
                                      </div>
                                      <div>
                                        <Label htmlFor="notes">Notes</Label>
                                        <Textarea
                                          id="notes"
                                          value={logNotes}
                                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setLogNotes(e.target.value)}
                                          placeholder="Add notes about this receipt log"
                                        />
                                      </div>
                                      <div className="flex justify-end gap-2">
                                        <Button
                                          type="button"
                                          variant="outline"
                                          onClick={() => setLogDialogOpen(false)}
                                        >
                                          Cancel
                                        </Button>
                                        <Button
                                          type="button"
                                          onClick={handleAddLog}
                                          disabled={logQuantity <= 0}
                                        >
                                          Add Log
                                        </Button>
                                      </div>
                                    </div>
                                  </DialogContent>
                                </Dialog>

                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    // Show receipt logs for this item
                                  }}
                                >
                                  {React.createElement(History as any, { className: "h-4 w-4 mr-1" })}
                                  Logs ({receiptLogs[poItem.id]?.length || 0})
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end gap-4 pt-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={createMutation.isPending || !selectedPoId || fields.length === 0}
                  >
                    {createMutation.isPending ? "Saving..." : "Save Good Receipt"}
                  </Button>
                </div>
              </div>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
