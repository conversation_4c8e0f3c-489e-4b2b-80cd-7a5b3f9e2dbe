'use client';

import React, { useState, useCallback, useRef } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  Connection,
  EdgeChange,
  NodeChange,
  ReactFlowProvider,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Save,
  Play,
  Settings,
  Trash2,
  Copy,
  Download,
  Upload,
  Undo,
  Redo
} from 'lucide-react';

import { WorkflowStageNode } from './nodes/WorkflowStageNode';

const nodeTypes = {
  workflowStage: WorkflowStageNode,
};

interface WorkflowBuilderProps {
  workflowId?: string;
  initialWorkflow?: any;
  onSave?: (workflow: any) => void;
  onTest?: (workflow: any) => void;
  readOnly?: boolean;
}

const initialNodes: Node[] = [
  {
    id: 'start',
    type: 'workflowStage',
    position: { x: 100, y: 100 },
    data: {
      label: 'Start',
      type: 'START',
      stage: 'ANNOUNCEMENT',
      description: 'Procurement process begins',
      isRequired: true,
      config: {},
    },
  },
];

const initialEdges: Edge[] = [];

export function WorkflowBuilder({
  workflowId,
  initialWorkflow,
  onSave,
  onTest,
  readOnly = false,
}: WorkflowBuilderProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [isPropertiesPanelOpen, setIsPropertiesPanelOpen] = useState(false);
  const [workflowName, setWorkflowName] = useState(initialWorkflow?.name || 'New Workflow');
  const [workflowDescription, setWorkflowDescription] = useState(initialWorkflow?.description || '');
  const [isDirty, setIsDirty] = useState(false);
  const [history, setHistory] = useState<{ nodes: Node[]; edges: Edge[] }[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const reactFlowInstance = useRef<any>(null);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge = {
        ...params,
        id: `edge-${Date.now()}`,
        type: 'smoothstep',
        animated: true,
        data: {
          condition: null,
          label: '',
        },
      };
      setEdges((eds) => addEdge(newEdge, eds));
      setIsDirty(true);
      saveToHistory();
    },
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setSelectedEdge(null);
    setIsPropertiesPanelOpen(true);
  }, []);

  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
    setIsPropertiesPanelOpen(true);
  }, []);

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
    setSelectedEdge(null);
    setIsPropertiesPanelOpen(false);
  }, []);

  const saveToHistory = useCallback(() => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push({ nodes: [...nodes], edges: [...edges] });
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [nodes, edges, history, historyIndex]);

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const prevState = history[historyIndex - 1];
      setNodes(prevState.nodes);
      setEdges(prevState.edges);
      setHistoryIndex(historyIndex - 1);
      setIsDirty(true);
    }
  }, [history, historyIndex, setNodes, setEdges]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const nextState = history[historyIndex + 1];
      setNodes(nextState.nodes);
      setEdges(nextState.edges);
      setHistoryIndex(historyIndex + 1);
      setIsDirty(true);
    }
  }, [history, historyIndex, setNodes, setEdges]);

  const addNode = useCallback((type: string, position?: { x: number; y: number }) => {
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type,
      position: position || { x: Math.random() * 400 + 100, y: Math.random() * 400 + 100 },
      data: {
        label: `New ${type}`,
        type: type.toUpperCase(),
        description: '',
        isRequired: true,
        config: {},
      },
    };

    setNodes((nds) => [...nds, newNode]);
    setIsDirty(true);
    saveToHistory();
  }, [setNodes, saveToHistory]);

  const deleteNode = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
    setSelectedNode(null);
    setIsDirty(true);
    saveToHistory();
  }, [setNodes, setEdges, saveToHistory]);

  const duplicateNode = useCallback((node: Node) => {
    const newNode: Node = {
      ...node,
      id: `node-${Date.now()}`,
      position: {
        x: node.position.x + 50,
        y: node.position.y + 50,
      },
      data: {
        ...node.data,
        label: `${node.data.label} (Copy)`,
      },
    };

    setNodes((nds) => [...nds, newNode]);
    setIsDirty(true);
    saveToHistory();
  }, [setNodes, saveToHistory]);

  const updateNodeData = useCallback((nodeId: string, newData: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...newData } }
          : node
      )
    );
    setIsDirty(true);
  }, [setNodes]);

  const updateEdgeData = useCallback((edgeId: string, newData: any) => {
    setEdges((eds) =>
      eds.map((edge) =>
        edge.id === edgeId
          ? { ...edge, data: { ...edge.data, ...newData } }
          : edge
      )
    );
    setIsDirty(true);
  }, [setEdges]);

  const handleSave = useCallback(async () => {
    const workflow = {
      id: workflowId,
      name: workflowName,
      description: workflowDescription,
      nodes,
      edges,
      config: {
        layout: 'horizontal',
        autoSave: true,
      },
    };

    try {
      if (onSave) {
        await onSave(workflow);
        setIsDirty(false);
      }
    } catch (error) {
      console.error('Failed to save workflow:', error);
    }
  }, [workflowId, workflowName, workflowDescription, nodes, edges, onSave]);

  const handleTest = useCallback(async () => {
    const workflow = {
      id: workflowId,
      name: workflowName,
      description: workflowDescription,
      nodes,
      edges,
    };

    try {
      if (onTest) {
        await onTest(workflow);
      }
    } catch (error) {
      console.error('Failed to test workflow:', error);
    }
  }, [workflowId, workflowName, workflowDescription, nodes, edges, onTest]);

  const exportWorkflow = useCallback(() => {
    const workflow = {
      name: workflowName,
      description: workflowDescription,
      nodes,
      edges,
      version: '1.0',
      exportedAt: new Date().toISOString(),
    };

    const dataStr = JSON.stringify(workflow, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

    const exportFileDefaultName = `${workflowName.replace(/\s+/g, '_')}_workflow.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }, [workflowName, workflowDescription, nodes, edges]);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold">{workflowName}</h1>
            {isDirty && <Badge variant="secondary">Unsaved Changes</Badge>}
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={undo}
              disabled={historyIndex <= 0}
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={redo}
              disabled={historyIndex >= history.length - 1}
            >
              <Redo className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={exportWorkflow}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={handleTest}>
              <Play className="h-4 w-4 mr-2" />
              Test
            </Button>
            <Button onClick={handleSave} disabled={!isDirty || readOnly}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Workflow Canvas */}
        <div className="flex-1 relative" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onEdgeClick={onEdgeClick}
            onPaneClick={onPaneClick}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
            onInit={(instance) => {
              reactFlowInstance.current = instance;
            }}
          >
            <Background />
            <Controls />
            <MiniMap />

            {/* Toolbar */}
            <Panel position="top-left">
              <div className="bg-white p-2 rounded shadow">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addNode('workflowStage')}
                  disabled={readOnly}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Stage
                </Button>
              </div>
            </Panel>
          </ReactFlow>
        </div>

        {/* Properties Panel */}
        {isPropertiesPanelOpen && (
          <div className="w-80 border-l bg-white p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Properties</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPropertiesPanelOpen(false)}
              >
                ×
              </Button>
            </div>
            {selectedNode && (
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Label</label>
                  <input
                    type="text"
                    value={selectedNode.data.label}
                    onChange={(e) => updateNodeData(selectedNode.id, { label: e.target.value })}
                    className="w-full mt-1 px-3 py-2 border rounded"
                    disabled={readOnly}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <textarea
                    value={selectedNode.data.description || ''}
                    onChange={(e) => updateNodeData(selectedNode.id, { description: e.target.value })}
                    className="w-full mt-1 px-3 py-2 border rounded"
                    rows={3}
                    disabled={readOnly}
                  />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
