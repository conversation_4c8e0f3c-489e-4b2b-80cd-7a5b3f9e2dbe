'use client';

import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  Users,
  Settings
} from 'lucide-react';

interface WorkflowStageNodeData {
  label: string;
  type: 'START' | 'ANNOUNCEMENT' | 'SUBMISSION' | 'EVALUATION' | 'AWARD' | 'CONTRACT' | 'END';
  stage: string;
  description?: string;
  isRequired: boolean;
  duration?: number;
  config: {
    minDuration?: number;
    maxDuration?: number;
    requiresApproval?: boolean;
    requiredDocuments?: string[];
    notifications?: {
      onStart?: boolean;
      onComplete?: boolean;
    };
  };
  status?: 'pending' | 'active' | 'completed' | 'error';
}

const stageIcons = {
  START: CheckCircle,
  ANNOUNCEMENT: FileText,
  SUBMISSION: Users,
  EVALUATION: Settings,
  AWARD: CheckCircle,
  CONTRACT: FileText,
  END: CheckCircle,
};

const stageColors = {
  START: 'bg-green-100 border-green-300 text-green-800',
  ANNOUNCEMENT: 'bg-blue-100 border-blue-300 text-blue-800',
  SUBMISSION: 'bg-yellow-100 border-yellow-300 text-yellow-800',
  EVALUATION: 'bg-purple-100 border-purple-300 text-purple-800',
  AWARD: 'bg-orange-100 border-orange-300 text-orange-800',
  CONTRACT: 'bg-indigo-100 border-indigo-300 text-indigo-800',
  END: 'bg-gray-100 border-gray-300 text-gray-800',
};

export const WorkflowStageNode = memo<NodeProps<WorkflowStageNodeData>>(({ data, selected }) => {
  const Icon = stageIcons[data.type] || Settings;
  const colorClass = stageColors[data.type] || 'bg-gray-100 border-gray-300 text-gray-800';

  return (
    <div className="workflow-stage-node">
      {/* Input Handle */}
      {data.type !== 'START' && (
        <Handle
          type="target"
          position={Position.Left}
          className="w-3 h-3 !bg-blue-500 border-2 border-white"
        />
      )}

      {/* Node Content */}
      <Card className={`min-w-[200px] transition-all duration-200 ${
        selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md'
      } ${colorClass}`}>
        <CardContent className="p-4">
          {/* Header */}
          <div className="flex items-center space-x-2 mb-2">
            <Icon className="h-5 w-5" />
            <span className="font-semibold text-sm">{data.label}</span>
            {data.isRequired && (
              <Badge variant="secondary" className="text-xs">Required</Badge>
            )}
          </div>

          {/* Description */}
          {data.description && (
            <p className="text-xs text-gray-600 mb-2 line-clamp-2">
              {data.description}
            </p>
          )}

          {/* Stage Info */}
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Stage:</span>
              <span className="font-medium">{data.stage}</span>
            </div>

            {data.duration && (
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500 flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  Duration:
                </span>
                <span className="font-medium">{data.duration}h</span>
              </div>
            )}

            {data.config.requiresApproval && (
              <div className="flex items-center text-xs text-orange-600">
                <AlertCircle className="h-3 w-3 mr-1" />
                <span>Requires Approval</span>
              </div>
            )}

            {data.config.requiredDocuments && data.config.requiredDocuments.length > 0 && (
              <div className="flex items-center text-xs text-blue-600">
                <FileText className="h-3 w-3 mr-1" />
                <span>{data.config.requiredDocuments.length} Documents</span>
              </div>
            )}
          </div>

          {/* Status Indicator */}
          {data.status && (
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className={`flex items-center text-xs ${
                data.status === 'completed' ? 'text-green-600' :
                data.status === 'active' ? 'text-blue-600' :
                data.status === 'error' ? 'text-red-600' :
                'text-gray-500'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  data.status === 'completed' ? 'bg-green-500' :
                  data.status === 'active' ? 'bg-blue-500' :
                  data.status === 'error' ? 'bg-red-500' :
                  'bg-gray-400'
                }`} />
                <span className="capitalize">{data.status}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output Handle */}
      {data.type !== 'END' && (
        <Handle
          type="source"
          position={Position.Right}
          className="w-3 h-3 !bg-blue-500 border-2 border-white"
        />
      )}
    </div>
  );
});

WorkflowStageNode.displayName = 'WorkflowStageNode';
