'use client';

import React, { memo } from 'react';
import { <PERSON>le, Position, NodeProps } from 'reactflow';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  UserCheck, 
  Users, 
  Clock, 
  AlertTriangle,
  Shield,
  CheckCircle2
} from 'lucide-react';

interface ApprovalNodeData {
  label: string;
  type: 'APPROVAL';
  description?: string;
  approverType: 'SPECIFIC_USER' | 'ROLE_BASED' | 'DEPARTMENT' | 'HIERARCHY' | 'COMMITTEE';
  approvers: Array<{
    id: string;
    name: string;
    type: 'user' | 'role' | 'department';
  }>;
  requiredCount: number;
  allowDelegation: boolean;
  timeoutHours?: number;
  escalation?: {
    enabled: boolean;
    escalateToIds: string[];
    escalateAfterHours: number;
  };
  status?: 'pending' | 'active' | 'approved' | 'rejected' | 'escalated';
}

const approverTypeLabels = {
  SPECIFIC_USER: 'Specific Users',
  ROLE_BASED: 'Role Based',
  DEPARTMENT: 'Department',
  HIERARCHY: 'Hierarchy',
  COMMITTEE: 'Committee',
};

const approverTypeIcons = {
  SPECIFIC_USER: UserCheck,
  ROLE_BASED: Shield,
  DEPARTMENT: Users,
  HIERARCHY: Users,
  COMMITTEE: Users,
};

export const ApprovalNode = memo<NodeProps<ApprovalNodeData>>(({ data, selected }) => {
  const Icon = approverTypeIcons[data.approverType] || UserCheck;

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'rejected':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'escalated':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'active':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="approval-node">
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-orange-500 border-2 border-white"
      />

      {/* Node Content */}
      <Card className={`min-w-[220px] transition-all duration-200 ${
        selected ? 'ring-2 ring-orange-500 shadow-lg' : 'shadow-md'
      } ${getStatusColor(data.status)}`}>
        <CardContent className="p-4">
          {/* Header */}
          <div className="flex items-center space-x-2 mb-2">
            <Icon className="h-5 w-5" />
            <span className="font-semibold text-sm">{data.label}</span>
            <Badge variant="outline" className="text-xs">
              Approval
            </Badge>
          </div>

          {/* Description */}
          {data.description && (
            <p className="text-xs text-gray-600 mb-3 line-clamp-2">
              {data.description}
            </p>
          )}

          {/* Approval Configuration */}
          <div className="space-y-2">
            {/* Approver Type */}
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Type:</span>
              <span className="font-medium">
                {approverTypeLabels[data.approverType]}
              </span>
            </div>

            {/* Required Count */}
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Required:</span>
              <span className="font-medium">
                {data.requiredCount} of {data.approvers.length}
              </span>
            </div>

            {/* Approvers List */}
            {data.approvers.length > 0 && (
              <div className="text-xs">
                <span className="text-gray-500 block mb-1">Approvers:</span>
                <div className="space-y-1">
                  {data.approvers.slice(0, 3).map((approver, index) => (
                    <div key={index} className="flex items-center space-x-1">
                      <div className={`w-2 h-2 rounded-full ${
                        approver.type === 'user' ? 'bg-blue-400' :
                        approver.type === 'role' ? 'bg-purple-400' :
                        'bg-green-400'
                      }`} />
                      <span className="truncate">{approver.name}</span>
                    </div>
                  ))}
                  {data.approvers.length > 3 && (
                    <div className="text-gray-400">
                      +{data.approvers.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Timeout */}
            {data.timeoutHours && (
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500 flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  Timeout:
                </span>
                <span className="font-medium">{data.timeoutHours}h</span>
              </div>
            )}

            {/* Delegation */}
            {data.allowDelegation && (
              <div className="flex items-center text-xs text-blue-600">
                <UserCheck className="h-3 w-3 mr-1" />
                <span>Delegation Allowed</span>
              </div>
            )}

            {/* Escalation */}
            {data.escalation?.enabled && (
              <div className="flex items-center text-xs text-orange-600">
                <AlertTriangle className="h-3 w-3 mr-1" />
                <span>Auto-escalation: {data.escalation.escalateAfterHours}h</span>
              </div>
            )}
          </div>

          {/* Status Indicator */}
          {data.status && (
            <div className="mt-3 pt-2 border-t border-gray-200">
              <div className={`flex items-center text-xs ${
                data.status === 'approved' ? 'text-green-600' :
                data.status === 'rejected' ? 'text-red-600' :
                data.status === 'escalated' ? 'text-orange-600' :
                data.status === 'active' ? 'text-blue-600' :
                'text-gray-500'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  data.status === 'approved' ? 'bg-green-500' :
                  data.status === 'rejected' ? 'bg-red-500' :
                  data.status === 'escalated' ? 'bg-orange-500' :
                  data.status === 'active' ? 'bg-blue-500' :
                  'bg-gray-400'
                }`} />
                <span className="capitalize">{data.status}</span>
                {data.status === 'approved' && (
                  <CheckCircle2 className="h-3 w-3 ml-1" />
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output Handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="approved"
        className="w-3 h-3 !bg-green-500 border-2 border-white"
        style={{ top: '30%' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="rejected"
        className="w-3 h-3 !bg-red-500 border-2 border-white"
        style={{ top: '70%' }}
      />
    </div>
  );
});

ApprovalNode.displayName = 'ApprovalNode';
