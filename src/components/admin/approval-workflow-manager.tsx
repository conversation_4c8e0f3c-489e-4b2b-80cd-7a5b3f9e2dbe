'use client';

import React, { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Play,
  Pause,
  Settings,
  CheckCircle,

  Users,
  Clock,
  BarChart3,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';

// Types
interface ApprovalWorkflow {
  id: string;
  name: string;
  description?: string;
  entityType: string;
  stage: string;
  isActive: boolean;
  isDefault: boolean;
  version: number;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  steps: ApprovalStep[];
  usageStats?: {
    activeInstances: number;
    totalInstances: number;
    completedInstances: number;
  };
}

interface ApprovalStep {
  id: string;
  name: string;
  description?: string;
  sequence: number;
  stepType: string;
  isRequired: boolean;
  approverType: string;
  requiredCount: number;
  allowDelegation: boolean;
  timeoutHours?: number;
}

// API Functions
async function fetchApprovalWorkflows(filters: {
  entityType?: string;
  stage?: string;
  isActive?: boolean;
  search?: string;
}): Promise<{ workflows: ApprovalWorkflow[]; totalCount: number }> {
  try {
    const token = localStorage.getItem("auth-token");
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const params = new URLSearchParams();
    if (filters.entityType) params.append('entityType', filters.entityType);
    if (filters.stage) params.append('stage', filters.stage);
    if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
    if (filters.search) params.append('search', filters.search);

    const response = await fetch(`/api/admin/approval-workflows?${params}`, { headers });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: Failed to fetch approval workflows`);
    }

    const result = await response.json();
    return result.data || { workflows: [], totalCount: 0 };
  } catch (error) {
    console.error('Failed to fetch approval workflows:', error);
    throw error;
  }
}

async function toggleWorkflowStatus(workflowId: string, isActive: boolean): Promise<ApprovalWorkflow> {
  try {
    const token = localStorage.getItem("auth-token");
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(`/api/admin/approval-workflows/${workflowId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify({ isActive }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: Failed to update workflow status`);
    }

    const result = await response.json();
    return result.data?.workflow || result.workflow || result;
  } catch (error) {
    console.error('Failed to update workflow status:', error);
    throw error;
  }
}

async function deleteApprovalWorkflow(workflowId: string): Promise<void> {
  try {
    const token = localStorage.getItem("auth-token");
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(`/api/admin/approval-workflows/${workflowId}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: Failed to delete workflow`);
    }
  } catch (error) {
    console.error('Failed to delete workflow:', error);
    throw error;
  }
}

// Entity Types and Stages
const ENTITY_TYPES = [
  { value: 'procurement', label: 'Procurement' },
  { value: 'vendor', label: 'Vendor' },
  { value: 'contract', label: 'Contract' },
  { value: 'payment', label: 'Payment' },
  { value: 'purchase_order', label: 'Purchase Order' },
  { value: 'invoice', label: 'Invoice' },
];

const PROCUREMENT_STAGES = [
  { value: 'vendor_registration', label: 'Vendor Registration' },
  { value: 'rfq_creation', label: 'RFQ Creation' },
  { value: 'rfq_publication', label: 'RFQ Publication' },
  { value: 'offer_submission', label: 'Offer Submission' },
  { value: 'offer_evaluation', label: 'Offer Evaluation' },
  { value: 'contract_award', label: 'Contract Award' },
  { value: 'po_creation', label: 'PO Creation' },
  { value: 'po_approval', label: 'PO Approval' },
  { value: 'delivery_confirmation', label: 'Delivery Confirmation' },
  { value: 'invoice_processing', label: 'Invoice Processing' },
  { value: 'payment_approval', label: 'Payment Approval' },
  { value: 'contract_completion', label: 'Contract Completion' },
];

export function ApprovalWorkflowManager() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<ApprovalWorkflow | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [entityTypeFilter, setEntityTypeFilter] = useState<string>('all');
  const [stageFilter, setStageFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const queryClient = useQueryClient();

  const { data: workflowsData, isLoading, error } = useQuery({
    queryKey: ['approval-workflows', { entityTypeFilter, stageFilter, statusFilter, searchTerm }],
    queryFn: () => fetchApprovalWorkflows({
      entityType: entityTypeFilter !== 'all' ? entityTypeFilter : undefined,
      stage: stageFilter !== 'all' ? stageFilter : undefined,
      isActive: statusFilter !== 'all' ? statusFilter === 'active' : undefined,
      search: searchTerm || undefined,
    }),
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (replaces cacheTime in newer versions)
  });

  const toggleStatusMutation = useMutation({
    mutationFn: ({ workflowId, isActive }: { workflowId: string; isActive: boolean }) =>
      toggleWorkflowStatus(workflowId, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['approval-workflows'] });
      toast.success('Workflow status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update workflow status');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteApprovalWorkflow,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['approval-workflows'] });
      toast.success('Workflow deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete workflow');
    },
  });

  const workflows: ApprovalWorkflow[] = workflowsData?.workflows || [];

  const handleToggleStatus = useCallback((workflowId: string, currentStatus: boolean) => {
    toggleStatusMutation.mutate({ workflowId, isActive: !currentStatus });
  }, [toggleStatusMutation]);

  const handleDelete = useCallback((workflowId: string, workflowName: string) => {
    if (confirm(`Are you sure you want to delete the workflow "${workflowName}"? This action cannot be undone.`)) {
      deleteMutation.mutate(workflowId);
    }
  }, [deleteMutation]);

  const handleView = useCallback((workflow: ApprovalWorkflow) => {
    setSelectedWorkflow(workflow);
    setIsViewDialogOpen(true);
  }, []);

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
        <CheckCircle className="h-3 w-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
        <Pause className="h-3 w-3 mr-1" />
        Inactive
      </Badge>
    );
  };

  const getEntityTypeBadge = (entityType: string) => {
    const colors: Record<string, string> = {
      procurement: 'bg-blue-100 text-blue-800 border-blue-200',
      vendor: 'bg-purple-100 text-purple-800 border-purple-200',
      contract: 'bg-green-100 text-green-800 border-green-200',
      payment: 'bg-orange-100 text-orange-800 border-orange-200',
      purchase_order: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      invoice: 'bg-pink-100 text-pink-800 border-pink-200',
    };

    return (
      <Badge variant="outline" className={colors[entityType] || 'bg-gray-100 text-gray-800 border-gray-200'}>
        {entityType.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Approval Workflow Management</h1>
          <p className="text-gray-600 mt-2">
            Configure and manage approval workflows for different business processes
          </p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Workflow
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Approval Workflow</DialogTitle>
            </DialogHeader>
            <div className="text-center py-8 text-gray-500">
              Workflow creation form will be implemented here
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search workflows..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label>Entity Type</Label>
              <Select value={entityTypeFilter} onValueChange={setEntityTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All entity types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All entity types</SelectItem>
                  {ENTITY_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Stage</Label>
              <Select value={stageFilter} onValueChange={setStageFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All stages" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All stages</SelectItem>
                  {PROCUREMENT_STAGES.map((stage) => (
                    <SelectItem key={stage.value} value={stage.value}>
                      {stage.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Workflows Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Approval Workflows
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading workflows...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-4">
                <Settings className="h-12 w-12 mx-auto mb-2" />
                <p className="font-medium">Failed to load workflows</p>
                <p className="text-sm text-gray-600 mt-1">
                  {error instanceof Error ? error.message : 'An unexpected error occurred'}
                </p>
              </div>
              <Button
                variant="outline"
                onClick={() => queryClient.invalidateQueries({ queryKey: ['approval-workflows'] })}
              >
                Try Again
              </Button>
            </div>
          ) : workflows.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Settings className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="font-medium">No approval workflows found</p>
              <p className="text-sm mt-1">Create your first workflow to get started!</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Entity Type</TableHead>
                  <TableHead>Stage</TableHead>
                  <TableHead>Steps</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {workflows.map((workflow: ApprovalWorkflow) => (
                  <TableRow key={workflow.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{workflow.name}</div>
                        {workflow.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {workflow.description}
                          </div>
                        )}
                        {workflow.isDefault && (
                          <Badge variant="outline" className="mt-1 bg-yellow-100 text-yellow-800 border-yellow-200">
                            Default
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getEntityTypeBadge(workflow.entityType)}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {PROCUREMENT_STAGES.find(s => s.value === workflow.stage)?.label || workflow.stage}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{workflow.steps?.length || 0} steps</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {workflow.usageStats ? (
                        <div className="text-sm">
                          <div className="flex items-center gap-1">
                            <BarChart3 className="h-3 w-3 text-blue-500" />
                            <span>{workflow.usageStats.totalInstances} total</span>
                          </div>
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="h-3 w-3" />
                            <span>{workflow.usageStats.completedInstances} completed</span>
                          </div>
                          {workflow.usageStats.activeInstances > 0 && (
                            <div className="flex items-center gap-1 text-orange-600">
                              <Clock className="h-3 w-3" />
                              <span>{workflow.usageStats.activeInstances} active</span>
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">No usage data</span>
                      )}
                    </TableCell>
                    <TableCell>{getStatusBadge(workflow.isActive)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleView(workflow)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {/* Edit functionality will be implemented */ }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(workflow.id, workflow.isActive)}
                          disabled={toggleStatusMutation.isPending}
                        >
                          {workflow.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(workflow.id, workflow.name)}
                          disabled={deleteMutation.isPending || (workflow.usageStats?.activeInstances || 0) > 0}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Workflow Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Workflow Details: {selectedWorkflow?.name}</DialogTitle>
          </DialogHeader>
          {selectedWorkflow && (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Name</Label>
                      <div className="font-medium">{selectedWorkflow.name}</div>
                    </div>
                    <div>
                      <Label>Entity Type</Label>
                      <div>{getEntityTypeBadge(selectedWorkflow.entityType)}</div>
                    </div>
                    <div>
                      <Label>Stage</Label>
                      <div>{PROCUREMENT_STAGES.find(s => s.value === selectedWorkflow.stage)?.label || selectedWorkflow.stage}</div>
                    </div>
                    <div>
                      <Label>Status</Label>
                      <div>{getStatusBadge(selectedWorkflow.isActive)}</div>
                    </div>
                  </div>
                  {selectedWorkflow.description && (
                    <div>
                      <Label>Description</Label>
                      <div className="text-gray-700">{selectedWorkflow.description}</div>
                    </div>
                  )}
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
                    <div>
                      <Label>Created By</Label>
                      <div>{selectedWorkflow.createdBy.name} ({selectedWorkflow.createdBy.email})</div>
                    </div>
                    <div>
                      <Label>Version</Label>
                      <div>v{selectedWorkflow.version}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Workflow Steps */}
              <Card>
                <CardHeader>
                  <CardTitle>Workflow Steps</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedWorkflow.steps?.map((step) => (
                      <div key={step.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium">
                              {step.sequence}
                            </div>
                            <div>
                              <div className="font-medium">{step.name}</div>
                              {step.description && (
                                <div className="text-sm text-gray-500">{step.description}</div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {step.isRequired && (
                              <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
                                Required
                              </Badge>
                            )}
                            {step.allowDelegation && (
                              <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                                Delegatable
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <Label>Approver Type</Label>
                            <div className="capitalize">{step.approverType.replace('_', ' ')}</div>
                          </div>
                          <div>
                            <Label>Required Count</Label>
                            <div>{step.requiredCount}</div>
                          </div>
                          {step.timeoutHours && (
                            <div>
                              <Label>Timeout</Label>
                              <div>{step.timeoutHours} hours</div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Usage Statistics */}
              {selectedWorkflow.usageStats && (
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {selectedWorkflow.usageStats.totalInstances}
                        </div>
                        <div className="text-sm text-gray-600">Total Instances</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {selectedWorkflow.usageStats.completedInstances}
                        </div>
                        <div className="text-sm text-gray-600">Completed</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {selectedWorkflow.usageStats.activeInstances}
                        </div>
                        <div className="text-sm text-gray-600">Active</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
