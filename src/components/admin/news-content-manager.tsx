"use client";

import { useState, useCallback, memo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Edit, Trash2, Eye, Calendar, User, Tag, Image, X } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AssetPicker } from "@/components/ui/asset-picker";
import { toast } from "sonner";

interface NewsArticle {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featuredImage?: string;
  status: "DRAFT" | "PENDING_APPROVAL" | "APPROVED" | "PUBLISHED" | "ARCHIVED";
  publishedAt?: string;
  scheduledAt?: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  category: {
    id: string;
    name: string;
    slug: string;
  };
}

interface NewsCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  isActive: boolean;
}

interface ArticleFormData {
  title: string;
  excerpt: string;
  content: string;
  categoryId: string;
  featuredImage?: string;
  status: "DRAFT" | "PENDING_APPROVAL" | "PUBLISHED";
  scheduledAt?: string;
}

async function fetchArticles(): Promise<NewsArticle[]> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/admin/news", { headers });
  if (!response.ok) throw new Error("Failed to fetch articles");
  const data = await response.json();
  return data.articles || [];
}

async function fetchCategories(): Promise<NewsCategory[]> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/admin/news/categories", { headers });
  if (!response.ok) throw new Error("Failed to fetch categories");
  return response.json();
}

async function createArticle(data: ArticleFormData): Promise<NewsArticle> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch("/api/admin/news", {
    method: "POST",
    headers,
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error("Failed to create article");
  return response.json();
}

async function updateArticle(id: string, data: Partial<ArticleFormData>): Promise<NewsArticle> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/news/${id}`, {
    method: "PUT",
    headers,
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error("Failed to update article");
  return response.json();
}

async function deleteArticle(id: string): Promise<void> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {};

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/news/${id}`, {
    method: "DELETE",
    headers,
  });
  if (!response.ok) throw new Error("Failed to delete article");
}

function getStatusBadge(status: string) {
  const statusConfig = {
    DRAFT: { label: "Draft", variant: "secondary" as const },
    PENDING_APPROVAL: { label: "Pending", variant: "outline" as const },
    APPROVED: { label: "Approved", variant: "default" as const },
    PUBLISHED: { label: "Published", variant: "default" as const },
    ARCHIVED: { label: "Archived", variant: "secondary" as const },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT;
  return <Badge variant={config.variant}>{config.label}</Badge>;
}

// Separate ArticleForm component to prevent recreation
interface ArticleFormProps {
  isEdit?: boolean;
  formData: ArticleFormData;
  categories: NewsCategory[];
  onTitleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onExcerptChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onContentChange: (data: string) => void;
  onCategoryChange: (value: string) => void;
  onStatusChange: (value: "DRAFT" | "PENDING_APPROVAL" | "PUBLISHED") => void;
  onScheduledAtChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFeaturedImageChange: (imageUrl: string) => void;
  onFeaturedImageRemove: () => void;
  onFocus: () => void;
  onBlur: () => void;
  onCancel: () => void;
  onSubmit: () => void;
}

const ArticleForm = memo(({
  isEdit = false,
  formData,
  categories,
  onTitleChange,
  onExcerptChange,
  onContentChange,
  onCategoryChange,
  onStatusChange,
  onScheduledAtChange,
  onFeaturedImageChange,
  onFeaturedImageRemove,
  onFocus,
  onBlur,
  onCancel,
  onSubmit,
}: ArticleFormProps) => {
  console.log("🔍 DEBUG ARTICLE FORM: ArticleForm render", { isEdit, title: formData.title });

  const [isAssetPickerOpen, setIsAssetPickerOpen] = useState(false);

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="title">Judul Artikel *</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={onTitleChange}
          placeholder="Masukkan judul artikel"
        />
      </div>

      <div>
        <Label htmlFor="excerpt">Ringkasan</Label>
        <Textarea
          id="excerpt"
          value={formData.excerpt}
          onChange={onExcerptChange}
          placeholder="Ringkasan singkat artikel"
          rows={3}
        />
      </div>

      {/* Featured Image */}
      <div>
        <Label>Gambar Unggulan</Label>
        {formData.featuredImage ? (
          <div className="space-y-2">
            <div className="relative inline-block">
              <img
                src={formData.featuredImage}
                alt="Featured"
                className="w-32 h-32 object-cover rounded border"
              />
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="absolute -top-2 -right-2"
                onClick={onFeaturedImageRemove}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsAssetPickerOpen(true)}
            >
              <Image className="h-4 w-4 mr-2" />
              Ganti Gambar
            </Button>
          </div>
        ) : (
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsAssetPickerOpen(true)}
          >
            <Image className="h-4 w-4 mr-2" />
            Pilih Gambar Unggulan
          </Button>
        )}

        <AssetPicker
          isOpen={isAssetPickerOpen}
          onClose={() => setIsAssetPickerOpen(false)}
          onSelect={(imageUrl) => {
            onFeaturedImageChange(imageUrl);
            setIsAssetPickerOpen(false);
          }}
          allowedCategories={["news", "banners"]}
          title="Pilih Gambar Unggulan"
          description="Pilih gambar yang akan ditampilkan sebagai gambar unggulan artikel"
        />
      </div>

      <div>
        <Label htmlFor="content">Konten *</Label>
        <RichTextEditor
          value={formData.content}
          onChange={onContentChange}
          placeholder="Tulis artikel berita yang menarik di sini. Anda dapat menambahkan gambar, video, tabel, dan format teks yang kaya untuk membuat artikel yang lebih engaging."
          className="min-h-96"
          onFocus={onFocus}
          onBlur={onBlur}
        />
        <div className="mt-2 text-sm text-gray-600">
          <div className="flex items-center gap-4 flex-wrap">
            <div>✨ Editor konten lengkap dengan:</div>
            <div>📷 Upload gambar multiple</div>
            <div>📄 Upload dokumen PDF</div>
            <div>📊 Tabel dan formatting</div>
            <div>🎨 Text styling lengkap</div>
          </div>
        </div>
      </div>

      <div>
        <Label htmlFor="category">Kategori *</Label>
        <Select
          value={formData.categoryId}
          onValueChange={onCategoryChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Pilih kategori" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="status">Status *</Label>
        <Select
          value={formData.status}
          onValueChange={onStatusChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Pilih status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="DRAFT">Draft</SelectItem>
            <SelectItem value="PENDING_APPROVAL">Pending Approval</SelectItem>
            <SelectItem value="PUBLISHED">Published</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="scheduledAt">Jadwal Publikasi (Opsional)</Label>
        <Input
          id="scheduledAt"
          type="datetime-local"
          value={formData.scheduledAt}
          onChange={onScheduledAtChange}
        />
      </div>

      <div className="flex justify-end gap-2">
        <Button
          variant="outline"
          onClick={onCancel}
        >
          Batal
        </Button>
        <Button
          onClick={onSubmit}
          disabled={!formData.title || !formData.content || !formData.categoryId}
        >
          {isEdit ? "Perbarui" : "Buat"} Artikel
        </Button>
      </div>
    </div>
  );
});

ArticleForm.displayName = 'ArticleForm';

export function NewsContentManager() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingArticle, setEditingArticle] = useState<NewsArticle | null>(null);
  const [formData, setFormData] = useState<ArticleFormData>({
    title: "",
    excerpt: "",
    content: "",
    categoryId: "",
    status: "DRAFT",
  });

  const queryClient = useQueryClient();

  const { data: articles = [], isLoading: articlesLoading } = useQuery({
    queryKey: ["admin-articles"],
    queryFn: fetchArticles,
  });

  const { data: categories = [] } = useQuery({
    queryKey: ["admin-categories"],
    queryFn: fetchCategories,
  });

  const createMutation = useMutation({
    mutationFn: createArticle,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-articles"] });
      setIsCreateDialogOpen(false);
      setFormData({
        title: "",
        excerpt: "",
        content: "",
        categoryId: "",
        status: "DRAFT",
      });
      toast.success("Artikel berhasil dibuat");
    },
    onError: () => {
      toast.error("Gagal membuat artikel");
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ArticleFormData> }) =>
      updateArticle(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-articles"] });
      setIsEditDialogOpen(false);
      setEditingArticle(null);
      toast.success("Artikel berhasil diperbarui");
    },
    onError: () => {
      toast.error("Gagal memperbarui artikel");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteArticle,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-articles"] });
      toast.success("Artikel berhasil dihapus");
    },
    onError: () => {
      toast.error("Gagal menghapus artikel");
    },
  });

  const handleCreate = () => {
    createMutation.mutate(formData);
  };

  const handleEdit = (article: NewsArticle) => {
    setEditingArticle(article);
    // Map article status to form status (only allow editable statuses)
    const editableStatus = ["DRAFT", "PENDING_APPROVAL", "PUBLISHED"].includes(article.status)
      ? article.status as "DRAFT" | "PENDING_APPROVAL" | "PUBLISHED"
      : "DRAFT";

    setFormData({
      title: article.title,
      excerpt: article.excerpt || "",
      content: article.content,
      categoryId: article.category.id,
      status: editableStatus,
      scheduledAt: article.scheduledAt || "",
      featuredImage: article.featuredImage,
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdate = () => {
    if (!editingArticle) return;
    updateMutation.mutate({ id: editingArticle.id, data: formData });
  };

  const handleDelete = (id: string) => {
    if (confirm("Apakah Anda yakin ingin menghapus artikel ini?")) {
      deleteMutation.mutate(id);
    }
  };

  // Memoized callbacks to prevent recreating the editor
  const handleContentChange = useCallback((data: string) => {
    console.log("🔍 DEBUG NEWS RICH EDITOR: Content updated in news manager");
    setFormData(prev => ({ ...prev, content: data }));
  }, []);

  const handleTitleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, title: e.target.value }));
  }, []);

  const handleExcerptChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, excerpt: e.target.value }));
  }, []);

  const handleCategoryChange = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, categoryId: value }));
  }, []);

  const handleStatusChange = useCallback((value: "DRAFT" | "PENDING_APPROVAL" | "PUBLISHED") => {
    setFormData(prev => ({ ...prev, status: value }));
  }, []);

  const handleScheduledAtChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, scheduledAt: e.target.value }));
  }, []);

  const handleFeaturedImageChange = useCallback((imageUrl: string) => {
    setFormData(prev => ({ ...prev, featuredImage: imageUrl }));
  }, []);

  const handleFeaturedImageRemove = useCallback(() => {
    setFormData(prev => ({ ...prev, featuredImage: undefined }));
  }, []);

  const handleFocus = useCallback(() => console.log("🔍 DEBUG NEWS RICH EDITOR: News Editor focused"), []);
  const handleBlur = useCallback(() => console.log("🔍 DEBUG NEWS RICH EDITOR: News Editor blurred"), []);

  // Memoized cancel handlers
  const handleCreateCancel = useCallback(() => {
    setIsCreateDialogOpen(false);
  }, []);

  const handleEditCancel = useCallback(() => {
    setIsEditDialogOpen(false);
    setEditingArticle(null);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Manajemen Artikel Berita</h2>
          <p className="text-gray-600">Kelola artikel berita dan pengumuman</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Buat Artikel
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-5xl max-h-[100vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Buat Artikel Baru</DialogTitle>
            </DialogHeader>
            <ArticleForm
              formData={formData}
              categories={categories}
              onTitleChange={handleTitleChange}
              onExcerptChange={handleExcerptChange}
              onContentChange={handleContentChange}
              onCategoryChange={handleCategoryChange}
              onStatusChange={handleStatusChange}
              onScheduledAtChange={handleScheduledAtChange}
              onFeaturedImageChange={handleFeaturedImageChange}
              onFeaturedImageRemove={handleFeaturedImageRemove}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onCancel={handleCreateCancel}
              onSubmit={handleCreate}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Daftar Artikel
          </CardTitle>
        </CardHeader>
        <CardContent>
          {articlesLoading ? (
            <div className="text-center py-8">Loading...</div>
          ) : articles.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Belum ada artikel. Buat artikel pertama Anda!
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Judul</TableHead>
                  <TableHead>Kategori</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Penulis</TableHead>
                  <TableHead>Tanggal</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {articles.map((article) => (
                  <TableRow key={article.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{article.title}</div>
                        {article.excerpt && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {article.excerpt}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{article.category.name}</Badge>
                    </TableCell>
                    <TableCell>{getStatusBadge(article.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="text-sm">{article.author.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="text-sm">
                          {format(new Date(article.createdAt), "dd MMM yyyy", { locale: id })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(`/news/${article.slug}`, "_blank")}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(article)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(article.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Artikel</DialogTitle>
          </DialogHeader>
          <ArticleForm
            isEdit
            formData={formData}
            categories={categories}
            onTitleChange={handleTitleChange}
            onExcerptChange={handleExcerptChange}
            onContentChange={handleContentChange}
            onCategoryChange={handleCategoryChange}
            onStatusChange={handleStatusChange}
            onScheduledAtChange={handleScheduledAtChange}
            onFeaturedImageChange={handleFeaturedImageChange}
            onFeaturedImageRemove={handleFeaturedImageRemove}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onCancel={handleEditCancel}
            onSubmit={handleUpdate}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}