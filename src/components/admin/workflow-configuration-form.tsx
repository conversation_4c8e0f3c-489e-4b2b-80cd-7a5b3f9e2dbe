"use client";

import { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Trash2, MoveUp, MoveDown } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { <PERSON>, CardContent, Card<PERSON>eader, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

const approverAssignmentSchema = z.object({
  type: z.enum(["SPECIFIC_USER", "ROLE_BASED", "DEPARTMENT", "HIERARCHY", "DYNAMIC", "COMMITTEE", "VALUE_THRESHOLD"]),
  config: z.object({
    userIds: z.array(z.string()).optional(),
    roleNames: z.array(z.string()).optional(),
    departmentIds: z.array(z.string()).optional(),
    hierarchyLevel: z.number().optional(),
    dynamicRule: z.string().optional(),
    committeeType: z.string().optional(),
    valueThresholds: z.array(z.object({
      minValue: z.number(),
      maxValue: z.number().optional(),
      approverIds: z.array(z.string()),
    })).optional(),
  }),
});

const stepConfigSchema = z.object({
  name: z.string().min(1, "Step name is required"),
  description: z.string().optional(),
  sequence: z.number().min(1),
  stepType: z.enum(["APPROVAL", "REVIEW", "NOTIFICATION", "CONDITIONAL", "PARALLEL", "SEQUENTIAL", "ESCALATION", "SIGNATURE"]),
  isRequired: z.boolean(),
  approverAssignment: approverAssignmentSchema,
  requiredCount: z.number().min(1),
  allowDelegation: z.boolean(),
  timeoutHours: z.number().optional(),
  escalationConfig: z.object({
    escalateToIds: z.array(z.string()),
    escalationMessage: z.string(),
  }).optional(),
  signatureConfig: z.object({
    position: z.object({
      x: z.number(),
      y: z.number(),
    }),
    size: z.object({
      width: z.number(),
      height: z.number(),
    }),
    page: z.number().optional(),
    required: z.boolean(),
  }).optional(),
});

const workflowConfigSchema = z.object({
  name: z.string().min(1, "Workflow name is required"),
  description: z.string().optional(),
  entityType: z.string().min(1, "Entity type is required"),
  stage: z.string().min(1, "Stage is required"),
  steps: z.array(stepConfigSchema).min(1, "At least one step is required"),
  isDefault: z.boolean(),
});

type WorkflowConfigForm = z.infer<typeof workflowConfigSchema>;

interface WorkflowConfigurationFormProps {
  workflow?: any;
  onSuccess: () => void;
  onCancel: () => void;
}

const PROCUREMENT_STAGES = [
  { value: "vendor_registration", label: "Vendor Registration" },
  { value: "rfq_creation", label: "RFQ Creation" },
  { value: "rfq_publication", label: "RFQ Publication" },
  { value: "offer_submission", label: "Offer Submission" },
  { value: "offer_evaluation", label: "Offer Evaluation" },
  { value: "contract_award", label: "Contract Award" },
  { value: "po_creation", label: "PO Creation" },
  { value: "po_approval", label: "PO Approval" },
  { value: "delivery_confirmation", label: "Delivery Confirmation" },
  { value: "invoice_processing", label: "Invoice Processing" },
  { value: "payment_approval", label: "Payment Approval" },
  { value: "contract_completion", label: "Contract Completion" },
];

const ENTITY_TYPES = [
  { value: "procurement", label: "Procurement" },
  { value: "vendor", label: "Vendor" },
  { value: "contract", label: "Contract" },
  { value: "purchase_order", label: "Purchase Order" },
  { value: "invoice", label: "Invoice" },
  { value: "payment", label: "Payment" },
];

const STEP_TYPES = [
  { value: "APPROVAL", label: "Approval" },
  { value: "REVIEW", label: "Review" },
  { value: "NOTIFICATION", label: "Notification" },
  { value: "CONDITIONAL", label: "Conditional" },
  { value: "PARALLEL", label: "Parallel" },
  { value: "SEQUENTIAL", label: "Sequential" },
  { value: "ESCALATION", label: "Escalation" },
  { value: "SIGNATURE", label: "Signature" },
];

const APPROVER_TYPES = [
  { value: "SPECIFIC_USER", label: "Specific Users" },
  { value: "ROLE_BASED", label: "Role-Based" },
  { value: "DEPARTMENT", label: "Department" },
  { value: "HIERARCHY", label: "Hierarchy" },
  { value: "DYNAMIC", label: "Dynamic Rule" },
  { value: "COMMITTEE", label: "Committee" },
  { value: "VALUE_THRESHOLD", label: "Value Threshold" },
];

async function createWorkflow(data: WorkflowConfigForm) {
  const response = await fetch("/api/admin/approval-workflows", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Failed to create workflow");
  }

  return response.json();
}

async function updateWorkflow(workflowId: string, data: WorkflowConfigForm) {
  const response = await fetch(`/api/admin/approval-workflows/${workflowId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Failed to update workflow");
  }

  return response.json();
}

export function WorkflowConfigurationForm({
  workflow,
  onSuccess,
  onCancel,
}: WorkflowConfigurationFormProps) {
  const queryClient = useQueryClient();
  const isEditing = !!workflow;

  const form = useForm<WorkflowConfigForm>({
    resolver: zodResolver(workflowConfigSchema),
    defaultValues: {
      name: workflow?.name || "",
      description: workflow?.description || "",
      entityType: workflow?.entityType || "",
      stage: workflow?.stage || "",
      isDefault: workflow?.isDefault || false,
      steps: workflow?.steps?.map((step: any, index: number) => ({
        name: step.name,
        description: step.description || "",
        sequence: index + 1,
        stepType: step.stepType,
        isRequired: step.isRequired,
        approverAssignment: {
          type: step.approverType,
          config: step.approverConfig || {},
        },
        requiredCount: step.requiredCount,
        allowDelegation: step.allowDelegation,
        timeoutHours: step.timeoutHours,
        escalationConfig: step.config?.escalation,
        signatureConfig: step.signatureConfig,
      })) || [
        {
          name: "Initial Approval",
          description: "",
          sequence: 1,
          stepType: "APPROVAL" as const,
          isRequired: true,
          approverAssignment: {
            type: "ROLE_BASED" as const,
            config: {
              roleNames: ["APPROVER"],
            },
          },
          requiredCount: 1,
          allowDelegation: false,
        },
      ],
    },
  });

  const { fields, append, remove, move } = useFieldArray({
    control: form.control,
    name: "steps",
  });

  const createMutation = useMutation({
    mutationFn: createWorkflow,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["approval-workflows"] });
      toast.success("Workflow created successfully");
      onSuccess();
    },
    onError: (error: Error) => {
      toast.error(`Failed to create workflow: ${error.message}`);
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: WorkflowConfigForm) => updateWorkflow(workflow.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["approval-workflows"] });
      toast.success("Workflow updated successfully");
      onSuccess();
    },
    onError: (error: Error) => {
      toast.error(`Failed to update workflow: ${error.message}`);
    },
  });

  const onSubmit = (data: WorkflowConfigForm) => {
    // Update sequence numbers
    const stepsWithSequence = data.steps.map((step, index) => ({
      ...step,
      sequence: index + 1,
    }));

    const finalData = {
      ...data,
      steps: stepsWithSequence,
    };

    if (isEditing) {
      updateMutation.mutate(finalData);
    } else {
      createMutation.mutate(finalData);
    }
  };

  const addStep = () => {
    append({
      name: `Step ${fields.length + 1}`,
      description: "",
      sequence: fields.length + 1,
      stepType: "APPROVAL",
      isRequired: true,
      approverAssignment: {
        type: "ROLE_BASED",
        config: {
          roleNames: ["APPROVER"],
        },
      },
      requiredCount: 1,
      allowDelegation: false,
    });
  };

  const moveStep = (index: number, direction: "up" | "down") => {
    const newIndex = direction === "up" ? index - 1 : index + 1;
    if (newIndex >= 0 && newIndex < fields.length) {
      move(index, newIndex);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }: { field: any }) => (
                  <FormItem>
                    <FormLabel>Workflow Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter workflow name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="entityType"
                render={({ field }: { field: any }) => (
                  <FormItem>
                    <FormLabel>Entity Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select entity type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {ENTITY_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="stage"
                render={({ field }: { field: any }) => (
                  <FormItem>
                    <FormLabel>Procurement Stage</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select stage" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {PROCUREMENT_STAGES.map((stage) => (
                          <SelectItem key={stage.value} value={stage.value}>
                            {stage.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isDefault"
                render={({ field }: { field: any }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Default Workflow</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Use this as the default workflow for this stage
                      </p>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }: { field: any }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter workflow description"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Approval Steps */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Approval Steps</CardTitle>
              <Button type="button" onClick={addStep} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Step
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {fields.map((field, index) => (
                <Card key={field.id} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">
                          Step {index + 1}
                        </span>
                        <FormField
                          control={form.control}
                          name={`steps.${index}.name`}
                          render={({ field }: { field: any }) => (
                            <Input
                              {...field}
                              className="font-medium border-none p-0 h-auto"
                              placeholder="Step name"
                            />
                          )}
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => moveStep(index, "up")}
                          disabled={index === 0}
                        >
                          <MoveUp className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => moveStep(index, "down")}
                          disabled={index === fields.length - 1}
                        >
                          <MoveDown className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => remove(index)}
                          disabled={fields.length === 1}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name={`steps.${index}.stepType`}
                        render={({ field }: { field: any }) => (
                          <FormItem>
                            <FormLabel>Step Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {STEP_TYPES.map((type) => (
                                  <SelectItem key={type.value} value={type.value}>
                                    {type.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`steps.${index}.approverAssignment.type`}
                        render={({ field }: { field: any }) => (
                          <FormItem>
                            <FormLabel>Approver Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {APPROVER_TYPES.map((type) => (
                                  <SelectItem key={type.value} value={type.value}>
                                    {type.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`steps.${index}.requiredCount`}
                        render={({ field }: { field: any }) => (
                          <FormItem>
                            <FormLabel>Required Approvals</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                {...field}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => field.onChange(parseInt(e.target.value))}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`steps.${index}.timeoutHours`}
                        render={({ field }: { field: any }) => (
                          <FormItem>
                            <FormLabel>Timeout (Hours)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                placeholder="Optional"
                                {...field}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <div className="flex items-center space-x-4">
                        <FormField
                          control={form.control}
                          name={`steps.${index}.isRequired`}
                          render={({ field }: { field: any }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <FormLabel>Required</FormLabel>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`steps.${index}.allowDelegation`}
                          render={({ field }: { field: any }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <FormLabel>Allow Delegation</FormLabel>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name={`steps.${index}.description`}
                      render={({ field }: { field: any }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Step description"
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {createMutation.isPending || updateMutation.isPending
              ? "Saving..."
              : isEditing
              ? "Update Workflow"
              : "Create Workflow"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
