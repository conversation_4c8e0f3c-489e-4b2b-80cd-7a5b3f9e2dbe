'use client';

import React, { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Copy,
  Settings,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Clock,
  BarChart3,
  Star,
  Users,
  Calendar,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';

// Types
interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  type: string;
  category: string;
  isDefault: boolean;
  isActive: boolean;
  config: {
    stages: Array<{
      name: string;
      type: string;
      sequence: number;
      duration?: number;
      requirements?: string[];
    }>;
    rules?: {
      minValue?: number;
      maxValue?: number;
      requiresCommittee?: boolean;
      autoApproval?: boolean;
    };
    conditions?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
  };
  createdAt: string;
  updatedAt: string;
  creator: {
    id: string;
    name: string;
    email: string;
  };
  stages?: any[];
  vendorRequirements?: any[];
  scheduleTemplates?: any[];
  procurements?: any[];
  _count?: {
    procurements: number;
    stages: number;
    vendorRequirements: number;
    scheduleTemplates: number;
  };
}

// API Functions
async function fetchWorkflowTemplates(filters: {
  type?: string;
  category?: string;
  isActive?: boolean;
  search?: string;
}): Promise<{ templates: WorkflowTemplate[]; totalCount: number }> {
  try {
    const token = localStorage.getItem("auth-token");
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const params = new URLSearchParams();
    if (filters.type) params.append('type', filters.type);
    if (filters.category) params.append('category', filters.category);
    if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
    if (filters.search) params.append('search', filters.search);

    const response = await fetch(`/api/admin/procurement-workflow-templates?${params}`, { headers });
    if (!response.ok) throw new Error('Failed to fetch workflow templates');

    const result = await response.json();
    return result.data || { templates: result || [], totalCount: (result || []).length };
  } catch (error) {
    console.error('Failed to fetch workflow templates:', error);
    throw error;
  }
}

async function toggleTemplateStatus(templateId: string, isActive: boolean): Promise<WorkflowTemplate> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/procurement-workflow-templates/${templateId}`, {
    method: 'PUT',
    headers,
    body: JSON.stringify({ isActive }),
  });

  if (!response.ok) throw new Error('Failed to update template status');

  return response.json();
}

async function cloneWorkflowTemplate(templateId: string, newName: string): Promise<WorkflowTemplate> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  // First get the original template
  const getResponse = await fetch(`/api/admin/procurement-workflow-templates/${templateId}`, { headers });
  if (!getResponse.ok) throw new Error('Failed to fetch template for cloning');

  const originalTemplate = await getResponse.json();

  // Create a new template based on the original
  const cloneData = {
    name: newName,
    description: `Cloned from ${originalTemplate.name}`,
    type: originalTemplate.type,
    category: originalTemplate.category,
    isDefault: false, // Clones are never default
    config: originalTemplate.config,
  };

  const response = await fetch('/api/admin/procurement-workflow-templates', {
    method: 'POST',
    headers,
    body: JSON.stringify(cloneData),
  });

  if (!response.ok) throw new Error('Failed to clone template');

  return response.json();
}

async function deleteWorkflowTemplate(templateId: string): Promise<void> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/admin/procurement-workflow-templates/${templateId}`, {
    method: 'DELETE',
    headers,
  });

  if (!response.ok) throw new Error('Failed to delete template');
}

// Template Types and Categories
const TEMPLATE_TYPES = [
  { value: 'TENDER', label: 'Tender' },
  { value: 'RFQ', label: 'Request for Quotation' },
  { value: 'DIRECT_PURCHASE', label: 'Direct Purchase' },
  { value: 'FRAMEWORK', label: 'Framework Agreement' },
];

const TEMPLATE_CATEGORIES = [
  { value: 'GOODS', label: 'Goods' },
  { value: 'SERVICES', label: 'Services' },
  { value: 'CONSTRUCTION', label: 'Construction' },
];

export function WorkflowTemplateManager() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [cloneTemplateName, setCloneTemplateName] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const queryClient = useQueryClient();

  const { data: templatesData, isLoading, error, isError } = useQuery({
    queryKey: ['workflow-templates', { typeFilter, categoryFilter, statusFilter, searchTerm }],
    queryFn: () => fetchWorkflowTemplates({
      type: typeFilter !== 'all' ? typeFilter : undefined,
      category: categoryFilter !== 'all' ? categoryFilter : undefined,
      isActive: statusFilter !== 'all' ? statusFilter === 'active' : undefined,
      search: searchTerm || undefined,
    }),
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const toggleStatusMutation = useMutation({
    mutationFn: ({ templateId, isActive }: { templateId: string; isActive: boolean }) =>
      toggleTemplateStatus(templateId, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflow-templates'] });
      toast.success('Template status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update template status');
    },
  });

  const cloneMutation = useMutation({
    mutationFn: ({ templateId, newName }: { templateId: string; newName: string }) =>
      cloneWorkflowTemplate(templateId, newName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflow-templates'] });
      toast.success('Template cloned successfully');
      setIsCloneDialogOpen(false);
      setCloneTemplateName('');
      setSelectedTemplate(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to clone template');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteWorkflowTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflow-templates'] });
      toast.success('Template deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete template');
    },
  });

  const templates: WorkflowTemplate[] = templatesData?.templates || [];

  const handleToggleStatus = useCallback((templateId: string, currentStatus: boolean) => {
    toggleStatusMutation.mutate({ templateId, isActive: !currentStatus });
  }, [toggleStatusMutation]);

  const handleClone = useCallback((template: WorkflowTemplate) => {
    setSelectedTemplate(template);
    setCloneTemplateName(`${template.name} (Copy)`);
    setIsCloneDialogOpen(true);
  }, []);

  const handleConfirmClone = useCallback(() => {
    if (selectedTemplate && cloneTemplateName.trim()) {
      cloneMutation.mutate({ templateId: selectedTemplate.id, newName: cloneTemplateName.trim() });
    }
  }, [selectedTemplate, cloneTemplateName, cloneMutation]);

  const handleDelete = useCallback((templateId: string, templateName: string) => {
    if (confirm(`Are you sure you want to delete the template "${templateName}"? This action cannot be undone.`)) {
      deleteMutation.mutate(templateId);
    }
  }, [deleteMutation]);

  const handleView = useCallback((template: WorkflowTemplate) => {
    setSelectedTemplate(template);
    setIsViewDialogOpen(true);
  }, []);

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
        <CheckCircle className="h-3 w-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
        <XCircle className="h-3 w-3 mr-1" />
        Inactive
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      TENDER: 'bg-blue-100 text-blue-800 border-blue-200',
      RFQ: 'bg-purple-100 text-purple-800 border-purple-200',
      DIRECT_PURCHASE: 'bg-green-100 text-green-800 border-green-200',
      FRAMEWORK: 'bg-orange-100 text-orange-800 border-orange-200',
    };

    return (
      <Badge variant="outline" className={colors[type] || 'bg-gray-100 text-gray-800 border-gray-200'}>
        {TEMPLATE_TYPES.find(t => t.value === type)?.label || type}
      </Badge>
    );
  };

  const getCategoryBadge = (category: string) => {
    const colors: Record<string, string> = {
      GOODS: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      SERVICES: 'bg-pink-100 text-pink-800 border-pink-200',
      CONSTRUCTION: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    };

    return (
      <Badge variant="outline" className={colors[category] || 'bg-gray-100 text-gray-800 border-gray-200'}>
        {TEMPLATE_CATEGORIES.find(c => c.value === category)?.label || category}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Workflow Template Management</h1>
          <p className="text-gray-600 mt-2">
            Create and manage procurement workflow templates for different business processes
          </p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Workflow Template</DialogTitle>
            </DialogHeader>
            <div className="text-center py-8 text-gray-500">
              Template creation form will be implemented here
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label>Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All types</SelectItem>
                  {TEMPLATE_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All categories</SelectItem>
                  {TEMPLATE_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Workflow Templates
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading templates...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-4">
                <FileText className="h-12 w-12 mx-auto mb-2" />
                <p className="font-medium">Failed to load templates</p>
                <p className="text-sm text-gray-600 mt-1">
                  {error instanceof Error ? error.message : 'An unexpected error occurred'}
                </p>
              </div>
              <Button
                variant="outline"
                onClick={() => queryClient.invalidateQueries({ queryKey: ['workflow-templates'] })}
              >
                Try Again
              </Button>
            </div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="font-medium">No workflow templates found</p>
              <p className="text-sm mt-1">Create your first template to get started!</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Stages</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell>
                      <div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">{template.name}</div>
                          {template.isDefault && (
                            <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                              <Star className="h-3 w-3 mr-1" />
                              Default
                            </Badge>
                          )}
                        </div>
                        {template.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {template.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getTypeBadge(template.type)}</TableCell>
                    <TableCell>{getCategoryBadge(template.category)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Settings className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{template.config?.stages?.length || 0} stages</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {template._count ? (
                        <div className="text-sm">
                          <div className="flex items-center gap-1">
                            <BarChart3 className="h-3 w-3 text-blue-500" />
                            <span>{template._count.procurements} procurements</span>
                          </div>
                          <div className="flex items-center gap-1 text-green-600">
                            <Users className="h-3 w-3" />
                            <span>{template._count.vendorRequirements} requirements</span>
                          </div>
                          <div className="flex items-center gap-1 text-purple-600">
                            <Calendar className="h-3 w-3" />
                            <span>{template._count.scheduleTemplates} schedules</span>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">No usage data</span>
                      )}
                    </TableCell>
                    <TableCell>{getStatusBadge(template.isActive)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleView(template)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {/* Edit functionality will be implemented */ }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleClone(template)}
                          disabled={cloneMutation.isPending}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(template.id, template.isActive)}
                          disabled={toggleStatusMutation.isPending}
                        >
                          {template.isActive ? <XCircle className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(template.id, template.name)}
                          disabled={deleteMutation.isPending || (template._count?.procurements || 0) > 0}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Clone Template Dialog */}
      <Dialog open={isCloneDialogOpen} onOpenChange={setIsCloneDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Clone Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="clone-name">New Template Name</Label>
              <Input
                id="clone-name"
                value={cloneTemplateName}
                onChange={(e) => setCloneTemplateName(e.target.value)}
                placeholder="Enter name for cloned template"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsCloneDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleConfirmClone}
                disabled={!cloneTemplateName.trim() || cloneMutation.isPending}
              >
                {cloneMutation.isPending ? 'Cloning...' : 'Clone Template'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* View Template Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Template Details: {selectedTemplate?.name}</DialogTitle>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Name</Label>
                      <div className="font-medium">{selectedTemplate.name}</div>
                    </div>
                    <div>
                      <Label>Type</Label>
                      <div>{getTypeBadge(selectedTemplate.type)}</div>
                    </div>
                    <div>
                      <Label>Category</Label>
                      <div>{getCategoryBadge(selectedTemplate.category)}</div>
                    </div>
                    <div>
                      <Label>Status</Label>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(selectedTemplate.isActive)}
                        {selectedTemplate.isDefault && (
                          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                            <Star className="h-3 w-3 mr-1" />
                            Default
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  {selectedTemplate.description && (
                    <div>
                      <Label>Description</Label>
                      <div className="text-gray-700">{selectedTemplate.description}</div>
                    </div>
                  )}
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
                    <div>
                      <Label>Created By</Label>
                      <div>{selectedTemplate.creator.name} ({selectedTemplate.creator.email})</div>
                    </div>
                    <div>
                      <Label>Created At</Label>
                      <div>{new Date(selectedTemplate.createdAt).toLocaleDateString()}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Workflow Stages */}
              <Card>
                <CardHeader>
                  <CardTitle>Workflow Stages</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedTemplate.config.stages.map((stage, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium">
                              {stage.sequence}
                            </div>
                            <div>
                              <div className="font-medium">{stage.name}</div>
                              <div className="text-sm text-gray-500 capitalize">{stage.type.replace('_', ' ')}</div>
                            </div>
                          </div>
                          {stage.duration && (
                            <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
                              <Clock className="h-3 w-3 mr-1" />
                              {stage.duration}h
                            </Badge>
                          )}
                        </div>
                        {stage.requirements && stage.requirements.length > 0 && (
                          <div className="mt-2">
                            <Label>Requirements</Label>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {stage.requirements.map((req, reqIndex) => (
                                <Badge key={reqIndex} variant="outline" className="text-xs">
                                  {req}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Configuration Rules */}
              {selectedTemplate.config.rules && (
                <Card>
                  <CardHeader>
                    <CardTitle>Configuration Rules</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      {selectedTemplate.config.rules.minValue && (
                        <div>
                          <Label>Minimum Value</Label>
                          <div>{selectedTemplate.config.rules.minValue.toLocaleString()}</div>
                        </div>
                      )}
                      {selectedTemplate.config.rules.maxValue && (
                        <div>
                          <Label>Maximum Value</Label>
                          <div>{selectedTemplate.config.rules.maxValue.toLocaleString()}</div>
                        </div>
                      )}
                      <div>
                        <Label>Requires Committee</Label>
                        <div>{selectedTemplate.config.rules.requiresCommittee ? 'Yes' : 'No'}</div>
                      </div>
                      <div>
                        <Label>Auto Approval</Label>
                        <div>{selectedTemplate.config.rules.autoApproval ? 'Yes' : 'No'}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Usage Statistics */}
              {selectedTemplate._count && (
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {selectedTemplate._count.procurements}
                        </div>
                        <div className="text-sm text-gray-600">Procurements</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {selectedTemplate._count.vendorRequirements}
                        </div>
                        <div className="text-sm text-gray-600">Requirements</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {selectedTemplate._count.scheduleTemplates}
                        </div>
                        <div className="text-sm text-gray-600">Schedules</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {selectedTemplate._count.stages}
                        </div>
                        <div className="text-sm text-gray-600">Stages</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
