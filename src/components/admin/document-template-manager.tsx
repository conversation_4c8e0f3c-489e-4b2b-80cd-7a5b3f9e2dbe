'use client';

import React, { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Copy,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  BarChart3,
  Star,
  Users,
  Calendar,
  Download,
  Upload,
  Settings,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { mockApiService } from '@/lib/mock-data/workflow-data';

// Types
interface DocumentTemplate {
  id: string;
  name: string;
  description?: string;
  type: string;
  category: string;
  status: string;
  version: number;
  isActive: boolean;
  content: {
    htmlTemplate: string;
    cssStyles?: string;
    signatureFields?: Array<{
      id: string;
      name: string;
      position: { x: number; y: number; page?: number };
      size: { width: number; height: number };
      required: boolean;
    }>;
  };
  variables: Array<{
    name: string;
    type: string;
    description?: string;
    required: boolean;
    defaultValue?: any;
  }>;
  createdAt: string;
  updatedAt: string;
  creator: {
    id: string;
    name: string;
    email: string;
  };
  updater?: {
    id: string;
    name: string;
    email: string;
  };
  approver?: {
    id: string;
    name: string;
    email: string;
  };
  versions?: Array<{
    id: string;
    version: number;
    changelog: string;
    createdAt: string;
    creator: {
      id: string;
      name: string;
      email: string;
    };
  }>;
  _count?: {
    usages: number;
  };
}

// API Functions
async function fetchDocumentTemplates(filters: {
  type?: string;
  category?: string;
  status?: string;
  search?: string;
}): Promise<{ templates: DocumentTemplate[]; totalCount: number }> {
  try {
    const token = localStorage.getItem("auth-token");
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const params = new URLSearchParams();
    if (filters.type) params.append('type', filters.type);
    if (filters.category) params.append('category', filters.category);
    if (filters.status) params.append('status', filters.status);
    if (filters.search) params.append('search', filters.search);

    const response = await fetch(`/api/templates?${params}`, { headers });
    if (!response.ok) throw new Error('Failed to fetch document templates');

    const result = await response.json();
    return { templates: result.data || result, totalCount: (result.data || result).length };
  } catch (error) {
    console.warn('API failed, using mock data:', error);
    return mockApiService.getDocumentTemplates(filters);
  }
}

async function toggleTemplateStatus(templateId: string, isActive: boolean): Promise<DocumentTemplate> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/templates/${templateId}`, {
    method: 'PUT',
    headers,
    body: JSON.stringify({ isActive }),
  });

  if (!response.ok) throw new Error('Failed to update template status');

  const result = await response.json();
  return result.data || result;
}

async function deleteDocumentTemplate(templateId: string): Promise<void> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/templates/${templateId}`, {
    method: 'DELETE',
    headers,
  });

  if (!response.ok) throw new Error('Failed to delete template');
}

async function approveTemplate(templateId: string): Promise<DocumentTemplate> {
  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/templates/${templateId}/approve`, {
    method: 'POST',
    headers,
  });

  if (!response.ok) throw new Error('Failed to approve template');

  const result = await response.json();
  return result.data || result;
}

// Template Types and Categories
const TEMPLATE_TYPES = [
  { value: 'PURCHASE_ORDER', label: 'Purchase Order' },
  { value: 'BAST', label: 'BAST (Berita Acara Serah Terima)' },
  { value: 'CONTRACT', label: 'Contract' },
  { value: 'INVOICE', label: 'Invoice' },
  { value: 'RFQ', label: 'Request for Quotation' },
  { value: 'AANWIJZING', label: 'Aanwijzing' },
  { value: 'EVALUATION_REPORT', label: 'Evaluation Report' },
  { value: 'AWARD_LETTER', label: 'Award Letter' },
  { value: 'PURCHASE_REQUISITION', label: 'Purchase Requisition' },
  { value: 'DELIVERY_NOTE', label: 'Delivery Note' },
  { value: 'CUSTOM', label: 'Custom Document' },
];

const TEMPLATE_CATEGORIES = [
  { value: 'PROCUREMENT', label: 'Procurement' },
  { value: 'FINANCIAL', label: 'Financial' },
  { value: 'LEGAL', label: 'Legal' },
  { value: 'ADMINISTRATIVE', label: 'Administrative' },
  { value: 'TECHNICAL', label: 'Technical' },
];

const TEMPLATE_STATUSES = [
  { value: 'DRAFT', label: 'Draft' },
  { value: 'PENDING_APPROVAL', label: 'Pending Approval' },
  { value: 'APPROVED', label: 'Approved' },
  { value: 'REJECTED', label: 'Rejected' },
  { value: 'ARCHIVED', label: 'Archived' },
];

export function DocumentTemplateManager() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const queryClient = useQueryClient();

  const { data: templatesData, isLoading } = useQuery({
    queryKey: ['document-templates', { typeFilter, categoryFilter, statusFilter, searchTerm }],
    queryFn: () => fetchDocumentTemplates({
      type: typeFilter !== 'all' ? typeFilter : undefined,
      category: categoryFilter !== 'all' ? categoryFilter : undefined,
      status: statusFilter !== 'all' ? statusFilter : undefined,
      search: searchTerm || undefined,
    }),
  });

  const toggleStatusMutation = useMutation({
    mutationFn: ({ templateId, isActive }: { templateId: string; isActive: boolean }) =>
      toggleTemplateStatus(templateId, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document-templates'] });
      toast.success('Template status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update template status');
    },
  });

  const approveMutation = useMutation({
    mutationFn: approveTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document-templates'] });
      toast.success('Template approved successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to approve template');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteDocumentTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document-templates'] });
      toast.success('Template deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete template');
    },
  });

  const templates = templatesData?.templates || [];

  const handleToggleStatus = useCallback((templateId: string, currentStatus: boolean) => {
    toggleStatusMutation.mutate({ templateId, isActive: !currentStatus });
  }, [toggleStatusMutation]);

  const handleApprove = useCallback((templateId: string) => {
    if (confirm('Are you sure you want to approve this template?')) {
      approveMutation.mutate(templateId);
    }
  }, [approveMutation]);

  const handleDelete = useCallback((templateId: string, templateName: string) => {
    if (confirm(`Are you sure you want to delete the template "${templateName}"? This action cannot be undone.`)) {
      deleteMutation.mutate(templateId);
    }
  }, [deleteMutation]);

  const handleView = useCallback((template: DocumentTemplate) => {
    setSelectedTemplate(template);
    setIsViewDialogOpen(true);
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
      DRAFT: { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: <Edit className="h-3 w-3 mr-1" /> },
      PENDING_APPROVAL: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: <Clock className="h-3 w-3 mr-1" /> },
      APPROVED: { color: 'bg-green-100 text-green-800 border-green-200', icon: <CheckCircle className="h-3 w-3 mr-1" /> },
      REJECTED: { color: 'bg-red-100 text-red-800 border-red-200', icon: <XCircle className="h-3 w-3 mr-1" /> },
      ARCHIVED: { color: 'bg-purple-100 text-purple-800 border-purple-200', icon: <FileText className="h-3 w-3 mr-1" /> },
    };

    const config = statusConfig[status] || statusConfig.DRAFT;

    return (
      <Badge variant="outline" className={config.color}>
        {config.icon}
        {TEMPLATE_STATUSES.find(s => s.value === status)?.label || status}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    const colors: Record<string, string> = {
      PURCHASE_ORDER: 'bg-blue-100 text-blue-800 border-blue-200',
      BAST: 'bg-purple-100 text-purple-800 border-purple-200',
      CONTRACT: 'bg-green-100 text-green-800 border-green-200',
      INVOICE: 'bg-orange-100 text-orange-800 border-orange-200',
      RFQ: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      AANWIJZING: 'bg-pink-100 text-pink-800 border-pink-200',
      EVALUATION_REPORT: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      AWARD_LETTER: 'bg-cyan-100 text-cyan-800 border-cyan-200',
      PURCHASE_REQUISITION: 'bg-teal-100 text-teal-800 border-teal-200',
      DELIVERY_NOTE: 'bg-lime-100 text-lime-800 border-lime-200',
      CUSTOM: 'bg-slate-100 text-slate-800 border-slate-200',
    };

    return (
      <Badge variant="outline" className={colors[type] || 'bg-gray-100 text-gray-800 border-gray-200'}>
        {TEMPLATE_TYPES.find(t => t.value === type)?.label || type}
      </Badge>
    );
  };

  const getCategoryBadge = (category: string) => {
    const colors: Record<string, string> = {
      PROCUREMENT: 'bg-blue-100 text-blue-800 border-blue-200',
      FINANCIAL: 'bg-green-100 text-green-800 border-green-200',
      LEGAL: 'bg-red-100 text-red-800 border-red-200',
      ADMINISTRATIVE: 'bg-purple-100 text-purple-800 border-purple-200',
      TECHNICAL: 'bg-orange-100 text-orange-800 border-orange-200',
    };

    return (
      <Badge variant="outline" className={colors[category] || 'bg-gray-100 text-gray-800 border-gray-200'}>
        {TEMPLATE_CATEGORIES.find(c => c.value === category)?.label || category}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Document Template Management</h1>
          <p className="text-gray-600 mt-2">
            Create and manage document templates for procurement processes
          </p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Document Template</DialogTitle>
            </DialogHeader>
            <div className="text-center py-8 text-gray-500">
              Template creation form will be implemented here
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label>Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All types</SelectItem>
                  {TEMPLATE_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All categories</SelectItem>
                  {TEMPLATE_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  {TEMPLATE_STATUSES.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Templates
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading...</div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No document templates found. Create your first template!
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{template.name}</div>
                        {template.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {template.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getTypeBadge(template.type)}</TableCell>
                    <TableCell>{getCategoryBadge(template.category)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                          v{template.version}
                        </Badge>
                        {template.isActive && (
                          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {template._count ? (
                        <div className="text-sm">
                          <div className="flex items-center gap-1">
                            <BarChart3 className="h-3 w-3 text-blue-500" />
                            <span>{template._count.usages} usages</span>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">No usage data</span>
                      )}
                    </TableCell>
                    <TableCell>{getStatusBadge(template.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleView(template)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {/* Edit functionality will be implemented */ }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        {template.status === 'PENDING_APPROVAL' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleApprove(template.id)}
                            disabled={approveMutation.isPending}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(template.id, template.isActive)}
                          disabled={toggleStatusMutation.isPending}
                        >
                          {template.isActive ? <XCircle className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(template.id, template.name)}
                          disabled={deleteMutation.isPending || (template._count?.usages || 0) > 0}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Template Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Template Details: {selectedTemplate?.name}</DialogTitle>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Name</Label>
                      <div className="font-medium">{selectedTemplate.name}</div>
                    </div>
                    <div>
                      <Label>Type</Label>
                      <div>{getTypeBadge(selectedTemplate.type)}</div>
                    </div>
                    <div>
                      <Label>Category</Label>
                      <div>{getCategoryBadge(selectedTemplate.category)}</div>
                    </div>
                    <div>
                      <Label>Status</Label>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(selectedTemplate.status)}
                        {selectedTemplate.isActive && (
                          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  {selectedTemplate.description && (
                    <div>
                      <Label>Description</Label>
                      <div className="text-gray-700">{selectedTemplate.description}</div>
                    </div>
                  )}
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
                    <div>
                      <Label>Created By</Label>
                      <div>{selectedTemplate.creator.name} ({selectedTemplate.creator.email})</div>
                    </div>
                    <div>
                      <Label>Version</Label>
                      <div>v{selectedTemplate.version}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Template Variables */}
              {selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Template Variables</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedTemplate.variables.map((variable, index) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <div className="font-medium">{variable.name}</div>
                              <Badge variant="outline" className="text-xs">
                                {variable.type}
                              </Badge>
                              {variable.required && (
                                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200 text-xs">
                                  Required
                                </Badge>
                              )}
                            </div>
                          </div>
                          {variable.description && (
                            <div className="text-sm text-gray-600 mb-2">{variable.description}</div>
                          )}
                          {variable.defaultValue && (
                            <div className="text-sm">
                              <Label>Default Value:</Label>
                              <span className="ml-2 text-gray-700">{String(variable.defaultValue)}</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Signature Fields */}
              {selectedTemplate.content.signatureFields && selectedTemplate.content.signatureFields.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Signature Fields</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedTemplate.content.signatureFields.map((field, index) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium">{field.name}</div>
                            {field.required && (
                              <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
                                Required
                              </Badge>
                            )}
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                              <Label>Position:</Label>
                              <span className="ml-2">x: {field.position.x}, y: {field.position.y}</span>
                              {field.position.page && <span>, page: {field.position.page}</span>}
                            </div>
                            <div>
                              <Label>Size:</Label>
                              <span className="ml-2">{field.size.width} x {field.size.height}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Version History */}
              {selectedTemplate.versions && selectedTemplate.versions.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Version History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedTemplate.versions.map((version, index) => (
                        <div key={version.id} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                                v{version.version}
                              </Badge>
                              <div className="text-sm text-gray-600">
                                by {version.creator.name}
                              </div>
                            </div>
                            <div className="text-sm text-gray-500">
                              {new Date(version.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                          {version.changelog && (
                            <div className="text-sm text-gray-700">{version.changelog}</div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Usage Statistics */}
              {selectedTemplate._count && (
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {selectedTemplate._count.usages}
                      </div>
                      <div className="text-sm text-gray-600">Total Usages</div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
