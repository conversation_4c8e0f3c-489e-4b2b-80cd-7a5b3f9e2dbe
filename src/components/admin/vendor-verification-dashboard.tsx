"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Eye, Check, X, FileText } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";

interface VendorVerificationStep {
  id: string;
  stepName: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  verifiedById?: string;
  verifiedBy?: {
    id: string;
    name: string;
  };
  verifiedAt?: string;
  comments?: string;
}

interface Vendor {
  id: string;
  companyName: string;
  npwpNumber: string;
  address: string;
  picName: string;
  picEmail: string;
  picPhone: string;
  verificationStatus: "PENDING" | "VERIFIED" | "REJECTED";
  rejectionReason?: string;
  createdAt: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
  documents: Array<{
    id: string;
    fileName: string;
    fileUrl: string;
    fileType: string;
    description?: string;
  }>;
  verificationSteps?: VendorVerificationStep[];
}

interface VendorsResponse {
  vendors: Vendor[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

async function fetchVendors(status: string, page = 1): Promise<VendorsResponse> {
  const params = new URLSearchParams();
  params.append("status", status);
  params.append("page", page.toString());

  const response = await fetch(`/api/admin/vendors?${params}`);
  if (!response.ok) {
    throw new Error("Failed to fetch vendors");
  }
  const result = await response.json();
  return result.data;
}

async function updateVendorStatus(vendorId: string, status: string, rejectionReason?: string) {
  const response = await fetch(`/api/admin/vendors/${vendorId}/status`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ status, rejectionReason }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to update vendor status");
  }

  return response.json();
}

async function verifyVendorStep(vendorId: string, stepName: string, status: string, comments?: string) {
  const response = await fetch(`/api/admin/vendors/${vendorId}/verify-step`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ stepName, status, comments }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to verify step");
  }

  return response.json();
}

export function VendorVerificationDashboard() {
  const [activeTab, setActiveTab] = useState("PENDING");
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [activeVerificationTab, setActiveVerificationTab] = useState("Administrasi");
  const [stepComments, setStepComments] = useState("");
  const [showStepDialog, setShowStepDialog] = useState(false);
  const [currentStep, setCurrentStep] = useState<{ stepName: string; action: "APPROVED" | "REJECTED" } | null>(null);

  const queryClient = useQueryClient();

  const { data, isLoading, error: fetchError } = useQuery({
    queryKey: ["vendors", activeTab],
    queryFn: () => fetchVendors(activeTab),
    staleTime: 30000,
  });

  const updateStatusMutation = useMutation({
    mutationFn: ({ vendorId, status, rejectionReason }: {
      vendorId: string;
      status: string;
      rejectionReason?: string;
    }) => updateVendorStatus(vendorId, status, rejectionReason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["vendors"] });
      setSelectedVendor(null);
      setShowRejectDialog(false);
      setRejectionReason("");
      setError(null);
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const verifyStepMutation = useMutation({
    mutationFn: ({ vendorId, stepName, status, comments }: {
      vendorId: string;
      stepName: string;
      status: string;
      comments?: string;
    }) => verifyVendorStep(vendorId, stepName, status, comments),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["vendors"] });
      setShowStepDialog(false);
      setCurrentStep(null);
      setStepComments("");
      setError(null);
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const handleApprove = (vendor: Vendor) => {
    updateStatusMutation.mutate({
      vendorId: vendor.id,
      status: "VERIFIED",
    });
  };

  const handleReject = () => {
    if (!selectedVendor || !rejectionReason.trim()) {
      setError("Alasan penolakan harus diisi");
      return;
    }

    updateStatusMutation.mutate({
      vendorId: selectedVendor.id,
      status: "REJECTED",
      rejectionReason: rejectionReason.trim(),
    });
  };

  const handleStepVerification = () => {
    if (!selectedVendor || !currentStep) return;

    if (currentStep.action === "REJECTED" && !stepComments.trim()) {
      setError("Komentar wajib diisi untuk penolakan");
      return;
    }

    verifyStepMutation.mutate({
      vendorId: selectedVendor.id,
      stepName: currentStep.stepName,
      status: currentStep.action,
      comments: stepComments.trim() || undefined,
    });
  };

  const getVerificationSteps = () => {
    return [
      "Administrasi",
      "Pajak",
      "Keuangan",
      "Teknis",
      "Legal",
    ];
  };

  const getStepStatus = (stepName: string) => {
    if (!selectedVendor?.verificationSteps) return "PENDING";
    const step = selectedVendor.verificationSteps.find(s => s.stepName === stepName);
    return step?.status || "PENDING";
  };

  const getStepVerifier = (stepName: string) => {
    if (!selectedVendor?.verificationSteps) return null;
    const step = selectedVendor.verificationSteps.find(s => s.stepName === stepName);
    return step?.verifiedBy || null;
  };

  const getStepComments = (stepName: string) => {
    if (!selectedVendor?.verificationSteps) return null;
    const step = selectedVendor.verificationSteps.find(s => s.stepName === stepName);
    return step?.comments || null;
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: "secondary" as const, label: "Menunggu Verifikasi" },
      VERIFIED: { variant: "default" as const, label: "Terverifikasi" },
      REJECTED: { variant: "destructive" as const, label: "Ditolak" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const openDocumentInNewTab = (url: string) => {
    window.open(url, "_blank");
  };

  if (fetchError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            Error loading vendors: {(fetchError as Error).message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Daftar Vendor</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="PENDING">Menunggu Verifikasi</TabsTrigger>
              <TabsTrigger value="VERIFIED">Terverifikasi</TabsTrigger>
              <TabsTrigger value="REJECTED">Ditolak</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama Perusahaan</TableHead>
                      <TableHead>PIC</TableHead>
                      <TableHead>NPWP</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Tanggal Daftar</TableHead>
                      <TableHead>Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          Loading...
                        </TableCell>
                      </TableRow>
                    ) : data?.vendors.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          Tidak ada vendor ditemukan
                        </TableCell>
                      </TableRow>
                    ) : (
                      data?.vendors.map((vendor) => (
                        <TableRow key={vendor.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{vendor.companyName}</p>
                              <p className="text-sm text-gray-600">{vendor.user.email}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">{vendor.picName}</p>
                              <p className="text-sm text-gray-600">{vendor.picEmail}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="font-mono text-sm">{vendor.npwpNumber}</span>
                          </TableCell>
                          <TableCell>{getStatusBadge(vendor.verificationStatus)}</TableCell>
                          <TableCell>
                            <span className="text-sm">
                              {format(new Date(vendor.createdAt), "dd MMM yyyy", {
                                locale: id,
                              })}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedVendor(vendor)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                Review
                              </Button>
                              {vendor.verificationStatus === "PENDING" && (
                                <>
                                  <Button
                                    variant="default"
                                    size="sm"
                                    onClick={() => handleApprove(vendor)}
                                    disabled={updateStatusMutation.isPending}
                                  >
                                    <Check className="h-4 w-4 mr-1" />
                                    Setujui
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedVendor(vendor);
                                      setShowRejectDialog(true);
                                    }}
                                    disabled={updateStatusMutation.isPending}
                                  >
                                    <X className="h-4 w-4 mr-1" />
                                    Tolak
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Enhanced Vendor Detail Dialog with Granular Verification */}
      {selectedVendor && !showRejectDialog && (
        <Dialog open={!!selectedVendor} onOpenChange={() => setSelectedVendor(null)}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Detail Vendor - {selectedVendor.companyName}</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-6">
              {/* Company Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Informasi Perusahaan</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Nama Perusahaan</Label>
                    <p className="text-sm">{selectedVendor.companyName}</p>
                  </div>
                  <div>
                    <Label>NPWP</Label>
                    <p className="text-sm font-mono">{selectedVendor.npwpNumber}</p>
                  </div>
                  <div className="col-span-2">
                    <Label>Alamat</Label>
                    <p className="text-sm">{selectedVendor.address}</p>
                  </div>
                </div>
              </div>

              {/* PIC Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Informasi PIC</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Nama PIC</Label>
                    <p className="text-sm">{selectedVendor.picName}</p>
                  </div>
                  <div>
                    <Label>Email PIC</Label>
                    <p className="text-sm">{selectedVendor.picEmail}</p>
                  </div>
                  <div>
                    <Label>Telepon PIC</Label>
                    <p className="text-sm">{selectedVendor.picPhone}</p>
                  </div>
                </div>
              </div>

              {/* Verification Steps */}
              {selectedVendor.verificationStatus === "PENDING" && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Verifikasi Bertahap</h3>
                  <Tabs value={activeVerificationTab} onValueChange={setActiveVerificationTab}>
                    <TabsList className="grid w-full grid-cols-5">
                      {getVerificationSteps().map((step) => (
                        <TabsTrigger key={step} value={step} className="text-xs">
                          {step}
                        </TabsTrigger>
                      ))}
                    </TabsList>

                    {getVerificationSteps().map((stepName) => (
                      <TabsContent key={stepName} value={stepName} className="mt-4">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">Verifikasi {stepName}</h4>
                            <Badge
                              variant={
                                getStepStatus(stepName) === "APPROVED" ? "default" :
                                getStepStatus(stepName) === "REJECTED" ? "destructive" : "secondary"
                              }
                            >
                              {getStepStatus(stepName) === "PENDING" ? "Menunggu" :
                               getStepStatus(stepName) === "APPROVED" ? "Disetujui" : "Ditolak"}
                            </Badge>
                          </div>

                          {getStepStatus(stepName) !== "PENDING" && (
                            <div className="bg-gray-50 p-3 rounded">
                              <p className="text-sm">
                                <strong>Diverifikasi oleh:</strong> {getStepVerifier(stepName)?.name || "Unknown"}
                              </p>
                              {getStepComments(stepName) && (
                                <p className="text-sm mt-1">
                                  <strong>Komentar:</strong> {getStepComments(stepName)}
                                </p>
                              )}
                            </div>
                          )}

                          {getStepStatus(stepName) === "PENDING" && (
                            <div className="flex gap-2">
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => {
                                  setCurrentStep({ stepName, action: "APPROVED" });
                                  setShowStepDialog(true);
                                }}
                                disabled={verifyStepMutation.isPending}
                              >
                                <Check className="h-4 w-4 mr-1" />
                                Setujui {stepName}
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => {
                                  setCurrentStep({ stepName, action: "REJECTED" });
                                  setShowStepDialog(true);
                                }}
                                disabled={verifyStepMutation.isPending}
                              >
                                <X className="h-4 w-4 mr-1" />
                                Tolak {stepName}
                              </Button>
                            </div>
                          )}
                        </div>
                      </TabsContent>
                    ))}
                  </Tabs>
                </div>
              )}

              {/* Documents */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Dokumen Terlampir</h3>
                <div className="space-y-2">
                  {selectedVendor.documents.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-gray-500" />
                        <div>
                          <p className="font-medium">{doc.fileName}</p>
                          <p className="text-sm text-gray-600">{doc.description || doc.fileType}</p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openDocumentInNewTab(doc.fileUrl)}
                      >
                        Lihat
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {selectedVendor.verificationStatus === "REJECTED" && selectedVendor.rejectionReason && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 text-red-600">Alasan Penolakan</h3>
                  <p className="text-sm bg-red-50 p-3 rounded border border-red-200">
                    {selectedVendor.rejectionReason}
                  </p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedVendor(null)}>
                Tutup
              </Button>
              {selectedVendor.verificationStatus === "PENDING" && (
                <>
                  <Button
                    variant="default"
                    onClick={() => handleApprove(selectedVendor)}
                    disabled={updateStatusMutation.isPending}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Setujui Verifikasi
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => setShowRejectDialog(true)}
                    disabled={updateStatusMutation.isPending}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Tolak Verifikasi
                  </Button>
                </>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Rejection Dialog */}
      {showRejectDialog && selectedVendor && (
        <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Tolak Verifikasi Vendor</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <p>Anda akan menolak verifikasi untuk <strong>{selectedVendor.companyName}</strong></p>

              <div className="space-y-2">
                <Label htmlFor="rejectionReason">Alasan Penolakan *</Label>
                <Textarea
                  id="rejectionReason"
                  placeholder="Masukkan alasan penolakan..."
                  value={rejectionReason}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setRejectionReason(e.target.value)}
                  rows={4}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
                Batal
              </Button>
              <Button
                variant="destructive"
                onClick={handleReject}
                disabled={updateStatusMutation.isPending || !rejectionReason.trim()}
              >
                {updateStatusMutation.isPending ? "Menolak..." : "Kirim Penolakan"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Step Verification Dialog */}
      {showStepDialog && selectedVendor && currentStep && (
        <Dialog open={showStepDialog} onOpenChange={setShowStepDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {currentStep.action === "APPROVED" ? "Setujui" : "Tolak"} Verifikasi {currentStep.stepName}
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <p>
                Anda akan {currentStep.action === "APPROVED" ? "menyetujui" : "menolak"} verifikasi{" "}
                <strong>{currentStep.stepName}</strong> untuk vendor{" "}
                <strong>{selectedVendor.companyName}</strong>
              </p>

              <div className="space-y-2">
                <Label htmlFor="stepComments">
                  Komentar {currentStep.action === "REJECTED" ? "*" : "(Opsional)"}
                </Label>
                <Textarea
                  id="stepComments"
                  placeholder={
                    currentStep.action === "APPROVED"
                      ? "Komentar tambahan..."
                      : "Masukkan alasan penolakan..."
                  }
                  value={stepComments}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setStepComments(e.target.value)}
                  rows={4}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowStepDialog(false)}>
                Batal
              </Button>
              <Button
                variant={currentStep.action === "APPROVED" ? "default" : "destructive"}
                onClick={handleStepVerification}
                disabled={
                  verifyStepMutation.isPending ||
                  (currentStep.action === "REJECTED" && !stepComments.trim())
                }
              >
                {verifyStepMutation.isPending
                  ? "Memproses..."
                  : currentStep.action === "APPROVED"
                    ? "Setujui"
                    : "Tolak"
                }
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
