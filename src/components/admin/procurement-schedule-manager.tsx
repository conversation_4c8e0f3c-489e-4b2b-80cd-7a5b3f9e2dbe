'use client';

import React, { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format, addDays, addHours, isWeekend, parseISO } from 'date-fns';
import { id } from 'date-fns/locale';
import {
  Plus,
  Edit,
  Trash2,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  Play,
  Pause,
  Settings,
  Download,
  Upload,
  Filter,
  Search,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

interface ProcurementScheduleTemplate {
  id: string;
  templateId: string;
  name: string;
  description?: string;
  isActive: boolean;
  stages: Array<{
    stageId: string;
    stageName: string;
    duration: number; // Duration in hours
    bufferTime: number; // Buffer time in hours
    dependencies: string[];
    workingDaysOnly: boolean;
    notifications?: {
      beforeStart?: number; // Hours before start
      beforeEnd?: number; // Hours before end
      onOverdue: boolean;
    };
  }>;
  milestones: Array<{
    name: string;
    type: 'STAGE_START' | 'STAGE_END' | 'DEADLINE' | 'REVIEW';
    stageId?: string;
    offsetDays: number; // Days offset from stage start/end
    isRequired: boolean;
    notifications?: {
      beforeDue?: number; // Hours before due
      onOverdue: boolean;
    };
  }>;
  buffers: {
    betweenStages: number; // Default buffer between stages in hours
    beforeDeadlines: number; // Buffer before critical deadlines
    contingency: number; // Contingency percentage
  };
  workingDays: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturday: boolean;
    sunday: boolean;
    workingHours: {
      start: string;
      end: string;
    };
  };
  holidays: Array<{
    date: string;
    name: string;
    recurring: boolean;
  }>;
  createdAt: string;
  updatedAt: string;
  creator: {
    id: string;
    name: string;
    email: string;
  };
  template: {
    id: string;
    name: string;
    type: string;
    category: string;
  };
  _count: {
    procurementSchedules: number;
  };
}

interface ScheduleTemplateFormData {
  templateId: string;
  name: string;
  description: string;
  stages: Array<{
    stageId: string;
    stageName: string;
    duration: number;
    bufferTime: number;
    dependencies: string[];
    workingDaysOnly: boolean;
    notifications: {
      beforeStart?: number;
      beforeEnd?: number;
      onOverdue: boolean;
    };
  }>;
  milestones: Array<{
    name: string;
    type: 'STAGE_START' | 'STAGE_END' | 'DEADLINE' | 'REVIEW';
    stageId?: string;
    offsetDays: number;
    isRequired: boolean;
    notifications: {
      beforeDue?: number;
      onOverdue: boolean;
    };
  }>;
  buffers: {
    betweenStages: number;
    beforeDeadlines: number;
    contingency: number;
  };
  workingDays: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturday: boolean;
    sunday: boolean;
    workingHours: {
      start: string;
      end: string;
    };
  };
  holidays: Array<{
    date: string;
    name: string;
    recurring: boolean;
  }>;
}

// API functions
async function fetchScheduleTemplates(filters?: {
  templateId?: string;
  search?: string;
}) {
  const params = new URLSearchParams();
  if (filters?.templateId) params.append('templateId', filters.templateId);
  if (filters?.search) params.append('search', filters.search);

  const response = await fetch(`/api/admin/procurement-schedule-templates?${params}`);
  if (!response.ok) throw new Error('Failed to fetch schedule templates');
  return response.json();
}

async function createScheduleTemplate(data: ScheduleTemplateFormData) {
  const response = await fetch('/api/admin/procurement-schedule-templates', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Failed to create schedule template');
  return response.json();
}

async function updateScheduleTemplate(id: string, data: Partial<ScheduleTemplateFormData>) {
  const response = await fetch(`/api/admin/procurement-schedule-templates/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Failed to update schedule template');
  return response.json();
}

async function deleteScheduleTemplate(id: string) {
  const response = await fetch(`/api/admin/procurement-schedule-templates/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) throw new Error('Failed to delete schedule template');
  return response.json();
}

async function fetchWorkflowTemplates() {
  const response = await fetch('/api/admin/procurement-workflow-templates');
  if (!response.ok) throw new Error('Failed to fetch workflow templates');
  return response.json();
}

export function ProcurementScheduleManager() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ProcurementScheduleTemplate | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [templateFilter, setTemplateFilter] = useState<string>('');

  const [formData, setFormData] = useState<ScheduleTemplateFormData>({
    templateId: '',
    name: '',
    description: '',
    stages: [],
    milestones: [],
    buffers: {
      betweenStages: 24,
      beforeDeadlines: 48,
      contingency: 10,
    },
    workingDays: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false,
      workingHours: {
        start: '08:00',
        end: '17:00',
      },
    },
    holidays: [],
  });

  const queryClient = useQueryClient();

  const { data: scheduleTemplatesData, isLoading } = useQuery({
    queryKey: ['schedule-templates', { templateFilter, searchTerm }],
    queryFn: () => fetchScheduleTemplates({
      templateId: templateFilter || undefined,
      search: searchTerm || undefined,
    }),
  });

  const { data: workflowTemplatesData } = useQuery({
    queryKey: ['workflow-templates'],
    queryFn: fetchWorkflowTemplates,
  });

  const scheduleTemplates = scheduleTemplatesData?.scheduleTemplates || [];
  const workflowTemplates = workflowTemplatesData?.templates || [];

  const createMutation = useMutation({
    mutationFn: createScheduleTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedule-templates'] });
      toast.success('Schedule template created successfully');
      setIsCreateDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create schedule template');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ScheduleTemplateFormData> }) =>
      updateScheduleTemplate(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedule-templates'] });
      toast.success('Schedule template updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update schedule template');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteScheduleTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedule-templates'] });
      toast.success('Schedule template deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete schedule template');
    },
  });

  const resetForm = useCallback(() => {
    setFormData({
      templateId: '',
      name: '',
      description: '',
      stages: [],
      milestones: [],
      buffers: {
        betweenStages: 24,
        beforeDeadlines: 48,
        contingency: 10,
      },
      workingDays: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: false,
        sunday: false,
        workingHours: {
          start: '08:00',
          end: '17:00',
        },
      },
      holidays: [],
    });
    setEditingTemplate(null);
  }, []);

  const handleCreate = useCallback(() => {
    createMutation.mutate(formData);
  }, [createMutation, formData]);

  const handleUpdate = useCallback(() => {
    if (editingTemplate) {
      updateMutation.mutate({ id: editingTemplate.id, data: formData });
    }
  }, [updateMutation, editingTemplate, formData]);

  const handleEdit = useCallback((template: ProcurementScheduleTemplate) => {
    setEditingTemplate(template);
    setFormData({
      templateId: template.templateId,
      name: template.name,
      description: template.description || '',
      stages: template.stages.map(stage => ({
        ...stage,
        notifications: stage.notifications || { onOverdue: true }
      })),
      milestones: template.milestones.map(milestone => ({
        ...milestone,
        notifications: milestone.notifications || { onOverdue: true }
      })),
      buffers: template.buffers,
      workingDays: template.workingDays,
      holidays: template.holidays,
    });
    setIsEditDialogOpen(true);
  }, []);

  const handleDelete = useCallback((id: string) => {
    if (confirm('Are you sure you want to delete this schedule template?')) {
      deleteMutation.mutate(id);
    }
  }, [deleteMutation]);

  const calculateTotalDuration = (stages: any[]) => {
    return stages.reduce((total, stage) => total + stage.duration + stage.bufferTime, 0);
  };

  const formatDuration = (hours: number) => {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    if (days > 0) {
      return `${days}d ${remainingHours}h`;
    }
    return `${remainingHours}h`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Procurement Schedule Management</h1>
          <p className="text-gray-600 mt-2">
            Manage procurement timelines, milestones, and process schedules
          </p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Create Schedule Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Schedule Template</DialogTitle>
            </DialogHeader>
            <ScheduleTemplateForm
              formData={formData}
              setFormData={setFormData}
              workflowTemplates={workflowTemplates}
              onSubmit={handleCreate}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search schedule templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label>Workflow Template</Label>
              <Select value={templateFilter} onValueChange={setTemplateFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All templates" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All templates</SelectItem>
                  {workflowTemplates.map((template: any) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name} ({template.type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Schedule Templates Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Schedule Templates
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading...</div>
          ) : scheduleTemplates.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No schedule templates found. Create your first template!
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Workflow Template</TableHead>
                  <TableHead>Stages</TableHead>
                  <TableHead>Total Duration</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {scheduleTemplates.map((template: ProcurementScheduleTemplate) => (
                  <TableRow key={template.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{template.name}</div>
                        {template.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {template.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">{template.template.name}</div>
                        <div className="text-gray-500">{template.template.type}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {template.stages.length} stages
                        </Badge>
                        <Badge variant="outline">
                          {template.milestones.length} milestones
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDuration(calculateTotalDuration(template.stages))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {template._count.procurementSchedules} schedules
                      </div>
                    </TableCell>
                    <TableCell>
                      {template.isActive ? (
                        <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
                          <Pause className="h-3 w-3 mr-1" />
                          Inactive
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(template)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(template.id)}
                          disabled={template._count.procurementSchedules > 0}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Schedule Template</DialogTitle>
          </DialogHeader>
          <ScheduleTemplateForm
            formData={formData}
            setFormData={setFormData}
            workflowTemplates={workflowTemplates}
            onSubmit={handleUpdate}
            onCancel={() => setIsEditDialogOpen(false)}
            isLoading={updateMutation.isPending}
            isEdit
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Schedule Template Form Component
interface ScheduleTemplateFormProps {
  formData: ScheduleTemplateFormData;
  setFormData: React.Dispatch<React.SetStateAction<ScheduleTemplateFormData>>;
  workflowTemplates: any[];
  onSubmit: () => void;
  onCancel: () => void;
  isLoading: boolean;
  isEdit?: boolean;
}

function ScheduleTemplateForm({
  formData,
  setFormData,
  workflowTemplates,
  onSubmit,
  onCancel,
  isLoading,
  isEdit = false,
}: ScheduleTemplateFormProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  // Get selected workflow template stages
  React.useEffect(() => {
    if (formData.templateId) {
      const template = workflowTemplates.find(t => t.id === formData.templateId);
      setSelectedTemplate(template);

      // Initialize stages from workflow template if not already set
      if (template && formData.stages.length === 0) {
        const stages = template.stages?.map((stage: any) => ({
          stageId: stage.id,
          stageName: stage.name,
          duration: stage.minDuration || 24,
          bufferTime: 0,
          dependencies: stage.dependencies || [],
          workingDaysOnly: true,
          notifications: {
            onOverdue: true,
          },
        })) || [];

        setFormData(prev => ({ ...prev, stages }));
      }
    }
  }, [formData.templateId, workflowTemplates, formData.stages.length, setFormData]);

  const addStage = () => {
    setFormData(prev => ({
      ...prev,
      stages: [
        ...prev.stages,
        {
          stageId: '',
          stageName: '',
          duration: 24,
          bufferTime: 0,
          dependencies: [],
          workingDaysOnly: true,
          notifications: {
            onOverdue: true,
          },
        },
      ],
    }));
  };

  const removeStage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      stages: prev.stages.filter((_, i) => i !== index),
    }));
  };

  const updateStage = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      stages: prev.stages.map((stage, i) =>
        i === index ? { ...stage, [field]: value } : stage
      ),
    }));
  };

  const addMilestone = () => {
    setFormData(prev => ({
      ...prev,
      milestones: [
        ...prev.milestones,
        {
          name: '',
          type: 'DEADLINE',
          offsetDays: 0,
          isRequired: true,
          notifications: {
            onOverdue: true,
          },
        },
      ],
    }));
  };

  const removeMilestone = (index: number) => {
    setFormData(prev => ({
      ...prev,
      milestones: prev.milestones.filter((_, i) => i !== index),
    }));
  };

  const updateMilestone = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      milestones: prev.milestones.map((milestone, i) =>
        i === index ? { ...milestone, [field]: value } : milestone
      ),
    }));
  };

  const addHoliday = () => {
    setFormData(prev => ({
      ...prev,
      holidays: [
        ...prev.holidays,
        {
          date: '',
          name: '',
          recurring: false,
        },
      ],
    }));
  };

  const removeHoliday = (index: number) => {
    setFormData(prev => ({
      ...prev,
      holidays: prev.holidays.filter((_, i) => i !== index),
    }));
  };

  const updateHoliday = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      holidays: prev.holidays.map((holiday, i) =>
        i === index ? { ...holiday, [field]: value } : holiday
      ),
    }));
  };

  return (
    <Tabs defaultValue="basic" className="space-y-4">
      <TabsList>
        <TabsTrigger value="basic">Basic Information</TabsTrigger>
        <TabsTrigger value="stages">Stages & Timeline</TabsTrigger>
        <TabsTrigger value="milestones">Milestones</TabsTrigger>
        <TabsTrigger value="settings">Settings</TabsTrigger>
      </TabsList>

      <TabsContent value="basic" className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Template Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter template name"
            />
          </div>

          <div>
            <Label htmlFor="template">Workflow Template *</Label>
            <Select
              value={formData.templateId}
              onValueChange={(value) => setFormData(prev => ({ ...prev, templateId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select workflow template" />
              </SelectTrigger>
              <SelectContent>
                {workflowTemplates.map((template: any) => (
                  <SelectItem key={template.id} value={template.id}>
                    {template.name} ({template.type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Enter template description"
            rows={3}
          />
        </div>

        {selectedTemplate && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Workflow Template Info</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Type:</span> {selectedTemplate.type}
                </div>
                <div>
                  <span className="font-medium">Category:</span> {selectedTemplate.category}
                </div>
                <div>
                  <span className="font-medium">Stages:</span> {selectedTemplate.stages?.length || 0}
                </div>
                <div>
                  <span className="font-medium">Default:</span> {selectedTemplate.isDefault ? 'Yes' : 'No'}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </TabsContent>

      <TabsContent value="stages" className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>Process Stages</Label>
          <Button type="button" variant="outline" size="sm" onClick={addStage}>
            <Plus className="h-4 w-4 mr-2" />
            Add Stage
          </Button>
        </div>

        <div className="space-y-4">
          {formData.stages.map((stage, index) => (
            <Card key={index}>
              <CardContent className="pt-4">
                <div className="flex items-start justify-between mb-4">
                  <h4 className="font-medium">Stage {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeStage(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Stage Name</Label>
                    <Input
                      value={stage.stageName}
                      onChange={(e) => updateStage(index, 'stageName', e.target.value)}
                      placeholder="Stage name"
                    />
                  </div>

                  <div>
                    <Label>Duration (hours)</Label>
                    <Input
                      type="number"
                      min="1"
                      value={stage.duration}
                      onChange={(e) => updateStage(index, 'duration', parseInt(e.target.value) || 1)}
                    />
                  </div>

                  <div>
                    <Label>Buffer Time (hours)</Label>
                    <Input
                      type="number"
                      min="0"
                      value={stage.bufferTime}
                      onChange={(e) => updateStage(index, 'bufferTime', parseInt(e.target.value) || 0)}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={stage.workingDaysOnly}
                      onCheckedChange={(checked) => updateStage(index, 'workingDaysOnly', checked)}
                    />
                    <Label>Working days only</Label>
                  </div>
                </div>

                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Notify before start (hours)</Label>
                    <Input
                      type="number"
                      min="0"
                      value={stage.notifications?.beforeStart || ''}
                      onChange={(e) => updateStage(index, 'notifications', {
                        ...stage.notifications,
                        beforeStart: parseInt(e.target.value) || undefined
                      })}
                    />
                  </div>

                  <div>
                    <Label>Notify before end (hours)</Label>
                    <Input
                      type="number"
                      min="0"
                      value={stage.notifications?.beforeEnd || ''}
                      onChange={(e) => updateStage(index, 'notifications', {
                        ...stage.notifications,
                        beforeEnd: parseInt(e.target.value) || undefined
                      })}
                    />
                  </div>
                </div>

                <div className="mt-4 flex items-center space-x-2">
                  <Checkbox
                    checked={stage.notifications?.onOverdue || false}
                    onCheckedChange={(checked) => updateStage(index, 'notifications', {
                      ...stage.notifications,
                      onOverdue: checked
                    })}
                  />
                  <Label>Notify on overdue</Label>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="milestones" className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>Project Milestones</Label>
          <Button type="button" variant="outline" size="sm" onClick={addMilestone}>
            <Plus className="h-4 w-4 mr-2" />
            Add Milestone
          </Button>
        </div>

        <div className="space-y-4">
          {formData.milestones.map((milestone, index) => (
            <Card key={index}>
              <CardContent className="pt-4">
                <div className="flex items-start justify-between mb-4">
                  <h4 className="font-medium">Milestone {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeMilestone(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Milestone Name</Label>
                    <Input
                      value={milestone.name}
                      onChange={(e) => updateMilestone(index, 'name', e.target.value)}
                      placeholder="Milestone name"
                    />
                  </div>

                  <div>
                    <Label>Type</Label>
                    <Select
                      value={milestone.type}
                      onValueChange={(value) => updateMilestone(index, 'type', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="STAGE_START">Stage Start</SelectItem>
                        <SelectItem value="STAGE_END">Stage End</SelectItem>
                        <SelectItem value="DEADLINE">Deadline</SelectItem>
                        <SelectItem value="REVIEW">Review</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Offset Days</Label>
                    <Input
                      type="number"
                      value={milestone.offsetDays}
                      onChange={(e) => updateMilestone(index, 'offsetDays', parseInt(e.target.value) || 0)}
                    />
                  </div>

                  <div>
                    <Label>Related Stage</Label>
                    <Select
                      value={milestone.stageId || ''}
                      onValueChange={(value) => updateMilestone(index, 'stageId', value || undefined)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select stage (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No specific stage</SelectItem>
                        {formData.stages.map((stage, stageIndex) => (
                          <SelectItem key={stageIndex} value={stage.stageId || `stage-${stageIndex}`}>
                            {stage.stageName || `Stage ${stageIndex + 1}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={milestone.isRequired}
                      onCheckedChange={(checked) => updateMilestone(index, 'isRequired', checked)}
                    />
                    <Label>Required milestone</Label>
                  </div>

                  <div>
                    <Label>Notify before due (hours)</Label>
                    <Input
                      type="number"
                      min="0"
                      value={milestone.notifications?.beforeDue || ''}
                      onChange={(e) => updateMilestone(index, 'notifications', {
                        ...milestone.notifications,
                        beforeDue: parseInt(e.target.value) || undefined
                      })}
                    />
                  </div>
                </div>

                <div className="mt-4 flex items-center space-x-2">
                  <Checkbox
                    checked={milestone.notifications?.onOverdue || false}
                    onCheckedChange={(checked) => updateMilestone(index, 'notifications', {
                      ...milestone.notifications,
                      onOverdue: checked
                    })}
                  />
                  <Label>Notify on overdue</Label>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="settings" className="space-y-6">
        {/* Buffer Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Buffer Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Between Stages (hours)</Label>
                <Input
                  type="number"
                  min="0"
                  value={formData.buffers.betweenStages}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    buffers: {
                      ...prev.buffers,
                      betweenStages: parseInt(e.target.value) || 0
                    }
                  }))}
                />
              </div>

              <div>
                <Label>Before Deadlines (hours)</Label>
                <Input
                  type="number"
                  min="0"
                  value={formData.buffers.beforeDeadlines}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    buffers: {
                      ...prev.buffers,
                      beforeDeadlines: parseInt(e.target.value) || 0
                    }
                  }))}
                />
              </div>

              <div>
                <Label>Contingency (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={formData.buffers.contingency}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    buffers: {
                      ...prev.buffers,
                      contingency: parseInt(e.target.value) || 0
                    }
                  }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Working Days */}
        <Card>
          <CardHeader>
            <CardTitle>Working Days & Hours</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(formData.workingDays).filter(([key]) => key !== 'workingHours').map(([day, isWorking]) => (
                <div key={day} className="flex items-center space-x-2">
                  <Checkbox
                    checked={isWorking as boolean}
                    onCheckedChange={(checked) => setFormData(prev => ({
                      ...prev,
                      workingDays: {
                        ...prev.workingDays,
                        [day]: checked
                      }
                    }))}
                  />
                  <Label className="capitalize">{day}</Label>
                </div>
              ))}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Start Time</Label>
                <Input
                  type="time"
                  value={formData.workingDays.workingHours.start}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    workingDays: {
                      ...prev.workingDays,
                      workingHours: {
                        ...prev.workingDays.workingHours,
                        start: e.target.value
                      }
                    }
                  }))}
                />
              </div>

              <div>
                <Label>End Time</Label>
                <Input
                  type="time"
                  value={formData.workingDays.workingHours.end}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    workingDays: {
                      ...prev.workingDays,
                      workingHours: {
                        ...prev.workingDays.workingHours,
                        end: e.target.value
                      }
                    }
                  }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Holidays */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Holidays
              <Button type="button" variant="outline" size="sm" onClick={addHoliday}>
                <Plus className="h-4 w-4 mr-2" />
                Add Holiday
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {formData.holidays.map((holiday, index) => (
                <div key={index} className="flex items-center gap-4">
                  <Input
                    type="date"
                    value={holiday.date}
                    onChange={(e) => updateHoliday(index, 'date', e.target.value)}
                    className="flex-1"
                  />
                  <Input
                    value={holiday.name}
                    onChange={(e) => updateHoliday(index, 'name', e.target.value)}
                    placeholder="Holiday name"
                    className="flex-1"
                  />
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={holiday.recurring}
                      onCheckedChange={(checked) => updateHoliday(index, 'recurring', checked)}
                    />
                    <Label>Recurring</Label>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeHoliday(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <Button
          onClick={onSubmit}
          disabled={isLoading || !formData.name || !formData.templateId}
        >
          {isLoading ? 'Saving...' : isEdit ? 'Update' : 'Create'} Template
        </Button>
      </div>
    </Tabs>
  );
}
