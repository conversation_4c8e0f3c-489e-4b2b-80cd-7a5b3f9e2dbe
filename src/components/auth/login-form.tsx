"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { loginSchema, type LoginInput } from "@/lib/validations/auth";

interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    user: {
      id: string;
      email: string;
      name: string;
      roles: string[];
      vendor?: {
        id: string;
        companyName: string;
        verificationStatus: string;
      } | null;
    };
    token: string;
  };
}

async function loginUser(data: LoginInput): Promise<LoginResponse> {
  const response = await fetch("/api/auth/login", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
    credentials: "include", // Include cookies in the request
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Login failed");
  }

  return response.json();
}

export function LoginForm() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);

  const form = useForm<LoginInput>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const loginMutation = useMutation({
    mutationFn: loginUser,
    onSuccess: (data) => {
      console.log("🎉 Login successful, response data:", data);
      console.log("👤 User roles:", data.data.user.roles);

      // Store user data in localStorage for client-side access
      localStorage.setItem("user", JSON.stringify(data.data.user));

      // Note: Auth token is now stored in HTTP-only cookie by the server
      // No need to store it in localStorage for security reasons
      console.log("🔑 Auth token stored in HTTP-only cookie by server");

      // Redirect based on user role
      if (data.data.user.roles.includes("ADMIN")) {
        console.log("🚀 Redirecting admin to /admin/dashboard");
        router.push("/admin/dashboard");
      } else if (data.data.user.roles.includes("VENDOR")) {
        console.log("🚀 Redirecting vendor to /vendor/dashboard");
        router.push("/vendor/dashboard");
      } else {
        console.log("🚀 Redirecting user to /dashboard");
        router.push("/dashboard");
      }
    },
    onError: (error: Error) => {
      console.error("❌ Login error:", error);
      setError(error.message);
    },
  });

  const onSubmit = (data: LoginInput) => {
    setError(null);
    loginMutation.mutate(data);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Masuk ke Sistem E-Procurement</CardTitle>
        <CardDescription>
          Masukkan email dan password Anda untuk mengakses sistem
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              {...form.register("email")}
            />
            {form.formState.errors.email && (
              <p className="text-sm text-red-500">
                {form.formState.errors.email.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="Masukkan password"
              {...form.register("password")}
            />
            {form.formState.errors.password && (
              <p className="text-sm text-red-500">
                {form.formState.errors.password.message}
              </p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={loginMutation.isPending}
          >
            {loginMutation.isPending ? "Memproses..." : "Masuk"}
          </Button>

          <div className="text-center space-y-2">
            <Link
              href="/auth/forgot-password"
              className="text-sm text-blue-600 hover:underline"
            >
              Lupa password?
            </Link>
            <div className="text-sm text-gray-600">
              Belum punya akun?{" "}
              <Link href="/auth/register" className="text-blue-600 hover:underline">
                Daftar sebagai vendor
              </Link>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
