"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { FileUpload } from "@/components/ui/file-upload";
import { vendorRegistrationSchema, type VendorRegistrationInput } from "@/lib/validations/auth";
import { type UploadResult } from "@/lib/upload";

interface RegistrationResponse {
  success: boolean;
  message: string;
  data: {
    userId: string;
    vendorId: string;
    message: string;
  };
}

async function registerVendor(data: VendorRegistrationInput): Promise<RegistrationResponse> {
  const response = await fetch("/api/auth/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Registration failed");
  }

  return response.json();
}

export function VendorRegistrationForm() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [documents, setDocuments] = useState<{
    npwp?: UploadResult;
    siup?: UploadResult;
    tdp?: UploadResult;
  }>({});

  const form = useForm<VendorRegistrationInput>({
    resolver: zodResolver(vendorRegistrationSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
      companyName: "",
      npwpNumber: "",
      address: "",
      npwpAddress: "",
      phone: "",
      picName: "",
      picEmail: "",
      picPhone: "",
      businessEntityType: "PT",
      termsAndConditions: false,
    },
  });

  const registrationMutation = useMutation({
    mutationFn: registerVendor,
    onSuccess: (data) => {
      setSuccess(data.data.message);
      setError(null);
      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push("/auth/login");
      }, 3000);
    },
    onError: (error: Error) => {
      setError(error.message);
      setSuccess(null);
    },
  });

  const onSubmit = (data: VendorRegistrationInput) => {
    setError(null);
    setSuccess(null);

    // Validate required documents
    if (!documents.npwp || !documents.siup) {
      setError("Dokumen NPWP dan SIUP/NIB wajib diupload");
      return;
    }

    // Prepare documents array
    const documentArray = [];
    if (documents.npwp) {
      documentArray.push({
        documentType: "NPWP",
        fileName: documents.npwp.fileName,
        fileUrl: documents.npwp.url,
        fileType: documents.npwp.fileType,
      });
    }
    if (documents.siup) {
      documentArray.push({
        documentType: "SIUP",
        fileName: documents.siup.fileName,
        fileUrl: documents.siup.url,
        fileType: documents.siup.fileType,
      });
    }
    if (documents.tdp) {
      documentArray.push({
        documentType: "TDP",
        fileName: documents.tdp.fileName,
        fileUrl: documents.tdp.url,
        fileType: documents.tdp.fileType,
      });
    }

    registrationMutation.mutate({
      ...data,
      documents: documentArray,
    });
  };

  if (success) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-green-600">Pendaftaran Berhasil!</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertDescription>
              {success} Anda akan diarahkan ke halaman login dalam beberapa detik.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Form Pendaftaran Akun E-Procurement</CardTitle>
        <CardDescription>
          PT Bank BPD Sulteng - Silakan lengkapi data perusahaan Anda
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="nasional" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="nasional">Vendor Nasional</TabsTrigger>
            <TabsTrigger value="internasional" disabled>
              Vendor Internasional
            </TabsTrigger>
          </TabsList>

          <TabsContent value="nasional">
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Data Perusahaan */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Data Perusahaan</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Nama Perusahaan *</Label>
                    <Input
                      id="companyName"
                      placeholder="PT Contoh Perusahaan"
                      {...form.register("companyName")}
                    />
                    {form.formState.errors.companyName && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.companyName.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="businessEntityType">Bentuk Badan Usaha *</Label>
                    <Select
                      value={form.watch("businessEntityType")}
                      onValueChange={(value: string) => form.setValue("businessEntityType", value as any)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih bentuk badan usaha" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PT">PT (Perseroan Terbatas)</SelectItem>
                        <SelectItem value="CV">CV (Commanditaire Vennootschap)</SelectItem>
                        <SelectItem value="UD">UD (Usaha Dagang)</SelectItem>
                        <SelectItem value="KOPERASI">Koperasi</SelectItem>
                        <SelectItem value="YAYASAN">Yayasan</SelectItem>
                      </SelectContent>
                    </Select>
                    {form.formState.errors.businessEntityType && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.businessEntityType.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="npwpNumber">Nomor NPWP *</Label>
                    <Input
                      id="npwpNumber"
                      placeholder="123456789012345"
                      maxLength={15}
                      {...form.register("npwpNumber")}
                    />
                    {form.formState.errors.npwpNumber && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.npwpNumber.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Nomor Telepon *</Label>
                    <Input
                      id="phone"
                      placeholder="+62812345678"
                      {...form.register("phone")}
                    />
                    {form.formState.errors.phone && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.phone.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Alamat Perusahaan *</Label>
                  <Textarea
                    id="address"
                    placeholder="Jl. Contoh No. 123, Kota, Provinsi"
                    {...form.register("address")}
                  />
                  {form.formState.errors.address && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.address.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="npwpAddress">Alamat NPWP *</Label>
                  <Textarea
                    id="npwpAddress"
                    placeholder="Alamat sesuai NPWP"
                    {...form.register("npwpAddress")}
                  />
                  {form.formState.errors.npwpAddress && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.npwpAddress.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Legal Documents */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Dokumen Legal</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Dokumen NPWP *</Label>
                    <FileUpload
                      label="Upload NPWP"
                      description="Upload file NPWP (PDF, JPG, PNG - Max 10MB)"
                      onUpload={(result) => setDocuments(prev => ({ ...prev, npwp: result }))}
                      onRemove={() => setDocuments(prev => ({ ...prev, npwp: undefined }))}
                      currentFile={documents.npwp ? {
                        name: documents.npwp.fileName,
                        url: documents.npwp.url
                      } : undefined}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>SIUP/NIB *</Label>
                    <FileUpload
                      label="Upload SIUP/NIB"
                      description="Upload file SIUP atau NIB (PDF, JPG, PNG - Max 10MB)"
                      onUpload={(result) => setDocuments(prev => ({ ...prev, siup: result }))}
                      onRemove={() => setDocuments(prev => ({ ...prev, siup: undefined }))}
                      currentFile={documents.siup ? {
                        name: documents.siup.fileName,
                        url: documents.siup.url
                      } : undefined}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>TDP/Akta Pendirian (Opsional)</Label>
                  <FileUpload
                    label="Upload TDP/Akta"
                    description="Upload file TDP atau Akta Pendirian (PDF, JPG, PNG - Max 10MB)"
                    onUpload={(result) => setDocuments(prev => ({ ...prev, tdp: result }))}
                    onRemove={() => setDocuments(prev => ({ ...prev, tdp: undefined }))}
                    currentFile={documents.tdp ? {
                      name: documents.tdp.fileName,
                      url: documents.tdp.url
                    } : undefined}
                  />
                </div>
              </div>

              {/* Data PIC */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Data Person In Charge (PIC)</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="picName">Nama PIC *</Label>
                    <Input
                      id="picName"
                      placeholder="John Doe"
                      {...form.register("picName")}
                    />
                    {form.formState.errors.picName && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.picName.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="picPhone">Nomor Telepon PIC *</Label>
                    <Input
                      id="picPhone"
                      placeholder="+62812345678"
                      {...form.register("picPhone")}
                    />
                    {form.formState.errors.picPhone && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.picPhone.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="picEmail">Email PIC *</Label>
                  <Input
                    id="picEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    {...form.register("picEmail")}
                  />
                  {form.formState.errors.picEmail && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.picEmail.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Data Akun */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Data Akun</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email Login *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...form.register("email")}
                  />
                  {form.formState.errors.email && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.email.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="password">Password *</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Minimal 8 karakter"
                      {...form.register("password")}
                    />
                    {form.formState.errors.password && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.password.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Konfirmasi Password *</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      placeholder="Ulangi password"
                      {...form.register("confirmPassword")}
                    />
                    {form.formState.errors.confirmPassword && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.confirmPassword.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Terms and Conditions */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="termsAndConditions"
                    checked={form.watch("termsAndConditions")}
                    onCheckedChange={(checked: boolean) =>
                      form.setValue("termsAndConditions", checked as boolean)
                    }
                  />
                  <Label htmlFor="termsAndConditions" className="text-sm">
                    Saya telah membaca dan menyetujui{" "}
                    <Link href="/terms" className="text-blue-600 hover:underline">
                      syarat dan ketentuan
                    </Link>{" "}
                    yang berlaku
                  </Label>
                </div>
                {form.formState.errors.termsAndConditions && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.termsAndConditions.message}
                  </p>
                )}
              </div>

              <div className="flex justify-between">
                <Link href="/auth/login">
                  <Button type="button" variant="outline">
                    Sudah punya akun? Masuk
                  </Button>
                </Link>
                
                <Button
                  type="submit"
                  disabled={registrationMutation.isPending || !form.watch("termsAndConditions")}
                >
                  {registrationMutation.isPending ? "Mendaftar..." : "DAFTAR"}
                </Button>
              </div>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
