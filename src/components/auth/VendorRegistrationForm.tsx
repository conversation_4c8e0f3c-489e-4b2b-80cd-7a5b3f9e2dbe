"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";

// Define the Zod schema for vendor registration
const vendorRegistrationSchema = z
  .object({
    email: z.string().email({ message: "Invalid email address" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" }),
    confirmPassword: z.string(),
    companyName: z.string().min(1, { message: "Company name is required" }),
    npwpNumber: z
      .string()
      .regex(/^\d{16}$/, { message: "NPWP number must be 16 digits" }),
    address: z.string().min(1, { message: "Address is required" }),
    picName: z.string().min(1, { message: "PIC name is required" }),
    picEmail: z.string().email({ message: "Invalid PIC email address" }),
    picPhone: z
      .string()
      .regex(/^\+?\d{10,14}$/, { message: "Invalid phone number format" }),
    businessEntityType: z.string().min(1, { message: "Business entity type is required" }),
    termsAndConditions: z.boolean().refine(value => value === true, {
      message: "You must agree to the terms and conditions",
    }),
    captcha: z.string().min(1, { message: "Captcha is required" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

// Type for form data
type VendorRegistrationFormData = z.infer<typeof vendorRegistrationSchema>;

// Mock function for file upload (to be implemented with actual cloud storage)
const uploadFile = async (file: File): Promise<string> => {
  // Simulate file upload and return a URL
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(`https://storage.cloud/${file.name}`);
    }, 1000);
  });
};

// API call to register vendor (to be implemented with actual API endpoint)
const registerVendor = async (data: VendorRegistrationFormData): Promise<{ message: string }> => {
  // Simulate API call
  console.log("Registering vendor with data:", data);
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (data.email === "<EMAIL>") {
        reject(new Error("Registration failed: Email already exists"));
      } else {
        resolve({ message: "Registration successful. Please wait for verification." });
      }
    }, 1000);
  });
};

export function VendorRegistrationForm() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("national");

  const form = useForm<VendorRegistrationFormData>({
    resolver: zodResolver(vendorRegistrationSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
      companyName: "",
      npwpNumber: "",
      address: "",
      picName: "",
      picEmail: "",
      picPhone: "",
      businessEntityType: "",
      termsAndConditions: false,
      captcha: "",
    },
  });

  const mutation = useMutation<{ message: string }, Error, VendorRegistrationFormData>({
    mutationFn: registerVendor,
    onSuccess: (data) => {
      toast.success("Success", {
        description: data.message,
      });
      router.push("/login");
    },
    onError: (error) => {
      toast.error("Error", {
        description: error.message || "Registration failed. Please try again.",
      });
    },
  });

  const onSubmit = (data: VendorRegistrationFormData) => {
    mutation.mutate(data);
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="text-2xl">
          Form Pendaftaran Akun Eprocurement PT Bank BPD Sulteng
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="national" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="national">Vendor Nasional</TabsTrigger>
            <TabsTrigger value="international">Vendor Internasional</TabsTrigger>
          </TabsList>
          <TabsContent value="national" className="space-y-4">
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Data Perusahaan</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="npwpNumber">Nomor NPWP</Label>
                    <div className="flex gap-2">
                      <Input
                        id="npwpNumber"
                        {...form.register("npwpNumber")}
                        placeholder="1234567890123456"
                        className="flex-1"
                      />
                      <Button variant="outline" size="sm" type="button">
                        Cek
                      </Button>
                    </div>
                    {form.formState.errors.npwpNumber && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.npwpNumber.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessEntityType">Jenis Badan Usaha</Label>
                    <Select
                      onValueChange={(value: string) =>
                        form.setValue("businessEntityType", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih jenis badan usaha" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PT">PT</SelectItem>
                        <SelectItem value="CV">CV</SelectItem>
                        <SelectItem value="Koperasi">Koperasi</SelectItem>
                        <SelectItem value="Lainnya">Lainnya</SelectItem>
                      </SelectContent>
                    </Select>
                    {form.formState.errors.businessEntityType && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.businessEntityType.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Nama Perusahaan</Label>
                    <Input
                      id="companyName"
                      {...form.register("companyName")}
                      placeholder="PT Sejahtera"
                    />
                    {form.formState.errors.companyName && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.companyName.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      {...form.register("email")}
                      placeholder="<EMAIL>"
                    />
                    {form.formState.errors.email && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.email.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      {...form.register("password")}
                    />
                    {form.formState.errors.password && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.password.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Konfirmasi Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      {...form.register("confirmPassword")}
                    />
                    {form.formState.errors.confirmPassword && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.confirmPassword.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Alamat</Label>
                  <Textarea
                    id="address"
                    {...form.register("address")}
                    placeholder="Jl. Sudirman No. 1"
                  />
                  {form.formState.errors.address && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.address.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Data PIC</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="picName">Nama PIC</Label>
                    <Input
                      id="picName"
                      {...form.register("picName")}
                      placeholder="John Doe"
                    />
                    {form.formState.errors.picName && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.picName.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="picEmail">Email PIC</Label>
                    <Input
                      id="picEmail"
                      type="email"
                      {...form.register("picEmail")}
                      placeholder="<EMAIL>"
                    />
                    {form.formState.errors.picEmail && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.picEmail.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="picPhone">No. Telepon PIC</Label>
                    <Input
                      id="picPhone"
                      {...form.register("picPhone")}
                      placeholder="+6281234567890"
                    />
                    {form.formState.errors.picPhone && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.picPhone.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Legal Documents</h3>
                {/* File upload components would be implemented here */}
                <p className="text-sm text-gray-500">
                  File upload functionality for NPWP, SIUP, and TDP/NIB documents will be implemented.
                </p>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Finalization</h3>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="termsAndConditions"
                    checked={form.watch("termsAndConditions")}
                    onCheckedChange={(checked: boolean | "indeterminate") =>
                      form.setValue("termsAndConditions", checked === true)
                    }
                  />
                  <Label htmlFor="termsAndConditions">
                    Saya telah membaca dan menyetujui syarat dan ketentuan yang berlaku
                  </Label>
                </div>
                {form.formState.errors.termsAndConditions && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.termsAndConditions.message}
                  </p>
                )}
                <div className="space-y-2">
                  <Label htmlFor="captcha">Captcha</Label>
                  <Input
                    id="captcha"
                    {...form.register("captcha")}
                    placeholder="Enter captcha"
                  />
                  {form.formState.errors.captcha && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.captcha.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={!form.formState.isValid || mutation.isPending}
                >
                  {mutation.isPending ? "Mendaftar..." : "DAFTAR"}
                </Button>
              </div>
            </form>
          </TabsContent>
          <TabsContent value="international" className="space-y-4">
            <p className="text-center text-gray-500">
              Form for international vendors will be implemented soon.
            </p>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
