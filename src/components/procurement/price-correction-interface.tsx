"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  BarChart3,
  History,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  User,
  DollarSign
} from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/dialog";

import { 
  priceCorrectionSchema,
  type PriceCorrectionInput 
} from "@/lib/validations/enhanced-procurement";

interface PriceCorrectionLog {
  id: string;
  originalPrice: number;
  correctedPrice: number;
  reason: string;
  correctedBy: {
    id: string;
    name: string;
    email: string;
  };
  correctedAt: string;
}

interface PriceAnalysis {
  originalPrice: number;
  currentPrice: number;
  estimatedPrice: number;
  totalCorrections: number;
  priceVarianceFromEstimate: number;
  totalPriceChange: number;
}

interface OfferItem {
  id: string;
  price: number;
  correctedPrice?: number;
  correctionReason?: string;
  correctedAt?: string;
  correctedBy?: {
    id: string;
    name: string;
    email: string;
  };
  offer: {
    id: string;
    vendor: {
      id: string;
      companyName: string;
    };
    procurement: {
      id: string;
      procurementNumber: string;
      title: string;
    };
  };
  item: {
    id: string;
    name: string;
    description?: string;
    quantity: number;
    unit: string;
    estimatedPrice: number;
  };
  correctionLogs: PriceCorrectionLog[];
}

interface CorrectionData {
  offerItem: OfferItem;
  correctionHistory: PriceCorrectionLog[];
  priceAnalysis: PriceAnalysis;
  canCorrect: boolean;
}

async function fetchCorrectionData(offerItemId: string): Promise<CorrectionData> {
  const response = await fetch(`/api/offers/${offerItemId}/correct-arithmetic`);
  if (!response.ok) throw new Error("Failed to fetch correction data");
  const result = await response.json();
  return result.data;
}

async function correctPrice(offerItemId: string, data: PriceCorrectionInput) {
  const response = await fetch(`/api/offers/${offerItemId}/correct-arithmetic`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to correct price");
  }
  
  return response.json();
}

interface PriceCorrectionInterfaceProps {
  offerItemId: string;
  userRoles: string[];
}

export function PriceCorrectionInterface({ offerItemId, userRoles }: PriceCorrectionInterfaceProps) {
  const queryClient = useQueryClient();
  const [correctionDialogOpen, setCorrectionDialogOpen] = useState(false);

  const { data, isLoading, error } = useQuery({
    queryKey: ["price-correction", offerItemId],
    queryFn: () => fetchCorrectionData(offerItemId),
  });

  const form = useForm<PriceCorrectionInput>({
    resolver: zodResolver(priceCorrectionSchema),
    defaultValues: {
      correctedPrice: 0,
      reason: "",
    },
  });

  const correctionMutation = useMutation({
    mutationFn: (data: PriceCorrectionInput) => correctPrice(offerItemId, data),
    onSuccess: () => {
      toast.success("Price correction applied successfully");
      queryClient.invalidateQueries({ queryKey: ["price-correction", offerItemId] });
      setCorrectionDialogOpen(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const onSubmit = (data: PriceCorrectionInput) => {
    correctionMutation.mutate(data);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (change < 0) return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <DollarSign className="h-4 w-4 text-gray-500" />;
  };

  const getPriceChangeColor = (change: number) => {
    if (change > 0) return "text-red-600";
    if (change < 0) return "text-green-600";
    return "text-gray-600";
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading price correction data...</div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            Error loading price correction data: {(error as Error)?.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  const { offerItem, priceAnalysis, correctionHistory, canCorrect } = data;
  const currentPrice = offerItem.correctedPrice || offerItem.price;
  const priceChange = currentPrice - offerItem.price;

  return (
    <div className="space-y-6">
      {/* Current Price Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Price Correction
            </CardTitle>
            
            {canCorrect && (
              <Dialog open={correctionDialogOpen} onOpenChange={setCorrectionDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Correct Price
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Correct Offer Price</DialogTitle>
                  </DialogHeader>
                  
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Item:</p>
                          <p className="font-medium">{offerItem.item.name}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Current Price:</p>
                          <p className="font-medium">{formatCurrency(currentPrice)}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Estimated Price:</p>
                          <p className="font-medium">{formatCurrency(offerItem.item.estimatedPrice)}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Quantity:</p>
                          <p className="font-medium">{offerItem.item.quantity} {offerItem.item.unit}</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="correctedPrice">Corrected Price *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        id="correctedPrice"
                        {...form.register("correctedPrice", { valueAsNumber: true })}
                        placeholder="Enter corrected price"
                      />
                      {form.formState.errors.correctedPrice && (
                        <p className="text-sm text-red-500">{form.formState.errors.correctedPrice.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="reason">Reason for Correction *</Label>
                      <Textarea
                        id="reason"
                        {...form.register("reason")}
                        placeholder="Explain why this price correction is necessary"
                        rows={3}
                      />
                      {form.formState.errors.reason && (
                        <p className="text-sm text-red-500">{form.formState.errors.reason.message}</p>
                      )}
                    </div>

                    {/* Price Impact Preview */}
                    {form.watch("correctedPrice") > 0 && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-medium text-blue-800 mb-2">Price Impact Preview</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-blue-600">Price Change:</p>
                            <p className={`font-medium ${getPriceChangeColor(form.watch("correctedPrice") - currentPrice)}`}>
                              {formatCurrency(form.watch("correctedPrice") - currentPrice)}
                            </p>
                          </div>
                          <div>
                            <p className="text-blue-600">New Total:</p>
                            <p className="font-medium">
                              {formatCurrency(form.watch("correctedPrice") * offerItem.item.quantity)}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex justify-end gap-2 pt-4 border-t">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setCorrectionDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={correctionMutation.isPending}
                      >
                        {correctionMutation.isPending ? "Applying..." : "Apply Correction"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Item Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Item Name:</p>
                  <p className="font-medium">{offerItem.item.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Vendor:</p>
                  <p className="font-medium">{offerItem.offer.vendor.companyName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Procurement:</p>
                  <p className="font-medium">{offerItem.offer.procurement.title}</p>
                  <p className="text-xs text-gray-500">{offerItem.offer.procurement.procurementNumber}</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Quantity:</p>
                  <p className="font-medium">{offerItem.item.quantity} {offerItem.item.unit}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Estimated Price:</p>
                  <p className="font-medium">{formatCurrency(offerItem.item.estimatedPrice)}</p>
                </div>
                {offerItem.item.description && (
                  <div>
                    <p className="text-sm text-gray-600">Description:</p>
                    <p className="text-sm">{offerItem.item.description}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Price Analysis */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <h3 className="font-medium mb-3">Price Analysis</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{formatCurrency(priceAnalysis.originalPrice)}</p>
                  <p className="text-sm text-gray-600">Original Price</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{formatCurrency(priceAnalysis.currentPrice)}</p>
                  <p className="text-sm text-gray-600">Current Price</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1">
                    {getPriceChangeIcon(priceAnalysis.totalPriceChange)}
                    <p className={`text-2xl font-bold ${getPriceChangeColor(priceAnalysis.totalPriceChange)}`}>
                      {formatCurrency(Math.abs(priceAnalysis.totalPriceChange))}
                    </p>
                  </div>
                  <p className="text-sm text-gray-600">Total Change</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{priceAnalysis.totalCorrections}</p>
                  <p className="text-sm text-gray-600">Corrections</p>
                </div>
              </div>
              
              {/* Variance from Estimate */}
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Variance from Estimate:</span>
                  <div className="flex items-center gap-2">
                    {priceAnalysis.priceVarianceFromEstimate !== 0 && (
                      <>
                        {getPriceChangeIcon(priceAnalysis.priceVarianceFromEstimate)}
                        <span className={`font-medium ${getPriceChangeColor(priceAnalysis.priceVarianceFromEstimate)}`}>
                          {formatPercentage(priceAnalysis.priceVarianceFromEstimate)}
                        </span>
                      </>
                    )}
                    {priceAnalysis.priceVarianceFromEstimate === 0 && (
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Matches Estimate
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Current Correction Status */}
            {offerItem.correctedPrice && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div className="flex-1">
                    <h4 className="font-medium text-yellow-800">Price Corrected</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      This price has been corrected from {formatCurrency(offerItem.price)} to {formatCurrency(offerItem.correctedPrice)}
                    </p>
                    {offerItem.correctionReason && (
                      <p className="text-sm text-yellow-700 mt-1">
                        <strong>Reason:</strong> {offerItem.correctionReason}
                      </p>
                    )}
                    {offerItem.correctedBy && offerItem.correctedAt && (
                      <p className="text-xs text-yellow-600 mt-2">
                        Corrected by {offerItem.correctedBy.name} on {format(new Date(offerItem.correctedAt), "dd MMM yyyy HH:mm", { locale: id })}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Correction History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Correction History
          </CardTitle>
        </CardHeader>
        <CardContent>
          {correctionHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No price corrections have been made
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Original Price</TableHead>
                    <TableHead>Corrected Price</TableHead>
                    <TableHead>Change</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead>Corrected By</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {correctionHistory.map((log) => {
                    const change = log.correctedPrice - log.originalPrice;
                    return (
                      <TableRow key={log.id}>
                        <TableCell className="font-medium">
                          {formatCurrency(log.originalPrice)}
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(log.correctedPrice)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getPriceChangeIcon(change)}
                            <span className={`font-medium ${getPriceChangeColor(change)}`}>
                              {formatCurrency(Math.abs(change))}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">{log.reason}</p>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium">{log.correctedBy.name}</p>
                              <p className="text-xs text-gray-500">{log.correctedBy.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {format(new Date(log.correctedAt), "dd MMM yyyy HH:mm", { locale: id })}
                          </span>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
