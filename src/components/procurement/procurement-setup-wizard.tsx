"use client";

import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { ChevronLeft, ChevronRight, Package, Users, Settings, FileText } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

import { procurementSchema, type ProcurementInput } from "@/lib/validations/procurement";
import { ItemMasterField } from "@/components/ui/item-master-autocomplete";

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const wizardSteps: WizardStep[] = [
  {
    id: "general",
    title: "Informasi Umum",
    description: "Informasi dasar pengadaan",
    icon: FileText,
  },
  {
    id: "package",
    title: "Atur Paket Pengadaan",
    description: "Konfigurasi HPS, PPN, dan evaluasi",
    icon: Package,
  },
  {
    id: "team",
    title: "Susunan Tim",
    description: "Komite pengadaan dan evaluasi",
    icon: Users,
  },
  {
    id: "settings",
    title: "Pengaturan Lanjutan",
    description: "Tahapan dan konfigurasi final",
    icon: Settings,
  },
];

interface EvaluationTemplate {
  id: string;
  name: string;
  description?: string;
}

interface TeamTemplate {
  id: string;
  name: string;
  description?: string;
  members: Array<{
    userId: string;
    role: string;
    user: {
      name: string;
      email: string;
    };
  }>;
}

async function fetchEvaluationTemplates(): Promise<EvaluationTemplate[]> {
  const response = await fetch("/api/admin/evaluation-templates");
  if (!response.ok) throw new Error("Failed to fetch evaluation templates");
  const result = await response.json();
  return result.data;
}

async function fetchTeamTemplates(): Promise<TeamTemplate[]> {
  const response = await fetch("/api/admin/procurement-team-templates");
  if (!response.ok) throw new Error("Failed to fetch team templates");
  const result = await response.json();
  return result.data;
}

async function fetchUsers() {
  const response = await fetch("/api/admin/users?roles=PROCUREMENT_USER,COMMITTEE,APPROVER");
  if (!response.ok) throw new Error("Failed to fetch users");
  const result = await response.json();
  return result.data;
}

async function createProcurement(data: ProcurementInput) {
  const response = await fetch("/api/procurements", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to create procurement");
  }
  
  return response.json();
}

export function ProcurementSetupWizard() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [currentStep, setCurrentStep] = useState(0);
  const [useTeamTemplate, setUseTeamTemplate] = useState(false);
  const [selectedTeamTemplate, setSelectedTeamTemplate] = useState<string>("");

  const { data: evaluationTemplates } = useQuery({
    queryKey: ["evaluation-templates"],
    queryFn: fetchEvaluationTemplates,
  });

  const { data: teamTemplates } = useQuery({
    queryKey: ["team-templates"],
    queryFn: fetchTeamTemplates,
  });

  const { data: users } = useQuery({
    queryKey: ["users"],
    queryFn: fetchUsers,
  });

  const form = useForm<ProcurementInput>({
    resolver: zodResolver(procurementSchema),
    defaultValues: {
      title: "",
      type: "RFQ" as const,
      status: "DRAFT" as const,
      ownerEstimate: 0,
      showOwnerEstimateToVendor: false,
      evaluationMethod: "Lowest Price",
      workTimeUnit: "HARI" as const,
      hpsIncludesVat: false,
      vatRate: 11,
      items: [
        {
          itemMasterId: "",
          name: "",
          description: "",
          quantity: 1,
          unit: "",
          ownerEstimate: 0,
        },
      ],
      stages: [
        {
          name: "Pengumuman",
          sequence: 1,
          startDate: new Date(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
        {
          name: "Penawaran",
          sequence: 2,
          startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        },
        {
          name: "Evaluasi",
          sequence: 3,
          startDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
        },
      ],
      committee: [],
    },
  });

  const { fields: itemFields, append: appendItem, remove: removeItem } = useFieldArray({
    control: form.control,
    name: "items",
  });

  const { fields: committeeFields, append: appendCommittee, remove: removeCommittee } = useFieldArray({
    control: form.control,
    name: "committee",
  });

  const createMutation = useMutation({
    mutationFn: createProcurement,
    onSuccess: (data) => {
      toast.success("Procurement created successfully");
      queryClient.invalidateQueries({ queryKey: ["procurements"] });
      router.push(`/procurements/${data.data.id}`);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const watchedValues = form.watch();
  const hpsIncludesVat = form.watch("hpsIncludesVat") || false;
  const vatRate = form.watch("vatRate") || 11;
  const ownerEstimate = form.watch("ownerEstimate") || 0;

  // Calculate HPS values
  const baseHPS = hpsIncludesVat ? ownerEstimate / (1 + vatRate / 100) : ownerEstimate;
  const vatAmount = hpsIncludesVat ? ownerEstimate - baseHPS : (ownerEstimate * vatRate) / 100;
  const totalHPS = hpsIncludesVat ? ownerEstimate : ownerEstimate + vatAmount;

  const handleNext = async () => {
    const stepFields = getStepFields(currentStep);
    const isValid = await form.trigger(stepFields);
    
    if (isValid) {
      if (currentStep < wizardSteps.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        // Final step - submit form
        const formData = form.getValues();
        createMutation.mutate(formData);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepFields = (step: number): (keyof ProcurementInput)[] => {
    switch (step) {
      case 0:
        return ["title", "type"];
      case 1:
        return ["ownerEstimate", "evaluationMethod"];
      case 2:
        return ["committee"];
      case 3:
        return ["stages"];
      default:
        return [];
    }
  };

  const applyTeamTemplate = (templateId: string) => {
    const template = teamTemplates?.find(t => t.id === templateId);
    if (template) {
      // Clear existing committee
      while (committeeFields.length > 0) {
        removeCommittee(0);
      }
      
      // Add template members
      template.members.forEach(member => {
        appendCommittee({
          userId: member.userId,
          committeeRole: member.role,
        });
      });
      
      setSelectedTeamTemplate(templateId);
      toast.success(`Template "${template.name}" applied successfully`);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <GeneralInfoStep form={form} />;
      case 1:
        return (
          <PackageConfigStep 
            form={form} 
            evaluationTemplates={evaluationTemplates}
            baseHPS={baseHPS}
            vatAmount={vatAmount}
            totalHPS={totalHPS}
          />
        );
      case 2:
        return (
          <TeamSetupStep 
            form={form}
            users={users}
            teamTemplates={teamTemplates}
            useTeamTemplate={useTeamTemplate}
            setUseTeamTemplate={setUseTeamTemplate}
            selectedTeamTemplate={selectedTeamTemplate}
            applyTeamTemplate={applyTeamTemplate}
            committeeFields={committeeFields}
            appendCommittee={appendCommittee}
            removeCommittee={removeCommittee}
          />
        );
      case 3:
        return <AdvancedSettingsStep form={form} itemFields={itemFields} appendItem={appendItem} removeItem={removeItem} />;
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Progress Steps */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            {wizardSteps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center gap-3 ${isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                      isActive ? 'border-primary bg-primary/10' : 
                      isCompleted ? 'border-green-600 bg-green-50' : 
                      'border-gray-300'
                    }`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div className="hidden md:block">
                      <p className="font-medium">{step.title}</p>
                      <p className="text-sm text-gray-600">{step.description}</p>
                    </div>
                  </div>
                  {index < wizardSteps.length - 1 && (
                    <div className={`w-12 h-0.5 mx-4 ${isCompleted ? 'bg-green-600' : 'bg-gray-300'}`} />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle>{wizardSteps[currentStep].title}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(() => handleNext())}>
            {renderStepContent()}
          </form>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Sebelumnya
        </Button>
        
        <Button
          type="button"
          onClick={handleNext}
          disabled={createMutation.isPending}
        >
          {currentStep === wizardSteps.length - 1 ? (
            createMutation.isPending ? "Membuat..." : "Buat Pengadaan"
          ) : (
            <>
              Selanjutnya
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

// Step Components (to be implemented in separate files or here)
function GeneralInfoStep({ form }: { form: any }) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="title">Judul Pengadaan *</Label>
        <Input
          id="title"
          placeholder="Masukkan judul pengadaan"
          {...form.register("title")}
        />
        {form.formState.errors.title && (
          <p className="text-sm text-red-500">{form.formState.errors.title.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="type">Jenis Pengadaan *</Label>
        <Select value={form.watch("type")} onValueChange={(value: string) => form.setValue("type", value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="RFQ">Request for Quotation (RFQ)</SelectItem>
            <SelectItem value="TENDER">Tender</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

function PackageConfigStep({ 
  form, 
  evaluationTemplates, 
  baseHPS, 
  vatAmount, 
  totalHPS 
}: { 
  form: any; 
  evaluationTemplates?: EvaluationTemplate[];
  baseHPS: number;
  vatAmount: number;
  totalHPS: number;
}) {
  return (
    <div className="space-y-6">
      {/* HPS Configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Konfigurasi HPS (Harga Perkiraan Sendiri)</h3>
        
        <div className="space-y-2">
          <Label htmlFor="ownerEstimate">Nilai HPS *</Label>
          <Input
            id="ownerEstimate"
            type="number"
            step="0.01"
            placeholder="Masukkan nilai HPS"
            {...form.register("ownerEstimate", { valueAsNumber: true })}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="hpsIncludesVat"
            checked={form.watch("hpsIncludesVat")}
            onCheckedChange={(checked: boolean) => form.setValue("hpsIncludesVat", checked)}
          />
          <Label htmlFor="hpsIncludesVat">Termasuk PPN</Label>
        </div>

        {form.watch("ownerEstimate") > 0 && (
          <Card>
            <CardContent className="p-4">
              <h4 className="font-medium mb-2">Rincian HPS:</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>HPS Dasar:</span>
                  <span>{baseHPS.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}</span>
                </div>
                <div className="flex justify-between">
                  <span>PPN ({form.watch("vatRate")}%):</span>
                  <span>{vatAmount.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Total HPS:</span>
                  <span>{totalHPS.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Work Time Unit */}
      <div className="space-y-2">
        <Label htmlFor="workTimeUnit">Satuan Waktu Pekerjaan</Label>
        <Select 
          value={form.watch("workTimeUnit")} 
          onValueChange={(value: string) => form.setValue("workTimeUnit", value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="HARI">Hari</SelectItem>
            <SelectItem value="MINGGU">Minggu</SelectItem>
            <SelectItem value="BULAN">Bulan</SelectItem>
            <SelectItem value="TAHUN">Tahun</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Evaluation Template */}
      <div className="space-y-2">
        <Label htmlFor="evaluationTemplate">Template Evaluasi</Label>
        <Select 
          value={form.watch("evaluationTemplateId") || ""} 
          onValueChange={(value: string) => form.setValue("evaluationTemplateId", value || undefined)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Pilih template evaluasi (opsional)" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Tidak menggunakan template</SelectItem>
            {evaluationTemplates?.map((template) => (
              <SelectItem key={template.id} value={template.id}>
                {template.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

function TeamSetupStep({ 
  form, 
  users, 
  teamTemplates, 
  useTeamTemplate, 
  setUseTeamTemplate, 
  selectedTeamTemplate, 
  applyTeamTemplate,
  committeeFields,
  appendCommittee,
  removeCommittee
}: any) {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Susunan Tim Pengadaan</h3>
        
        {/* Template Selection */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="useTemplate"
              checked={useTeamTemplate}
              onCheckedChange={setUseTeamTemplate}
            />
            <Label htmlFor="useTemplate">Gunakan Template Tim</Label>
          </div>

          {useTeamTemplate && (
            <div className="space-y-2">
              <Label>Pilih Template Tim</Label>
              <Select 
                value={selectedTeamTemplate} 
                onValueChange={applyTeamTemplate}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih template tim" />
                </SelectTrigger>
                <SelectContent>
                  {teamTemplates?.map((template: any) => (
                    <SelectItem key={template.id} value={template.id}>
                      <div>
                        <p className="font-medium">{template.name}</p>
                        <p className="text-sm text-gray-600">{template.description}</p>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* Manual Team Setup */}
        {!useTeamTemplate && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Anggota Komite</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendCommittee({ userId: "", committeeRole: "" })}
              >
                Tambah Anggota
              </Button>
            </div>

            {committeeFields.map((field: any, index: number) => (
              <div key={field.id} className="flex gap-4 items-end">
                <div className="flex-1">
                  <Label>Anggota</Label>
                  <Select 
                    value={form.watch(`committee.${index}.userId`)} 
                    onValueChange={(value: string) => form.setValue(`committee.${index}.userId`, value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih anggota" />
                    </SelectTrigger>
                    <SelectContent>
                      {users?.map((user: any) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.name} ({user.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex-1">
                  <Label>Peran</Label>
                  <Input
                    placeholder="Contoh: Ketua, Anggota, Sekretaris"
                    {...form.register(`committee.${index}.committeeRole`)}
                  />
                </div>
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeCommittee(index)}
                >
                  Hapus
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Current Team Display */}
        {committeeFields.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <h4 className="font-medium mb-2">Tim Saat Ini:</h4>
              <div className="space-y-2">
                {committeeFields.map((field: any, index: number) => {
                  const userId = form.watch(`committee.${index}.userId`);
                  const role = form.watch(`committee.${index}.committeeRole`);
                  const user = users?.find((u: any) => u.id === userId);
                  
                  return (
                    <div key={field.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div>
                        <p className="font-medium">{user?.name || "Belum dipilih"}</p>
                        <p className="text-sm text-gray-600">{role || "Peran belum diisi"}</p>
                      </div>
                      <Badge variant="outline">{role || "No Role"}</Badge>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

function AdvancedSettingsStep({ form, itemFields, appendItem, removeItem }: any) {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Item Pengadaan</h3>
        
        <div className="space-y-4">
          {itemFields.map((field: any, index: number) => (
            <Card key={field.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium">Item {index + 1}</h4>
                  {itemFields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeItem(index)}
                    >
                      Hapus
                    </Button>
                  )}
                </div>
                
                <div className="grid grid-cols-1 gap-4">
                  <ItemMasterField
                    label="Item"
                    value={form.watch(`items.${index}.itemMasterId`)}
                    onItemSelect={(item) => {
                      if (item) {
                        form.setValue(`items.${index}.itemMasterId`, item.id);
                        form.setValue(`items.${index}.name`, item.name);
                        form.setValue(`items.${index}.unit`, item.unit);
                        form.setValue(`items.${index}.description`, item.description || "");
                      } else {
                        form.setValue(`items.${index}.itemMasterId`, "");
                        form.setValue(`items.${index}.name`, "");
                        form.setValue(`items.${index}.unit`, "");
                        form.setValue(`items.${index}.description`, "");
                      }
                    }}
                    onManualEntry={(data) => {
                      form.setValue(`items.${index}.itemMasterId`, "");
                      form.setValue(`items.${index}.name`, data.name);
                      form.setValue(`items.${index}.unit`, data.unit);
                      form.setValue(`items.${index}.description`, data.description || "");
                    }}
                    placeholder="Search for items or create new"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  
                  <div className="space-y-2">
                    <Label>Kuantitas *</Label>
                    <Input
                      type="number"
                      step="0.01"
                      {...form.register(`items.${index}.quantity`, { valueAsNumber: true })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Estimasi Harga *</Label>
                    <Input
                      type="number"
                      step="0.01"
                      {...form.register(`items.${index}.ownerEstimate`, { valueAsNumber: true })}
                    />
                  </div>
                  
                  <div className="md:col-span-2 space-y-2">
                    <Label>Deskripsi</Label>
                    <Textarea
                      placeholder="Deskripsi item"
                      {...form.register(`items.${index}.description`)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          <Button
            type="button"
            variant="outline"
            onClick={() => appendItem({
              itemMasterId: "",
              name: "",
              description: "",
              quantity: 1,
              unit: "",
              ownerEstimate: 0,
            })}
          >
            Tambah Item
          </Button>
        </div>
      </div>
    </div>
  );
}
