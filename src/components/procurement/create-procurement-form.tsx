"use client";

import { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { Plus, Trash2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { procurementSchema, type ProcurementInput } from "@/lib/validations/procurement";

interface User {
  id: string;
  name: string;
  email: string;
  roles: string[];
}

async function fetchUsers(): Promise<User[]> {
  const response = await fetch("/api/users?roles=ADMIN,PROCUREMENT_USER,APPROVER");
  if (!response.ok) {
    throw new Error("Failed to fetch users");
  }
  const result = await response.json();
  return result.data;
}

async function createProcurement(data: ProcurementInput) {
  const response = await fetch("/api/procurements", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to create procurement");
  }

  return response.json();
}

export function CreateProcurementForm() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);

  const form = useForm<ProcurementInput>({
    resolver: zodResolver(procurementSchema) as any,
    defaultValues: {
      title: "",
      type: "RFQ",
      status: "DRAFT",
      ownerEstimate: 0,
      showOwnerEstimateToVendor: false,
      evaluationMethod: "Lowest Price",
      items: [
        {
          name: "",
          description: "",
          quantity: 1,
          unit: "",
          ownerEstimate: 0,
        },
      ],
      stages: [
        {
          name: "Pengumuman",
          sequence: 1,
          startDate: new Date(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
        {
          name: "Penawaran",
          sequence: 2,
          startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        },
        {
          name: "Evaluasi",
          sequence: 3,
          startDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
        },
      ],
      committee: [],
    },
  });

  const {
    fields: itemFields,
    append: appendItem,
    remove: removeItem,
  } = useFieldArray({
    control: form.control,
    name: "items",
  });

  const {
    fields: stageFields,
    append: appendStage,
    remove: removeStage,
  } = useFieldArray({
    control: form.control,
    name: "stages",
  });

  const {
    fields: committeeFields,
    append: appendCommittee,
    remove: removeCommittee,
  } = useFieldArray({
    control: form.control,
    name: "committee",
  });

  const { data: users = [] } = useQuery({
    queryKey: ["users"],
    queryFn: fetchUsers,
  });

  const createMutation = useMutation({
    mutationFn: createProcurement,
    onSuccess: (data) => {
      router.push(`/procurements/${data.data.id}`);
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const onSubmit = (data: ProcurementInput) => {
    setError(null);
    createMutation.mutate(data);
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <form onSubmit={form.handleSubmit(onSubmit as any)} className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Dasar</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Judul Pengadaan *</Label>
                <Input
                  id="title"
                  placeholder="Contoh: Pengadaan Laptop untuk Kantor Pusat"
                  {...form.register("title")}
                />
                {form.formState.errors.title && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.title.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Jenis Pengadaan *</Label>
                <Select
                  value={form.watch("type")}
                  onValueChange={(value: string) => form.setValue("type", value as "TENDER" | "RFQ")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis pengadaan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="RFQ">Request for Quotation (RFQ)</SelectItem>
                    <SelectItem value="TENDER">Tender</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.type && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.type.message}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ownerEstimate">Estimasi Pemilik (Rp) *</Label>
                <Input
                  id="ownerEstimate"
                  type="number"
                  placeholder="100000000"
                  {...form.register("ownerEstimate", { valueAsNumber: true })}
                />
                {form.formState.errors.ownerEstimate && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.ownerEstimate.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="evaluationMethod">Metode Evaluasi *</Label>
                <Select
                  value={form.watch("evaluationMethod")}
                  onValueChange={(value: string) => form.setValue("evaluationMethod", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih metode evaluasi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Lowest Price">Harga Terendah</SelectItem>
                    <SelectItem value="Quality and Cost Based">Kualitas dan Biaya</SelectItem>
                    <SelectItem value="Technical and Price">Teknis dan Harga</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.evaluationMethod && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.evaluationMethod.message}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="showOwnerEstimateToVendor"
                checked={form.watch("showOwnerEstimateToVendor")}
                onCheckedChange={(checked: boolean) =>
                  form.setValue("showOwnerEstimateToVendor", checked as boolean)
                }
              />
              <Label htmlFor="showOwnerEstimateToVendor" className="text-sm">
                Tampilkan estimasi pemilik kepada vendor
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Item Pengadaan
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() =>
                  appendItem({
                    name: "",
                    description: "",
                    quantity: 1,
                    unit: "",
                    ownerEstimate: 0,
                  })
                }
              >
                <Plus className="h-4 w-4 mr-2" />
                Tambah Item
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {itemFields.map((field, index) => (
              <div key={field.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Item {index + 1}</h4>
                  {itemFields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeItem(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Nama Item *</Label>
                    <Input
                      placeholder="Contoh: Laptop Dell Latitude 5520"
                      {...form.register(`items.${index}.name`)}
                    />
                    {form.formState.errors.items?.[index]?.name && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.items[index]?.name?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Unit *</Label>
                    <Input
                      placeholder="Contoh: Unit, Pcs, Set"
                      {...form.register(`items.${index}.unit`)}
                    />
                    {form.formState.errors.items?.[index]?.unit && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.items[index]?.unit?.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Kuantitas *</Label>
                    <Input
                      type="number"
                      placeholder="1"
                      {...form.register(`items.${index}.quantity`, { valueAsNumber: true })}
                    />
                    {form.formState.errors.items?.[index]?.quantity && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.items[index]?.quantity?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Estimasi Harga (Rp) *</Label>
                    <Input
                      type="number"
                      placeholder="15000000"
                      {...form.register(`items.${index}.ownerEstimate`, { valueAsNumber: true })}
                    />
                    {form.formState.errors.items?.[index]?.ownerEstimate && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.items[index]?.ownerEstimate?.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Deskripsi</Label>
                  <Textarea
                    placeholder="Spesifikasi detail item..."
                    {...form.register(`items.${index}.description`)}
                  />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Stages */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Tahapan Pengadaan
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() =>
                  appendStage({
                    name: "",
                    sequence: stageFields.length + 1,
                    startDate: new Date(),
                    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                  })
                }
              >
                <Plus className="h-4 w-4 mr-2" />
                Tambah Tahapan
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {stageFields.map((field, index) => (
              <div key={field.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Tahapan {index + 1}</h4>
                  {stageFields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeStage(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Nama Tahapan *</Label>
                    <Input
                      placeholder="Contoh: Pengumuman, Penawaran, Evaluasi"
                      {...form.register(`stages.${index}.name`)}
                    />
                    {form.formState.errors.stages?.[index]?.name && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.stages[index]?.name?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Tanggal Mulai *</Label>
                    <Input
                      type="datetime-local"
                      {...form.register(`stages.${index}.startDate`, {
                        valueAsDate: true,
                      })}
                    />
                    {form.formState.errors.stages?.[index]?.startDate && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.stages[index]?.startDate?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Tanggal Selesai *</Label>
                    <Input
                      type="datetime-local"
                      {...form.register(`stages.${index}.endDate`, {
                        valueAsDate: true,
                      })}
                    />
                    {form.formState.errors.stages?.[index]?.endDate && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.stages[index]?.endDate?.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Committee */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Panitia Pengadaan
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() =>
                  appendCommittee({
                    userId: "",
                    committeeRole: "",
                  })
                }
              >
                <Plus className="h-4 w-4 mr-2" />
                Tambah Anggota
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {committeeFields.map((field, index) => (
              <div key={field.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Anggota {index + 1}</h4>
                  {committeeFields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeCommittee(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Pilih User *</Label>
                    <Select
                      value={form.watch(`committee.${index}.userId`)}
                      onValueChange={(value: string) => form.setValue(`committee.${index}.userId`, value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih user" />
                      </SelectTrigger>
                      <SelectContent>
                        {users.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {form.formState.errors.committee?.[index]?.userId && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.committee[index]?.userId?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Peran dalam Panitia *</Label>
                    <Select
                      value={form.watch(`committee.${index}.committeeRole`)}
                      onValueChange={(value: string) => form.setValue(`committee.${index}.committeeRole`, value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih peran" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Ketua">Ketua Panitia</SelectItem>
                        <SelectItem value="Sekretaris">Sekretaris</SelectItem>
                        <SelectItem value="Anggota Teknis">Anggota Teknis</SelectItem>
                        <SelectItem value="Anggota Administrasi">Anggota Administrasi</SelectItem>
                        <SelectItem value="Anggota Keuangan">Anggota Keuangan</SelectItem>
                      </SelectContent>
                    </Select>
                    {form.formState.errors.committee?.[index]?.committeeRole && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.committee[index]?.committeeRole?.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Save and Continue */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Batal
          </Button>
          <Button
            type="submit"
            disabled={createMutation.isPending}
          >
            {createMutation.isPending ? "Menyimpan..." : "Simpan Draft"}
          </Button>
        </div>
      </form>
    </div>
  );
}
