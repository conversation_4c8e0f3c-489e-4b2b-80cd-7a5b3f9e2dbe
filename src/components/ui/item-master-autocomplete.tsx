"use client";

import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Check, ChevronsUpDown, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface ItemMaster {
  id: string;
  itemCode: string;
  name: string;
  description?: string;
  unit: string;
  category: string;
  specifications?: Record<string, any>;
}

interface ItemMasterAutocompleteProps {
  value?: string;
  onSelect: (item: ItemMaster | null) => void;
  onCreateNew?: (itemName: string) => void;
  placeholder?: string;
  disabled?: boolean;
  category?: string;
}

async function searchItemMasters(query: string, category?: string): Promise<ItemMaster[]> {
  if (query.length < 2) return [];
  
  const params = new URLSearchParams();
  params.append("q", query);
  if (category) params.append("category", category);
  
  const response = await fetch(`/api/admin/item-master/search?${params}`);
  if (!response.ok) throw new Error("Failed to search items");
  const result = await response.json();
  return result.data.data;
}

export function ItemMasterAutocomplete({
  value,
  onSelect,
  onCreateNew,
  placeholder = "Search items...",
  disabled = false,
  category,
}: ItemMasterAutocompleteProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItem, setSelectedItem] = useState<ItemMaster | null>(null);

  const { data: items = [], isLoading } = useQuery({
    queryKey: ["item-master-search", searchQuery, category],
    queryFn: () => searchItemMasters(searchQuery, category),
    enabled: searchQuery.length >= 2,
    staleTime: 30000,
  });

  // Find selected item when value changes
  useEffect(() => {
    if (value && items.length > 0) {
      const item = items.find(i => i.id === value);
      if (item) {
        setSelectedItem(item);
      }
    } else if (!value) {
      setSelectedItem(null);
    }
  }, [value, items]);

  const handleSelect = (item: ItemMaster) => {
    setSelectedItem(item);
    onSelect(item);
    setOpen(false);
    setSearchQuery("");
  };

  const handleClear = () => {
    setSelectedItem(null);
    onSelect(null);
    setSearchQuery("");
  };

  const handleCreateNew = () => {
    if (onCreateNew && searchQuery.trim()) {
      onCreateNew(searchQuery.trim());
      setOpen(false);
      setSearchQuery("");
    }
  };

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            {selectedItem ? (
              <div className="flex items-center gap-2 flex-1 text-left">
                <span className="font-medium">{selectedItem.name}</span>
                <Badge variant="outline" className="text-xs">
                  {selectedItem.category}
                </Badge>
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Type to search items..."
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
            <CommandEmpty>
              {isLoading ? (
                "Searching..."
              ) : searchQuery.length < 2 ? (
                "Type at least 2 characters to search"
              ) : (
                <div className="p-2 space-y-2">
                  <p>No items found.</p>
                  {onCreateNew && searchQuery.trim() && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCreateNew}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create "{searchQuery.trim()}"
                    </Button>
                  )}
                </div>
              )}
            </CommandEmpty>
            <CommandGroup>
              {items.map((item) => (
                <CommandItem
                  key={item.id}
                  value={item.id}
                  onSelect={() => handleSelect(item)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedItem?.id === item.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {item.itemCode} • {item.unit}
                      </p>
                      {item.description && (
                        <p className="text-xs text-muted-foreground">
                          {item.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {item.category}
                  </Badge>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedItem && (
        <div className="flex items-center justify-between p-2 bg-muted rounded-md">
          <div className="text-sm">
            <p className="font-medium">{selectedItem.name}</p>
            <p className="text-muted-foreground">
              Code: {selectedItem.itemCode} • Unit: {selectedItem.unit}
            </p>
            {selectedItem.description && (
              <p className="text-muted-foreground">{selectedItem.description}</p>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={handleClear}>
            Clear
          </Button>
        </div>
      )}
    </div>
  );
}

interface ItemMasterFieldProps {
  label: string;
  value?: string;
  onItemSelect: (item: ItemMaster | null) => void;
  onManualEntry?: (data: { name: string; unit: string; description?: string }) => void;
  placeholder?: string;
  disabled?: boolean;
  category?: string;
  required?: boolean;
}

export function ItemMasterField({
  label,
  value,
  onItemSelect,
  onManualEntry,
  placeholder,
  disabled = false,
  category,
  required = false,
}: ItemMasterFieldProps) {
  const [useManualEntry, setUseManualEntry] = useState(false);
  const [manualData, setManualData] = useState({
    name: "",
    unit: "",
    description: "",
  });

  const handleCreateNew = (itemName: string) => {
    setManualData(prev => ({ ...prev, name: itemName }));
    setUseManualEntry(true);
  };

  const handleManualSubmit = () => {
    if (onManualEntry && manualData.name.trim() && manualData.unit.trim()) {
      onManualEntry(manualData);
      setUseManualEntry(false);
      setManualData({ name: "", unit: "", description: "" });
    }
  };

  const handleCancelManual = () => {
    setUseManualEntry(false);
    setManualData({ name: "", unit: "", description: "" });
  };

  if (useManualEntry) {
    return (
      <div className="space-y-4">
        <Label>{label} (Manual Entry) {required && "*"}</Label>
        <div className="space-y-3 p-4 border rounded-lg bg-muted/50">
          <div className="space-y-2">
            <Label htmlFor="manual-name">Item Name *</Label>
            <Input
              id="manual-name"
              value={manualData.name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                setManualData(prev => ({ ...prev, name: e.target.value }))
              }
              placeholder="Enter item name"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="manual-unit">Unit *</Label>
            <Input
              id="manual-unit"
              value={manualData.unit}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                setManualData(prev => ({ ...prev, unit: e.target.value }))
              }
              placeholder="e.g., pcs, kg, meter"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="manual-description">Description</Label>
            <Input
              id="manual-description"
              value={manualData.description}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                setManualData(prev => ({ ...prev, description: e.target.value }))
              }
              placeholder="Optional description"
            />
          </div>
          
          <div className="flex gap-2">
            <Button onClick={handleManualSubmit} size="sm">
              Use This Item
            </Button>
            <Button variant="outline" onClick={handleCancelManual} size="sm">
              Cancel
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label>{label} {required && "*"}</Label>
      <ItemMasterAutocomplete
        value={value}
        onSelect={onItemSelect}
        onCreateNew={onManualEntry ? handleCreateNew : undefined}
        placeholder={placeholder}
        disabled={disabled}
        category={category}
      />
    </div>
  );
}
