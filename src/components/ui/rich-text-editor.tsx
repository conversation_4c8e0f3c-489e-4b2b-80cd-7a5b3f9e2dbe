"use client";

import React, { useRef, useEffect, useState, useMemo, useCallback } from "react";
import { cn } from "@/lib/utils";

// Singleton module loader
class CKEditorLoader {
  private static instance: CKEditorLoader;
  private modules: { CKEditor: any; ClassicEditor: any } | null = null;
  private loading = false;
  private callbacks: Array<(modules: any) => void> = [];

  static getInstance(): CKEditorLoader {
    if (!CKEditorLoader.instance) {
      CKEditorLoader.instance = new CKEditorLoader();
    }
    return CKEditorLoader.instance;
  }

  async loadModules(): Promise<{ CKEditor: any; ClassicEditor: any }> {
    if (this.modules) {
      return this.modules;
    }

    if (this.loading) {
      return new Promise((resolve) => {
        this.callbacks.push(resolve);
      });
    }

    this.loading = true;

    try {
      const [ckeditorModule, classicEditorModule] = await Promise.all([
        import("@ckeditor/ckeditor5-react"),
        import("@ckeditor/ckeditor5-build-classic")
      ]);

      this.modules = {
        CKEditor: ckeditorModule.CKEditor,
        ClassicEditor: classicEditorModule.default,
      };

      // Notify all waiting callbacks
      this.callbacks.forEach(callback => callback(this.modules!));
      this.callbacks = [];

      console.log("🔍 DEBUG RICH EDITOR: CKEditor modules loaded (singleton)");
      return this.modules;
    } catch (error) {
      console.error("Failed to load CKEditor:", error);
      this.loading = false;
      throw error;
    }
  }

  getModules() {
    return this.modules;
  }
}

interface RichTextEditorProps {
  value: string;
  onChange: (data: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  onReady?: (editor: any) => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

function RichTextEditorComponent({
  value,
  onChange,
  placeholder = "Start writing...",
  className,
  disabled = false,
  onReady,
  onFocus,
  onBlur,
}: RichTextEditorProps) {
  const editorRef = useRef<any>(null);
  const renderCountRef = useRef(0);
  const initializedRef = useRef(false);
  const isChangingContentRef = useRef(false);
  const lastValueRef = useRef(value);
  const [modules, setModules] = useState<{ CKEditor: any; ClassicEditor: any } | null>(null);
  const [isClient, setIsClient] = useState(false);

  // Track component renders
  renderCountRef.current += 1;
  console.log("🔍 DEBUG RICH EDITOR: Component render #", renderCountRef.current);

  // Stable change handler to prevent recreation
  const handleChange = useCallback((data: string) => {
    if (isChangingContentRef.current) return;

    console.log("🔍 DEBUG RICH EDITOR: Content changed, length:", data.length);

    // Prevent recursive updates
    isChangingContentRef.current = true;
    onChange(data);
    setTimeout(() => {
      isChangingContentRef.current = false;
    }, 0);
  }, [onChange]);

  // Prevent re-renders when value changes from external source
  const stableValue = useRef(value);
  useEffect(() => {
    if (!isChangingContentRef.current) {
      stableValue.current = value;
    }
  }, [value]);

  // Ensure we're on client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load CKEditor modules using singleton
  useEffect(() => {
    if (!isClient || initializedRef.current) return;

    initializedRef.current = true;
    const loader = CKEditorLoader.getInstance();

    // Check if modules are already loaded
    const existingModules = loader.getModules();
    if (existingModules) {
      setModules(existingModules);
      return;
    }

    // Load modules
    loader.loadModules().then((loadedModules) => {
      setModules(loadedModules);
    }).catch((error) => {
      console.error("Failed to load CKEditor modules:", error);
      initializedRef.current = false;
    });
  }, [isClient]);

  // Memoize upload adapter to prevent recreation
  const uploadAdapterPlugin = useMemo(() => {
    return function MyCustomUploadAdapterPlugin(editor: any) {
      editor.plugins.get("FileRepository").createUploadAdapter = (loader: any) => {
        return new MyUploadAdapter(loader);
      };
    };
  }, []);

  // Memoize editor configuration to prevent recreation
  const editorConfiguration = useMemo(() => ({
    toolbar: {
      items: [
        "heading",
        "|",
        "bold",
        "italic",
        "underline",
        "strikethrough",
        "|",
        "bulletedList",
        "numberedList",
        "|",
        "outdent",
        "indent",
        "|",
        "link",
        "imageUpload",
        "insertTable",
        "|",
        "blockQuote",
        "codeBlock",
        "|",
        "undo",
        "redo",
        "|",
        "alignment",
        "fontSize",
        "fontColor",
        "fontBackgroundColor",
      ],
    },
    language: "en",
    image: {
      toolbar: [
        "imageTextAlternative",
        "imageStyle:inline",
        "imageStyle:block",
        "imageStyle:side",
        "|",
        "toggleImageCaption",
        "imageStyle:alignLeft",
        "imageStyle:alignCenter",
        "imageStyle:alignRight",
      ],
      resizeOptions: [
        {
          name: "resizeImage:original",
          label: "Original",
          value: null,
        },
        {
          name: "resizeImage:50",
          label: "50%",
          value: "50",
        },
        {
          name: "resizeImage:75",
          label: "75%",
          value: "75",
        },
      ],
    },
    table: {
      contentToolbar: [
        "tableColumn",
        "tableRow",
        "mergeTableCells",
        "tableCellProperties",
        "tableProperties",
      ],
    },
    placeholder,
    height: 500, // Set the height of the editing area to 500px
    extraPlugins: [uploadAdapterPlugin],
    removePlugins: ["MediaEmbedToolbar"],
  }), [placeholder, uploadAdapterPlugin]);

  // Show loading state while modules are loading
  if (!isClient || !modules) {
    return (
      <div className={cn("min-h-32 w-full rounded-md border bg-transparent p-4", className)}>
        <div className="text-gray-500">Loading editor...</div>
      </div>
    );
  }

  const { CKEditor, ClassicEditor } = modules;



  return (
    <div className={cn("w-full", className)}>
      <CKEditor
        editor={ClassicEditor}
        config={editorConfiguration}
        data={value}
        disabled={disabled}
        onReady={(editor: any) => {
          editorRef.current = editor;
          console.log("🔍 DEBUG RICH EDITOR: Editor is ready");

          // Apply custom height to the editing area
          setTimeout(() => {
            const editableElement = editor.ui.getEditableElement();
            if (editableElement) {
              editableElement.style.minHeight = '500px';
              editableElement.style.height = 'auto';
            }
          }, 100);

          if (onReady) onReady(editor);
        }}
        onChange={(event: any, editor: any) => {
          const data = editor.getData();
          handleChange(data);
        }}
        onFocus={(event: any, editor: any) => {
          console.log("🔍 DEBUG RICH EDITOR: Editor focused");
          if (onFocus) onFocus();
        }}
        onBlur={(event: any, editor: any) => {
          console.log("🔍 DEBUG RICH EDITOR: Editor blurred");
          if (onBlur) onBlur();
        }}
      />
    </div>
  );
}

// Export memoized component to prevent unnecessary re-renders
export const RichTextEditor = React.memo(RichTextEditorComponent, (prevProps, nextProps) => {
  // Only re-render if value, placeholder, className, or disabled changes
  return (
    prevProps.value === nextProps.value &&
    prevProps.placeholder === nextProps.placeholder &&
    prevProps.className === nextProps.className &&
    prevProps.disabled === nextProps.disabled
  );
});

// Upload adapter class outside component to prevent recreation
class MyUploadAdapter {
  private loader: any;

  constructor(loader: any) {
    this.loader = loader;
  }

  upload() {
    return this.loader.file.then((file: File) =>
      new Promise((resolve, reject) => {
        const formData = new FormData();
        formData.append("file", file);

        // Use the existing upload endpoint
        fetch("/api/upload", {
          method: "POST",
          body: formData,
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        })
          .then((response) => response.json())
          .then((result) => {
            if (result.success) {
              resolve({
                default: result.data.url,
              });
            } else {
              reject(result.error || "Upload failed");
            }
          })
          .catch((error) => {
            reject(error);
          });
      })
    );
  }

  abort() {
    // Implement abort logic if needed
  }
}