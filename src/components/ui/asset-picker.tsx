"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Search, Image, FileText, X, Check } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface PublicAsset {
  id: string;
  filename: string;
  originalName: string;
  url: string;
  title?: string;
  description?: string;
  category: string;
  size: number;
  mimeType: string;
  uploadedAt: string;
}

interface AssetListResponse {
  assets: string[];
  count: number;
}

async function fetchPublicAssets(category?: string): Promise<AssetListResponse> {
  const params = new URLSearchParams();
  if (category) params.append("category", category);

  const token = localStorage.getItem("auth-token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`/api/public-assets?${params}`, {
    headers,
  });
  if (!response.ok) {
    throw new Error("Failed to fetch public assets");
  }
  const result = await response.json();
  return result.data;
}

interface AssetPickerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (assetUrl: string) => void;
  allowedCategories?: string[];
  title?: string;
  description?: string;
}

const ASSET_CATEGORIES = [
  { value: "all", label: "Semua" },
  { value: "news", label: "Berita" },
  { value: "announcements", label: "Pengumuman" },
  { value: "banners", label: "Banner" },
  { value: "logos", label: "Logo" },
  { value: "documents", label: "Dokumen" },
];

export function AssetPicker({
  isOpen,
  onClose,
  onSelect,
  allowedCategories,
  title = "Pilih Asset",
  description = "Pilih asset yang telah diupload untuk digunakan dalam konten"
}: AssetPickerProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedAsset, setSelectedAsset] = useState<string | null>(null);

  // Filter categories based on allowedCategories prop
  const availableCategories = allowedCategories 
    ? ASSET_CATEGORIES.filter(cat => cat.value === "all" || allowedCategories.includes(cat.value))
    : ASSET_CATEGORIES;

  const { data: assets, isLoading, error } = useQuery({
    queryKey: ["public-assets", selectedCategory === "all" ? undefined : selectedCategory],
    queryFn: () => fetchPublicAssets(selectedCategory === "all" ? undefined : selectedCategory),
    enabled: isOpen,
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getAssetCategory = (key: string) => {
    const parts = key.split("/");
    return parts[1] || "unknown";
  };

  const getAssetInfo = (key: string) => {
    const parts = key.split("/");
    const filename = parts[parts.length - 1] || key;
    const category = getAssetCategory(key);
    return { filename, category };
  };

  const filteredAssets = assets?.assets?.filter(assetKey => {
    if (!searchQuery) return true;
    const { filename } = getAssetInfo(assetKey);
    return filename.toLowerCase().includes(searchQuery.toLowerCase());
  }) || [];

  const handleSelect = () => {
    if (selectedAsset) {
      onSelect(`/assets/${selectedAsset}`);
      onClose();
      setSelectedAsset(null);
    }
  };

  const handleClose = () => {
    onClose();
    setSelectedAsset(null);
    setSearchQuery("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <p className="text-sm text-gray-600">{description}</p>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Cari asset..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Tabs */}
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid w-full grid-cols-6">
              {availableCategories.map((category) => (
                <TabsTrigger key={category.value} value={category.value}>
                  {category.label}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={selectedCategory} className="mt-4">
              {isLoading && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {[...Array(8)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="aspect-square bg-gray-200 rounded mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {error && (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Gagal memuat asset</p>
                </div>
              )}

              {!isLoading && !error && filteredAssets.length === 0 && (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    {searchQuery ? "Tidak ada asset yang ditemukan" : "Belum ada asset"}
                  </p>
                </div>
              )}

              {!isLoading && !error && filteredAssets.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                  {filteredAssets.map((assetKey) => {
                    const { filename, category } = getAssetInfo(assetKey);
                    const isImage = assetKey.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i);
                    const isSelected = selectedAsset === assetKey;

                    return (
                      <Card 
                        key={assetKey} 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          isSelected ? "ring-2 ring-blue-500" : ""
                        }`}
                        onClick={() => setSelectedAsset(assetKey)}
                      >
                        <CardContent className="p-3">
                          <div className="relative">
                            {isImage ? (
                              <div className="aspect-square bg-gray-100 rounded mb-2 overflow-hidden">
                                <img
                                  src={`/assets/${assetKey}`}
                                  alt={filename}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className="aspect-square bg-gray-100 rounded mb-2 flex items-center justify-center">
                                <FileText className="h-8 w-8 text-gray-400" />
                              </div>
                            )}
                            
                            {isSelected && (
                              <div className="absolute top-1 right-1 bg-blue-500 text-white rounded-full p-1">
                                <Check className="h-3 w-3" />
                              </div>
                            )}
                          </div>
                          
                          <div className="space-y-1">
                            <p className="text-xs font-medium truncate" title={filename}>
                              {filename}
                            </p>
                            <Badge variant="outline" className="text-xs">
                              {category}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Batal
            </Button>
            <Button onClick={handleSelect} disabled={!selectedAsset}>
              Pilih Asset
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
