/**
 * JSON utility functions for safe JSON operations
 * Provides type-safe JSON parsing, stringifying, and validation
 */

/**
 * Safely parse JSON string with error handling
 */
export function safeJsonParse<T = any>(jsonString: string, defaultValue?: T): T | null {
  try {
    const parsed = JSON.parse(jsonString);
    return parsed as T;
  } catch (error) {
    console.warn('Failed to parse JSON:', error);
    return defaultValue !== undefined ? defaultValue : null;
  }
}

/**
 * Safely stringify object to JSON with error handling
 */
export function safeJsonStringify(obj: any, space?: number): string | null {
  try {
    return JSON.stringify(obj, null, space);
  } catch (error) {
    console.warn('Failed to stringify JSON:', error);
    return null;
  }
}

/**
 * Check if a string is valid JSON
 */
export function isValidJson(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}

/**
 * Deep clone an object using JSON serialization
 * Note: This will lose functions, undefined values, and symbols
 */
export function deepCloneJson<T>(obj: T): T | null {
  try {
    return JSON.parse(JSON.stringify(obj));
  } catch (error) {
    console.warn('Failed to deep clone object:', error);
    return null;
  }
}

/**
 * Merge two JSON objects deeply
 */
export function mergeJsonObjects<T extends Record<string, any>>(
  target: T,
  source: Partial<T>
): T {
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key];
      const targetValue = result[key];
      
      if (
        sourceValue &&
        typeof sourceValue === 'object' &&
        !Array.isArray(sourceValue) &&
        targetValue &&
        typeof targetValue === 'object' &&
        !Array.isArray(targetValue)
      ) {
        result[key] = mergeJsonObjects(targetValue, sourceValue);
      } else {
        result[key] = sourceValue as T[Extract<keyof T, string>];
      }
    }
  }
  
  return result;
}

/**
 * Extract specific keys from a JSON object
 */
export function pickJsonKeys<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  
  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }
  
  return result;
}

/**
 * Omit specific keys from a JSON object
 */
export function omitJsonKeys<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj };
  
  for (const key of keys) {
    delete result[key];
  }
  
  return result;
}

/**
 * Flatten nested JSON object
 */
export function flattenJson(
  obj: Record<string, any>,
  prefix: string = '',
  separator: string = '.'
): Record<string, any> {
  const flattened: Record<string, any> = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}${separator}${key}` : key;
      const value = obj[key];
      
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        Object.assign(flattened, flattenJson(value, newKey, separator));
      } else {
        flattened[newKey] = value;
      }
    }
  }
  
  return flattened;
}

/**
 * Unflatten a flattened JSON object
 */
export function unflattenJson(
  obj: Record<string, any>,
  separator: string = '.'
): Record<string, any> {
  const result: Record<string, any> = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const keys = key.split(separator);
      let current = result;
      
      for (let i = 0; i < keys.length - 1; i++) {
        const k = keys[i];
        if (!(k in current)) {
          current[k] = {};
        }
        current = current[k];
      }
      
      current[keys[keys.length - 1]] = obj[key];
    }
  }
  
  return result;
}

/**
 * Validate JSON schema (basic validation)
 */
export function validateJsonSchema(
  data: any,
  schema: {
    required?: string[];
    properties?: Record<string, { type: string; required?: boolean }>;
  }
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check required fields
  if (schema.required) {
    for (const field of schema.required) {
      if (!(field in data) || data[field] === undefined || data[field] === null) {
        errors.push(`Required field '${field}' is missing`);
      }
    }
  }
  
  // Check property types
  if (schema.properties) {
    for (const [field, fieldSchema] of Object.entries(schema.properties)) {
      if (field in data) {
        const value = data[field];
        const expectedType = fieldSchema.type;
        const actualType = Array.isArray(value) ? 'array' : typeof value;
        
        if (actualType !== expectedType) {
          errors.push(`Field '${field}' should be of type '${expectedType}', got '${actualType}'`);
        }
      } else if (fieldSchema.required) {
        errors.push(`Required field '${field}' is missing`);
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Convert object to JSON-compatible format (remove functions, undefined, etc.)
 */
export function toJsonCompatible(obj: any): any {
  if (obj === null || obj === undefined) {
    return null;
  }
  
  if (typeof obj === 'function') {
    return undefined;
  }
  
  if (typeof obj !== 'object') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(toJsonCompatible).filter(item => item !== undefined);
  }
  
  const result: Record<string, any> = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = toJsonCompatible(obj[key]);
      if (value !== undefined) {
        result[key] = value;
      }
    }
  }
  
  return result;
}

/**
 * Compare two JSON objects for equality
 */
export function jsonEquals(a: any, b: any): boolean {
  try {
    return JSON.stringify(a) === JSON.stringify(b);
  } catch {
    return false;
  }
}

/**
 * Get the size of a JSON object in bytes
 */
export function getJsonSize(obj: any): number {
  try {
    return new Blob([JSON.stringify(obj)]).size;
  } catch {
    return 0;
  }
}

/**
 * Compress JSON by removing whitespace and unnecessary characters
 */
export function compressJson(obj: any): string {
  try {
    return JSON.stringify(obj);
  } catch (error) {
    console.warn('Failed to compress JSON:', error);
    return '';
  }
}

/**
 * Pretty print JSON with indentation
 */
export function prettyPrintJson(obj: any, indent: number = 2): string {
  try {
    return JSON.stringify(obj, null, indent);
  } catch (error) {
    console.warn('Failed to pretty print JSON:', error);
    return '';
  }
}

/**
 * Convert data to Prisma-compatible JSON format
 * Ensures the data is serializable and compatible with Prisma's JsonValue type
 */
export function toPrismaJson(data: any): any {
  if (data === null || data === undefined) {
    return null;
  }

  if (typeof data === 'string' || typeof data === 'number' || typeof data === 'boolean') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(toPrismaJson);
  }

  if (typeof data === 'object') {
    const result: Record<string, any> = {};
    for (const [key, value] of Object.entries(data)) {
      result[key] = toPrismaJson(value);
    }
    return result;
  }

  // For functions, symbols, etc., return null
  return null;
}
