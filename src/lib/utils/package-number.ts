/**
 * Utility functions for generating package numbers
 * Provides consistent package number generation across the application
 */

import { prisma } from '@/lib/db';

/**
 * Generate a unique package number
 * Format: PKG-YYYY-MM-NNNN
 * Where YYYY is year, MM is month, NNNN is sequential number
 */
export async function generatePackageNumber(): Promise<string> {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const prefix = `PKG-${year}-${month}`;

  try {
    // Find the highest existing package number for this month
    const existingPackages = await prisma.procurementPackage.findMany({
      where: {
        packageNumber: {
          startsWith: prefix,
        },
      },
      select: {
        packageNumber: true,
      },
      orderBy: {
        packageNumber: 'desc',
      },
      take: 1,
    });

    let nextSequence = 1;

    if (existingPackages.length > 0 && existingPackages[0].packageNumber) {
      // Extract the sequence number from the last package
      const lastPackageNumber = existingPackages[0].packageNumber;
      const sequencePart = lastPackageNumber.split('-').pop();
      
      if (sequencePart && !isNaN(parseInt(sequencePart))) {
        nextSequence = parseInt(sequencePart) + 1;
      }
    }

    // Format sequence number with leading zeros (4 digits)
    const sequenceStr = String(nextSequence).padStart(4, '0');
    
    return `${prefix}-${sequenceStr}`;
  } catch (error) {
    console.error('Error generating package number:', error);
    
    // Fallback: use timestamp-based number
    const timestamp = Date.now().toString().slice(-4);
    return `${prefix}-${timestamp}`;
  }
}

/**
 * Validate package number format
 */
export function validatePackageNumber(packageNumber: string): boolean {
  const pattern = /^PKG-\d{4}-\d{2}-\d{4}$/;
  return pattern.test(packageNumber);
}

/**
 * Parse package number components
 */
export function parsePackageNumber(packageNumber: string): {
  year: number;
  month: number;
  sequence: number;
} | null {
  if (!validatePackageNumber(packageNumber)) {
    return null;
  }

  const parts = packageNumber.split('-');
  return {
    year: parseInt(parts[1]),
    month: parseInt(parts[2]),
    sequence: parseInt(parts[3]),
  };
}

/**
 * Generate package number synchronously (for cases where async is not possible)
 * Uses current timestamp as fallback
 */
export function generatePackageNumberSync(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const timestamp = Date.now().toString().slice(-4);
  
  return `PKG-${year}-${month}-${timestamp}`;
}

/**
 * Check if package number already exists
 */
export async function isPackageNumberExists(packageNumber: string): Promise<boolean> {
  try {
    const existing = await prisma.procurementPackage.findFirst({
      where: {
        packageNumber,
      },
      select: {
        id: true,
      },
    });

    return !!existing;
  } catch (error) {
    console.error('Error checking package number existence:', error);
    return false;
  }
}

/**
 * Generate unique package number with retry logic
 */
export async function generateUniquePackageNumber(maxRetries: number = 5): Promise<string> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    const packageNumber = await generatePackageNumber();
    
    const exists = await isPackageNumberExists(packageNumber);
    if (!exists) {
      return packageNumber;
    }

    // If collision detected, wait a bit and try again
    if (attempt < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, 100 * attempt));
    }
  }

  // Final fallback with random component
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  
  return `PKG-${year}-${month}-${random}`;
}

/**
 * Get package statistics for a given period
 */
export async function getPackageStats(year: number, month?: number): Promise<{
  total: number;
  byStatus: Record<string, number>;
  lastPackageNumber?: string;
}> {
  try {
    const prefix = month 
      ? `PKG-${year}-${String(month).padStart(2, '0')}`
      : `PKG-${year}`;

    const packages = await prisma.procurementPackage.findMany({
      where: {
        packageNumber: {
          startsWith: prefix,
        },
      },
      select: {
        packageNumber: true,
        status: true,
      },
      orderBy: {
        packageNumber: 'desc',
      },
    });

    const byStatus: Record<string, number> = {};
    packages.forEach(pkg => {
      const status = pkg.status ?? 'UNKNOWN';
      byStatus[status] = (byStatus[status] || 0) + 1;
    });

    return {
      total: packages.length,
      byStatus,
      lastPackageNumber: packages[0]?.packageNumber || undefined,
    };
  } catch (error) {
    console.error('Error getting package stats:', error);
    return {
      total: 0,
      byStatus: {},
    };
  }
}
