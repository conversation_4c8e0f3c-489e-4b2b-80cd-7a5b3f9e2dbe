import { NextRequest } from "next/server";
import { writeFile, mkdir } from "fs/promises";
import { existsSync } from "fs";
import path from "path";

export interface UploadResult {
  url: string;
  fileName: string;
  fileSize: number;
  fileType: string;
}

export interface StorageConfig {
  provider: "local" | "aws-s3" | "gcs" | "cloudflare-r2";
  local?: {
    storagePath: string;
    baseUrl: string;
  };
  aws?: {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    bucket: string;
    cdnUrl?: string;
  };
  gcs?: {
    projectId: string;
    keyFile: string;
    bucket: string;
    cdnUrl?: string;
  };
  r2?: {
    accountId: string;
    accessKeyId: string;
    secretAccessKey: string;
    bucket: string;
    cdnUrl?: string;
  };
}

// Get storage configuration from environment variables
function getStorageConfig(): StorageConfig {
  const provider = (process.env.STORAGE_PROVIDER || "local") as StorageConfig["provider"];

  const config: StorageConfig = { provider };

  switch (provider) {
    case "local":
      config.local = {
        storagePath: process.env.LOCAL_STORAGE_PATH || "./uploads",
        baseUrl: process.env.LOCAL_STORAGE_URL || "http://localhost:3000/uploads",
      };
      break;

    case "aws-s3":
      config.aws = {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        region: process.env.AWS_REGION || "us-east-1",
        bucket: process.env.AWS_S3_BUCKET!,
        cdnUrl: process.env.AWS_S3_CDN_URL,
      };
      break;

    case "gcs":
      config.gcs = {
        projectId: process.env.GCS_PROJECT_ID!,
        keyFile: process.env.GCS_KEY_FILE!,
        bucket: process.env.GCS_BUCKET!,
        cdnUrl: process.env.GCS_CDN_URL,
      };
      break;

    case "cloudflare-r2":
      config.r2 = {
        accountId: process.env.R2_ACCOUNT_ID!,
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
        bucket: process.env.R2_BUCKET!,
        cdnUrl: process.env.R2_CDN_URL,
      };
      break;

    default:
      throw new Error(`Unsupported storage provider: ${provider}`);
  }

  return config;
}

// Upload file to local storage
async function uploadToLocal(file: File, config: StorageConfig): Promise<UploadResult> {
  if (!config.local) {
    throw new Error("Local storage configuration missing");
  }

  const { storagePath, baseUrl } = config.local;

  // Ensure upload directory exists
  if (!existsSync(storagePath)) {
    await mkdir(storagePath, { recursive: true });
  }

  // Generate unique filename
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const fileExtension = path.extname(file.name);
  const fileName = `${timestamp}-${randomString}${fileExtension}`;
  const filePath = path.join(storagePath, fileName);

  // Convert File to Buffer
  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  // Write file to disk
  await writeFile(filePath, buffer);

  return {
    url: `${baseUrl}/${fileName}`,
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type,
  };
}

// Upload file to AWS S3
async function uploadToS3(file: File, config: StorageConfig): Promise<UploadResult> {
  if (!config.aws) {
    throw new Error("AWS S3 configuration missing");
  }

  try {
    // Dynamic import to avoid bundling AWS SDK if not used
    const { S3Client, PutObjectCommand } = await import("@aws-sdk/client-s3");

    const s3Client = new S3Client({
      region: config.aws.region,
      credentials: {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
      },
    });

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(file.name);
    const fileName = `uploads/${timestamp}-${randomString}${fileExtension}`;

    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to S3
    const command = new PutObjectCommand({
      Bucket: config.aws.bucket,
      Key: fileName,
      Body: buffer,
      ContentType: file.type,
      ContentLength: file.size,
    });

    await s3Client.send(command);

    // Generate URL
    const baseUrl = config.aws.cdnUrl || `https://${config.aws.bucket}.s3.${config.aws.region}.amazonaws.com`;
    const url = `${baseUrl}/${fileName}`;

    return {
      url,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
    };
  } catch (error) {
    console.error("S3 upload error:", error);
    throw new Error("Failed to upload to S3");
  }
}

// Upload file to Google Cloud Storage
async function uploadToGCS(file: File, config: StorageConfig): Promise<UploadResult> {
  if (!config.gcs) {
    throw new Error("Google Cloud Storage configuration missing");
  }

  try {
    // Dynamic import to avoid bundling GCS SDK if not used
    const { Storage } = await import("@google-cloud/storage");

    const storage = new Storage({
      projectId: config.gcs.projectId,
      keyFilename: config.gcs.keyFile,
    });

    const bucket = storage.bucket(config.gcs.bucket);

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(file.name);
    const fileName = `uploads/${timestamp}-${randomString}${fileExtension}`;

    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to GCS
    const gcsFile = bucket.file(fileName);
    await gcsFile.save(buffer, {
      metadata: {
        contentType: file.type,
      },
    });

    // Generate URL
    const baseUrl = config.gcs.cdnUrl || `https://storage.googleapis.com/${config.gcs.bucket}`;
    const url = `${baseUrl}/${fileName}`;

    return {
      url,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
    };
  } catch (error) {
    console.error("GCS upload error:", error);
    throw new Error("Failed to upload to Google Cloud Storage");
  }
}

// Upload file to Cloudflare R2
async function uploadToR2(file: File, config: StorageConfig): Promise<UploadResult> {
  if (!config.r2) {
    throw new Error("Cloudflare R2 configuration missing");
  }

  try {
    // Dynamic import to avoid bundling AWS SDK if not used (R2 uses S3-compatible API)
    const { S3Client, PutObjectCommand } = await import("@aws-sdk/client-s3");

    const r2Client = new S3Client({
      region: "auto",
      endpoint: `https://${config.r2.accountId}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: config.r2.accessKeyId,
        secretAccessKey: config.r2.secretAccessKey,
      },
    });

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(file.name);
    const fileName = `uploads/${timestamp}-${randomString}${fileExtension}`;

    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to R2
    const command = new PutObjectCommand({
      Bucket: config.r2.bucket,
      Key: fileName,
      Body: buffer,
      ContentType: file.type,
      ContentLength: file.size,
    });

    await r2Client.send(command);

    // Generate URL
    const baseUrl = config.r2.cdnUrl || `https://${config.r2.bucket}.${config.r2.accountId}.r2.cloudflarestorage.com`;
    const url = `${baseUrl}/${fileName}`;

    return {
      url,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
    };
  } catch (error) {
    console.error("R2 upload error:", error);
    throw new Error("Failed to upload to Cloudflare R2");
  }
}

// Main upload function with multiple storage backend support
export async function uploadFile(file: File): Promise<UploadResult> {
  // Validate file
  const validation = validateFileUpload(file);
  if (!validation.isValid) {
    throw new Error(validation.error || "Invalid file");
  }

  const config = getStorageConfig();

  switch (config.provider) {
    case "local":
      return uploadToLocal(file, config);
    case "aws-s3":
      return uploadToS3(file, config);
    case "gcs":
      return uploadToGCS(file, config);
    case "cloudflare-r2":
      return uploadToR2(file, config);
    default:
      throw new Error(`Unsupported storage provider: ${config.provider}`);
  }
}

export async function generateUploadUrl(fileName: string, fileType: string): Promise<string> {
  const config = getStorageConfig();

  switch (config.provider) {
    case "aws-s3":
      return generateS3PresignedUrl(fileName, fileType, config);
    case "cloudflare-r2":
      return generateR2PresignedUrl(fileName, fileType, config);
    case "gcs":
      return generateGCSSignedUrl(fileName, fileType, config);
    case "local":
      // For local storage, return the upload endpoint
      return `/api/upload?fileName=${encodeURIComponent(fileName)}&fileType=${encodeURIComponent(fileType)}`;
    default:
      throw new Error(`Pre-signed URLs not supported for provider: ${config.provider}`);
  }
}

// Generate S3 pre-signed URL
async function generateS3PresignedUrl(fileName: string, fileType: string, config: StorageConfig): Promise<string> {
  if (!config.aws) {
    throw new Error("AWS S3 configuration missing");
  }

  try {
    const { S3Client, PutObjectCommand } = await import("@aws-sdk/client-s3");
    // Note: @aws-sdk/s3-request-presigner needs to be installed separately
    // npm install @aws-sdk/s3-request-presigner
    let getSignedUrl: any;
    try {
      // Use dynamic import with string to avoid TypeScript module resolution
      const presignerModule = await import("@aws-sdk" + "/s3-request-presigner");
      getSignedUrl = presignerModule.getSignedUrl;
    } catch (error) {
      throw new Error("@aws-sdk/s3-request-presigner is required for pre-signed URLs. Install with: npm install @aws-sdk/s3-request-presigner");
    }

    const s3Client = new S3Client({
      region: config.aws.region,
      credentials: {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
      },
    });

    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(fileName);
    const key = `uploads/${timestamp}-${randomString}${fileExtension}`;

    const command = new PutObjectCommand({
      Bucket: config.aws.bucket,
      Key: key,
      ContentType: fileType,
      ContentDisposition: `attachment; filename="${fileName}"`,
    });

    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour
    return signedUrl;
  } catch (error) {
    console.error("S3 pre-signed URL generation error:", error);
    throw new Error("Failed to generate S3 pre-signed URL");
  }
}

// Generate R2 pre-signed URL
async function generateR2PresignedUrl(fileName: string, fileType: string, config: StorageConfig): Promise<string> {
  if (!config.r2) {
    throw new Error("Cloudflare R2 configuration missing");
  }

  try {
    const { S3Client, PutObjectCommand } = await import("@aws-sdk/client-s3");
    // Note: @aws-sdk/s3-request-presigner needs to be installed separately
    let getSignedUrl: any;
    try {
      // Use dynamic import with string to avoid TypeScript module resolution
      const presignerModule = await import("@aws-sdk" + "/s3-request-presigner");
      getSignedUrl = presignerModule.getSignedUrl;
    } catch (error) {
      throw new Error("@aws-sdk/s3-request-presigner is required for pre-signed URLs. Install with: npm install @aws-sdk/s3-request-presigner");
    }

    const r2Client = new S3Client({
      region: "auto",
      endpoint: `https://${config.r2.accountId}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: config.r2.accessKeyId,
        secretAccessKey: config.r2.secretAccessKey,
      },
    });

    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(fileName);
    const key = `uploads/${timestamp}-${randomString}${fileExtension}`;

    const command = new PutObjectCommand({
      Bucket: config.r2.bucket,
      Key: key,
      ContentType: fileType,
      ContentDisposition: `attachment; filename="${fileName}"`,
    });

    const signedUrl = await getSignedUrl(r2Client, command, { expiresIn: 3600 }); // 1 hour
    return signedUrl;
  } catch (error) {
    console.error("R2 pre-signed URL generation error:", error);
    throw new Error("Failed to generate R2 pre-signed URL");
  }
}

// Generate GCS signed URL
async function generateGCSSignedUrl(fileName: string, fileType: string, config: StorageConfig): Promise<string> {
  if (!config.gcs) {
    throw new Error("Google Cloud Storage configuration missing");
  }

  try {
    const { Storage } = await import("@google-cloud/storage");

    const storage = new Storage({
      projectId: config.gcs.projectId,
      keyFilename: config.gcs.keyFile,
    });

    const bucket = storage.bucket(config.gcs.bucket);

    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(fileName);
    const filename = `uploads/${timestamp}-${randomString}${fileExtension}`;

    const file = bucket.file(filename);

    const [signedUrl] = await file.getSignedUrl({
      version: 'v4',
      action: 'write',
      expires: Date.now() + 60 * 60 * 1000, // 1 hour
      contentType: fileType,
      extensionHeaders: {
        'content-disposition': `attachment; filename="${fileName}"`,
      },
    });

    return signedUrl;
  } catch (error) {
    console.error("GCS signed URL generation error:", error);
    throw new Error("Failed to generate GCS signed URL");
  }
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

export interface SecurityScanResult {
  isSafe: boolean;
  threats: string[];
  fileType: string;
  actualMimeType?: string;
}

// Enhanced file validation with security checks
export function validateFileUpload(file: File): FileValidationResult {
  const warnings: string[] = [];

  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: "File size exceeds 10MB limit",
    };
  }

  // Check minimum file size (prevent empty files)
  if (file.size < 10) {
    return {
      isValid: false,
      error: "File is too small or empty",
    };
  }

  // Check file type
  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/jpg",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ];

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: "File type not allowed. Please upload PDF, DOC, DOCX, XLS, XLSX, or image files.",
    };
  }

  // Check file name for suspicious patterns
  const suspiciousPatterns = [
    /\.exe$/i,
    /\.bat$/i,
    /\.cmd$/i,
    /\.scr$/i,
    /\.vbs$/i,
    /\.js$/i,
    /\.jar$/i,
    /\.php$/i,
    /\.asp$/i,
    /\.jsp$/i,
    /\.(sh|bash)$/i,
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(file.name)) {
      return {
        isValid: false,
        error: "File name contains suspicious extension",
      };
    }
  }

  // Check for double extensions
  const doubleExtensionPattern = /\.[a-zA-Z0-9]+\.[a-zA-Z0-9]+$/;
  if (doubleExtensionPattern.test(file.name)) {
    warnings.push("File has multiple extensions, please verify it's safe");
  }

  // Check file name length
  if (file.name.length > 255) {
    return {
      isValid: false,
      error: "File name is too long",
    };
  }

  // Check for null bytes in filename
  if (file.name.includes('\0')) {
    return {
      isValid: false,
      error: "File name contains invalid characters",
    };
  }

  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

// Scan file content for security threats
export async function scanFileForThreats(file: File): Promise<SecurityScanResult> {
  const threats: string[] = [];

  try {
    // Read file header to verify actual file type
    const buffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(buffer);
    const actualMimeType = detectFileType(uint8Array);

    // Check if declared MIME type matches actual file type
    if (actualMimeType && actualMimeType !== file.type) {
      threats.push(`File type mismatch: declared ${file.type}, actual ${actualMimeType}`);
    }

    // Scan for embedded executables
    if (containsExecutableSignatures(uint8Array)) {
      threats.push("File contains executable code signatures");
    }

    // Scan for script injections
    if (containsScriptInjections(uint8Array)) {
      threats.push("File contains potential script injections");
    }

    // Scan for malicious patterns
    if (containsMaliciousPatterns(uint8Array)) {
      threats.push("File contains suspicious patterns");
    }

    return {
      isSafe: threats.length === 0,
      threats,
      fileType: file.type,
      actualMimeType: actualMimeType || undefined,
    };
  } catch (error) {
    console.error("File scanning error:", error);
    return {
      isSafe: false,
      threats: ["Failed to scan file for security threats"],
      fileType: file.type,
    };
  }
}

// Detect file type by magic numbers
function detectFileType(buffer: Uint8Array): string | null {
  const signatures: { [key: string]: number[] } = {
    "application/pdf": [0x25, 0x50, 0x44, 0x46], // %PDF
    "image/jpeg": [0xFF, 0xD8, 0xFF],
    "image/png": [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A],
    "application/zip": [0x50, 0x4B, 0x03, 0x04], // ZIP (includes DOCX, XLSX)
    "application/msword": [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1], // DOC
  };

  for (const [mimeType, signature] of Object.entries(signatures)) {
    if (buffer.length >= signature.length) {
      const matches = signature.every((byte, index) => buffer[index] === byte);
      if (matches) {
        return mimeType;
      }
    }
  }

  return null;
}

// Check for executable signatures
function containsExecutableSignatures(buffer: Uint8Array): boolean {
  const executableSignatures = [
    [0x4D, 0x5A], // MZ (Windows executable)
    [0x7F, 0x45, 0x4C, 0x46], // ELF (Linux executable)
    [0xFE, 0xED, 0xFA, 0xCE], // Mach-O (macOS executable)
    [0xCE, 0xFA, 0xED, 0xFE], // Mach-O (macOS executable, reverse)
  ];

  for (const signature of executableSignatures) {
    for (let i = 0; i <= buffer.length - signature.length; i++) {
      const matches = signature.every((byte, index) => buffer[i + index] === byte);
      if (matches) {
        return true;
      }
    }
  }

  return false;
}

// Check for script injections
function containsScriptInjections(buffer: Uint8Array): boolean {
  const decoder = new TextDecoder('utf-8', { fatal: false });
  const text = decoder.decode(buffer).toLowerCase();

  const scriptPatterns = [
    /<script/,
    /javascript:/,
    /vbscript:/,
    /onload=/,
    /onerror=/,
    /onclick=/,
    /eval\(/,
    /document\.write/,
    /window\.location/,
  ];

  return scriptPatterns.some(pattern => pattern.test(text));
}

// Check for malicious patterns
function containsMaliciousPatterns(buffer: Uint8Array): boolean {
  const decoder = new TextDecoder('utf-8', { fatal: false });
  const text = decoder.decode(buffer).toLowerCase();

  const maliciousPatterns = [
    /cmd\.exe/,
    /powershell/,
    /\/bin\/sh/,
    /\/bin\/bash/,
    /system\(/,
    /exec\(/,
    /shell_exec/,
    /passthru/,
    /base64_decode/,
    /eval\(/,
    /assert\(/,
  ];

  return maliciousPatterns.some(pattern => pattern.test(text));
}
