import { prisma } from "@/lib/db";
import { toPrismaJson } from "@/lib/utils/json";

export interface CreateDiscussionData {
  procurementId: string;
  title: string;
  description?: string;
  type: "AANWIJZING" | "QA_SESSION" | "CLARIFICATION" | "NEGOTIATION" | "TECHNICAL_DISCUSSION" | "GENERAL";
  createdById: string;
  meetingDate?: Date;
  meetingLocation?: string;
  meetingType?: "PHYSICAL" | "VIRTUAL" | "HYBRID";
  maxParticipants?: number;
  isPublic?: boolean;
  allowAnonymous?: boolean;
  endDate?: Date;
}

export interface CreateMessageData {
  discussionId: string;
  content: string;
  authorId: string;
  messageType?: "MESSAGE" | "QUESTION" | "ANSWER" | "CLARIFICATION" | "ANNOUNCEMENT" | "SYSTEM";
  parentId?: string;
  isOfficial?: boolean;
  isAnonymous?: boolean;
  attachments?: Array<{
    fileName: string;
    originalName: string;
    fileSize: number;
    mimeType: string;
    filePath: string;
    description?: string;
  }>;
}

export interface CreateNegotiationData {
  procurementId: string;
  vendorId: string;
  title: string;
  description?: string;
  initiatedById: string;
  scheduledAt?: Date;
}

export class DiscussionService {
  // Create a new discussion
  async createDiscussion(data: CreateDiscussionData) {
    const discussion = await prisma.procurementDiscussion.create({
      data: {
        procurementId: data.procurementId,
        title: data.title,
        description: data.description,
        type: data.type as any,
        createdById: data.createdById,
        meetingDate: data.meetingDate,
        meetingLocation: data.meetingLocation,
        meetingType: data.meetingType as any,
        maxParticipants: data.maxParticipants,
        isPublic: data.isPublic ?? true,
        allowAnonymous: data.allowAnonymous ?? false,
        endDate: data.endDate,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        procurement: {
          select: {
            id: true,
            title: true,
            procurementNumber: true,
          },
        },
      },
    });

    // Auto-add creator as moderator
    await this.addParticipant(discussion.id, data.createdById, "MODERATOR");

    // Notify relevant users about new discussion
    await this.notifyDiscussionCreated(discussion);

    return discussion;
  }

  // Add participant to discussion
  async addParticipant(
    discussionId: string,
    userId: string,
    role: "MODERATOR" | "FACILITATOR" | "PARTICIPANT" | "OBSERVER" = "PARTICIPANT"
  ) {
    return await prisma.discussionParticipant.upsert({
      where: {
        discussionId_userId: {
          discussionId,
          userId,
        },
      },
      update: {
        role: role as any,
        isActive: true,
      },
      create: {
        discussionId,
        userId,
        role: role as any,
        canPost: role !== "OBSERVER",
        canModerate: role === "MODERATOR" || role === "FACILITATOR",
      },
    });
  }

  // Create a message in discussion
  async createMessage(data: CreateMessageData) {
    // Check if user can post in this discussion
    const participant = await prisma.discussionParticipant.findUnique({
      where: {
        discussionId_userId: {
          discussionId: data.discussionId,
          userId: data.authorId,
        },
      },
    });

    if (!participant?.canPost) {
      throw new Error("User is not allowed to post in this discussion");
    }

    const message = await prisma.discussionMessage.create({
      data: {
        discussionId: data.discussionId,
        content: data.content,
        authorId: data.authorId,
        messageType: (data.messageType as any) || "MESSAGE",
        parentId: data.parentId,
        isOfficial: data.isOfficial || false,
        isAnonymous: data.isAnonymous || false,
        attachments: data.attachments ? {
          create: data.attachments.map(attachment => ({
            ...attachment,
            uploadedById: data.authorId,
          })),
        } : undefined,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        attachments: true,
        parent: {
          select: {
            id: true,
            content: true,
            author: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    // Update participant's last seen
    await prisma.discussionParticipant.update({
      where: {
        discussionId_userId: {
          discussionId: data.discussionId,
          userId: data.authorId,
        },
      },
      data: {
        lastSeenAt: new Date(),
      },
    });

    // Notify other participants
    await this.notifyNewMessage(message);

    return message;
  }

  // Get discussion with messages
  async getDiscussion(discussionId: string, userId?: string) {
    const discussion = await prisma.procurementDiscussion.findUnique({
      where: { id: discussionId },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        procurement: {
          select: {
            id: true,
            title: true,
            procurementNumber: true,
            status: true,
          },
        },
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        messages: {
          where: {
            isDeleted: false,
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            attachments: true,
            reactions: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            _count: {
              select: {
                replies: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
        _count: {
          select: {
            messages: true,
            participants: true,
          },
        },
      },
    });

    if (!discussion) {
      throw new Error("Discussion not found");
    }

    // Check access permissions
    if (!discussion.isPublic && userId) {
      const participant = discussion.participants.find((p: any) => p.userId === userId);
      if (!participant) {
        throw new Error("Access denied to this discussion");
      }
    }

    return discussion;
  }

  // Create negotiation session
  async createNegotiation(data: CreateNegotiationData) {
    const negotiation = await prisma.negotiationSession.create({
      data: {
        procurementId: data.procurementId,
        vendorId: data.vendorId,
        title: data.title,
        description: data.description,
        initiatedById: data.initiatedById,
        scheduledAt: data.scheduledAt,
      },
      include: {
        procurement: {
          select: {
            id: true,
            title: true,
            procurementNumber: true,
          },
        },
        vendor: {
          select: {
            id: true,
            companyName: true,
          },
        },
        initiatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Notify vendor about negotiation invitation
    await this.notifyNegotiationCreated(negotiation);

    return negotiation;
  }

  // Submit negotiation round
  async submitNegotiationRound(data: {
    sessionId: string;
    proposedById: string;
    proposedPrice?: number;
    proposedTerms?: any;
    vendorNotes?: string;
  }) {
    const session = await prisma.negotiationSession.findUnique({
      where: { id: data.sessionId },
      include: {
        rounds: {
          orderBy: { roundNumber: "desc" },
          take: 1,
        },
      },
    });

    if (!session) {
      throw new Error("Negotiation session not found");
    }

    const nextRoundNumber = (session.rounds[0]?.roundNumber || 0) + 1;

    const round = await prisma.negotiationRound.create({
      data: {
        sessionId: data.sessionId,
        roundNumber: nextRoundNumber,
        proposedPrice: data.proposedPrice,
        proposedTerms: data.proposedTerms,
        vendorNotes: data.vendorNotes,
        proposedById: data.proposedById,
      },
      include: {
        proposedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        session: {
          include: {
            procurement: true,
            vendor: true,
          },
        },
      },
    });

    // Update session status
    await prisma.negotiationSession.update({
      where: { id: data.sessionId },
      data: { status: "ACTIVE" },
    });

    // Notify other party
    await this.notifyNegotiationRound(round);

    return round;
  }

  // Respond to negotiation round
  async respondToNegotiationRound(data: {
    roundId: string;
    respondedById: string;
    counterPrice?: number;
    counterTerms?: any;
    buyerNotes?: string;
    decision: "ACCEPTED" | "REJECTED" | "COUNTER";
  }) {
    const round = await prisma.negotiationRound.update({
      where: { id: data.roundId },
      data: {
        counterPrice: data.counterPrice,
        counterTerms: data.counterTerms,
        buyerNotes: data.buyerNotes,
        respondedById: data.respondedById,
        status: data.decision === "ACCEPTED" ? "ACCEPTED" : 
                data.decision === "REJECTED" ? "REJECTED" : "RESPONDED",
      },
      include: {
        proposedBy: true,
        respondedBy: true,
        session: {
          include: {
            procurement: true,
            vendor: true,
          },
        },
      },
    });

    // If accepted, complete the negotiation
    if (data.decision === "ACCEPTED") {
      await prisma.negotiationSession.update({
        where: { id: round.sessionId },
        data: {
          status: "COMPLETED",
          finalPrice: round.proposedPrice,
          agreedTerms: toPrismaJson(round.proposedTerms),
        },
      });
    }

    // Notify proposer
    await this.notifyNegotiationResponse(round, data.decision);

    return round;
  }

  // Private notification methods
  private async notifyDiscussionCreated(discussion: any) {
    // Implementation would notify relevant users
    console.log("Discussion created:", discussion.title);
  }

  private async notifyNewMessage(message: any) {
    // Implementation would notify discussion participants
    console.log("New message in discussion:", message.content);
  }

  private async notifyNegotiationCreated(negotiation: any) {
    // Implementation would notify vendor
    console.log("Negotiation created:", negotiation.title);
  }

  private async notifyNegotiationRound(round: any) {
    // Implementation would notify other party
    console.log("New negotiation round:", round.roundNumber);
  }

  private async notifyNegotiationResponse(round: any, decision: string) {
    // Implementation would notify proposer
    console.log("Negotiation response:", decision);
  }
}

// Singleton instance
export const discussionService = new DiscussionService();
