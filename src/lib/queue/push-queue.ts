import { PushQueueJob, QueueJobStatus } from "@/lib/types";
import { prisma } from "@/lib/db";

/**
 * Enterprise-grade Push Notification Queue System
 * Handles push notification processing with retry logic, scheduling, and comprehensive error handling
 */

export class PushQueue {
  private static instance: PushQueue;
  private readonly maxRetries = 3;
  private readonly retryDelays = [1000, 5000, 15000]; // Progressive delay in ms

  static getInstance(): PushQueue {
    if (!PushQueue.instance) {
      PushQueue.instance = new PushQueue();
    }
    return PushQueue.instance;
  }

  /**
   * Add push notification to queue for processing
   */
  async addPushNotification(
    pushData: Omit<
      PushQueueJob,
      "id" | "status" | "attempts" | "createdAt" | "updatedAt"
    >,
    options?: { priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT" }
  ): Promise<string> {
    try {
      const job = await prisma.pushQueue.create({
        data: {
          ...pushData,
          status: "PENDING" as QueueJobStatus,
          attempts: 0,
          maxAttempts: pushData.maxAttempts || this.maxRetries,
          priority: options?.priority || pushData.priority || "MEDIUM",
          data: pushData.data || {},
          metadata: pushData.metadata || {},
          scheduledAt: pushData.scheduledAt || new Date(),
        },
      });

      return job.id;
    } catch (error) {
      console.error("Failed to enqueue push notification:", error);
      throw new Error("Failed to add push notification to queue");
    }
  }

  /**
   * Process pending push notifications in the queue
   */
  async processPendingNotifications(limit: number = 10): Promise<void> {
    try {
      const pendingJobs = await prisma.pushQueue.findMany({
        where: {
          status: "PENDING",
          scheduledAt: {
            lte: new Date(),
          },
        },
        orderBy: [{ priority: "desc" }, { createdAt: "asc" }],
        take: limit,
      });

      for (const job of pendingJobs) {
        await this.processPushJob(job);
      }
    } catch (error) {
      console.error("Error processing push notification queue:", error);
    }
  }

  /**
   * Process individual push notification job
   */
  private async processPushJob(job: any): Promise<void> {
    try {
      // Update status to processing
      await prisma.pushQueue.update({
        where: { id: job.id },
        data: {
          status: "PROCESSING",
          attempts: job.attempts + 1,
        },
      });

      // Send push notification
      const success = await this.sendPushNotification(job);

      if (success) {
        await prisma.pushQueue.update({
          where: { id: job.id },
          data: {
            status: "COMPLETED",
            processedAt: new Date(),
          },
        });
      } else {
        await this.handlePushFailure(job);
      }
    } catch (error) {
      console.error(
        `Failed to process push notification job ${job.id}:`,
        error
      );
      await this.handlePushFailure(
        job,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Handle push notification sending failure with retry logic
   */
  private async handlePushFailure(
    job: any,
    errorMessage?: string
  ): Promise<void> {
    const isMaxAttemptsReached = job.attempts >= job.maxAttempts;

    if (isMaxAttemptsReached) {
      await prisma.pushQueue.update({
        where: { id: job.id },
        data: {
          status: "FAILED",
          failedAt: new Date(),
          error: errorMessage || "Max retry attempts reached",
        },
      });
    } else {
      // Schedule retry with progressive delay
      const retryDelay =
        this.retryDelays[Math.min(job.attempts, this.retryDelays.length - 1)];
      const nextAttemptAt = new Date(Date.now() + retryDelay);

      await prisma.pushQueue.update({
        where: { id: job.id },
        data: {
          status: "PENDING",
          scheduledAt: nextAttemptAt,
          error: errorMessage,
        },
      });
    }
  }

  /**
   * Send push notification using configured service
   */
  private async sendPushNotification(job: any): Promise<boolean> {
    try {
      // This is a placeholder for actual push notification service integration
      // Replace with your push service (Firebase FCM, Apple APNS, etc.)

      console.log(`Sending push notification to user: ${job.userId}`);
      console.log(`Title: ${job.title}`);
      console.log(`Body: ${job.body}`);

      // Simulate processing time
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Simulate 97% success rate for testing
      return Math.random() > 0.03;
    } catch (error) {
      console.error("Push notification service error:", error);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    total: number;
  }> {
    const [pending, processing, completed, failed, total] = await Promise.all([
      prisma.pushQueue.count({ where: { status: "PENDING" } }),
      prisma.pushQueue.count({ where: { status: "PROCESSING" } }),
      prisma.pushQueue.count({ where: { status: "COMPLETED" } }),
      prisma.pushQueue.count({ where: { status: "FAILED" } }),
      prisma.pushQueue.count(),
    ]);

    return {
      pending,
      processing,
      completed,
      failed,
      total,
    };
  }

  /**
   * Retry failed push notifications
   */
  async retryFailedNotifications(jobIds?: string[]): Promise<number> {
    const whereClause: any = { status: "FAILED" };

    if (jobIds && jobIds.length > 0) {
      whereClause.id = { in: jobIds };
    }

    const result = await prisma.pushQueue.updateMany({
      where: whereClause,
      data: {
        status: "PENDING",
        attempts: 0,
        scheduledAt: new Date(),
        error: null,
        failedAt: null,
      },
    });

    return result.count;
  }

  /**
   * Clean up old completed and failed jobs
   */
  async cleanupOldJobs(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await prisma.pushQueue.deleteMany({
      where: {
        OR: [
          { status: "COMPLETED", processedAt: { lt: cutoffDate } },
          { status: "FAILED", failedAt: { lt: cutoffDate } },
        ],
      },
    });

    return result.count;
  }
}

// Export singleton instance
export const pushQueue = PushQueue.getInstance();
