import { SmsQueueJob, QueueJobStatus } from "@/lib/types";
import { prisma } from "@/lib/db";

/**
 * Enterprise-grade SMS Queue System
 * Handles SMS processing with retry logic, scheduling, and comprehensive error handling
 */

export class SmsQueue {
  private static instance: SmsQueue;
  private readonly maxRetries = 3;
  private readonly retryDelays = [1000, 5000, 15000]; // Progressive delay in ms

  static getInstance(): SmsQueue {
    if (!SmsQueue.instance) {
      SmsQueue.instance = new SmsQueue();
    }
    return SmsQueue.instance;
  }

  /**
   * Add SMS to queue for processing
   */
  async addSms(
    smsData: Omit<
      SmsQueueJob,
      "id" | "status" | "attempts" | "createdAt" | "updatedAt"
    >,
    options?: { priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT" }
  ): Promise<string> {
    try {
      const job = await prisma.smsQueue.create({
        data: {
          ...smsData,
          status: "PENDING" as QueueJobStatus,
          attempts: 0,
          maxAttempts: smsData.maxAttempts || this.maxRetries,
          priority: options?.priority || smsData.priority || "MEDIUM",
          templateData: smsData.templateData || {},
          metadata: smsData.metadata || {},
          scheduledAt: smsData.scheduledAt || new Date(),
        },
      });

      return job.id;
    } catch (error) {
      console.error("Failed to enqueue SMS:", error);
      throw new Error("Failed to add SMS to queue");
    }
  }

  /**
   * Process pending SMS messages in the queue
   */
  async processPendingSms(limit: number = 10): Promise<void> {
    try {
      const pendingJobs = await prisma.smsQueue.findMany({
        where: {
          status: "PENDING",
          scheduledAt: {
            lte: new Date(),
          },
        },
        orderBy: [{ priority: "desc" }, { createdAt: "asc" }],
        take: limit,
      });

      for (const job of pendingJobs) {
        await this.processSmsJob(job);
      }
    } catch (error) {
      console.error("Error processing SMS queue:", error);
    }
  }

  /**
   * Process individual SMS job
   */
  private async processSmsJob(job: any): Promise<void> {
    try {
      // Update status to processing
      await prisma.smsQueue.update({
        where: { id: job.id },
        data: {
          status: "PROCESSING",
          attempts: job.attempts + 1,
        },
      });

      // Send SMS
      const success = await this.sendSms(job);

      if (success) {
        await prisma.smsQueue.update({
          where: { id: job.id },
          data: {
            status: "COMPLETED",
            processedAt: new Date(),
          },
        });
      } else {
        await this.handleSmsFailure(job);
      }
    } catch (error) {
      console.error(`Failed to process SMS job ${job.id}:`, error);
      await this.handleSmsFailure(
        job,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Handle SMS sending failure with retry logic
   */
  private async handleSmsFailure(
    job: any,
    errorMessage?: string
  ): Promise<void> {
    const isMaxAttemptsReached = job.attempts >= job.maxAttempts;

    if (isMaxAttemptsReached) {
      await prisma.smsQueue.update({
        where: { id: job.id },
        data: {
          status: "FAILED",
          failedAt: new Date(),
          error: errorMessage || "Max retry attempts reached",
        },
      });
    } else {
      // Schedule retry with progressive delay
      const retryDelay =
        this.retryDelays[Math.min(job.attempts, this.retryDelays.length - 1)];
      const nextAttemptAt = new Date(Date.now() + retryDelay);

      await prisma.smsQueue.update({
        where: { id: job.id },
        data: {
          status: "PENDING",
          scheduledAt: nextAttemptAt,
          error: errorMessage,
        },
      });
    }
  }

  /**
   * Send SMS using configured SMS service
   */
  private async sendSms(job: any): Promise<boolean> {
    try {
      // This is a placeholder for actual SMS service integration
      // Replace with your SMS service (Twilio, AWS SNS, etc.)

      console.log(`Sending SMS to: ${job.to}`);
      console.log(`Message: ${job.message}`);

      // Simulate processing time
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Simulate 98% success rate for testing
      return Math.random() > 0.02;
    } catch (error) {
      console.error("SMS service error:", error);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    total: number;
  }> {
    const [pending, processing, completed, failed, total] = await Promise.all([
      prisma.smsQueue.count({ where: { status: "PENDING" } }),
      prisma.smsQueue.count({ where: { status: "PROCESSING" } }),
      prisma.smsQueue.count({ where: { status: "COMPLETED" } }),
      prisma.smsQueue.count({ where: { status: "FAILED" } }),
      prisma.smsQueue.count(),
    ]);

    return {
      pending,
      processing,
      completed,
      failed,
      total,
    };
  }

  /**
   * Retry failed SMS messages
   */
  async retryFailedSms(jobIds?: string[]): Promise<number> {
    const whereClause: any = { status: "FAILED" };

    if (jobIds && jobIds.length > 0) {
      whereClause.id = { in: jobIds };
    }

    const result = await prisma.smsQueue.updateMany({
      where: whereClause,
      data: {
        status: "PENDING",
        attempts: 0,
        scheduledAt: new Date(),
        error: null,
        failedAt: null,
      },
    });

    return result.count;
  }

  /**
   * Clean up old completed and failed jobs
   */
  async cleanupOldJobs(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await prisma.smsQueue.deleteMany({
      where: {
        OR: [
          { status: "COMPLETED", processedAt: { lt: cutoffDate } },
          { status: "FAILED", failedAt: { lt: cutoffDate } },
        ],
      },
    });

    return result.count;
  }

  /**
   * Cancel pending SMS jobs
   */
  async cancelJobs(jobIds: string[]): Promise<number> {
    const result = await prisma.smsQueue.updateMany({
      where: {
        id: { in: jobIds },
        status: { in: ["PENDING", "PROCESSING"] },
      },
      data: {
        status: "CANCELLED",
        error: "Job cancelled by user",
      },
    });

    return result.count;
  }
}

// Export singleton instance
export const smsQueue = SmsQueue.getInstance();
