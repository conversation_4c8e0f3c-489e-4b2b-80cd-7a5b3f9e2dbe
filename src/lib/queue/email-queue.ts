import { EmailQueueJob, QueueJobStatus } from "@/lib/types";
import { prisma } from "@/lib/db";

/**
 * Enterprise-grade Email Queue System
 * Handles email processing with retry logic, scheduling, and comprehensive error handling
 */

export class EmailQueue {
  private static instance: EmailQueue;
  private readonly maxRetries = 3;
  private readonly retryDelays = [1000, 5000, 15000]; // Progressive delay in ms

  static getInstance(): EmailQueue {
    if (!EmailQueue.instance) {
      EmailQueue.instance = new EmailQueue();
    }
    return EmailQueue.instance;
  }

  /**
   * Add email to queue for processing
   */
  async addEmail(
    emailData: Omit<
      EmailQueueJob,
      "id" | "status" | "attempts" | "createdAt" | "updatedAt"
    >,
    options?: { priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT" }
  ): Promise<string> {
    try {
      const job = await prisma.emailQueue.create({
        data: {
          ...emailData,
          to: Array.isArray(emailData.to)
            ? emailData.to.join(",")
            : emailData.to,
          cc: emailData.cc
            ? Array.isArray(emailData.cc)
              ? emailData.cc.join(",")
              : emailData.cc
            : null,
          bcc: emailData.bcc
            ? Array.isArray(emailData.bcc)
              ? emailData.bcc.join(",")
              : emailData.bcc
            : null,
          status: "PENDING" as QueueJobStatus,
          attempts: 0,
          maxAttempts: emailData.maxAttempts || this.maxRetries,
          priority: options?.priority || emailData.priority || "MEDIUM",
          attachments: emailData.attachments || [],
          templateData: emailData.templateData || {},
          metadata: emailData.metadata || {},
          scheduledAt: emailData.scheduledAt || new Date(),
        },
      });

      return job.id;
    } catch (error) {
      console.error("Failed to enqueue email:", error);
      throw new Error("Failed to add email to queue");
    }
  }

  /**
   * Process pending emails in the queue
   */
  async processPendingEmails(limit: number = 10): Promise<void> {
    try {
      const pendingJobs = await prisma.emailQueue.findMany({
        where: {
          status: "PENDING",
          scheduledAt: {
            lte: new Date(),
          },
        },
        orderBy: [{ priority: "desc" }, { createdAt: "asc" }],
        take: limit,
      });

      for (const job of pendingJobs) {
        await this.processEmailJob(job);
      }
    } catch (error) {
      console.error("Error processing email queue:", error);
    }
  }

  /**
   * Process individual email job
   */
  private async processEmailJob(job: any): Promise<void> {
    try {
      // Update status to processing
      await prisma.emailQueue.update({
        where: { id: job.id },
        data: {
          status: "PROCESSING",
          attempts: job.attempts + 1,
        },
      });

      // Simulate email sending (replace with actual email service)
      const success = await this.sendEmail(job);

      if (success) {
        await prisma.emailQueue.update({
          where: { id: job.id },
          data: {
            status: "COMPLETED",
            processedAt: new Date(),
          },
        });
      } else {
        await this.handleEmailFailure(job);
      }
    } catch (error) {
      console.error(`Failed to process email job ${job.id}:`, error);
      await this.handleEmailFailure(
        job,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Handle email sending failure with retry logic
   */
  private async handleEmailFailure(
    job: any,
    errorMessage?: string
  ): Promise<void> {
    const isMaxAttemptsReached = job.attempts >= job.maxAttempts;

    if (isMaxAttemptsReached) {
      await prisma.emailQueue.update({
        where: { id: job.id },
        data: {
          status: "FAILED",
          failedAt: new Date(),
          error: errorMessage || "Max retry attempts reached",
        },
      });
    } else {
      // Schedule retry with progressive delay
      const retryDelay =
        this.retryDelays[Math.min(job.attempts, this.retryDelays.length - 1)];
      const nextAttemptAt = new Date(Date.now() + retryDelay);

      await prisma.emailQueue.update({
        where: { id: job.id },
        data: {
          status: "PENDING",
          scheduledAt: nextAttemptAt,
          error: errorMessage,
        },
      });
    }
  }

  /**
   * Send email using configured email service
   */
  private async sendEmail(job: any): Promise<boolean> {
    try {
      // This is a placeholder for actual email service integration
      // Replace with your email service (SendGrid, AWS SES, Nodemailer, etc.)

      console.log(`Sending email to: ${job.to}`);
      console.log(`Subject: ${job.subject}`);

      // Simulate processing time
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Simulate 95% success rate for testing
      return Math.random() > 0.05;
    } catch (error) {
      console.error("Email service error:", error);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    total: number;
  }> {
    const [pending, processing, completed, failed, total] = await Promise.all([
      prisma.emailQueue.count({ where: { status: "PENDING" } }),
      prisma.emailQueue.count({ where: { status: "PROCESSING" } }),
      prisma.emailQueue.count({ where: { status: "COMPLETED" } }),
      prisma.emailQueue.count({ where: { status: "FAILED" } }),
      prisma.emailQueue.count(),
    ]);

    return {
      pending,
      processing,
      completed,
      failed,
      total,
    };
  }

  /**
   * Retry failed emails
   */
  async retryFailedEmails(jobIds?: string[]): Promise<number> {
    const whereClause: any = { status: "FAILED" };

    if (jobIds && jobIds.length > 0) {
      whereClause.id = { in: jobIds };
    }

    const result = await prisma.emailQueue.updateMany({
      where: whereClause,
      data: {
        status: "PENDING",
        attempts: 0,
        scheduledAt: new Date(),
        error: null,
        failedAt: null,
      },
    });

    return result.count;
  }

  /**
   * Clean up old completed and failed jobs
   */
  async cleanupOldJobs(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await prisma.emailQueue.deleteMany({
      where: {
        OR: [
          { status: "COMPLETED", processedAt: { lt: cutoffDate } },
          { status: "FAILED", failedAt: { lt: cutoffDate } },
        ],
      },
    });

    return result.count;
  }

  /**
   * Cancel pending email jobs
   */
  async cancelJobs(jobIds: string[]): Promise<number> {
    const result = await prisma.emailQueue.updateMany({
      where: {
        id: { in: jobIds },
        status: { in: ["PENDING", "PROCESSING"] },
      },
      data: {
        status: "CANCELLED",
        error: "Job cancelled by user",
      },
    });

    return result.count;
  }

  /**
   * Get job details by ID
   */
  async getJobById(jobId: string): Promise<any | null> {
    return await prisma.emailQueue.findUnique({
      where: { id: jobId },
    });
  }

  /**
   * Get jobs with pagination and filtering
   */
  async getJobs(
    options: {
      status?: QueueJobStatus;
      priority?: "LOW" | "NORMAL" | "HIGH" | "URGENT";
      page?: number;
      limit?: number;
      sortBy?: "createdAt" | "scheduledAt" | "priority";
      sortOrder?: "asc" | "desc";
    } = {}
  ): Promise<{
    jobs: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      status,
      priority,
      page = 1,
      limit = 50,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const whereClause: any = {};
    if (status) whereClause.status = status;
    if (priority) whereClause.priority = priority;

    const [jobs, total] = await Promise.all([
      prisma.emailQueue.findMany({
        where: whereClause,
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.emailQueue.count({ where: whereClause }),
    ]);

    return {
      jobs,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }
}

// Export singleton instance
export const emailQueue = EmailQueue.getInstance();
