import { prisma } from '@/lib/db';

export interface KPITemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  metrics: KPIMetric[];
  weightings: Record<string, number>;
  thresholds: Record<string, { min: number; max: number; target: number }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface KPIMetric {
  id: string;
  name: string;
  description?: string;
  type: 'PERCENTAGE' | 'NUMBER' | 'CURRENCY' | 'BOOLEAN' | 'RATING';
  unit?: string;
  weight: number;
  calculation: 'SUM' | 'AVERAGE' | 'COUNT' | 'PERCENTAGE' | 'CUSTOM';
  formula?: string;
  isRequired: boolean;
}

export interface VendorEvaluation {
  id: string;
  vendorId: string;
  templateId: string;
  evaluationPeriod: {
    startDate: Date;
    endDate: Date;
  };
  scores: Record<string, number>;
  overallScore: number;
  ranking: number;
  status: 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'REJECTED';
  evaluatedBy: string;
  evaluatedAt: Date;
  comments?: string;
  metadata?: any;
}

export interface VendorPerformanceData {
  vendorId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  metrics: {
    deliveryPerformance: number;
    qualityScore: number;
    costEffectiveness: number;
    responsiveness: number;
    compliance: number;
    innovation: number;
  };
  transactions: {
    totalOrders: number;
    completedOrders: number;
    cancelledOrders: number;
    totalValue: number;
    averageOrderValue: number;
  };
  issues: {
    totalIssues: number;
    resolvedIssues: number;
    averageResolutionTime: number;
  };
}

export interface VendorRanking {
  vendorId: string;
  vendorName: string;
  overallScore: number;
  ranking: number;
  category: string;
  evaluationCount: number;
  lastEvaluationDate: Date;
  trend: 'IMPROVING' | 'STABLE' | 'DECLINING';
  strengths: string[];
  weaknesses: string[];
}

export class VendorKPISystem {
  async createKPITemplate(templateData: {
    name: string;
    description?: string;
    category: string;
    metrics: Array<{
      name: string;
      description?: string;
      type: string;
      weight: number;
      calculation: string;
      isRequired: boolean;
    }>;
    createdById: string;
  }): Promise<any> {
    // Validate metric weights sum to 100
    if (!this.validateMetricWeights(templateData.metrics)) {
      throw new Error('Total metric weights must equal 100%');
    }

    // Prepare metrics and weights as JSON
    const metricsJson = templateData.metrics.map((metric, index) => ({
      id: `metric_${index}`,
      name: metric.name,
      description: metric.description,
      type: metric.type,
      calculation: metric.calculation,
      isRequired: metric.isRequired,
    }));

    const weightsJson = templateData.metrics.reduce((acc: any, metric, index) => {
      acc[`metric_${index}`] = metric.weight;
      return acc;
    }, {});

    const template = await prisma.vendorKpiTemplate.create({
      data: {
        name: templateData.name,
        description: templateData.description,
        category: templateData.category as any, // Cast to match enum
        metrics: metricsJson,
        weights: weightsJson,
        thresholds: {
          excellent: { min: 90, max: 100 },
          good: { min: 75, max: 89 },
          fair: { min: 60, max: 74 },
          poor: { min: 0, max: 59 },
        },
        isActive: true,
        createdById: templateData.createdById,
      },
    });

    return template;
  }

  async evaluateVendorKPI(evaluationData: {
    vendorId: string;
    templateId: string;
    evaluatedById: string;
    scores: Array<{
      metricId: string;
      score: number;
    }>;
    comments?: string;
  }): Promise<any> {
    // Get template with metrics
    const template = await prisma.vendorKpiTemplate.findUnique({
      where: { id: evaluationData.templateId },
    });

    if (!template) {
      throw new Error('KPI template not found');
    }

    // Parse metrics and weights from JSON
    const metrics = Array.isArray(template.metrics) ? template.metrics : [];
    const weights = typeof template.weights === 'object' ? template.weights as any : {};

    // Validate all metrics are scored
    const scoredMetricIds = evaluationData.scores.map((s: any) => s.metricId);
    const requiredMetricIds = metrics.filter((m: any) => m.isRequired).map((m: any) => m.id);
    const missingMetrics = requiredMetricIds.filter((id: any) => !scoredMetricIds.includes(id));

    if (missingMetrics.length > 0) {
      throw new Error('All required metrics must be scored');
    }

    // Calculate weighted overall score
    const overallScore = this.calculateWeightedScore(
      evaluationData.scores.map(s => ({
        score: s.score,
        weight: weights[s.metricId] || 0,
      }))
    );

    // Prepare metric scores as JSON
    const metricScoresJson = evaluationData.scores.reduce((acc: any, score) => {
      acc[score.metricId] = score.score;
      return acc;
    }, {});

    // Create evaluation
    const evaluation = await prisma.vendorKpiEvaluation.create({
      data: {
        vendorId: evaluationData.vendorId,
        templateId: evaluationData.templateId,
        evaluationPeriod: `${new Date().getFullYear()}-Q${Math.ceil((new Date().getMonth() + 1) / 3)}`,
        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        endDate: new Date(),
        overallScore,
        categoryScores: {}, // Could be calculated from metric categories
        metricScores: metricScoresJson,
        rating: this.getPerformanceRating(overallScore),
        evaluatedById: evaluationData.evaluatedById,
        recommendations: evaluationData.comments,
      },
    });

    // Create individual scores
    for (const score of evaluationData.scores) {
      await prisma.vendorKpiScore.create({
        data: {
          evaluationId: evaluation.id,
          metricId: score.metricId,
          score: score.score,
        },
      });
    }

    return evaluation;
  }

  async updateKPITemplate(id: string, updates: Partial<KPITemplate>): Promise<KPITemplate> {
    const updated = await prisma.vendorKpiTemplate.update({
      where: { id },
      data: {
        name: updates.name,
        description: updates.description,
        category: updates.category as any,
        isActive: updates.isActive,
        updatedAt: new Date(),
      },
    });

    return updated as unknown as KPITemplate;
  }

  async deleteKPITemplate(id: string): Promise<void> {
    await prisma.vendorKpiTemplate.update({
      where: { id },
      data: { isActive: false },
    });
  }

  async evaluateVendor(
    vendorId: string,
    templateId: string,
    evaluationData: Partial<VendorEvaluation>,
    evaluatedBy: string
  ): Promise<VendorEvaluation> {
    const template = await prisma.vendorKpiTemplate.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      throw new Error('KPI template not found');
    }

    // Calculate overall score based on weighted metrics
    const weights = typeof template.weights === 'object' ? template.weights as any : {};
    const overallScore = this.calculateOverallScore(
      evaluationData.scores || {},
      weights
    );

    const evaluation = await prisma.vendorKpiEvaluation.create({
      data: {
        vendorId,
        templateId,
        evaluationPeriod: evaluationData.evaluationPeriod?.startDate ?
          `${evaluationData.evaluationPeriod.startDate.getFullYear()}-Q${Math.ceil((evaluationData.evaluationPeriod.startDate.getMonth() + 1) / 3)}` :
          `${new Date().getFullYear()}-Q${Math.ceil((new Date().getMonth() + 1) / 3)}`,
        startDate: evaluationData.evaluationPeriod?.startDate || new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        endDate: evaluationData.evaluationPeriod?.endDate || new Date(),
        overallScore,
        categoryScores: {}, // Could be calculated from metric categories
        metricScores: evaluationData.scores || {},
        rating: this.getPerformanceRating(overallScore),
        recommendations: evaluationData.comments,
        evaluatedById: evaluatedBy,
      },
    });

    // Update vendor rankings
    await this.updateVendorRankings(templateId);

    return evaluation as unknown as VendorEvaluation;
  }

  async getVendorEvaluations(
    vendorId?: string,
    templateId?: string,
    period?: { startDate: Date; endDate: Date }
  ): Promise<VendorEvaluation[]> {
    const where: any = {};
    
    if (vendorId) where.vendorId = vendorId;
    if (templateId) where.templateId = templateId;
    if (period) {
      where.evaluatedAt = {
        gte: period.startDate,
        lte: period.endDate,
      };
    }

    const evaluations = await prisma.vendorKpiEvaluation.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });

    return evaluations as unknown as VendorEvaluation[];
  }

  async getVendorPerformanceData(
    vendorId: string,
    period: { startDate: Date; endDate: Date }
  ): Promise<VendorPerformanceData> {
    try {
      // Get purchase orders for the vendor in the specified period
      const purchaseOrders = await prisma.purchaseOrder.findMany({
        where: {
          vendorId,
          createdAt: {
            gte: period.startDate,
            lte: period.endDate,
          },
        },
        include: {
          items: true,
          goodReceipts: {
            include: {
              items: true,
            },
          },
        },
      });

      // Get vendor evaluations for the period
      const evaluations = await prisma.vendorKpiEvaluation.findMany({
        where: {
          vendorId,
          createdAt: {
            gte: period.startDate,
            lte: period.endDate,
          },
        },
      });

      // Get vendor issues/complaints for the period
      const issues = await prisma.vendorIssue.findMany({
        where: {
          vendorId,
          createdAt: {
            gte: period.startDate,
            lte: period.endDate,
          },
        },
      });

      // Calculate delivery performance
      const deliveryPerformance = this.calculateDeliveryPerformance(purchaseOrders);

      // Calculate quality score from evaluations
      const qualityScore = this.calculateQualityScore(evaluations);

      // Calculate cost effectiveness
      const costEffectiveness = this.calculateCostEffectiveness(purchaseOrders);

      // Calculate responsiveness (based on response times to communications)
      const responsiveness = await this.calculateResponsiveness(vendorId, period);

      // Calculate compliance score
      const compliance = await this.calculateComplianceScore(vendorId, period);

      // Calculate innovation score from evaluations
      const innovation = this.calculateInnovationScore(evaluations);

      // Calculate transaction metrics
      const totalOrders = purchaseOrders.length;
      const completedOrders = purchaseOrders.filter((po: any) => po.status === 'COMPLETED').length;
      const cancelledOrders = purchaseOrders.filter((po: any) => po.status === 'CANCELLED').length;
      const totalValue = purchaseOrders.reduce((sum: any, po: any) => sum + po.totalValue, 0);
      const averageOrderValue = totalOrders > 0 ? totalValue / totalOrders : 0;

      // Calculate issue metrics
      const totalIssues = issues.length;
      const resolvedIssues = issues.filter((issue: any) => issue.status === 'RESOLVED').length;
      const averageResolutionTime = this.calculateAverageResolutionTime(issues);

      const performanceData: VendorPerformanceData = {
        vendorId,
        period,
        metrics: {
          deliveryPerformance,
          qualityScore,
          costEffectiveness,
          responsiveness,
          compliance,
          innovation,
        },
        transactions: {
          totalOrders,
          completedOrders,
          cancelledOrders,
          totalValue,
          averageOrderValue,
        },
        issues: {
          totalIssues,
          resolvedIssues,
          averageResolutionTime,
        },
      };

      return performanceData;
    } catch (error) {
      console.error('Error calculating vendor performance data:', error);
      throw new Error('Failed to calculate vendor performance data');
    }
  }

  async getVendorRankings(
    category?: string,
    limit: number = 50
  ): Promise<VendorRanking[]> {
    try {
      // Get recent evaluations (last 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const whereClause: any = {
        createdAt: {
          gte: sixMonthsAgo,
        },
        status: 'APPROVED',
      };

      if (category) {
        whereClause.vendor = {
          businessCategory: category,
        };
      }

      // Get average scores per vendor
      const evaluations = await prisma.vendorKpiEvaluation.findMany({
        where: whereClause,
        include: {
          vendor: {
            select: {
              id: true,
              companyName: true,
              businessCategory: true,
            },
          },
        },
      });

      // Group by vendor and calculate average scores
      const vendorScores = new Map<string, { vendor: any; scores: number[]; totalEvaluations: number }>();

      evaluations.forEach((evaluation: any) => {
        const vendorId = evaluation.vendorId;
        if (!vendorScores.has(vendorId)) {
          vendorScores.set(vendorId, {
            vendor: evaluation.vendor,
            scores: [],
            totalEvaluations: 0,
          });
        }
        const vendorData = vendorScores.get(vendorId)!;
        vendorData.scores.push(evaluation.overallScore);
        vendorData.totalEvaluations++;
      });

      // Calculate rankings
      const rankings: any[] = Array.from(vendorScores.entries())
        .map(([vendorId, data]) => {
          const averageScore = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
          return {
            vendorId,
            vendorName: data.vendor.companyName,
            category: data.vendor.businessCategory,
            overallScore: averageScore,
            ranking: 0, // Will be set below
            evaluationCount: data.totalEvaluations,
            lastEvaluationDate: new Date(),
            trend: 'STABLE' as const,
            strengths: [],
            weaknesses: [],
          };
        })
        .sort((a, b) => b.overallScore - a.overallScore)
        .slice(0, limit);

      // Assign ranks
      rankings.forEach((ranking, index) => {
        ranking.ranking = index + 1;
      });

      return rankings;
    } catch (error) {
      console.error('Error calculating vendor rankings:', error);
      return [];
    }
  }

  async generateVendorReport(
    vendorId: string,
    period: { startDate: Date; endDate: Date }
  ): Promise<{
    vendor: any;
    performanceData: VendorPerformanceData;
    evaluations: VendorEvaluation[];
    ranking: VendorRanking | null;
    recommendations: string[];
  }> {
    const [vendor, performanceData, evaluations] = await Promise.all([
      prisma.vendor.findUnique({ where: { id: vendorId } }),
      this.getVendorPerformanceData(vendorId, period),
      this.getVendorEvaluations(vendorId, undefined, period),
    ]);

    if (!vendor) {
      throw new Error('Vendor not found');
    }

    const rankings = await this.getVendorRankings();
    const ranking = rankings.find(r => r.vendorId === vendorId) || null;

    const recommendations = this.generateRecommendations(performanceData, evaluations);

    return {
      vendor,
      performanceData,
      evaluations,
      ranking,
      recommendations,
    };
  }

  private validateMetricWeights(metrics: Array<{ weight: number }>): boolean {
    const totalWeight = metrics.reduce((sum, metric) => sum + metric.weight, 0);
    return Math.abs(totalWeight - 100) < 0.01; // Allow for floating point precision
  }

  private calculateWeightedScore(scores: Array<{ score: number; weight: number }>): number {
    const totalWeightedScore = scores.reduce((sum, item) => sum + (item.score * item.weight / 100), 0);
    return Math.round(totalWeightedScore * 100) / 100; // Round to 2 decimal places
  }

  private calculateOverallScore(scores: Record<string, number>, weightings: Record<string, number>): number {
    let totalWeightedScore = 0;
    let totalWeight = 0;

    for (const [metricId, score] of Object.entries(scores)) {
      const weight = weightings[metricId] || 0;
      totalWeightedScore += score * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
  }

  private async updateVendorRankings(templateId: string): Promise<void> {
    // Get all evaluations for this template
    const evaluations = await prisma.vendorKpiEvaluation.findMany({
      where: { templateId },
      orderBy: { overallScore: 'desc' },
    });

    // Update rankings (this would be more complex in a real implementation)
    // For now, we'll just ensure the data is consistent
    console.log(`Updated rankings for ${evaluations.length} evaluations`);
  }

  private calculateDeliveryPerformance(purchaseOrders: any[]): number {
    if (purchaseOrders.length === 0) return 0;

    const onTimeDeliveries = purchaseOrders.filter(po => {
      // Simplified logic - in reality, you'd compare actual vs expected delivery dates
      return po.status === 'COMPLETED';
    }).length;

    return (onTimeDeliveries / purchaseOrders.length) * 100;
  }

  private calculateQualityScore(evaluations: any[]): number {
    if (evaluations.length === 0) return 0;

    const totalScore = evaluations.reduce((sum, evaluation) => sum + evaluation.overallScore, 0);
    return totalScore / evaluations.length;
  }

  private calculateCostEffectiveness(purchaseOrders: any[]): number {
    if (purchaseOrders.length === 0) return 0;

    // Simplified calculation - compare actual costs vs budgeted costs
    // For now, return a baseline score
    return 75; // Placeholder
  }

  private async calculateResponsiveness(vendorId: string, period: { startDate: Date; endDate: Date }): Promise<number> {
    // Calculate based on response times to communications, RFQ responses, etc.
    // This would involve analyzing communication logs, response times, etc.
    return 80; // Placeholder
  }

  private async calculateComplianceScore(vendorId: string, period: { startDate: Date; endDate: Date }): Promise<number> {
    // Calculate based on regulatory compliance, contract adherence, etc.
    const issues = await prisma.vendorIssue.findMany({
      where: {
        vendorId,
        category: 'COMPLIANCE',
        createdAt: {
          gte: period.startDate,
          lte: period.endDate,
        },
      },
    });

    // Simple calculation: fewer compliance issues = higher score
    const baseScore = 100;
    const penaltyPerIssue = 10;
    return Math.max(0, baseScore - (issues.length * penaltyPerIssue));
  }

  private calculateInnovationScore(evaluations: any[]): number {
    // Calculate innovation score from evaluation comments and specific metrics
    // This would involve NLP analysis of comments, innovation-specific metrics, etc.
    return 70; // Placeholder
  }

  private calculateAverageResolutionTime(issues: any[]): number {
    const resolvedIssues = issues.filter(issue => issue.resolvedAt);
    if (resolvedIssues.length === 0) return 0;

    const totalResolutionTime = resolvedIssues.reduce((sum, issue) => {
      const resolutionTime = issue.resolvedAt.getTime() - issue.reportedAt.getTime();
      return sum + (resolutionTime / (1000 * 60 * 60)); // Convert to hours
    }, 0);

    return totalResolutionTime / resolvedIssues.length;
  }

  private getPerformanceRating(score: number): 'EXCELLENT' | 'GOOD' | 'SATISFACTORY' | 'NEEDS_IMPROVEMENT' | 'POOR' {
    if (score >= 90) return 'EXCELLENT';
    if (score >= 75) return 'GOOD';
    if (score >= 60) return 'SATISFACTORY';
    if (score >= 45) return 'NEEDS_IMPROVEMENT';
    return 'POOR';
  }



  async getVendorKPIEvaluations(vendorId: string): Promise<any[]> {
    const evaluations = await prisma.vendorKpiEvaluation.findMany({
      where: { vendorId },
      include: {
        template: true,
        scores: {
          include: {
            metric: true,
          },
        },
        evaluatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return evaluations;
  }

  async getVendorKPIStatistics(vendorId: string): Promise<{
    averageScore: number;
    evaluationCount: number;
    lastEvaluationDate: Date | null;
    trend: 'IMPROVING' | 'STABLE' | 'DECLINING';
  }> {
    const evaluations = await prisma.vendorKpiEvaluation.findMany({
      where: { vendorId },
      orderBy: { createdAt: 'desc' },
      take: 10, // Last 10 evaluations for trend analysis
    });

    if (evaluations.length === 0) {
      return {
        averageScore: 0,
        evaluationCount: 0,
        lastEvaluationDate: null,
        trend: 'STABLE',
      };
    }

    const averageScore = evaluations.reduce((sum: any, evaluation: any) => sum + evaluation.overallScore, 0) / evaluations.length;

    // Calculate trend
    let trend: 'IMPROVING' | 'STABLE' | 'DECLINING' = 'STABLE';
    if (evaluations.length >= 3) {
      const recent = evaluations.slice(0, 3);
      const older = evaluations.slice(3, 6);

      if (recent.length >= 3 && older.length >= 3) {
        const recentAvg = recent.reduce((sum: any, evaluation: any) => sum + evaluation.overallScore, 0) / recent.length;
        const olderAvg = older.reduce((sum: any, evaluation: any) => sum + evaluation.overallScore, 0) / older.length;

        if (recentAvg > olderAvg + 5) trend = 'IMPROVING';
        else if (recentAvg < olderAvg - 5) trend = 'DECLINING';
      }
    }

    return {
      averageScore,
      evaluationCount: evaluations.length,
      lastEvaluationDate: evaluations[0].createdAt,
      trend,
    };
  }





  private generateRecommendations(
    performanceData: VendorPerformanceData,
    evaluations: VendorEvaluation[]
  ): string[] {
    const recommendations: string[] = [];

    // Analyze performance metrics
    if (performanceData.metrics.deliveryPerformance < 90) {
      recommendations.push('Improve delivery performance - consider better logistics planning');
    }

    if (performanceData.metrics.qualityScore < 85) {
      recommendations.push('Focus on quality improvement - implement quality control measures');
    }

    if (performanceData.metrics.responsiveness < 80) {
      recommendations.push('Enhance communication and responsiveness to requests');
    }

    // Analyze trends from evaluations
    if (evaluations.length >= 2) {
      const latest = evaluations[0];
      const previous = evaluations[1];
      
      if (latest.overallScore < previous.overallScore) {
        recommendations.push('Overall performance declining - schedule performance review meeting');
      }
    }

    return recommendations;
  }














}

// Export singleton instance
export const vendorKPISystem = new VendorKPISystem();
