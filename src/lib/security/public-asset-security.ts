import { NextRequest, NextResponse } from "next/server";

// Security configuration for public assets
export const PUBLIC_ASSET_SECURITY = {
  // Allowed file types for public assets (more restrictive than private files)
  ALLOWED_MIME_TYPES: [
    "image/jpeg",
    "image/png", 
    "image/webp",
    "image/svg+xml",
    "image/gif",
    "application/pdf", // Only for documents
  ],
  
  // Allowed file extensions
  ALLOWED_EXTENSIONS: [
    ".jpg", ".jpeg", ".png", ".webp", ".svg", ".gif", ".pdf"
  ],
  
  // Maximum file size (10MB for public assets)
  MAX_FILE_SIZE: 10 * 1024 * 1024,
  
  // Allowed categories
  ALLOWED_CATEGORIES: [
    "news", "announcements", "banners", "logos", "documents"
  ],
  
  // Security headers for public assets
  SECURITY_HEADERS: {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Cache-Control": "public, max-age=31536000, immutable", // 1 year cache
  },
  
  // Content Security Policy for images
  IMAGE_CSP: "default-src 'none'; img-src 'self'; style-src 'unsafe-inline';",
};

// Validate public asset file
export function validatePublicAssetFile(file: File): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > PUBLIC_ASSET_SECURITY.MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `File size exceeds maximum allowed size of ${PUBLIC_ASSET_SECURITY.MAX_FILE_SIZE / 1024 / 1024}MB`
    };
  }
  
  // Check MIME type
  if (!PUBLIC_ASSET_SECURITY.ALLOWED_MIME_TYPES.includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not allowed for public assets`
    };
  }
  
  // Check file extension
  const extension = file.name.toLowerCase().match(/\.[^.]+$/)?.[0];
  if (!extension || !PUBLIC_ASSET_SECURITY.ALLOWED_EXTENSIONS.includes(extension)) {
    return {
      valid: false,
      error: `File extension ${extension} is not allowed for public assets`
    };
  }
  
  // Check file name for security
  if (file.name.includes("..") || file.name.includes("/") || file.name.includes("\\")) {
    return {
      valid: false,
      error: "Invalid file name - path traversal not allowed"
    };
  }
  
  // Check for suspicious file names
  const suspiciousPatterns = [
    /\.php$/i, /\.asp$/i, /\.jsp$/i, /\.exe$/i, /\.bat$/i, /\.cmd$/i,
    /\.sh$/i, /\.py$/i, /\.rb$/i, /\.pl$/i, /\.js$/i, /\.html$/i, /\.htm$/i
  ];
  
  if (suspiciousPatterns.some(pattern => pattern.test(file.name))) {
    return {
      valid: false,
      error: "File name contains suspicious patterns"
    };
  }
  
  return { valid: true };
}

// Validate category
export function validateAssetCategory(category: string): { valid: boolean; error?: string } {
  if (!PUBLIC_ASSET_SECURITY.ALLOWED_CATEGORIES.includes(category)) {
    return {
      valid: false,
      error: `Category ${category} is not allowed`
    };
  }
  
  return { valid: true };
}

// Generate secure file name
export function generateSecureFileName(originalName: string, category: string): string {
  // Remove any path components
  const baseName = originalName.replace(/.*[\/\\]/, "");
  
  // Get file extension
  const extension = baseName.toLowerCase().match(/\.[^.]+$/)?.[0] || "";
  
  // Remove extension from name
  const nameWithoutExt = baseName.replace(/\.[^.]+$/, "");
  
  // Sanitize name - only allow alphanumeric, hyphens, underscores
  const sanitizedName = nameWithoutExt
    .replace(/[^a-zA-Z0-9\-_]/g, "_")
    .substring(0, 50); // Limit length
  
  // Add timestamp and random component for uniqueness
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  
  return `${category}_${timestamp}_${random}_${sanitizedName}${extension}`;
}

// Middleware for serving public assets with security headers
export function createPublicAssetMiddleware() {
  return function publicAssetMiddleware(request: NextRequest) {
    const url = new URL(request.url);
    
    // Only apply to public asset paths
    if (!url.pathname.startsWith("/assets/public/")) {
      return NextResponse.next();
    }
    
    // Get file extension
    const extension = url.pathname.toLowerCase().match(/\.[^.]+$/)?.[0];
    
    // Create response with security headers
    const response = NextResponse.next();
    
    // Apply security headers
    Object.entries(PUBLIC_ASSET_SECURITY.SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    // Set appropriate Content-Type based on extension
    if (extension) {
      switch (extension) {
        case ".jpg":
        case ".jpeg":
          response.headers.set("Content-Type", "image/jpeg");
          break;
        case ".png":
          response.headers.set("Content-Type", "image/png");
          break;
        case ".webp":
          response.headers.set("Content-Type", "image/webp");
          break;
        case ".svg":
          response.headers.set("Content-Type", "image/svg+xml");
          response.headers.set("Content-Security-Policy", PUBLIC_ASSET_SECURITY.IMAGE_CSP);
          break;
        case ".gif":
          response.headers.set("Content-Type", "image/gif");
          break;
        case ".pdf":
          response.headers.set("Content-Type", "application/pdf");
          response.headers.set("Content-Disposition", "inline");
          break;
      }
    }
    
    return response;
  };
}

// Scan file content for malicious patterns (basic implementation)
export async function scanFileContent(file: File): Promise<{ safe: boolean; threats?: string[] }> {
  try {
    // For images, we can do basic header validation
    if (file.type.startsWith("image/")) {
      const buffer = await file.arrayBuffer();
      const bytes = new Uint8Array(buffer);
      
      // Check for valid image headers
      const isValidImage = validateImageHeader(bytes, file.type);
      if (!isValidImage) {
        return {
          safe: false,
          threats: ["Invalid image header - possible malicious file"]
        };
      }
    }
    
    // For PDFs, check for basic PDF structure
    if (file.type === "application/pdf") {
      const text = await file.text();
      if (!text.startsWith("%PDF-")) {
        return {
          safe: false,
          threats: ["Invalid PDF header"]
        };
      }
    }
    
    return { safe: true };
  } catch (error) {
    return {
      safe: false,
      threats: ["Failed to scan file content"]
    };
  }
}

// Validate image headers
function validateImageHeader(bytes: Uint8Array, mimeType: string): boolean {
  if (bytes.length < 4) return false;
  
  switch (mimeType) {
    case "image/jpeg":
      return bytes[0] === 0xFF && bytes[1] === 0xD8;
    case "image/png":
      return bytes[0] === 0x89 && bytes[1] === 0x50 && bytes[2] === 0x4E && bytes[3] === 0x47;
    case "image/gif":
      return (bytes[0] === 0x47 && bytes[1] === 0x49 && bytes[2] === 0x46) ||
             (bytes[0] === 0x47 && bytes[1] === 0x49 && bytes[2] === 0x46);
    case "image/webp":
      return bytes[0] === 0x52 && bytes[1] === 0x49 && bytes[2] === 0x46 && bytes[3] === 0x46;
    default:
      return true; // For other types, assume valid
  }
}
