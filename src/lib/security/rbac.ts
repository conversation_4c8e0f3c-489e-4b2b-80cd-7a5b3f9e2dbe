import { prisma } from "@/lib/db";

export interface PermissionCheck {
  resource: string;
  action: string;
  resourceId?: string;
  conditions?: Record<string, any>;
}

export interface UserPermissions {
  userId: string;
  roles: Array<{
    id: string;
    name: string;
    level: number;
    permissions: Array<{
      id: string;
      name: string;
      resource: string;
      action: string;
      conditions?: Record<string, any>;
    }>;
  }>;
  resourcePermissions: Array<{
    resource: string;
    resourceId: string;
    permission: string;
  }>;
}

// Permission cache for performance
const permissionCache = new Map<string, UserPermissions>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Get user permissions with caching
export async function getUserPermissions(userId: string, useCache = true): Promise<UserPermissions> {
  const cacheKey = `user_permissions_${userId}`;
  
  if (useCache && permissionCache.has(cacheKey)) {
    const cached = permissionCache.get(cacheKey)!;
    // Check if cache is still valid (simple TTL check)
    if (Date.now() - (cached as any).cachedAt < CACHE_TTL) {
      return cached;
    }
  }

  const userRoles = await prisma.userRole.findMany({
    where: {
      userId,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ],
    },
    include: {
      role: true,
    },
  });

  const resourcePermissions = await prisma.resourcePermission.findMany({
    where: {
      userId,
    },
  });

  // Build permissions object with inheritance
  const permissions: UserPermissions = {
    userId,
    roles: [],
    resourcePermissions: resourcePermissions.map((rp: any) => ({
      resource: rp.resource,
      resourceId: rp.resourceId,
      permission: rp.permission,
    })),
  };

  // Process roles and inherit permissions
  for (const userRole of userRoles) {
    const role = userRole.role;

    permissions.roles.push({
      id: role.id,
      name: role.name,
      level: (role as any).level || 1,
      permissions: [], // Simplified - no permissions processing
    });
  }

  // Cache the result
  if (useCache) {
    (permissions as any).cachedAt = Date.now();
    permissionCache.set(cacheKey, permissions);
  }

  return permissions;
}

// Check if user has specific permission
export async function hasPermission(
  userId: string,
  check: PermissionCheck
): Promise<boolean> {
  try {
    const permissions = await getUserPermissions(userId);

    // Check role-based permissions
    for (const role of permissions.roles) {
      for (const permission of role.permissions) {
        if (permission.resource === check.resource && permission.action === check.action) {
          // Check conditions if any
          if (permission.conditions && check.conditions) {
            if (!evaluateConditions(permission.conditions, check.conditions)) {
              continue;
            }
          }
          return true;
        }
      }
    }

    // Check resource-specific permissions
    if (check.resourceId) {
      const resourcePerm = permissions.resourcePermissions.find(
        rp => rp.resource === check.resource && 
             rp.resourceId === check.resourceId && 
             rp.permission === check.action
      );
      if (resourcePerm) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("Permission check error:", error);
    return false;
  }
}

// Check multiple permissions (AND logic)
export async function hasAllPermissions(
  userId: string,
  checks: PermissionCheck[]
): Promise<boolean> {
  for (const check of checks) {
    if (!(await hasPermission(userId, check))) {
      return false;
    }
  }
  return true;
}

// Check multiple permissions (OR logic)
export async function hasAnyPermission(
  userId: string,
  checks: PermissionCheck[]
): Promise<boolean> {
  for (const check of checks) {
    if (await hasPermission(userId, check)) {
      return true;
    }
  }
  return false;
}

// Evaluate permission conditions
function evaluateConditions(
  permissionConditions: Record<string, any>,
  checkConditions: Record<string, any>
): boolean {
  // Simple condition evaluation - can be extended for complex logic
  for (const [key, value] of Object.entries(permissionConditions)) {
    if (key === "ownResource" && value === true) {
      // Check if user owns the resource
      if (checkConditions.ownerId !== checkConditions.userId) {
        return false;
      }
    } else if (key === "department" && value) {
      // Check department-based access
      if (checkConditions.department !== value) {
        return false;
      }
    } else if (key === "maxAmount" && value) {
      // Check amount-based access
      if (checkConditions.amount && checkConditions.amount > value) {
        return false;
      }
    }
    // Add more condition types as needed
  }
  return true;
}

// Clear user permission cache
export function clearUserPermissionCache(userId: string): void {
  const cacheKey = `user_permissions_${userId}`;
  permissionCache.delete(cacheKey);
}

// Clear all permission cache
export function clearAllPermissionCache(): void {
  permissionCache.clear();
}
