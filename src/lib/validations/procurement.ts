import { z } from "zod";
import { ExtendedProcurementStatus, ExtendedApprovalStatus } from "@/lib/types";

// Procurement item validation
export const procurementItemSchema = z.object({
  itemMasterId: z.string().optional(),
  name: z.string().min(1, "Item name is required"),
  description: z.string().optional(),
  quantity: z.number().positive("Quantity must be a positive number"),
  unit: z.string().min(1, "Unit is required"),
  ownerEstimate: z
    .number()
    .positive("Owner estimate must be a positive number"),
});

// Procurement stage validation
export const procurementStageSchema = z.object({
  name: z.string().min(1, "Stage name is required"),
  sequence: z.number().int().positive("Sequence must be a positive integer"),
  startDate: z.date(),
  endDate: z.date(),
});

// Committee member validation
export const committeeMemberSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  committeeRole: z.string().min(1, "Committee role is required"),
});

// Main procurement validation
export const procurementSchema = z
  .object({
    title: z.string().min(1, "Title is required"),
    type: z.enum(["TENDER", "RFQ"]),
    status: z
      .enum([
        "DRAFT",
        "PUBLISHED",
        "AANWIJZING",
        "SUBMISSION",
        "NEGOTIATION",
        "EVALUATION",
        "WINNER_ANNOUNCEMENT",
        "AWARDED",
        "COMPLETED",
        "CANCELLED",
      ]),
    ownerEstimate: z
      .number()
      .positive("Owner estimate must be a positive number"),
    showOwnerEstimateToVendor: z.boolean(),
    evaluationMethod: z.string().min(1, "Evaluation method is required"),
    workTimeUnit: z.enum(["HARI", "MINGGU", "BULAN", "TAHUN"]).optional(),
    hpsIncludesVat: z.boolean(),
    vatRate: z.number().min(0).max(100),
    evaluationTemplateId: z.string().optional(),
    items: z
      .array(procurementItemSchema)
      .min(1, "At least one item is required"),
    stages: z
      .array(procurementStageSchema)
      .min(1, "At least one stage is required"),
    committee: z
      .array(committeeMemberSchema)
      .min(1, "At least one committee member is required"),
  })
  .refine(
    (data) => {
      // Validate that all stages have end date after start date
      return data.stages.every((stage) => stage.endDate > stage.startDate);
    },
    {
      message: "End date must be after start date for all stages",
      path: ["stages"],
    }
  )
  .refine(
    (data) => {
      // Validate that stages don't overlap
      const sortedStages = data.stages.sort((a, b) => a.sequence - b.sequence);
      for (let i = 0; i < sortedStages.length - 1; i++) {
        if (sortedStages[i].endDate > sortedStages[i + 1].startDate) {
          return false;
        }
      }
      return true;
    },
    {
      message: "Stages cannot overlap in time",
      path: ["stages"],
    }
  );

// Vendor offer item validation
export const vendorOfferItemSchema = z.object({
  itemId: z.string().min(1, "Item ID is required"),
  offeredPrice: z.number().positive("Offered price must be a positive number"),
});

// Vendor offer validation
export const vendorOfferSchema = z.object({
  procurementId: z.string().min(1, "Procurement ID is required"),
  offerItems: z
    .array(vendorOfferItemSchema)
    .min(1, "At least one offer item is required"),
  documents: z
    .array(
      z.object({
        documentType: z.string().min(1, "Document type is required"),
        fileUrl: z.string().url("Invalid file URL"),
        fileName: z.string().min(1, "File name is required"),
        fileType: z.string().min(1, "File type is required"),
        description: z.string().optional(),
      })
    )
    .optional(),
});

// Evaluation validation
export const evaluationSchema = z.object({
  offerId: z.string().min(1, "Offer ID is required"),
  status: z.enum([
    "PENDING",
    "APPROVED",
    "REJECTED",
    "CANCELLED",
    "EVALUATING_ADMIN",
    "PASSED_ADMIN",
    "FAILED_ADMIN",
    "EVALUATING_TECH",
    "PASSED_TECH",
    "FAILED_TECH",
    "EVALUATING_PRICE",
    "PASSED_PRICE",
    "FAILED_PRICE",
    "NEGOTIATING",
    "WINNER",
    "LOSER",
    "BACKUP_1",
    "BACKUP_2",
  ]),
  comments: z.string().optional(),
  scores: z
    .object({
      adminScore: z.number().min(0).max(100).optional(),
      techScore: z.number().min(0).max(100).optional(),
      priceScore: z.number().min(0).max(100).optional(),
    })
    .optional(),
});

// Negotiation validation
export const negotiationSchema = z.object({
  offerId: z.string().min(1, "Offer ID is required"),
  negotiatedPrice: z.number().positive("Negotiated price must be positive"),
  comments: z.string().optional(),
});

// Award validation
export const awardSchema = z.object({
  procurementId: z.string().min(1, "Procurement ID is required"),
  winningOfferId: z.string().min(1, "Winning offer ID is required"),
  backupOfferIds: z.array(z.string()).optional(),
  comments: z.string().optional(),
});

// Types
export type ProcurementInput = z.infer<typeof procurementSchema>;
export type ProcurementItemInput = z.infer<typeof procurementItemSchema>;
export type ProcurementStageInput = z.infer<typeof procurementStageSchema>;
export type VendorOfferInput = z.infer<typeof vendorOfferSchema>;
export type EvaluationInput = z.infer<typeof evaluationSchema>;
export type NegotiationInput = z.infer<typeof negotiationSchema>;
export type AwardInput = z.infer<typeof awardSchema>;
