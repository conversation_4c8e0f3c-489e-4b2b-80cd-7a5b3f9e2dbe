import { z } from "zod";

// Template component types
export const templateComponentSchema: any = z.discriminatedUnion("type", [
  // Text component
  z.object({
    type: z.literal("text"),
    id: z.string(),
    content: z.string(),
    style: z
      .object({
        fontSize: z.number().optional(),
        fontWeight: z.enum(["normal", "bold"]).optional(),
        textAlign: z.enum(["left", "center", "right", "justify"]).optional(),
        color: z.string().optional(),
        marginTop: z.number().optional(),
        marginBottom: z.number().optional(),
      })
      .optional(),
  }),

  // Variable placeholder component
  z.object({
    type: z.literal("variable"),
    id: z.string(),
    variableName: z.string(),
    label: z.string(),
    defaultValue: z.string().optional(),
    format: z
      .enum(["text", "number", "currency", "date", "datetime"])
      .optional(),
    style: z
      .object({
        fontSize: z.number().optional(),
        fontWeight: z.enum(["normal", "bold"]).optional(),
        textAlign: z.enum(["left", "center", "right", "justify"]).optional(),
        color: z.string().optional(),
      })
      .optional(),
  }),

  // Table component
  z.object({
    type: z.literal("table"),
    id: z.string(),
    headers: z.array(z.string()),
    rows: z.array(z.array(z.string())),
    variableRows: z.string().optional(), // Variable name for dynamic rows
    style: z
      .object({
        borderWidth: z.number().optional(),
        borderColor: z.string().optional(),
        headerBackgroundColor: z.string().optional(),
        cellPadding: z.number().optional(),
      })
      .optional(),
  }),

  // Image component
  z.object({
    type: z.literal("image"),
    id: z.string(),
    src: z.string().optional(),
    variableName: z.string().optional(), // For dynamic images
    alt: z.string().optional(),
    style: z
      .object({
        width: z.number().optional(),
        height: z.number().optional(),
        objectFit: z.enum(["contain", "cover", "fill"]).optional(),
      })
      .optional(),
  }),

  // Signature field component
  z.object({
    type: z.literal("signature"),
    id: z.string(),
    label: z.string(),
    signerRole: z.string().optional(),
    variableName: z.string().optional(),
    style: z
      .object({
        width: z.number().optional(),
        height: z.number().optional(),
        borderWidth: z.number().optional(),
        borderStyle: z.enum(["solid", "dashed", "dotted"]).optional(),
      })
      .optional(),
  }),

  // Page break component
  z.object({
    type: z.literal("pageBreak"),
    id: z.string(),
  }),

  // Container component for grouping
  z.object({
    type: z.literal("container"),
    id: z.string(),
    children: z.array(z.lazy(() => templateComponentSchema as any)),
    style: z
      .object({
        padding: z.number().optional(),
        margin: z.number().optional(),
        backgroundColor: z.string().optional(),
        borderWidth: z.number().optional(),
        borderColor: z.string().optional(),
        borderRadius: z.number().optional(),
      })
      .optional(),
  }),
]);

// Template variable definition
export const templateVariableSchema = z.object({
  name: z.string().min(1, "Variable name is required"),
  label: z.string().min(1, "Variable label is required"),
  type: z.enum([
    "text",
    "number",
    "currency",
    "date",
    "datetime",
    "boolean",
    "array",
    "object",
  ]),
  required: z.boolean().default(false),
  defaultValue: z.any().optional(),
  description: z.string().optional(),
  validation: z
    .object({
      min: z.number().optional(),
      max: z.number().optional(),
      pattern: z.string().optional(),
      options: z.array(z.string()).optional(),
    })
    .optional(),
});

// Template content structure
export const templateContentSchema = z.object({
  components: z.array(templateComponentSchema),
  pageSettings: z.object({
    size: z.enum(["A4", "A3", "Letter", "Legal"]).default("A4"),
    orientation: z.enum(["portrait", "landscape"]).default("portrait"),
    margins: z.object({
      top: z.number().default(20),
      right: z.number().default(20),
      bottom: z.number().default(20),
      left: z.number().default(20),
    }),
  }),
  styles: z.object({
    fontFamily: z.string().default("Arial"),
    fontSize: z.number().default(12),
    lineHeight: z.number().default(1.5),
  }),
});

// Main template schema
export const documentTemplateSchema = z.object({
  name: z.string().min(1, "Template name is required"),
  description: z.string().optional(),
  type: z.enum([
    "RFQ",
    "CONTRACT",
    "PURCHASE_ORDER",
    "INVOICE",
    "BAST",
    "AANWIJZING",
    "EVALUATION_REPORT",
    "AWARD_LETTER",
    "CUSTOM",
  ]),
  category: z.string().min(1, "Category is required"),
  content: templateContentSchema,
  variables: z.array(templateVariableSchema),
});

// Template update schema
export const updateTemplateSchema = documentTemplateSchema.partial().extend({
  id: z.string().min(1, "Template ID is required"),
  changelog: z.string().optional(),
});

// Template approval schema
export const approveTemplateSchema = z.object({
  status: z.enum(["APPROVED", "REJECTED"]),
  comments: z.string().optional(),
});

// Document generation schema
export const generateDocumentSchema = z.object({
  templateId: z.string().min(1, "Template ID is required"),
  name: z.string().min(1, "Document name is required"),
  data: z.record(z.any()), // Key-value pairs for template variables
  format: z.enum(["html", "pdf"]).default("pdf"),
});

// Template search and filter schema
export const templateSearchSchema = z.object({
  search: z.string().optional(),
  type: z
    .enum([
      "RFQ",
      "CONTRACT",
      "PURCHASE_ORDER",
      "INVOICE",
      "BAST",
      "AANWIJZING",
      "EVALUATION_REPORT",
      "AWARD_LETTER",
      "CUSTOM",
    ])
    .optional(),
  category: z.string().optional(),
  status: z
    .enum(["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "ARCHIVED"])
    .optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
});

// Types
export type TemplateComponent = z.infer<typeof templateComponentSchema>;
export type TemplateVariable = z.infer<typeof templateVariableSchema>;
export type TemplateContent = z.infer<typeof templateContentSchema>;
export type DocumentTemplateInput = z.infer<typeof documentTemplateSchema>;
export type UpdateTemplateInput = z.infer<typeof updateTemplateSchema>;
export type ApproveTemplateInput = z.infer<typeof approveTemplateSchema>;
export type GenerateDocumentInput = z.infer<typeof generateDocumentSchema>;
export type TemplateSearchInput = z.infer<typeof templateSearchSchema>;
