import { z } from "zod";

// Purchase Requisition Item validation
export const purchaseRequisitionItemSchema = z.object({
  itemMasterId: z.string().optional(),
  name: z.string().min(1, "Item name is required"),
  description: z.string().optional(),
  quantity: z.number().positive("Quantity must be a positive number"),
  unit: z.string().min(1, "Unit is required"),
  estimatedPrice: z
    .number()
    .positive("Estimated price must be a positive number"),
});

// Purchase Requisition validation
export const purchaseRequisitionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  type: z.enum(["INTERNAL", "EXTERNAL_ROUTINE", "EXTERNAL_NON_ROUTINE"]),
  sourceContractId: z.string().optional(),
  items: z
    .array(purchaseRequisitionItemSchema)
    .min(1, "At least one item is required"),
});

// Purchase Requisition update validation
export const purchaseRequisitionUpdateSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  type: z
    .enum(["INTERNAL", "EXTERNAL_ROUTINE", "EXTERNAL_NON_ROUTINE"])
    .optional(),
  sourceContractId: z.string().optional(),
  items: z.array(purchaseRequisitionItemSchema).optional(),
});

// Purchase Requisition release validation
export const purchaseRequisitionReleaseSchema = z.object({
  action: z.enum(["APPROVE", "REJECT"]),
  comments: z.string().optional(),
});

// Procurement Package validation
export const procurementPackageSchema = z.object({
  name: z.string().min(1, "Package name is required"),
  requisitionIds: z
    .array(z.string())
    .min(1, "At least one purchase requisition is required"),
});

// Procurement Package conversion validation
export const procurementPackageConvertSchema = z.object({
  title: z.string().min(1, "Procurement title is required"),
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  submissionDeadline: z.string().datetime("Invalid submission deadline"),
  workTimeUnit: z.string().optional(),
  hpsIncludesVat: z.boolean().default(false),
  vatRate: z.number().min(0).max(1).optional(),
  evaluationTemplateId: z.string().optional(),
  committee: z
    .array(
      z.object({
        userId: z.string().min(1, "User ID is required"),
        role: z.string().min(1, "Role is required"),
      })
    )
    .min(1, "At least one committee member is required"),
});

// Types
export type PurchaseRequisitionInput = z.infer<
  typeof purchaseRequisitionSchema
>;
export type PurchaseRequisitionItemInput = z.infer<
  typeof purchaseRequisitionItemSchema
>;
export type PurchaseRequisitionUpdateInput = z.infer<
  typeof purchaseRequisitionUpdateSchema
>;
export type PurchaseRequisitionReleaseInput = z.infer<
  typeof purchaseRequisitionReleaseSchema
>;
export type ProcurementPackageInput = z.infer<typeof procurementPackageSchema>;
export type ProcurementPackageConvertInput = z.infer<
  typeof procurementPackageConvertSchema
>;
