import { z } from "zod";

// Base user validation
export const userSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

// Login validation
export const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

// Vendor registration validation
export const vendorRegistrationSchema = z
  .object({
    // User fields
    email: z.string().email("Invalid email address"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one lowercase letter, one uppercase letter, and one number"
      ),
    confirmPassword: z.string(),

    // Company fields
    companyName: z.string().min(1, "Company name is required"),
    companyType: z.string().min(1, "Company type is required"),
    businessLicense: z.string().optional(),
    npwpNumber: z
      .string()
      .regex(/^\d{15}$/, "NPWP number must be exactly 15 digits"),
    address: z.string().min(1, "Address is required"),
    npwpAddress: z.string().min(1, "NPWP address is required"),
    city: z.string().min(1, "City is required"),
    province: z.string().min(1, "Province is required"),
    postalCode: z.string().min(1, "Postal code is required"),
    businessCategory: z.string().min(1, "Business category is required"),
    phone: z
      .string()
      .regex(/^\+?[\d\s-()]{10,15}$/, "Invalid phone number format"),

    // PIC fields
    picName: z.string().min(1, "PIC name is required"),
    picEmail: z.string().email("Invalid PIC email address"),
    picPhone: z
      .string()
      .regex(/^\+?[\d\s-()]{10,15}$/, "Invalid PIC phone number format"),
    identityNumber: z.string().optional(),

    // Business entity type
    businessEntityType: z.enum(["PT", "CV", "UD", "KOPERASI", "YAYASAN"]),

    // Terms and conditions
    termsAndConditions: z.boolean().refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),

    // Documents (optional in schema, validated in component)
    documents: z
      .array(
        z.object({
          documentType: z.string(),
          fileName: z.string(),
          fileUrl: z.string().url(),
          fileType: z.string(),
        })
      )
      .optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

// Password reset validation
export const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export const resetPasswordSchema = z
  .object({
    token: z.string().min(1, "Reset token is required"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one lowercase letter, one uppercase letter, and one number"
      ),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

// Types
export type LoginInput = z.infer<typeof loginSchema>;
export type VendorRegistrationInput = z.infer<typeof vendorRegistrationSchema>;
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
