import { z } from "zod";

// Receipt Log validation
export const receiptLogSchema = z.object({
  quantityChange: z.number().min(0, "Quantity change must be non-negative"),
  notes: z.string().optional(),
});

// Good Receipt Item validation
export const goodReceiptItemSchema = z.object({
  purchaseOrderItemId: z.string().min(1, "Purchase order item ID is required"),
  receivedQuantity: z.number().min(0, "Received quantity must be non-negative"),
  notes: z.string().optional(),
  receiptLogs: z.array(receiptLogSchema).optional(),
});

// Good Receipt validation
export const goodReceiptSchema = z.object({
  poId: z.string().min(1, "Purchase order ID is required"),
  receivedDate: z.string().datetime("Invalid received date"),
  items: z.array(goodReceiptItemSchema).min(1, "At least one item is required"),
});

// Good Receipt update validation
export const goodReceiptUpdateSchema = z.object({
  receivedDate: z.string().datetime("Invalid received date").optional(),
  items: z.array(goodReceiptItemSchema).optional(),
});

// Good Receipt completion validation
export const goodReceiptCompleteSchema = z.object({
  finalNotes: z.string().optional(),
  confirmCompletion: z.boolean().refine((val) => val === true, {
    message: "Must confirm completion",
  }),
});

// BAST Checklist Item validation
export const bastChecklistItemSchema = z.object({
  description: z.string().min(1, "Description is required"),
  defectNotes: z.string().optional(),
  targetDate: z.string().datetime("Invalid target date").optional(),
});

// BAST validation
export const bastSchema = z.object({
  poId: z.string().min(1, "Purchase order ID is required"),
  handoverDate: z.string().datetime("Invalid handover date"),
  summary: z.string().optional(),
  checklist: z.array(bastChecklistItemSchema).optional(),
});

// BAST update validation
export const bastUpdateSchema = z.object({
  handoverDate: z.string().datetime("Invalid handover date").optional(),
  summary: z.string().optional(),
  checklist: z.array(bastChecklistItemSchema).optional(),
});

// BAST approval validation
export const bastApprovalSchema = z.object({
  action: z.enum(["APPROVE", "REJECT"]),
  comments: z.string().optional(),
});

// Types
export type ReceiptLogInput = z.infer<typeof receiptLogSchema>;
export type GoodReceiptItemInput = z.infer<typeof goodReceiptItemSchema>;
export type GoodReceiptInput = z.infer<typeof goodReceiptSchema>;
export type GoodReceiptUpdateInput = z.infer<typeof goodReceiptUpdateSchema>;
export type GoodReceiptCompleteInput = z.infer<
  typeof goodReceiptCompleteSchema
>;
export type BastChecklistItemInput = z.infer<typeof bastChecklistItemSchema>;
export type BastInput = z.infer<typeof bastSchema>;
export type BastUpdateInput = z.infer<typeof bastUpdateSchema>;
export type BastApprovalInput = z.infer<typeof bastApprovalSchema>;
