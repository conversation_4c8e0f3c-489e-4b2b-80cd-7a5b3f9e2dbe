import { z } from "zod";

// BAST Checklist item validation
export const bastChecklistItemSchema = z.object({
  description: z.string().min(1, "Checklist description is required"),
  defectNotes: z.string().optional(),
  targetDate: z.string().datetime("Invalid target date").optional(),
});

// BAST creation validation
export const bastSchema = z.object({
  poId: z.string().min(1, "Purchase order ID is required"),
  handoverDate: z.string().datetime("Invalid handover date"),
  summary: z.string().optional(),
  checklist: z
    .array(bastChecklistItemSchema)
    .min(1, "At least one checklist item is required"),
});

// BAST approval validation
export const bastApprovalSchema = z.object({
  action: z.enum(["APPROVE", "REJECT"], {
    required_error: "Approval action is required",
  }),
  comments: z.string().optional(),
});

// BAST update validation
export const bastUpdateSchema = z.object({
  handoverDate: z.string().datetime("Invalid handover date").optional(),
  summary: z.string().optional(),
  checklist: z.array(bastChecklistItemSchema).optional(),
});

// Export types
export type BASTInput = z.infer<typeof bastSchema>;
export type BASTChecklistItemInput = z.infer<typeof bastChecklistItemSchema>;
export type BASTApprovalInput = z.infer<typeof bastApprovalSchema>;
export type BASTUpdateInput = z.infer<typeof bastUpdateSchema>;
