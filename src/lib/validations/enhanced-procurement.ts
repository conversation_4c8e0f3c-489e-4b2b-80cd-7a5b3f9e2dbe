import { z } from "zod";

// Price Correction validation
export const priceCorrectionSchema = z.object({
  correctedPrice: z.number().positive("Corrected price must be positive"),
  reason: z.string().min(1, "Reason for correction is required"),
});

// Enhanced Procurement Creation validation
export const enhancedProcurementSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  estimatedValue: z.number().positive("Estimated value must be positive"),
  submissionDeadline: z.string().datetime("Invalid submission deadline"),

  // Enhanced fields from gap analysis
  workTimeUnit: z.string().optional(),
  hpsIncludesVat: z.boolean().default(false),
  vatRate: z.number().min(0).max(1).optional(),
  evaluationTemplateId: z.string().optional(),

  // Items
  items: z
    .array(
      z.object({
        name: z.string().min(1, "Item name is required"),
        description: z.string().optional(),
        quantity: z.number().positive("Quantity must be positive"),
        unit: z.string().min(1, "Unit is required"),
        estimatedPrice: z.number().positive("Estimated price must be positive"),
        specifications: z.record(z.any()).optional(),
      })
    )
    .min(1, "At least one item is required"),

  // Committee with replacement support
  committee: z
    .array(
      z.object({
        userId: z.string().min(1, "User ID is required"),
        role: z.string().min(1, "Role is required"),
        isPrimary: z.boolean().default(true),
        replacementFor: z.string().optional(), // ID of the member being replaced
      })
    )
    .min(1, "At least one committee member is required"),

  // Stages
  stages: z
    .array(
      z.object({
        name: z.string().min(1, "Stage name is required"),
        sequence: z.number().int().positive("Sequence must be positive"),
        startDate: z.string().datetime("Invalid start date"),
        endDate: z.string().datetime("Invalid end date"),
      })
    )
    .min(1, "At least one stage is required"),
});

// Evaluation Template validation
export const evaluationTemplateSchema = z.object({
  name: z.string().min(1, "Template name is required"),
  method: z.string().min(1, "Evaluation method is required"),
  passGrade: z.number().min(0).max(100).optional(),
  criteria: z.record(
    z.object({
      weight: z.number().min(0).max(100),
      description: z.string().min(1),
      type: z.enum(["TECHNICAL", "ADMINISTRATIVE", "PRICE", "EXPERIENCE"]),
      subCriteria: z
        .array(
          z.object({
            name: z.string().min(1),
            weight: z.number().min(0).max(100),
            description: z.string().optional(),
          })
        )
        .optional(),
    })
  ),
});

// Committee Member Replacement validation
export const committeeMemberReplacementSchema = z.object({
  originalMemberId: z.string().min(1, "Original member ID is required"),
  replacementUserId: z.string().min(1, "Replacement user ID is required"),
  reason: z.string().min(1, "Reason for replacement is required"),
  effectiveDate: z.string().datetime("Invalid effective date"),
});

// Enhanced Offer Evaluation validation
export const enhancedOfferEvaluationSchema = z.object({
  offerId: z.string().min(1, "Offer ID is required"),
  evaluationType: z.enum(["ADMINISTRATIVE", "TECHNICAL", "PRICE"]),
  scores: z.record(z.number().min(0).max(100)),
  comments: z.string().optional(),
  status: z.enum([
    "EVALUATING",
    "PASSED",
    "FAILED",
    "NEEDS_CLARIFICATION",
    "CONDITIONALLY_PASSED",
  ]),
  clarificationRequests: z
    .array(
      z.object({
        question: z.string().min(1, "Question is required"),
        category: z.string().min(1, "Category is required"),
        priority: z.enum(["LOW", "MEDIUM", "HIGH"]).default("MEDIUM"),
      })
    )
    .optional(),
});

// Three-way Matching validation
export const threeWayMatchingSchema = z.object({
  poId: z.string().min(1, "Purchase Order ID is required"),
  grId: z.string().min(1, "Good Receipt ID is required"),
  invoiceId: z.string().min(1, "Invoice ID is required"),
  matchingRules: z
    .object({
      allowQuantityVariance: z.boolean().default(false),
      maxQuantityVariancePercent: z.number().min(0).max(100).default(5),
      allowPriceVariance: z.boolean().default(false),
      maxPriceVariancePercent: z.number().min(0).max(100).default(2),
      requireExactMatch: z.boolean().default(true),
    })
    .optional(),
});

// Procurement Setup Wizard validation
export const procurementSetupWizardSchema = z.object({
  // Step 1: Basic Information
  basicInfo: z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().min(1, "Description is required"),
    category: z.string().min(1, "Category is required"),
    estimatedValue: z.number().positive("Estimated value must be positive"),
    workTimeUnit: z.string().optional(),
  }),

  // Step 2: Financial Settings
  financialSettings: z.object({
    hpsIncludesVat: z.boolean().default(false),
    vatRate: z.number().min(0).max(1).optional(),
    currency: z.string().default("IDR"),
    paymentTerms: z.string().optional(),
  }),

  // Step 3: Evaluation Setup
  evaluationSettings: z.object({
    evaluationTemplateId: z.string().optional(),
    customCriteria: z
      .record(
        z.object({
          weight: z.number().min(0).max(100),
          description: z.string().min(1),
          type: z.enum(["TECHNICAL", "ADMINISTRATIVE", "PRICE", "EXPERIENCE"]),
        })
      )
      .optional(),
    passGrade: z.number().min(0).max(100).optional(),
  }),

  // Step 4: Committee Setup
  committeeSetup: z.object({
    members: z
      .array(
        z.object({
          userId: z.string().min(1, "User ID is required"),
          role: z.string().min(1, "Role is required"),
          isPrimary: z.boolean().default(true),
        })
      )
      .min(1, "At least one committee member is required"),
    allowReplacements: z.boolean().default(true),
  }),

  // Step 5: Timeline
  timeline: z.object({
    submissionDeadline: z.string().datetime("Invalid submission deadline"),
    evaluationDate: z.string().datetime("Invalid evaluation date").optional(),
    awardDate: z.string().datetime("Invalid award date").optional(),
    stages: z
      .array(
        z.object({
          name: z.string().min(1, "Stage name is required"),
          sequence: z.number().int().positive("Sequence must be positive"),
          startDate: z.string().datetime("Invalid start date"),
          endDate: z.string().datetime("Invalid end date"),
        })
      )
      .min(1, "At least one stage is required"),
  }),

  // Step 6: Items
  items: z
    .array(
      z.object({
        name: z.string().min(1, "Item name is required"),
        description: z.string().optional(),
        quantity: z.number().positive("Quantity must be positive"),
        unit: z.string().min(1, "Unit is required"),
        estimatedPrice: z.number().positive("Estimated price must be positive"),
        specifications: z.record(z.any()).optional(),
      })
    )
    .min(1, "At least one item is required"),
});

// Types
export type PriceCorrectionInput = z.infer<typeof priceCorrectionSchema>;
export type EnhancedProcurementInput = z.infer<
  typeof enhancedProcurementSchema
>;
export type EvaluationTemplateInput = z.infer<typeof evaluationTemplateSchema>;
export type CommitteeMemberReplacementInput = z.infer<
  typeof committeeMemberReplacementSchema
>;
export type EnhancedOfferEvaluationInput = z.infer<
  typeof enhancedOfferEvaluationSchema
>;
export type ThreeWayMatchingInput = z.infer<typeof threeWayMatchingSchema>;
export type ProcurementSetupWizardInput = z.infer<
  typeof procurementSetupWizardSchema
>;
