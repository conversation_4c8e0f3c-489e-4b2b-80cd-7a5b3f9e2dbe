import { z } from "zod";

// Vendor Verification Step validation
export const vendorVerificationStepSchema = z.object({
  stepName: z.string().min(1, "Step name is required"),
  status: z.enum(["PENDING", "APPROVED", "REJECTED"]),
  comments: z.string().optional(),
});

// Vendor Verification validation
export const vendorVerificationSchema = z.object({
  vendorId: z.string().min(1, "Vendor ID is required"),
  steps: z
    .array(vendorVerificationStepSchema)
    .min(1, "At least one verification step is required"),
});

// Individual step verification
export const stepVerificationSchema = z.object({
  status: z.enum(["APPROVED", "REJECTED"]),
  comments: z.string().optional(),
});

// Vendor unlock request validation
export const vendorUnlockRequestSchema = z.object({
  sections: z.array(z.string()).min(1, "At least one section must be selected"),
  reason: z.string().min(10, "Reason must be at least 10 characters long"),
});

// Admin grant unlock validation
export const vendorUnlockGrantSchema = z.object({
  unlockRequestId: z.string().min(1, "Unlock request ID is required"),
  unlockDurationHours: z
    .number()
    .min(1)
    .max(168, "Unlock duration must be between 1 and 168 hours"), // Max 1 week
  adminComments: z.string().optional(),
});

// Legacy schema for backward compatibility
export const vendorUnlockSchema = z.object({
  reason: z.string().min(1, "Reason for unlock is required"),
  unlockDuration: z
    .number()
    .min(1)
    .max(168, "Unlock duration must be between 1 and 168 hours"), // Max 1 week
  allowedFields: z
    .array(z.string())
    .min(1, "At least one field must be specified for unlock"),
});

// Vendor profile update validation (when unlocked)
export const vendorProfileUpdateSchema = z.object({
  companyName: z.string().min(1, "Company name is required").optional(),
  companyType: z.string().min(1, "Company type is required").optional(),
  businessLicense: z.string().min(1, "Business license is required").optional(),
  taxId: z.string().min(1, "Tax ID is required").optional(),
  address: z.string().min(1, "Address is required").optional(),
  city: z.string().min(1, "City is required").optional(),
  province: z.string().min(1, "Province is required").optional(),
  postalCode: z.string().min(1, "Postal code is required").optional(),
  contactPerson: z.string().min(1, "Contact person is required").optional(),
  contactPhone: z.string().min(1, "Contact phone is required").optional(),
  contactEmail: z.string().email("Invalid email format").optional(),
  website: z.string().url("Invalid website URL").optional(),
  businessCategory: z
    .string()
    .min(1, "Business category is required")
    .optional(),
  businessDescription: z.string().optional(),
  establishedYear: z
    .number()
    .int()
    .min(1800)
    .max(new Date().getFullYear())
    .optional(),
  employeeCount: z.number().int().min(1).optional(),
  annualRevenue: z.number().min(0).optional(),
});

// Sanctioned Individual validation
export const sanctionedIndividualSchema = z.object({
  name: z.string().min(1, "Name is required"),
  identityNumber: z.string().min(1, "Identity number is required"),
  reason: z.string().min(1, "Reason is required"),
  sourceBlacklistId: z.string().optional(),
});

// Enhanced Blacklist validation
export const enhancedBlacklistSchema = z.object({
  vendorId: z.string().min(1, "Vendor ID is required"),
  reason: z.string().min(1, "Reason is required"),
  severity: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]),
  category: z.enum([
    "QUALITY_ISSUES",
    "DELIVERY_DELAYS",
    "CONTRACT_BREACH",
    "FRAUD",
    "CORRUPTION",
    "NON_COMPLIANCE",
    "POOR_PERFORMANCE",
    "LEGAL_ISSUES",
    "OTHER",
  ]),
  description: z.string().min(1, "Description is required"),
  evidence: z.string().optional(),
  startDate: z.string().datetime("Invalid start date"),
  endDate: z.string().datetime("Invalid end date").optional(),
  sanctionedIndividuals: z.array(sanctionedIndividualSchema).optional(),
});

// Blacklist appeal validation
export const blacklistAppealSchema = z.object({
  blacklistId: z.string().min(1, "Blacklist ID is required"),
  appealReason: z.string().min(1, "Appeal reason is required"),
  evidence: z.string().optional(),
  correctiveActions: z.string().min(1, "Corrective actions are required"),
});

// Vendor status update validation
export const vendorStatusUpdateSchema = z.object({
  status: z.enum([
    "PENDING_VERIFICATION",
    "VERIFIED",
    "REJECTED",
    "SUSPENDED",
    "BLACKLISTED",
  ]),
  reason: z.string().optional(),
  notes: z.string().optional(),
});

// Types
export type VendorVerificationStepInput = z.infer<
  typeof vendorVerificationStepSchema
>;
export type VendorVerificationInput = z.infer<typeof vendorVerificationSchema>;
export type StepVerificationInput = z.infer<typeof stepVerificationSchema>;
export type VendorUnlockRequestInput = z.infer<
  typeof vendorUnlockRequestSchema
>;
export type VendorUnlockGrantInput = z.infer<typeof vendorUnlockGrantSchema>;
export type VendorUnlockInput = z.infer<typeof vendorUnlockSchema>;
export type VendorProfileUpdateInput = z.infer<
  typeof vendorProfileUpdateSchema
>;
export type SanctionedIndividualInput = z.infer<
  typeof sanctionedIndividualSchema
>;
export type EnhancedBlacklistInput = z.infer<typeof enhancedBlacklistSchema>;
export type BlacklistAppealInput = z.infer<typeof blacklistAppealSchema>;
export type VendorStatusUpdateInput = z.infer<typeof vendorStatusUpdateSchema>;
