/**
 * API client utilities for making authenticated requests
 */

interface ApiRequestOptions extends RequestInit {
  requireAuth?: boolean;
}

/**
 * Make an authenticated API request with proper cookie handling
 */
export async function apiRequest<T = any>(
  url: string,
  options: ApiRequestOptions = {}
): Promise<T> {
  const { requireAuth = true, ...fetchOptions } = options;

  const defaultOptions: RequestInit = {
    credentials: "include", // Always include cookies
    headers: {
      "Content-Type": "application/json",
      ...fetchOptions.headers,
    },
    ...fetchOptions,
  };

  const response = await fetch(url, defaultOptions);

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: "Request failed" }));
    throw new Error(error.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

/**
 * GET request with authentication
 */
export async function apiGet<T = any>(url: string): Promise<T> {
  return apiRequest<T>(url, { method: "GET" });
}

/**
 * POST request with authentication
 */
export async function apiPost<T = any>(url: string, data?: any): Promise<T> {
  return apiRequest<T>(url, {
    method: "POST",
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * PUT request with authentication
 */
export async function apiPut<T = any>(url: string, data?: any): Promise<T> {
  return apiRequest<T>(url, {
    method: "PUT",
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * PATCH request with authentication
 */
export async function apiPatch<T = any>(url: string, data?: any): Promise<T> {
  return apiRequest<T>(url, {
    method: "PATCH",
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * DELETE request with authentication
 */
export async function apiDelete<T = any>(url: string): Promise<T> {
  return apiRequest<T>(url, { method: "DELETE" });
}
