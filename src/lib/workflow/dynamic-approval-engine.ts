import { prisma } from "@/lib/db";
import { auditLogger } from "@/lib/audit/comprehensive-audit-logger";

export interface ApprovalWorkflowConfig {
  id: string;
  name: string;
  entityType: string;
  description?: string;
  enabled: boolean;
  priority: number;
  conditions: ApprovalCondition[];
  steps: ApprovalStep[];
  escalationRules: EscalationRule[];
  delegationRules: DelegationRule[];
  settings: WorkflowSettings;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApprovalCondition {
  field: string;
  operator:
    | "EQUALS"
    | "NOT_EQUALS"
    | "GREATER_THAN"
    | "LESS_THAN"
    | "GREATER_EQUAL"
    | "LESS_EQUAL"
    | "IN"
    | "NOT_IN"
    | "CONTAINS";
  value: any;
  logicalOperator?: "AND" | "OR";
}

export interface ApprovalStep {
  id: string;
  name: string;
  sequence: number;
  type: "SINGLE" | "PARALLEL" | "SEQUENTIAL" | "COMMITTEE";
  approvers: ApproverConfig[];
  requiredApprovals: number;
  conditions?: ApprovalCondition[];
  timeoutHours: number;
  allowDelegation: boolean;
  allowSkip: boolean;
  skipConditions?: ApprovalCondition[];
  actions: StepAction[];
}

export interface ApproverConfig {
  type: "USER" | "ROLE" | "DEPARTMENT" | "DYNAMIC";
  value: string;
  weight?: number;
  required?: boolean;
  conditions?: ApprovalCondition[];
}

export interface EscalationRule {
  stepId: string;
  afterHours: number;
  escalateTo: ApproverConfig[];
  action: "NOTIFY" | "REASSIGN" | "AUTO_APPROVE" | "AUTO_REJECT";
  notificationTemplate?: string;
}

export interface DelegationRule {
  fromUserId: string;
  toUserId: string;
  entityTypes: string[];
  conditions?: ApprovalCondition[];
  startDate: Date;
  endDate: Date;
  enabled: boolean;
  reason?: string;
  isActive?: boolean;
}

export interface StepAction {
  type:
    | "UPDATE_STATUS"
    | "SEND_NOTIFICATION"
    | "CREATE_RECORD"
    | "CALL_INTEGRATION"
    | "GENERATE_DOCUMENT";
  parameters: Record<string, any>;
  executeOn: "APPROVED" | "REJECTED" | "TIMEOUT" | "ALWAYS";
}

export interface WorkflowSettings {
  allowParallelExecution: boolean;
  requireAllApprovals: boolean;
  autoProgressOnApproval: boolean;
  notifyOnStart: boolean;
  notifyOnComplete: boolean;
  retainHistory: boolean;
  maxRetries: number;
}

export interface ApprovalInstance {
  id: string;
  workflowConfigId: string;
  entityType: string;
  entityId: string;
  status:
    | "PENDING"
    | "IN_PROGRESS"
    | "APPROVED"
    | "REJECTED"
    | "CANCELLED"
    | "TIMEOUT";
  currentStepId?: string;
  startedBy: string;
  startedAt: Date;
  completedAt?: Date;
  steps: StepInstance[];
  context: Record<string, any>;
  metadata: Record<string, any>;
}

export interface StepInstance {
  id: string;
  stepConfigId: string;
  name: string;
  sequence: number;
  status:
    | "PENDING"
    | "IN_PROGRESS"
    | "APPROVED"
    | "REJECTED"
    | "SKIPPED"
    | "TIMEOUT";
  assignedTo: string[];
  approvals: ApprovalDecision[];
  startedAt?: Date;
  completedAt?: Date;
  timeoutAt?: Date;
  escalated: boolean;
  delegated: boolean;
}

export interface ApprovalDecision {
  id: string;
  stepInstanceId: string;
  userId: string;
  decision: "APPROVED" | "REJECTED";
  comments?: string;
  decidedAt: Date;
  weight: number;
  isDelegated: boolean;
  delegatedFrom?: string;
}

export class DynamicApprovalEngine {
  async startApprovalWorkflow(
    entityType: string,
    entityId: string,
    entityData: Record<string, any>,
    startedBy: string,
    context: Record<string, any> = {}
  ): Promise<ApprovalInstance> {
    // Find applicable workflow configuration
    const workflowConfig = await this.findApplicableWorkflow(
      entityType,
      entityData
    );

    if (!workflowConfig) {
      throw new Error(`No approval workflow found for ${entityType}`);
    }

    // Create approval instance
    const approvalInstance = await this.createApprovalInstance(
      workflowConfig,
      entityType,
      entityId,
      startedBy,
      context
    );

    // Start the first step
    await this.startNextStep(approvalInstance, entityData);

    // Log workflow start
    await auditLogger.logWorkflowStateChange(
      startedBy,
      approvalInstance.id,
      "APPROVAL_WORKFLOW",
      "CREATED",
      "IN_PROGRESS",
      entityType,
      entityId
    );

    return approvalInstance;
  }

  async processApprovalDecision(
    approvalInstanceId: string,
    stepInstanceId: string,
    userId: string,
    decision: "APPROVED" | "REJECTED",
    comments?: string
  ): Promise<ApprovalInstance> {
    // Get approval instance
    const approvalInstance = await this.getApprovalInstance(approvalInstanceId);
    if (!approvalInstance) {
      throw new Error("Approval instance not found");
    }

    // Get step instance
    const stepInstance = approvalInstance.steps.find(
      (s) => s.id === stepInstanceId
    );
    if (!stepInstance) {
      throw new Error("Step instance not found");
    }

    // Check if user is authorized to approve
    if (!stepInstance.assignedTo.includes(userId)) {
      throw new Error("User not authorized to approve this step");
    }

    // Check if user has already provided decision
    const existingDecision = stepInstance.approvals.find(
      (a) => a.userId === userId
    );
    if (existingDecision) {
      throw new Error("User has already provided approval decision");
    }

    // Check for delegation
    const delegation = await this.checkDelegation(
      userId,
      approvalInstance.entityType
    );
    const effectiveUserId = delegation?.toUserId || userId;

    // Create approval decision
    const approvalDecision: ApprovalDecision = {
      id: `approval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      stepInstanceId,
      userId: effectiveUserId,
      decision,
      comments,
      decidedAt: new Date(),
      weight: 1, // This could be configurable based on user role
      isDelegated: !!delegation,
      delegatedFrom: delegation?.fromUserId,
    };

    // Add decision to step
    stepInstance.approvals.push(approvalDecision);

    // Check if step is complete
    const stepComplete = await this.checkStepCompletion(
      stepInstance,
      approvalInstance
    );

    if (stepComplete) {
      stepInstance.status = stepInstance.approvals.some(
        (a) => a.decision === "REJECTED"
      )
        ? "REJECTED"
        : "APPROVED";
      stepInstance.completedAt = new Date();

      // Execute step actions
      await this.executeStepActions(stepInstance, approvalInstance);

      // Check if workflow is complete
      const workflowComplete = await this.checkWorkflowCompletion(
        approvalInstance
      );

      if (workflowComplete) {
        approvalInstance.status = approvalInstance.steps.some(
          (s) => s.status === "REJECTED"
        )
          ? "REJECTED"
          : "APPROVED";
        approvalInstance.completedAt = new Date();
      } else if (stepInstance.status === "APPROVED") {
        // Start next step
        await this.startNextStep(approvalInstance, approvalInstance.context);
      }
    }

    // Update approval instance
    await this.updateApprovalInstance(approvalInstance);

    // Log approval decision
    await auditLogger.logWorkflowStateChange(
      effectiveUserId,
      approvalInstance.id,
      "APPROVAL_STEP",
      stepInstance.status === "PENDING" ? "PENDING" : "COMPLETED",
      stepInstance.status,
      approvalInstance.entityType,
      approvalInstance.entityId,
      comments
    );

    return approvalInstance;
  }

  private async findApplicableWorkflow(
    entityType: string,
    entityData: Record<string, any>
  ): Promise<ApprovalWorkflowConfig | null> {
    // Get all enabled workflows for this entity type
    const workflows = await prisma.approvalWorkflowConfig.findMany({
      where: {
        entityType,
        isActive: true,
      },
      orderBy: {
        createdAt: "desc", // Higher priority first
      },
    });

    // Find the first workflow that matches conditions
    for (const workflow of workflows) {
      const conditionsMet = this.evaluateConditions(
        (workflow as any).conditions || [],
        entityData
      );
      if (conditionsMet) {
        return workflow as any;
      }
    }

    return null;
  }

  private evaluateConditions(
    conditions: ApprovalCondition[],
    data: Record<string, any>
  ): boolean {
    if (conditions.length === 0) return true;

    let result = true;
    let currentOperator: "AND" | "OR" = "AND";

    for (const condition of conditions) {
      const conditionResult = this.evaluateCondition(condition, data);

      if (currentOperator === "AND") {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }

      currentOperator = condition.logicalOperator || "AND";
    }

    return result;
  }

  private evaluateCondition(
    condition: ApprovalCondition,
    data: Record<string, any>
  ): boolean {
    const fieldValue = this.getNestedValue(data, condition.field);

    switch (condition.operator) {
      case "EQUALS":
        return fieldValue === condition.value;
      case "NOT_EQUALS":
        return fieldValue !== condition.value;
      case "GREATER_THAN":
        return Number(fieldValue) > Number(condition.value);
      case "LESS_THAN":
        return Number(fieldValue) < Number(condition.value);
      case "GREATER_EQUAL":
        return Number(fieldValue) >= Number(condition.value);
      case "LESS_EQUAL":
        return Number(fieldValue) <= Number(condition.value);
      case "IN":
        return (
          Array.isArray(condition.value) && condition.value.includes(fieldValue)
        );
      case "NOT_IN":
        return (
          Array.isArray(condition.value) &&
          !condition.value.includes(fieldValue)
        );
      case "CONTAINS":
        return String(fieldValue).includes(String(condition.value));
      default:
        return false;
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split(".").reduce((current, key) => current?.[key], obj);
  }

  // Create approval instance in database
  private async createApprovalInstance(
    config: ApprovalWorkflowConfig,
    entityType: string,
    entityId: string,
    startedBy: string,
    context: any
  ): Promise<ApprovalInstance> {
    const instanceId = `approval_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    const instance: ApprovalInstance = {
      id: instanceId,
      workflowConfigId: config.id,
      entityType,
      entityId,
      status: "IN_PROGRESS",
      startedBy,
      startedAt: new Date(),
      steps: config.steps.map((step) => ({
        id: `step_${Date.now()}_${step.sequence}_${Math.random()
          .toString(36)
          .substr(2, 9)}`,
        stepConfigId: step.id,
        name: step.name,
        sequence: step.sequence,
        status: "PENDING",
        assignedTo: [],
        approvals: [],
        escalated: false,
        delegated: false,
      })),
      context,
      metadata: {},
    };

    // Save to database
    await prisma.approvalInstance.create({
      data: {
        id: instance.id,
        workflow: { connect: { id: config.id } },
        entityType,
        entityId,
        status: instance.status,
        initiatedBy: { connect: { id: startedBy } },
        startedAt: instance.startedAt,
        context: instance.context,
        metadata: instance.metadata,
      },
    });

    // Create step instances
    for (const step of instance.steps) {
      await prisma.approvalStepInstance.create({
        data: {
          id: step.id,
          instance: { connect: { id: instance.id } },
          step: { connect: { id: step.stepConfigId || step.id } },
          sequence: step.sequence,
          status: step.status,
          assignedTo: Array.isArray(step.assignedTo) ? step.assignedTo.join(',') : step.assignedTo,
        },
      });
    }

    return instance;
  }

  private async startNextStep(
    instance: ApprovalInstance,
    entityData: any
  ): Promise<void> {
    const nextStep = instance.steps.find((s) => s.status === "PENDING");
    if (!nextStep) return;

    // Get step configuration
    const stepConfig = await prisma.approvalStep.findUnique({
      where: { id: nextStep.stepConfigId },
    });

    if (!stepConfig) return;

    // Assign approvers based on step configuration
    const assignedTo = await this.assignApprovers(
      stepConfig as any,
      entityData
    );

    // Update step instance
    await prisma.approvalStepInstance.update({
      where: { id: nextStep.id },
      data: {
        status: "IN_PROGRESS",
        assignedTo: Array.isArray(assignedTo) ? assignedTo.join(',') : assignedTo,
        startedAt: new Date(),
      },
    });

    nextStep.status = "IN_PROGRESS";
    nextStep.assignedTo = assignedTo;
    nextStep.startedAt = new Date();
  }

  private async getApprovalInstance(
    id: string
  ): Promise<ApprovalInstance | null> {
    const instance = await prisma.approvalInstance.findUnique({
      where: { id },
      include: {
        stepInstances: {
          include: {
            approvals: true,
          },
          orderBy: { sequence: "asc" },
        },
      },
    });

    if (!instance) return null;

    return {
      id: instance.id,
      workflowConfigId: instance.workflowConfigId || "",
      entityType: instance.entityType,
      entityId: instance.entityId,
      status: instance.status as any,
      currentStepId: instance.stepInstances.find(
        (s: any) => s.status === "IN_PROGRESS"
      )?.id,
      startedBy: instance.startedBy || "",
      startedAt: instance.startedAt,
      completedAt: instance.completedAt || undefined,
      steps: instance.stepInstances.map((si: any) => ({
        id: si.id,
        stepConfigId: si.stepConfigId,
        name: si.name,
        sequence: si.sequence,
        status: si.status as any,
        assignedTo: si.assignedTo as string[],
        approvals: si.approvals.map((a: any) => ({
          id: a.id,
          stepInstanceId: a.stepInstanceId,
          userId: a.performedById,
          decision: a.action as any,
          comments: a.comments || undefined,
          decidedAt: a.performedAt,
          weight: 1,
          isDelegated: false,
        })),
        startedAt: si.startedAt || undefined,
        completedAt: si.completedAt || undefined,
        timeoutAt: si.timeoutAt || undefined,
        escalated: false,
        delegated: false,
      })),
      context: instance.context as any,
      metadata: instance.metadata as any,
    };
  }

  private async updateApprovalInstance(
    instance: ApprovalInstance
  ): Promise<void> {
    await prisma.approvalInstance.update({
      where: { id: instance.id },
      data: {
        status: instance.status,
        completedAt: instance.completedAt,
        context: instance.context,
        metadata: instance.metadata,
      },
    });
  }

  private async checkStepCompletion(
    step: StepInstance,
    instance: ApprovalInstance
  ): Promise<boolean> {
    // Get step configuration to check required approvals
    const stepConfig = await prisma.approvalStep.findUnique({
      where: { id: step.stepConfigId },
    });

    if (!stepConfig) return false;

    const requiredApprovals = stepConfig.requiredCount || 1;
    const approvedCount = step.approvals.filter(
      (a) => a.decision === "APPROVED"
    ).length;
    const rejectedCount = step.approvals.filter(
      (a) => a.decision === "REJECTED"
    ).length;

    // Step is complete if we have enough approvals or any rejection
    return approvedCount >= requiredApprovals || rejectedCount > 0;
  }

  private async checkWorkflowCompletion(
    instance: ApprovalInstance
  ): Promise<boolean> {
    return instance.steps.every((s) =>
      ["APPROVED", "REJECTED", "SKIPPED"].includes(s.status)
    );
  }

  private async executeStepActions(
    step: StepInstance,
    instance: ApprovalInstance
  ): Promise<void> {
    // Execute any configured actions for this step
    // This could include notifications, status updates, etc.

    // Log step completion
    await auditLogger.logWorkflowStateChange(
      instance.startedBy || "",
      instance.id,
      "APPROVAL_STEP",
      "IN_PROGRESS",
      step.status,
      instance.entityType,
      instance.entityId
    );
  }

  private async checkDelegation(
    userId: string,
    entityType: string
  ): Promise<DelegationRule | null> {
    // Check if user has delegated their approval authority
    const delegation = await prisma.approvalDelegation.findFirst({
      where: {
        fromUserId: userId,
        entityType,
        isActive: true,
        startDate: { lte: new Date() },
        endDate: { gte: new Date() },
      },
    });

    if (!delegation) return null;

    return {
      fromUserId: delegation.fromUserId,
      toUserId: delegation.toUserId,
      entityTypes: [delegation.entityType],
      startDate: delegation.startDate,
      endDate: delegation.endDate,
      enabled: delegation.isActive,
      reason: delegation.reason || "",
      isActive: delegation.isActive,
    };
  }

  private async assignApprovers(
    stepConfig: any,
    entityData: any
  ): Promise<string[]> {
    const approverIds: string[] = [];

    for (const approverConfig of stepConfig.approvers || []) {
      switch (approverConfig.type) {
        case "USER":
          approverIds.push(approverConfig.value);
          break;

        case "ROLE":
          const roleUsers = await prisma.user.findMany({
            where: {
              roles: { has: approverConfig.value },
              isActive: true,
            },
            select: { id: true },
          });
          approverIds.push(...roleUsers.map((u: any) => u.id));
          break;

        case "DEPARTMENT":
          const deptUsers = await prisma.user.findMany({
            where: {
              department: approverConfig.value,
              isActive: true,
            },
            select: { id: true },
          });
          approverIds.push(...deptUsers.map((u: any) => u.id));
          break;
      }
    }

    return Array.from(new Set(approverIds)); // Remove duplicates
  }
}

export const dynamicApprovalEngine = new DynamicApprovalEngine();
