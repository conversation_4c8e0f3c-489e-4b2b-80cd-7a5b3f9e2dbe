import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { cookies } from "next/headers";
import { NextRequest } from "next/server";

import { prisma } from "./db";
import { UnauthorizedError } from "./errors";

const JWT_SECRET = process.env.JWT_SECRET || "fallback-secret-key";
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || "12");

export interface JWTPayload {
  userId: string;
  email: string;
  roles: string[];
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, BCRYPT_ROUNDS);
}

export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

export function generateToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: "7d" });
}

export function verifyToken(token: string): JWTPayload {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  } catch (error) {
    throw new UnauthorizedError("Invalid token");
  }
}

export async function getCurrentUser(request: NextRequest) {
  console.log("🔍 AUTH: getCurrentUser called for URL:", request.url);
  
  const cookieStore = await cookies();
  let token = cookieStore.get("auth-token")?.value;
  
  console.log("🍪 Cookie token found:", !!token);

  // If no cookie token, try Authorization header as fallback
  if (!token) {
    const authHeader = request.headers.get("authorization");
    console.log("🔑 Authorization header:", authHeader ? "present" : "missing");
    console.log("🔍 Authorization header value:", authHeader ? `"${authHeader}"` : "null");
    
    if (authHeader) {
      if (authHeader.startsWith("Bearer ")) {
        const extractedToken = authHeader.substring(7).trim();
        console.log("🎫 Bearer token extracted:", !!extractedToken);
        console.log("🔒 Token length:", extractedToken ? extractedToken.length : 0);
        
        // Check if token is empty or just whitespace
        if (!extractedToken) {
          console.log("❌ Authorization header contains 'Bearer' but no token");
          throw new UnauthorizedError("Authorization header is malformed: 'Bearer' found but no token provided");
        }
        
        token = extractedToken;
      } else if (authHeader.toLowerCase() === "bearer") {
        console.log("❌ Authorization header is just 'Bearer' without token");
        throw new UnauthorizedError("Authorization header is malformed: missing token after 'Bearer'");
      } else {
        console.log("⚠️ Authorization header does not start with 'Bearer '");
        console.log("🔍 Header format:", authHeader.substring(0, 20) + "...");
        // Try extracting token without Bearer prefix as fallback
        token = authHeader;
        console.log("🎫 Raw token extracted (no Bearer):", !!token);
      }
    }
  }

  if (!token) {
    console.log("❌ AUTH: No authentication token found for:", request.url);
    throw new UnauthorizedError("No authentication token found");
  }

  console.log("🔓 Token found, attempting verification...");
  console.log("🔍 Token preview:", token.substring(0, 20) + "...");

  let payload;
  try {
    payload = verifyToken(token);
    console.log("✅ Token verification successful");
    console.log("👤 User ID from token:", payload.userId);
  } catch (error) {
    console.log("❌ Token verification failed:", error instanceof Error ? error.message : String(error));
    console.log("🔍 Token that failed:", token.substring(0, 20) + "...");
    
    // If token verification fails due to signature mismatch, clear the stale cookie
    if (error instanceof UnauthorizedError) {
      await clearAuthCookie();
    }
    throw error;
  }

  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: {
      vendor: true,
    },
  });

  if (!user) {
    throw new UnauthorizedError("User not found");
  }

  return user;
}

export async function setAuthCookie(token: string) {
  const cookieStore = await cookies();
  cookieStore.set("auth-token", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 7 * 24 * 60 * 60, // 7 days
    path: "/",
  });
}

export async function clearAuthCookie() {
  const cookieStore = await cookies();
  cookieStore.delete("auth-token");
}

export function requireAuth(roles?: string[]) {
  return async (request: NextRequest) => {
    const user = await getCurrentUser(request);

    if (roles && roles.length > 0) {
      const hasRequiredRole = roles.some((role) =>
        user.roles.includes(role as any)
      );
      if (!hasRequiredRole) {
        throw new UnauthorizedError("Insufficient permissions");
      }
    }

    return user;
  };
}

export async function generatePasswordResetToken(): Promise<string> {
  const crypto = await import("crypto");
  return crypto.randomBytes(32).toString("hex");
}
