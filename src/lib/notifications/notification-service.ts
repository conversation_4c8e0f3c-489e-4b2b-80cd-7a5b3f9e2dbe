import { prisma } from "@/lib/db";

export interface NotificationData {
  userId: string;
  title: string;
  message: string;
  type?: "INFO" | "SUCCESS" | "WARNING" | "ERROR";
  metadata?: Record<string, any>;
}

export interface EmailNotificationData {
  to: string;
  subject: string;
  template: string;
  data: Record<string, any>;
}

// Create in-app notification
export async function createNotification(
  data: NotificationData
): Promise<any> {
  const notification = await prisma.notification.create({
    data: {
      userId: data.userId,
      title: data.title,
      message: data.message,
      type: data.type || "INFO",
      metadata: data.metadata,
    },
  });

  return notification;
}

// Create multiple notifications
export async function createBulkNotifications(
  notifications: NotificationData[]
): Promise<string[]> {
  const createdNotifications = await prisma.notification.createMany({
    data: notifications.map((notif: NotificationData) => ({
      userId: notif.userId,
      title: notif.title,
      message: notif.message,
      type: notif.type || "INFO",
      metadata: notif.metadata,
    })),
  });

  // Get the created notification IDs (this is a limitation of createMany)
  const latestNotifications = await prisma.notification.findMany({
    where: {
      userId: { in: notifications.map((n: NotificationData) => n.userId) },
    },
    orderBy: { createdAt: "desc" },
    take: notifications.length,
    select: { id: true },
  });

  return latestNotifications.map((n: { id: string }) => n.id);
}

// Get user notifications
export async function getUserNotifications(
  userId: string,
  options: {
    unreadOnly?: boolean;
    limit?: number;
    offset?: number;
  } = {}
): Promise<{
  notifications: Array<{
    id: string;
    title: string;
    message: string;
    type: string;
    read: boolean;
    metadata?: any;
    createdAt: Date;
  }>;
  unreadCount: number;
  totalCount: number;
}> {
  const { unreadOnly = false, limit = 20, offset = 0 } = options;

  const whereClause: any = { userId };
  if (unreadOnly) {
    whereClause.read = false;
  }

  const [notifications, unreadCount, totalCount] = await Promise.all([
    prisma.notification.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      take: limit,
      skip: offset,
      select: {
        id: true,
        title: true,
        message: true,
        type: true,
        read: true,
        metadata: true,
        createdAt: true,
      },
    }),
    prisma.notification.count({
      where: { userId, read: false },
    }),
    prisma.notification.count({
      where: { userId },
    }),
  ]);

  return {
    notifications,
    unreadCount,
    totalCount,
  };
}

// Mark notification as read
export async function markNotificationAsRead(
  notificationId: string,
  userId: string
): Promise<void> {
  await prisma.notification.updateMany({
    where: {
      id: notificationId,
      userId, // Ensure user can only mark their own notifications
    },
    data: {
      read: true,
    },
  });
}

// Mark all notifications as read
export async function markAllNotificationsAsRead(
  userId: string
): Promise<void> {
  await prisma.notification.updateMany({
    where: {
      userId,
      read: false,
    },
    data: {
      read: true,
    },
  });
}

// Delete notification
export async function deleteNotification(
  notificationId: string,
  userId: string
): Promise<void> {
  await prisma.notification.deleteMany({
    where: {
      id: notificationId,
      userId, // Ensure user can only delete their own notifications
    },
  });
}

// Procurement-specific notification helpers
export async function notifyProcurementCommittee(
  procurementId: string,
  title: string,
  message: string,
  type: "INFO" | "SUCCESS" | "WARNING" | "ERROR" = "INFO",
  metadata?: Record<string, any>
): Promise<void> {
  // Get committee members
  const committee = await prisma.procurementCommitteeMember.findMany({
    where: { procurementId },
    include: {
      user: {
        select: { id: true },
      },
    },
  });

  if (committee.length === 0) return;

  // Create notifications for all committee members
  const notifications: NotificationData[] = committee.map(
    (member: { user: { id: string } }) => ({
      userId: member.user.id,
      title,
      message,
      type,
      metadata: {
        ...metadata,
        procurementId,
        source: "procurement_committee",
      },
    })
  );

  await createBulkNotifications(notifications);
}

// Vendor-specific notification helpers
export async function notifyVendor(
  vendorId: string,
  title: string,
  message: string,
  type: "INFO" | "SUCCESS" | "WARNING" | "ERROR" = "INFO",
  metadata?: Record<string, any>
): Promise<void> {
  // Get vendor user
  const vendor = await prisma.vendor.findUnique({
    where: { id: vendorId },
    include: {
      user: {
        select: { id: true },
      },
    },
  });

  if (!vendor) return;

  await createNotification({
    userId: vendor.user.id,
    title,
    message,
    type,
    metadata: {
      ...metadata,
      vendorId,
      source: "vendor",
    },
  });
}

// Procurement workflow notifications
export async function notifyOfferSubmitted(
  procurementId: string,
  vendorCompanyName: string
): Promise<void> {
  await notifyProcurementCommittee(
    procurementId,
    "Penawaran Baru Diterima",
    `Penawaran baru telah diterima dari ${vendorCompanyName}`,
    "INFO",
    { action: "offer_submitted" }
  );
}

export async function notifyOfferEvaluated(
  offerId: string,
  vendorId: string,
  evaluatorName: string
): Promise<void> {
  await notifyVendor(
    vendorId,
    "Penawaran Telah Dievaluasi",
    `Penawaran Anda telah dievaluasi oleh ${evaluatorName}`,
    "INFO",
    { action: "offer_evaluated", offerId }
  );
}

export async function notifyVendorVerified(vendorId: string): Promise<void> {
  await notifyVendor(
    vendorId,
    "Akun Vendor Terverifikasi",
    "Selamat! Akun vendor Anda telah berhasil diverifikasi. Anda sekarang dapat mengikuti pengadaan.",
    "SUCCESS",
    { action: "vendor_verified" }
  );
}

export async function notifyVendorRejected(
  vendorId: string,
  rejectionReason: string
): Promise<void> {
  await notifyVendor(
    vendorId,
    "Verifikasi Vendor Ditolak",
    `Maaf, verifikasi akun vendor Anda ditolak. Alasan: ${rejectionReason}`,
    "ERROR",
    { action: "vendor_rejected", rejectionReason }
  );
}

export async function notifyProcurementStatusChange(
  procurementId: string,
  newStatus: string,
  statusDescription: string
): Promise<void> {
  await notifyProcurementCommittee(
    procurementId,
    "Status Pengadaan Berubah",
    `Status pengadaan telah berubah menjadi: ${statusDescription}`,
    "INFO",
    { action: "procurement_status_change", newStatus }
  );
}

// Email notification implementation using Nodemailer
export async function sendEmailNotification(
  data: EmailNotificationData
): Promise<void> {
  try {
    const nodemailer = await import('nodemailer');

    // Create transporter based on environment
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
      auth: process.env.SMTP_USER ? {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      } : undefined,
      // For development, use ethereal email or log to console
      ...(process.env.NODE_ENV === 'development' && !process.env.SMTP_HOST ? {
        streamTransport: true,
        newline: 'unix',
        buffer: true
      } : {})
    });

    // Generate email content from template
    const emailContent = await generateEmailContent(data.template, data.data);

    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: Array.isArray(data.to) ? data.to.join(', ') : data.to,
      subject: data.subject,
      html: emailContent.html,
      text: emailContent.text,
    };

    // Send email
    const result = await transporter.sendMail(mailOptions);

    // Log success
    console.log('Email sent successfully:', {
      messageId: result.messageId,
      to: data.to,
      subject: data.subject,
    });

    // In development, log the email content
    if (process.env.NODE_ENV === 'development' && result.messageId) {
      console.log('Email sent with messageId:', result.messageId);
    }

  } catch (error) {
    console.error('Failed to send email:', error);

    // In production, queue failed emails for retry
    if (process.env.NODE_ENV === 'production') {
      await queueFailedEmail(data, error as Error);
      throw new Error('Email sending failed - queued for retry');
    } else {
      // In development, just log the error but don't throw
      console.log('Email notification (dev mode):', {
        to: data.to,
        subject: data.subject,
        template: data.template,
        data: data.data,
      });
    }
  }
}

// Email template generation
async function generateEmailContent(
  template: string,
  data: Record<string, any>
): Promise<{ html: string; text: string }> {
  // Use dynamic import with proper error handling
  let handlebars: typeof import('handlebars');
  try {
    handlebars = await import('handlebars');
  } catch (error) {
    console.error('Failed to import handlebars:', error);
    // Fallback to simple string replacement if handlebars fails
    return generateEmailContentFallback(template, data);
  }

  // Email templates
  const templates: Record<string, { html: string; text: string }> = {
    'vendor-status-change': {
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Status Vendor Diperbarui</h2>
          <p>Halo {{vendorName}},</p>
          <p>Status vendor Anda telah diperbarui menjadi: <strong>{{status}}</strong></p>
          {{#if reason}}
          <p><strong>Alasan:</strong> {{reason}}</p>
          {{/if}}
          <p>Silakan login ke sistem untuk informasi lebih lanjut.</p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">Email ini dikirim secara otomatis dari sistem E-Procurement.</p>
        </div>
      `,
      text: `Status Vendor Diperbarui\n\nHalo {{vendorName}},\n\nStatus vendor Anda telah diperbarui menjadi: {{status}}\n\n{{#if reason}}Alasan: {{reason}}\n\n{{/if}}Silakan login ke sistem untuk informasi lebih lanjut.`
    },
    'password-reset': {
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Reset Password</h2>
          <p>Halo {{userName}},</p>
          <p>Anda telah meminta reset password untuk akun Anda.</p>
          <p>Klik tombol di bawah ini untuk reset password:</p>
          <a href="{{resetUrl}}" style="display: inline-block; background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">Reset Password</a>
          <p>Atau copy dan paste link berikut ke browser Anda:</p>
          <p style="word-break: break-all;">{{resetUrl}}</p>
          <p><strong>Link ini akan expired dalam 1 jam.</strong></p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">Jika Anda tidak meminta reset password, abaikan email ini.</p>
        </div>
      `,
      text: `Reset Password\n\nHalo {{userName}},\n\nAnda telah meminta reset password untuk akun Anda.\n\nKlik link berikut untuk reset password:\n{{resetUrl}}\n\nLink ini akan expired dalam 1 jam.\n\nJika Anda tidak meminta reset password, abaikan email ini.`
    },
    'account-verification': {
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Verifikasi Akun</h2>
          <p>Halo {{userName}},</p>
          <p>Terima kasih telah mendaftar di sistem E-Procurement.</p>
          <p>Silakan verifikasi email Anda dengan klik tombol di bawah ini:</p>
          <a href="{{verificationUrl}}" style="display: inline-block; background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">Verifikasi Email</a>
          <p>Atau copy dan paste link berikut ke browser Anda:</p>
          <p style="word-break: break-all;">{{verificationUrl}}</p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">Email ini dikirim secara otomatis dari sistem E-Procurement.</p>
        </div>
      `,
      text: `Verifikasi Akun\n\nHalo {{userName}},\n\nTerima kasih telah mendaftar di sistem E-Procurement.\n\nSilakan verifikasi email Anda dengan klik link berikut:\n{{verificationUrl}}`
    },
    'offer-evaluation': {
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Evaluasi Penawaran</h2>
          <p>Halo {{vendorName}},</p>
          <p>Penawaran Anda untuk tender "{{procurementTitle}}" telah dievaluasi.</p>
          <p><strong>Status:</strong> {{status}}</p>
          {{#if score}}
          <p><strong>Skor:</strong> {{score}}</p>
          {{/if}}
          {{#if feedback}}
          <p><strong>Feedback:</strong> {{feedback}}</p>
          {{/if}}
          <p>Silakan login ke sistem untuk informasi lebih lanjut.</p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">Email ini dikirim secara otomatis dari sistem E-Procurement.</p>
        </div>
      `,
      text: `Evaluasi Penawaran\n\nHalo {{vendorName}},\n\nPenawaran Anda untuk tender "{{procurementTitle}}" telah dievaluasi.\n\nStatus: {{status}}\n\n{{#if score}}Skor: {{score}}\n\n{{/if}}{{#if feedback}}Feedback: {{feedback}}\n\n{{/if}}Silakan login ke sistem untuk informasi lebih lanjut.`
    }
  };

  const templateData = templates[template];
  if (!templateData) {
    throw new Error(`Email template '${template}' not found`);
  }

  // Compile templates
  const htmlTemplate = handlebars.compile(templateData.html);
  const textTemplate = handlebars.compile(templateData.text);

  return {
    html: htmlTemplate(data),
    text: textTemplate(data)
  };
}

// Combined notification (in-app + email)
export async function sendCombinedNotification(
  notificationData: NotificationData,
  emailData?: EmailNotificationData
): Promise<void> {
  // Create in-app notification
  await createNotification(notificationData);

  // Send email if email data provided
  if (emailData) {
    await sendEmailNotification(emailData);
  }
}

// Email queue management functions

// Queue failed emails for retry
async function queueFailedEmail(emailData: EmailNotificationData, error: Error): Promise<void> {
  try {
    await prisma.emailQueue.create({
      data: {
        to: Array.isArray(emailData.to) ? emailData.to.join(',') : emailData.to,
        subject: emailData.subject,
        body: (emailData as any).body || emailData.subject,
        template: emailData.template,
        templateData: emailData.data || {},
        status: 'FAILED',
        error: error.message,
        retryCount: 0,
        maxRetries: 3,
        scheduledAt: new Date(Date.now() + 5 * 60 * 1000), // Retry in 5 minutes
      },
    });
  } catch (queueError) {
    console.error('Failed to queue email for retry:', queueError);
  }
}

// Process email queue (should be called by a background job)
export async function processEmailQueue(): Promise<void> {
  try {
    const failedEmails = await prisma.emailQueue.findMany({
      where: {
        status: 'FAILED',
        retryCount: { lt: 3 }, // maxRetries value
        scheduledAt: { lte: new Date() },
      },
      take: 10, // Process 10 at a time
      orderBy: { createdAt: 'asc' },
    });

    for (const queuedEmail of failedEmails) {
      try {
        await sendEmailNotification({
          to: queuedEmail.to,
          subject: queuedEmail.subject,
          template: queuedEmail.template || "",
          data: queuedEmail.templateData as any,
        });

        // Mark as sent
        await prisma.emailQueue.update({
          where: { id: queuedEmail.id },
          data: {
            status: 'SENT',
            sentAt: new Date(),
          },
        });
      } catch (error) {
        // Increment retry count
        const newRetryCount = queuedEmail.retryCount + 1;
        const nextRetryAt = new Date(Date.now() + Math.pow(2, newRetryCount) * 5 * 60 * 1000); // Exponential backoff

        await prisma.emailQueue.update({
          where: { id: queuedEmail.id },
          data: {
            retryCount: newRetryCount,
            error: (error as Error).message,
            status: newRetryCount >= queuedEmail.maxRetries ? 'PERMANENTLY_FAILED' : 'FAILED',
          },
        });
      }
    }
  } catch (error) {
    console.error('Error processing email queue:', error);
  }
}

// Get email queue statistics
export async function getEmailQueueStats(): Promise<{
  pending: number;
  failed: number;
  sent: number;
  permanentlyFailed: number;
}> {
  try {
    const [pending, failed, sent, permanentlyFailed] = await Promise.all([
      prisma.emailQueue.count({ where: { status: 'PENDING' } }),
      prisma.emailQueue.count({ where: { status: 'FAILED' } }),
      prisma.emailQueue.count({ where: { status: 'SENT' } }),
      prisma.emailQueue.count({ where: { status: 'PERMANENTLY_FAILED' } }),
    ]);

    return { pending, failed, sent, permanentlyFailed };
  } catch (error) {
    console.error('Error getting email queue stats:', error);
    return { pending: 0, failed: 0, sent: 0, permanentlyFailed: 0 };
  }
}

// Clean up old email queue entries
export async function cleanupEmailQueue(olderThanDays: number = 30): Promise<number> {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await prisma.emailQueue.deleteMany({
      where: {
        OR: [
          { status: 'SENT', sentAt: { lt: cutoffDate } },
          { status: 'PERMANENTLY_FAILED', updatedAt: { lt: cutoffDate } },
        ],
      },
    });

    return result.count;
  } catch (error) {
    console.error('Error cleaning up email queue:', error);
    return 0;
  }
}

// Fallback template engine for when handlebars fails
function generateEmailContentFallback(
  template: string,
  data: Record<string, any>
): { html: string; text: string } {
  // Simple template replacement without handlebars
  const templates: Record<string, { html: string; text: string }> = {
    'vendor-status-change': {
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Status Vendor Diperbarui</h2>
          <p>Halo ${data.vendorName || 'Vendor'},</p>
          <p>Status vendor Anda telah diperbarui menjadi: <strong>${data.status || 'Unknown'}</strong></p>
          ${data.reason ? `<p><strong>Alasan:</strong> ${data.reason}</p>` : ''}
          <p>Silakan login ke sistem untuk informasi lebih lanjut.</p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">Email ini dikirim secara otomatis dari sistem E-Procurement.</p>
        </div>
      `,
      text: `Status Vendor Diperbarui\n\nHalo ${data.vendorName || 'Vendor'},\n\nStatus vendor Anda telah diperbarui menjadi: ${data.status || 'Unknown'}\n\n${data.reason ? `Alasan: ${data.reason}\n\n` : ''}Silakan login ke sistem untuk informasi lebih lanjut.`
    },
    'bid-evaluation': {
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Evaluasi Penawaran</h2>
          <p>Halo ${data.vendorName || 'Vendor'},</p>
          <p>Penawaran Anda untuk tender "${data.procurementTitle || 'Unknown'}" telah dievaluasi.</p>
          <p><strong>Status:</strong> ${data.status || 'Unknown'}</p>
          ${data.score ? `<p><strong>Skor:</strong> ${data.score}</p>` : ''}
          ${data.feedback ? `<p><strong>Feedback:</strong> ${data.feedback}</p>` : ''}
          <p>Silakan login ke sistem untuk informasi lebih lanjut.</p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">Email ini dikirim secara otomatis dari sistem E-Procurement.</p>
        </div>
      `,
      text: `Evaluasi Penawaran\n\nHalo ${data.vendorName || 'Vendor'},\n\nPenawaran Anda untuk tender "${data.procurementTitle || 'Unknown'}" telah dievaluasi.\n\nStatus: ${data.status || 'Unknown'}\n\n${data.score ? `Skor: ${data.score}\n\n` : ''}${data.feedback ? `Feedback: ${data.feedback}\n\n` : ''}Silakan login ke sistem untuk informasi lebih lanjut.`
    }
  };

  const templateData = templates[template];
  if (!templateData) {
    throw new Error(`Email template '${template}' not found`);
  }

  return {
    html: templateData.html,
    text: templateData.text
  };
}
