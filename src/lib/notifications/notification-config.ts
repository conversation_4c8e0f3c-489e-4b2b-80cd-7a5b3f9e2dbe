import { NotificationTemplate, NotificationRule } from "./notification-engine";

export const DEFAULT_NOTIFICATION_TEMPLATES: Record<string, NotificationTemplate> = {
  // Purchase Requisition Templates
  PR_SUBMITTED: {
    id: "pr-submitted",
    name: "Purchase Requisition Submitted",
    subject: "Purchase Requisition {{entityData.prNumber}} Submitted for Approval",
    bodyTemplate: `
Dear {{recipient.name}},

A new purchase requisition has been submitted and requires your approval.

PR Details:
- PR Number: {{entityData.prNumber}}
- Title: {{entityData.title}}
- Requester: {{entityData.requester.name}}
- Department: {{entityData.requester.department.name}}
- Total Value: {{entityData.totalValue}}
- Items: {{entityData.items.length}} item(s)

Please review and approve this purchase requisition at your earliest convenience.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.prNumber", "entityData.title", "entityData.requester.name", "entityData.totalValue"],
    category: "WORKFLOW",
    priority: "MEDIUM"
  },

  PR_APPROVED: {
    id: "pr-approved",
    name: "Purchase Requisition Approved",
    subject: "Purchase Requisition {{entityData.prNumber}} Approved",
    bodyTemplate: `
Dear {{recipient.name}},

Your purchase requisition has been approved.

PR Details:
- PR Number: {{entityData.prNumber}}
- Title: {{entityData.title}}
- Approved by: {{triggerUser.name}}
- Total Value: {{entityData.totalValue}}

Your PR is now ready for consolidation and procurement processing.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.prNumber", "entityData.title", "triggerUser.name"],
    category: "WORKFLOW",
    priority: "MEDIUM"
  },

  PR_REJECTED: {
    id: "pr-rejected",
    name: "Purchase Requisition Rejected",
    subject: "Purchase Requisition {{entityData.prNumber}} Rejected",
    bodyTemplate: `
Dear {{recipient.name}},

Your purchase requisition has been rejected.

PR Details:
- PR Number: {{entityData.prNumber}}
- Title: {{entityData.title}}
- Rejected by: {{triggerUser.name}}
- Reason: {{metadata.rejectionReason}}

Please review the feedback and resubmit if necessary.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.prNumber", "entityData.title", "triggerUser.name", "metadata.rejectionReason"],
    category: "WORKFLOW",
    priority: "HIGH"
  },

  // Vendor Templates
  VENDOR_REGISTERED: {
    id: "vendor-registered",
    name: "Vendor Registration Received",
    subject: "Vendor Registration Received - {{entityData.companyName}}",
    bodyTemplate: `
Dear {{recipient.name}},

A new vendor registration has been received and is pending verification.

Vendor Details:
- Company Name: {{entityData.companyName}}
- Contact Person: {{entityData.contactPerson}}
- Email: {{entityData.contactEmail}}
- Business Category: {{entityData.businessCategory}}

Please review and verify this vendor registration.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.companyName", "entityData.contactPerson", "entityData.businessCategory"],
    category: "WORKFLOW",
    priority: "MEDIUM"
  },

  VENDOR_VERIFIED: {
    id: "vendor-verified",
    name: "Vendor Verification Completed",
    subject: "Vendor Verification Completed - Welcome to E-Procurement",
    bodyTemplate: `
Dear {{recipient.name}},

Congratulations! Your vendor registration has been successfully verified and approved.

Company: {{entityData.companyName}}
Verification Date: {{metadata.verificationDate}}

You can now participate in procurement opportunities. Please log in to your vendor portal to:
- Update your company profile
- Browse available procurement opportunities
- Submit proposals and bids

Welcome to our e-procurement platform!

Best regards,
E-Procurement Team
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.companyName", "metadata.verificationDate"],
    category: "WORKFLOW",
    priority: "HIGH"
  },

  // Procurement Templates
  PROCUREMENT_PUBLISHED: {
    id: "procurement-published",
    name: "New Procurement Opportunity",
    subject: "New Procurement Opportunity - {{entityData.title}}",
    bodyTemplate: `
Dear {{recipient.name}},

A new procurement opportunity has been published that may interest your company.

Procurement Details:
- Procurement Number: {{entityData.procurementNumber}}
- Title: {{entityData.title}}
- Category: {{entityData.category}}
- Estimated Value: {{entityData.estimatedValue}}
- Submission Deadline: {{entityData.submissionDeadline}}

Please review the full requirements and submit your proposal before the deadline.

Best regards,
E-Procurement Team
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.procurementNumber", "entityData.title", "entityData.estimatedValue"],
    category: "ANNOUNCEMENT",
    priority: "MEDIUM"
  },

  OFFER_SUBMITTED: {
    id: "offer-submitted",
    name: "Offer Submitted",
    subject: "Offer Submitted for {{entityData.procurement.title}}",
    bodyTemplate: `
Dear {{recipient.name}},

Your offer has been successfully submitted.

Offer Details:
- Procurement: {{entityData.procurement.title}}
- Procurement Number: {{entityData.procurement.procurementNumber}}
- Offer Number: {{entityData.offerNumber}}
- Total Value: {{entityData.totalValue}}
- Submitted by: {{entityData.vendor.companyName}}

Your offer is now under evaluation. You will be notified of the results.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.procurement.title", "entityData.offerNumber", "entityData.totalValue"],
    category: "WORKFLOW",
    priority: "MEDIUM"
  },

  // Good Receipt Templates
  GOOD_RECEIPT_CREATED: {
    id: "good-receipt-created",
    name: "Good Receipt Created",
    subject: "Good Receipt {{entityData.grNumber}} Created",
    bodyTemplate: `
Dear {{recipient.name}},

A good receipt has been created for your purchase order.

GR Details:
- GR Number: {{entityData.grNumber}}
- PO Number: {{entityData.po.poNumber}}
- Vendor: {{entityData.po.vendor.companyName}}
- Received Date: {{entityData.receivedDate}}
- Items: {{entityData.items.length}} item(s)

Please review the received items and confirm the good receipt.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.grNumber", "entityData.po.poNumber", "entityData.po.vendor.companyName"],
    category: "WORKFLOW",
    priority: "MEDIUM"
  },

  // BAST Templates
  BAST_CREATED: {
    id: "bast-created",
    name: "BAST Created",
    subject: "BAST {{entityData.bastNumber}} Created for Approval",
    bodyTemplate: `
Dear {{recipient.name}},

A Berita Acara Serah Terima (BAST) has been created and requires your approval.

BAST Details:
- BAST Number: {{entityData.bastNumber}}
- GR Number: {{entityData.goodReceipt.grNumber}}
- PO Number: {{entityData.goodReceipt.po.poNumber}}
- Vendor: {{entityData.goodReceipt.po.vendor.companyName}}
- Handover Date: {{entityData.handoverDate}}

Please review and approve this handover document.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.bastNumber", "entityData.goodReceipt.grNumber"],
    category: "WORKFLOW",
    priority: "MEDIUM"
  },

  // Three-way Matching Templates
  THREE_WAY_MATCHING_COMPLETED: {
    id: "three-way-matching-completed",
    name: "Three-way Matching Completed",
    subject: "Three-way Matching Completed - {{entityData.purchaseOrder.poNumber}}",
    bodyTemplate: `
Dear {{recipient.name}},

Three-way matching has been completed for the following documents:

Documents:
- Purchase Order: {{entityData.purchaseOrder.poNumber}}
- Good Receipt: {{entityData.goodReceipt.grNumber}}
- Invoice: {{entityData.invoice.invoiceNumber}}

Matching Results:
- Matching Score: {{metadata.matchingScore}}%
- Status: {{metadata.isMatched}}
- Discrepancies: {{metadata.discrepanciesCount}}

{{#if metadata.autoApproved}}
The invoice has been automatically approved based on the matching results.
{{else}}
Please review the matching results and approve the invoice if appropriate.
{{/if}}

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityData.purchaseOrder.poNumber", "metadata.matchingScore", "metadata.discrepanciesCount"],
    category: "WORKFLOW",
    priority: "MEDIUM"
  },

  // System Templates
  SYSTEM_ALERT: {
    id: "system-alert",
    name: "System Alert",
    subject: "System Alert - {{metadata.alertType}}",
    bodyTemplate: `
Dear {{recipient.name}},

A system alert has been triggered:

Alert Type: {{metadata.alertType}}
Severity: {{metadata.severity}}
Description: {{metadata.description}}

{{#if metadata.actionRequired}}
Action Required: {{metadata.actionRequired}}
{{/if}}

Please take appropriate action if necessary.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP", "SMS"],
    variables: ["metadata.alertType", "metadata.severity", "metadata.description"],
    category: "ALERT",
    priority: "HIGH"
  },

  // Reminder Templates
  APPROVAL_REMINDER: {
    id: "approval-reminder",
    name: "Approval Reminder",
    subject: "Reminder: {{entityType}} Pending Your Approval",
    bodyTemplate: `
Dear {{recipient.name}},

This is a reminder that the following item is pending your approval:

Item Type: {{entityType}}
Item ID: {{entityId}}
Pending Since: {{metadata.pendingSince}}
Priority: {{metadata.priority}}

Please review and take action at your earliest convenience.

Best regards,
E-Procurement System
    `,
    channels: ["EMAIL", "IN_APP"],
    variables: ["entityType", "entityId", "metadata.pendingSince"],
    category: "REMINDER",
    priority: "MEDIUM"
  }
};

export const DEFAULT_NOTIFICATION_RULES: Record<string, NotificationRule[]> = {
  // Purchase Requisition Events
  PR_SUBMITTED: [
    {
      id: "pr-submitted-to-approvers",
      name: "Notify Approvers of New PR",
      event: "PR_SUBMITTED",
      conditions: [
        {
          field: "status",
          operator: "EQUALS",
          value: "PENDING_APPROVAL"
        }
      ],
      recipients: [
        { type: "ROLE", value: "DEPARTMENT_HEAD" },
        { type: "ROLE", value: "PROCUREMENT_USER" }
      ],
      template: "pr-submitted",
      enabled: true,
      escalationRules: [
        {
          afterMinutes: 1440, // 24 hours
          recipients: [
            { type: "ROLE", value: "PROCUREMENT_MANAGER" }
          ]
        }
      ]
    }
  ],

  PR_APPROVED: [
    {
      id: "pr-approved-to-requester",
      name: "Notify Requester of PR Approval",
      event: "PR_APPROVED",
      conditions: [],
      recipients: [
        { type: "USER", value: "{{entityData.requesterId}}" }
      ],
      template: "pr-approved",
      enabled: true
    }
  ],

  PR_REJECTED: [
    {
      id: "pr-rejected-to-requester",
      name: "Notify Requester of PR Rejection",
      event: "PR_REJECTED",
      conditions: [],
      recipients: [
        { type: "USER", value: "{{entityData.requesterId}}" }
      ],
      template: "pr-rejected",
      enabled: true
    }
  ],

  // Vendor Events
  VENDOR_REGISTERED: [
    {
      id: "vendor-registered-to-admins",
      name: "Notify Admins of New Vendor Registration",
      event: "VENDOR_REGISTERED",
      conditions: [],
      recipients: [
        { type: "ROLE", value: "ADMIN" }
      ],
      template: "vendor-registered",
      enabled: true
    }
  ],

  VENDOR_VERIFIED: [
    {
      id: "vendor-verified-to-vendor",
      name: "Notify Vendor of Verification Completion",
      event: "VENDOR_VERIFIED",
      conditions: [],
      recipients: [
        { type: "USER", value: "{{entityData.userId}}" }
      ],
      template: "vendor-verified",
      enabled: true
    }
  ],

  // Procurement Events
  PROCUREMENT_PUBLISHED: [
    {
      id: "procurement-published-to-vendors",
      name: "Notify Verified Vendors of New Procurement",
      event: "PROCUREMENT_PUBLISHED",
      conditions: [
        {
          field: "status",
          operator: "EQUALS",
          value: "PUBLISHED"
        }
      ],
      recipients: [
        { type: "ROLE", value: "VENDOR_USER" }
      ],
      template: "procurement-published",
      enabled: true
    }
  ],

  // Good Receipt Events
  GOOD_RECEIPT_CREATED: [
    {
      id: "gr-created-to-warehouse",
      name: "Notify Warehouse of New Good Receipt",
      event: "GOOD_RECEIPT_CREATED",
      conditions: [],
      recipients: [
        { type: "ROLE", value: "WAREHOUSE_USER" },
        { type: "ROLE", value: "PROCUREMENT_USER" }
      ],
      template: "good-receipt-created",
      enabled: true
    }
  ],

  // BAST Events
  BAST_CREATED: [
    {
      id: "bast-created-to-approvers",
      name: "Notify Approvers of New BAST",
      event: "BAST_CREATED",
      conditions: [],
      recipients: [
        { type: "USER", value: "{{entityData.vendorRepresentativeId}}" },
        { type: "USER", value: "{{entityData.internalRepresentativeId}}" }
      ],
      template: "bast-created",
      enabled: true,
      escalationRules: [
        {
          afterMinutes: 4320, // 72 hours
          recipients: [
            { type: "ROLE", value: "PROCUREMENT_MANAGER" }
          ]
        }
      ]
    }
  ],

  // Three-way Matching Events
  THREE_WAY_MATCHING_COMPLETED: [
    {
      id: "matching-completed-to-finance",
      name: "Notify Finance of Matching Completion",
      event: "THREE_WAY_MATCHING_COMPLETED",
      conditions: [],
      recipients: [
        { type: "ROLE", value: "FINANCE_USER" },
        { type: "ROLE", value: "PROCUREMENT_USER" }
      ],
      template: "three-way-matching-completed",
      enabled: true
    }
  ],

  // System Events
  SYSTEM_ALERT: [
    {
      id: "system-alert-to-admins",
      name: "Notify Admins of System Alerts",
      event: "SYSTEM_ALERT",
      conditions: [],
      recipients: [
        { type: "ROLE", value: "ADMIN" }
      ],
      template: "system-alert",
      enabled: true
    }
  ]
};

export class NotificationConfigManager {
  static getTemplate(templateId: string): NotificationTemplate | null {
    return DEFAULT_NOTIFICATION_TEMPLATES[templateId] || null;
  }

  static getRulesForEvent(event: string): NotificationRule[] {
    return DEFAULT_NOTIFICATION_RULES[event] || [];
  }

  static getAllTemplates(): NotificationTemplate[] {
    return Object.values(DEFAULT_NOTIFICATION_TEMPLATES);
  }

  static getAllRules(): NotificationRule[] {
    return Object.values(DEFAULT_NOTIFICATION_RULES).flat();
  }

  static getTemplatesByCategory(category: string): NotificationTemplate[] {
    return Object.values(DEFAULT_NOTIFICATION_TEMPLATES).filter(
      template => template.category === category
    );
  }

  static getRulesByPriority(priority: string): NotificationRule[] {
    return Object.values(DEFAULT_NOTIFICATION_RULES)
      .flat()
      .filter(rule => {
        const template = DEFAULT_NOTIFICATION_TEMPLATES[rule.template];
        return template?.priority === priority;
      });
  }
}
