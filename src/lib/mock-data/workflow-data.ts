// Mock data for workflow management components
// This provides fallback data when API endpoints are not available

export const mockApprovalWorkflows = [
  {
    id: 'workflow-1',
    name: 'Purchase Requisition Approval',
    description: 'Standard approval workflow for purchase requisitions',
    entityType: 'purchase_requisition',
    stage: 'approval',
    isActive: true,
    isDefault: true,
    version: 1,
    conditions: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdById: 'user-1',
    steps: [
      {
        id: 'step-1',
        name: 'Department Head Approval',
        description: 'Approval by department head',
        sequence: 1,
        stepType: 'APPROVAL',
        isRequired: true,
        approverType: 'ROLE_BASED',
        requiredCount: 1,
        allowDelegation: true,
        timeoutHours: 48,
      },
      {
        id: 'step-2',
        name: 'Finance Review',
        description: 'Financial review and approval',
        sequence: 2,
        stepType: 'APPROVAL',
        isRequired: true,
        approverType: 'DEPARTMENT',
        requiredCount: 1,
        allowDelegation: false,
        timeoutHours: 24,
      },
    ],
  },
  {
    id: 'workflow-2',
    name: 'Vendor Registration Workflow',
    description: 'Workflow for vendor registration and verification',
    entityType: 'vendor',
    stage: 'registration',
    isActive: true,
    isDefault: false,
    version: 1,
    conditions: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdById: 'user-1',
    steps: [
      {
        id: 'step-3',
        name: 'Document Verification',
        description: 'Verify vendor documents',
        sequence: 1,
        stepType: 'REVIEW',
        isRequired: true,
        approverType: 'SPECIFIC_USER',
        requiredCount: 1,
        allowDelegation: true,
        timeoutHours: 72,
      },
    ],
  },
];

export const mockWorkflowTemplates = [
  {
    id: 'template-1',
    name: 'Standard Procurement Template',
    description: 'Standard template for procurement workflows',
    type: 'TENDER',
    category: 'GOODS',
    isDefault: true,
    isActive: true,
    version: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    creator: {
      id: 'user-1',
      name: 'Admin User',
      email: '<EMAIL>',
    },
    config: {
      stages: [
        {
          id: 'stage-1',
          name: 'Planning',
          type: 'PLANNING',
          sequence: 1,
          duration: 168, // 1 week in hours
          requirements: ['Budget approval', 'Specification document'],
        },
        {
          id: 'stage-2',
          name: 'Tender Publication',
          type: 'PUBLICATION',
          sequence: 2,
          duration: 336, // 2 weeks in hours
          requirements: ['Tender document', 'Evaluation criteria'],
        },
      ],
      rules: {
        minValue: 1000000,
        maxValue: 50000000,
        requiresCommittee: true,
        autoApproval: false,
      },
    },
    _count: {
      procurements: 5,
      vendorRequirements: 3,
      scheduleTemplates: 2,
    },
  },
  {
    id: 'template-2',
    name: 'Direct Purchase Template',
    description: 'Template for direct purchase workflows',
    type: 'DIRECT_PURCHASE',
    category: 'SERVICES',
    isDefault: false,
    isActive: true,
    version: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    creator: {
      id: 'user-1',
      name: 'Admin User',
      email: '<EMAIL>',
    },
    config: {
      stages: [
        {
          id: 'stage-3',
          name: 'Vendor Selection',
          type: 'SELECTION',
          sequence: 1,
          duration: 72, // 3 days in hours
          requirements: ['Vendor qualification'],
        },
      ],
      rules: {
        minValue: 0,
        maxValue: 1000000,
        requiresCommittee: false,
        autoApproval: true,
      },
    },
    _count: {
      procurements: 12,
      vendorRequirements: 1,
      scheduleTemplates: 1,
    },
  },
];

export const mockDocumentTemplates = [
  {
    id: 'doc-template-1',
    name: 'Purchase Order Template',
    description: 'Standard purchase order document template',
    type: 'PURCHASE_ORDER',
    category: 'PROCUREMENT',
    status: 'APPROVED',
    version: 1,
    isActive: true,
    content: {
      htmlTemplate: '<div>Purchase Order Template</div>',
      cssStyles: 'body { font-family: Arial; }',
      signatureFields: [],
    },
    variables: [
      {
        name: 'poNumber',
        type: 'text',
        description: 'Purchase Order Number',
        required: true,
        defaultValue: '',
      },
      {
        name: 'vendorName',
        type: 'text',
        description: 'Vendor Name',
        required: true,
        defaultValue: '',
      },
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    creator: {
      id: 'user-1',
      name: 'Admin User',
      email: '<EMAIL>',
    },
    _count: {
      usages: 25,
    },
  },
  {
    id: 'doc-template-2',
    name: 'Contract Template',
    description: 'Standard contract document template',
    type: 'CONTRACT',
    category: 'LEGAL',
    status: 'DRAFT',
    version: 1,
    isActive: false,
    content: {
      htmlTemplate: '<div>Contract Template</div>',
      cssStyles: 'body { font-family: Arial; }',
      signatureFields: [
        {
          id: 'sig-1',
          name: 'Vendor Signature',
          position: { x: 100, y: 500 },
          size: { width: 200, height: 50 },
          required: true,
        },
      ],
    },
    variables: [
      {
        name: 'contractNumber',
        type: 'text',
        description: 'Contract Number',
        required: true,
        defaultValue: '',
      },
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    creator: {
      id: 'user-1',
      name: 'Admin User',
      email: '<EMAIL>',
    },
    _count: {
      usages: 8,
    },
  },
];

// Mock API functions that return promises
export const mockApiService = {
  // Approval Workflows
  async getApprovalWorkflows(filters: any = {}) {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
    return {
      workflows: mockApprovalWorkflows.filter(workflow => {
        if (filters.entityType && filters.entityType !== 'all' && workflow.entityType !== filters.entityType) {
          return false;
        }
        if (filters.stage && filters.stage !== 'all' && workflow.stage !== filters.stage) {
          return false;
        }
        if (filters.search && !workflow.name.toLowerCase().includes(filters.search.toLowerCase())) {
          return false;
        }
        return true;
      }),
      totalCount: mockApprovalWorkflows.length,
    };
  },

  async createApprovalWorkflow(data: any) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { id: 'new-workflow-' + Date.now(), ...data };
  },

  async updateApprovalWorkflow(id: string, data: any) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { id, ...data };
  },

  async deleteApprovalWorkflow(id: string) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  },

  async toggleWorkflowStatus(id: string, isActive: boolean) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { id, isActive };
  },

  // Workflow Templates
  async getWorkflowTemplates(filters: any = {}) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      templates: mockWorkflowTemplates.filter(template => {
        if (filters.type && filters.type !== 'all' && template.type !== filters.type) {
          return false;
        }
        if (filters.category && filters.category !== 'all' && template.category !== filters.category) {
          return false;
        }
        if (filters.search && !template.name.toLowerCase().includes(filters.search.toLowerCase())) {
          return false;
        }
        return true;
      }),
      totalCount: mockWorkflowTemplates.length,
    };
  },

  async createWorkflowTemplate(data: any) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { id: 'new-template-' + Date.now(), ...data };
  },

  async cloneWorkflowTemplate(id: string, name: string) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const original = mockWorkflowTemplates.find(t => t.id === id);
    return { id: 'cloned-template-' + Date.now(), ...original, name };
  },

  async deleteWorkflowTemplate(id: string) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  },

  async toggleTemplateStatus(id: string, isActive: boolean) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { id, isActive };
  },

  // Document Templates
  async getDocumentTemplates(filters: any = {}) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      templates: mockDocumentTemplates.filter(template => {
        if (filters.type && filters.type !== 'all' && template.type !== filters.type) {
          return false;
        }
        if (filters.category && filters.category !== 'all' && template.category !== filters.category) {
          return false;
        }
        if (filters.status && filters.status !== 'all' && template.status !== filters.status) {
          return false;
        }
        if (filters.search && !template.name.toLowerCase().includes(filters.search.toLowerCase())) {
          return false;
        }
        return true;
      }),
      totalCount: mockDocumentTemplates.length,
    };
  },

  async createDocumentTemplate(data: any) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { id: 'new-doc-template-' + Date.now(), ...data };
  },

  async updateDocumentTemplate(id: string, data: any) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { id, ...data };
  },

  async deleteDocumentTemplate(id: string) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  },

  async toggleDocumentTemplateStatus(id: string, isActive: boolean) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { id, isActive };
  },

  async approveDocumentTemplate(id: string) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { id, status: 'APPROVED' };
  },
};
