import { prisma } from "@/lib/db";
import { toPrismaJson } from "@/lib/utils/json";

export interface WorkflowCondition {
  field: string;
  operator: "eq" | "ne" | "gt" | "gte" | "lt" | "lte" | "in" | "contains";
  value: any;
}

export interface ApproverConfig {
  userIds?: string[];
  roles?: string[];
  departments?: string[];
  hierarchyLevel?: number;
  dynamicQuery?: string;
  emailAddresses?: string[]; // For external approvers
}

export interface StepConfig {
  conditions?: WorkflowCondition[];
  escalationRules?: {
    timeoutHours: number;
    escalateTo: ApproverConfig;
  };
  parallelApproval?: boolean;
  minimumApprovals?: number;
  autoApprove?: {
    conditions: WorkflowCondition[];
  };
}

export class ApprovalWorkflowEngine {
  // Create a new workflow
  async createWorkflow(data: {
    name: string;
    description?: string;
    entityType: string;
    conditions?: any;
    createdById: string;
    steps: Array<{
      name: string;
      description?: string;
      sequence: number;
      stepType: string;
      approverType: string;
      approverConfig: ApproverConfig;
      requiredCount?: number;
      allowDelegation?: boolean;
      timeoutHours?: number;
      config?: StepConfig;
    }>;
  }) {
    return await prisma.approvalWorkflow.create({
      data: {
        name: data.name,
        description: data.description,
        entityType: data.entityType,
        conditions: data.conditions,
        createdById: data.createdById,
        steps: {
          create: data.steps.map(step => ({
            name: step.name,
            description: step.description,
            sequence: step.sequence,
            stepType: step.stepType as any,
            approverType: step.approverType as any,
            approverConfig: toPrismaJson(step.approverConfig),
            requiredCount: step.requiredCount || 1,
            allowDelegation: step.allowDelegation || false,
            timeoutHours: step.timeoutHours,
            config: toPrismaJson(step.config),
          })),
        },
      },
      include: {
        steps: {
          orderBy: { sequence: "asc" },
        },
      },
    });
  }

  // Find applicable workflow for an entity
  async findApplicableWorkflow(entityType: string, entityData: any) {
    const workflows = await prisma.approvalWorkflow.findMany({
      where: {
        entityType,
        isActive: true,
      },
      include: {
        steps: {
          orderBy: { sequence: "asc" },
        },
      },
      orderBy: [
        { isDefault: "asc" }, // Non-default first (more specific)
        { version: "desc" },
      ],
    });

    // Find the first workflow that matches conditions
    for (const workflow of workflows) {
      if (await this.evaluateWorkflowConditions(workflow.conditions, entityData)) {
        return workflow;
      }
    }

    // Return default workflow if no specific match
    return workflows.find((w: any) => w.isDefault) || null;
  }

  // Start approval process
  async startApproval(data: {
    entityType: string;
    entityId: string;
    entityData: any;
    initiatedById: string;
    title?: string;
    description?: string;
    priority?: "LOW" | "NORMAL" | "HIGH" | "URGENT";
    dueDate?: Date;
  }) {
    // Find applicable workflow
    const workflow = await this.findApplicableWorkflow(data.entityType, data.entityData);
    if (!workflow) {
      throw new Error(`No workflow found for entity type: ${data.entityType}`);
    }

    // Create approval instance
    const instance = await prisma.approvalInstance.create({
      data: {
        workflowId: workflow.id,
        entityType: data.entityType,
        entityId: data.entityId,
        title: data.title,
        description: data.description,
        priority: data.priority || "NORMAL",
        dueDate: data.dueDate,
        initiatedById: data.initiatedById,
        metadata: data.entityData,
        status: "PENDING",
      },
    });

    // Create step instances
    for (const step of workflow.steps) {
      await prisma.approvalStepInstance.create({
        data: {
          instanceId: instance.id,
          stepId: step.id,
          sequence: step.sequence,
          status: step.sequence === 1 ? "PENDING" : "PENDING",
        },
      });
    }

    // Start the first step
    await this.processNextStep(instance.id);

    return instance;
  }

  // Process the next step in the workflow
  async processNextStep(instanceId: string) {
    const instance = await prisma.approvalInstance.findUnique({
      where: { id: instanceId },
      include: {
        workflow: {
          include: {
            steps: {
              orderBy: { sequence: "asc" },
            },
          },
        },
        stepInstances: {
          include: {
            step: true,
            approvals: {
              include: {
                performedBy: true,
              },
            },
          },
          orderBy: { sequence: "asc" },
        },
      },
    });

    if (!instance) {
      throw new Error("Approval instance not found");
    }

    // Find the next step to process
    const nextStep = instance.stepInstances.find(
      (si: any) => si.status === "PENDING" && !si.startedAt
    );

    if (!nextStep) {
      // All steps completed, finalize approval
      await this.finalizeApproval(instanceId);
      return;
    }

    // Start the step
    await prisma.approvalStepInstance.update({
      where: { id: nextStep.id },
      data: {
        status: "IN_PROGRESS",
        startedAt: new Date(),
        dueDate: nextStep.step.timeoutHours
          ? new Date(Date.now() + nextStep.step.timeoutHours * 60 * 60 * 1000)
          : undefined,
      },
    });

    // Update instance status
    await prisma.approvalInstance.update({
      where: { id: instanceId },
      data: { status: "IN_PROGRESS" },
    });
  }

  // Evaluate workflow conditions
  private async evaluateWorkflowConditions(conditions: any, entityData: any): Promise<boolean> {
    if (!conditions) return true;

    // Simple condition evaluation - can be extended
    for (const condition of conditions) {
      const fieldValue = this.getNestedValue(entityData, condition.field);
      
      switch (condition.operator) {
        case "eq":
          if (fieldValue !== condition.value) return false;
          break;
        case "ne":
          if (fieldValue === condition.value) return false;
          break;
        case "gt":
          if (fieldValue <= condition.value) return false;
          break;
        case "gte":
          if (fieldValue < condition.value) return false;
          break;
        case "lt":
          if (fieldValue >= condition.value) return false;
          break;
        case "lte":
          if (fieldValue > condition.value) return false;
          break;
        case "in":
          if (!condition.value.includes(fieldValue)) return false;
          break;
        case "contains":
          if (!fieldValue?.toString().includes(condition.value)) return false;
          break;
      }
    }

    return true;
  }

  // Get nested value from object
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  // Finalize approval process
  private async finalizeApproval(instanceId: string) {
    const instance = await prisma.approvalInstance.findUnique({
      where: { id: instanceId },
      include: {
        stepInstances: {
          include: {
            approvals: true,
          },
        },
        initiatedBy: true,
      },
    });

    if (!instance) return;

    // Determine final status based on step results
    const hasRejection = instance.stepInstances.some(
      (si: any) => si.decision === "REJECTED"
    );

    const finalStatus = hasRejection ? "REJECTED" : "APPROVED";

    // Update instance
    await prisma.approvalInstance.update({
      where: { id: instanceId },
      data: {
        status: finalStatus as any,
        completedAt: new Date(),
      },
    });
  }

  // Perform approval action
  async performAction(data: {
    stepInstanceId: string;
    performedById: string;
    action: "APPROVE" | "REJECT" | "DELEGATE" | "REQUEST_INFO" | "ESCALATE" | "COMMENT";
    decision?: "APPROVED" | "REJECTED" | "DELEGATED" | "ESCALATED";
    comments?: string;
    delegatedToId?: string;
    metadata?: any;
  }) {
    const stepInstance = await prisma.approvalStepInstance.findUnique({
      where: { id: data.stepInstanceId },
      include: {
        instance: true,
        step: true,
        approvals: true,
      },
    });

    if (!stepInstance) {
      throw new Error("Step instance not found");
    }

    if (stepInstance.status !== "IN_PROGRESS") {
      throw new Error("Step is not in progress");
    }

    // Create approval action
    const action = await prisma.approvalAction.create({
      data: {
        stepInstanceId: data.stepInstanceId,
        action: data.action as any,
        decision: data.decision as any,
        comments: data.comments,
        performedById: data.performedById,
        delegatedToId: data.delegatedToId,
        metadata: data.metadata,
      },
    });

    // Check if step is complete
    const approvalCount = stepInstance.approvals.filter(
      (a: any) => a.decision === "APPROVED"
    ).length + (data.decision === "APPROVED" ? 1 : 0);

    const rejectionCount = stepInstance.approvals.filter(
      (a: any) => a.decision === "REJECTED"
    ).length + (data.decision === "REJECTED" ? 1 : 0);

    let stepComplete = false;
    let stepDecision: "APPROVED" | "REJECTED" | null = null;

    // Determine if step is complete
    if (rejectionCount > 0) {
      stepComplete = true;
      stepDecision = "REJECTED";
    } else if (approvalCount >= stepInstance.step.requiredCount) {
      stepComplete = true;
      stepDecision = "APPROVED";
    }

    if (stepComplete) {
      // Update step instance
      await prisma.approvalStepInstance.update({
        where: { id: data.stepInstanceId },
        data: {
          status: stepDecision === "APPROVED" ? "APPROVED" : "REJECTED",
          decision: stepDecision,
          completedAt: new Date(),
          comments: data.comments,
        },
      });

      if (stepDecision === "APPROVED") {
        // Process next step
        await this.processNextStep(stepInstance.instance.id);
      } else {
        // Reject entire approval
        await prisma.approvalInstance.update({
          where: { id: stepInstance.instance.id },
          data: {
            status: "REJECTED",
            completedAt: new Date(),
          },
        });
      }
    }

    return action;
  }

  // Get pending approvals for a user
  async getPendingApprovals(userId: string, options: {
    entityType?: string;
    priority?: string;
    limit?: number;
    offset?: number;
  } = {}) {
    const { entityType, priority, limit = 20, offset = 0 } = options;

    // For now, return step instances that need approval
    // This would be enhanced with proper approver matching logic
    const stepInstances = await prisma.approvalStepInstance.findMany({
      where: {
        status: "IN_PROGRESS",
      },
      include: {
        instance: {
          include: {
            initiatedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        step: {
          select: {
            name: true,
            description: true,
            requiredCount: true,
          },
        },
        approvals: {
          include: {
            performedBy: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: [
        { instance: { priority: "desc" } },
        { instance: { dueDate: "asc" } },
        { instance: { startedAt: "asc" } },
      ],
      skip: offset,
      take: limit,
    });

    return stepInstances;
  }
}

// Singleton instance
export const workflowEngine = new ApprovalWorkflowEngine();
