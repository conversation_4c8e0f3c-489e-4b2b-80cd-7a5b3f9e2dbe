import { documentManager, DocumentUploadOptions, DocumentSearchOptions } from './document-manager';
import { templateEngine, GenerateDocumentOptions } from './template-engine';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface DocumentCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  isActive: boolean;
  documentCount?: number;
}

export interface DocumentTag {
  id: string;
  name: string;
  color?: string;
  description?: string;
  usageCount?: number;
}

export interface DocumentStats {
  totalDocuments: number;
  totalSize: number;
  documentsByType: Record<string, number>;
  documentsByStatus: Record<string, number>;
  recentUploads: number;
  pendingApprovals: number;
}

export class DocumentService {
  /**
   * Upload a document with automatic categorization and tagging
   */
  async uploadDocument(
    userId: string,
    file: {
      buffer: Buffer;
      originalname: string;
      mimetype: string;
    },
    options: Partial<DocumentUploadOptions> = {}
  ) {
    try {
      // Auto-detect document type based on file extension and content
      const documentType = this.detectDocumentType(file.originalname, file.mimetype);
      
      // Auto-generate tags based on filename and content
      const autoTags = this.generateAutoTags(file.originalname, options);
      
      const uploadOptions: DocumentUploadOptions = {
        fileName: file.originalname,
        fileBuffer: file.buffer,
        fileType: file.mimetype,
        documentType,
        tags: [...(options.tags || []), ...autoTags],
        ...options,
      };

      const document = await documentManager.uploadDocument(userId, uploadOptions);
      
      // Update document statistics
      await this.updateDocumentStats();
      
      return document;
    } catch (error) {
      console.error('Error in document service upload:', error);
      throw error;
    }
  }

  /**
   * Search documents with enhanced filtering and sorting
   */
  async searchDocuments(
    userId: string,
    options: DocumentSearchOptions & {
      sortBy?: 'uploadedAt' | 'fileName' | 'updatedAt';
      sortOrder?: 'asc' | 'desc';
      includeStats?: boolean;
    } = {}
  ) {
    try {
      const documents = await documentManager.searchDocuments(options);

      // Apply additional sorting if specified
      if (options.sortBy && options.sortOrder && documents.length > 0) {
        documents.sort((a: any, b: any) => {
          const aValue = a[options.sortBy!];
          const bValue = b[options.sortBy!];

          if (options.sortOrder === 'desc') {
            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
          } else {
            return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
          }
        });
      }

      // Include statistics if requested
      let stats = null;
      if (options.includeStats) {
        stats = await this.getDocumentStats(options);
      }

      return {
        documents,
        stats,
        total: documents.length,
      };
    } catch (error) {
      console.error('Error in document service search:', error);
      throw error;
    }
  }

  /**
   * Generate document from template with validation
   */
  async generateDocument(
    userId: string,
    options: GenerateDocumentOptions & {
      validateData?: boolean;
      autoSave?: boolean;
    }
  ) {
    try {
      // Validate template data if requested
      if (options.validateData) {
        await this.validateTemplateData(options.templateId, options.data);
      }

      const document = await templateEngine.generateDocument(userId, options);
      
      // Auto-save to document management if requested
      if (options.autoSave) {
        // Convert generated document to uploadable format
        const documentBuffer = Buffer.from(JSON.stringify(document.content));
        
        await this.uploadDocument(userId, {
          buffer: documentBuffer,
          originalname: `${document.name}.json`,
          mimetype: 'application/json',
        }, {
          documentType: document.documentType || undefined,
          description: `Generated from template: ${document.templateName}`,
          metadata: {
            templateId: options.templateId,
            generatedAt: new Date(),
            entityType: options.entityType,
            entityId: options.entityId,
          },
          tags: ['generated', 'template'],
        });
      }

      return document;
    } catch (error) {
      console.error('Error in document service generation:', error);
      throw error;
    }
  }

  /**
   * Get document categories with hierarchy
   */
  async getDocumentCategories(): Promise<DocumentCategory[]> {
    try {
      // For now, return predefined categories
      // TODO: Implement database-backed categories once schema is fixed
      return [
        {
          id: 'procurement',
          name: 'Procurement Documents',
          description: 'Documents related to procurement processes',
          isActive: true,
          documentCount: 0,
        },
        {
          id: 'contracts',
          name: 'Contracts',
          description: 'Contract documents and agreements',
          isActive: true,
          documentCount: 0,
        },
        {
          id: 'invoices',
          name: 'Invoices',
          description: 'Invoice and billing documents',
          isActive: true,
          documentCount: 0,
        },
        {
          id: 'reports',
          name: 'Reports',
          description: 'Various reports and evaluations',
          isActive: true,
          documentCount: 0,
        },
        {
          id: 'legal',
          name: 'Legal Documents',
          description: 'Legal and compliance documents',
          isActive: true,
          documentCount: 0,
        },
      ];
    } catch (error) {
      console.error('Error getting document categories:', error);
      throw error;
    }
  }

  /**
   * Get popular document tags
   */
  async getDocumentTags(): Promise<DocumentTag[]> {
    try {
      // For now, return predefined tags
      // TODO: Implement database-backed tags once schema is fixed
      return [
        { id: 'urgent', name: 'Urgent', color: '#ff4444', usageCount: 0 },
        { id: 'draft', name: 'Draft', color: '#ffaa00', usageCount: 0 },
        { id: 'approved', name: 'Approved', color: '#00aa00', usageCount: 0 },
        { id: 'confidential', name: 'Confidential', color: '#aa0000', usageCount: 0 },
        { id: 'template', name: 'Template', color: '#0066cc', usageCount: 0 },
        { id: 'generated', name: 'Generated', color: '#6600cc', usageCount: 0 },
      ];
    } catch (error) {
      console.error('Error getting document tags:', error);
      throw error;
    }
  }

  /**
   * Get document statistics
   */
  async getDocumentStats(filters?: DocumentSearchOptions): Promise<DocumentStats> {
    try {
      // For now, return mock statistics
      // TODO: Implement real statistics once schema is fixed
      return {
        totalDocuments: 0,
        totalSize: 0,
        documentsByType: {},
        documentsByStatus: {},
        recentUploads: 0,
        pendingApprovals: 0,
      };
    } catch (error) {
      console.error('Error getting document stats:', error);
      throw error;
    }
  }

  /**
   * Detect document type based on filename and MIME type
   */
  private detectDocumentType(filename: string, mimeType: string): string {
    const extension = filename.toLowerCase().split('.').pop();
    
    // Map file extensions to document types
    const typeMap: Record<string, string> = {
      'pdf': 'PDF',
      'doc': 'WORD',
      'docx': 'WORD',
      'xls': 'EXCEL',
      'xlsx': 'EXCEL',
      'ppt': 'POWERPOINT',
      'pptx': 'POWERPOINT',
      'txt': 'TEXT',
      'csv': 'CSV',
      'jpg': 'IMAGE',
      'jpeg': 'IMAGE',
      'png': 'IMAGE',
      'gif': 'IMAGE',
      'zip': 'ARCHIVE',
      'rar': 'ARCHIVE',
    };

    return typeMap[extension || ''] || 'OTHER';
  }

  /**
   * Generate automatic tags based on filename and context
   */
  private generateAutoTags(filename: string, options: Partial<DocumentUploadOptions>): string[] {
    const tags: string[] = [];
    const lowerFilename = filename.toLowerCase();

    // Add tags based on filename patterns
    if (lowerFilename.includes('contract')) tags.push('contract');
    if (lowerFilename.includes('invoice')) tags.push('invoice');
    if (lowerFilename.includes('rfq')) tags.push('rfq');
    if (lowerFilename.includes('po')) tags.push('purchase-order');
    if (lowerFilename.includes('bast')) tags.push('bast');
    if (lowerFilename.includes('draft')) tags.push('draft');
    if (lowerFilename.includes('final')) tags.push('final');

    // Add tags based on context
    if (options.procurementId) tags.push('procurement');
    if (options.vendorId) tags.push('vendor');
    if (options.isConfidential) tags.push('confidential');
    if (options.requiresApproval) tags.push('pending-approval');

    return tags;
  }

  /**
   * Validate template data against template requirements
   */
  private async validateTemplateData(templateId: string, data: Record<string, any>): Promise<void> {
    try {
      const template = await templateEngine.getTemplate(templateId);
      const variables = template.variables as any[];

      // Check required variables
      const missingRequired = variables
        .filter(v => v.required && !(v.name in data))
        .map(v => v.name);

      if (missingRequired.length > 0) {
        throw new Error(`Missing required variables: ${missingRequired.join(', ')}`);
      }

      // Validate data types
      for (const variable of variables) {
        if (variable.name in data) {
          const value = data[variable.name];
          const isValid = this.validateDataType(value, variable.type);
          
          if (!isValid) {
            throw new Error(`Invalid data type for variable '${variable.name}'. Expected ${variable.type}.`);
          }
        }
      }
    } catch (error) {
      console.error('Error validating template data:', error);
      throw error;
    }
  }

  /**
   * Validate data type
   */
  private validateDataType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * Update document statistics (placeholder)
   */
  private async updateDocumentStats(): Promise<void> {
    // TODO: Implement statistics update once schema is fixed
    console.log('Document statistics updated');
  }
}

export const documentService = new DocumentService();
