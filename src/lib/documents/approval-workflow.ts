import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export enum ApprovalStatus {
  PENDING = 'PENDING',
  IN_REVIEW = 'IN_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

export enum ApprovalAction {
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  REQUEST_CHANGES = 'REQUEST_CHANGES',
  DELEGATE = 'DELEGATE',
  ESCALATE = 'ESCALATE'
}

export interface ApprovalStep {
  id: string;
  sequence: number;
  approverType: 'USER' | 'ROLE' | 'ANY_OF_ROLE' | 'ALL_OF_ROLE';
  approverIds: string[]; // User IDs or Role IDs
  isRequired: boolean;
  isParallel: boolean; // Can be processed in parallel with other steps
  conditions?: {
    field: string;
    operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
    value: any;
  }[];
  timeoutHours?: number; // Auto-escalate after timeout
  escalationApproverIds?: string[];
}

export interface ApprovalWorkflowTemplate {
  id: string;
  name: string;
  description: string;
  documentTypes: string[];
  category: string;
  steps: ApprovalStep[];
  isActive: boolean;
  autoStart: boolean;
  notificationSettings: {
    notifyOnSubmission: boolean;
    notifyOnApproval: boolean;
    notifyOnRejection: boolean;
    reminderIntervalHours: number;
  };
}

export interface DocumentApprovalRequest {
  id: string;
  documentId: string;
  workflowTemplateId: string;
  requestedBy: string;
  status: ApprovalStatus;
  currentStepId?: string;
  submittedAt: Date;
  completedAt?: Date;
  metadata: Record<string, any>;
  steps: ApprovalStepExecution[];
}

export interface ApprovalStepExecution {
  id: string;
  stepId: string;
  sequence: number;
  status: ApprovalStatus;
  assignedTo: string[];
  startedAt?: Date;
  completedAt?: Date;
  action?: ApprovalAction;
  comments?: string;
  approvedBy?: string;
  timeoutAt?: Date;
}

export class DocumentApprovalWorkflow {
  private workflowTemplates: ApprovalWorkflowTemplate[] = [
    {
      id: 'standard-document-approval',
      name: 'Standard Document Approval',
      description: 'Standard approval workflow for general documents',
      documentTypes: ['ANNOUNCEMENT', 'RFQ', 'CONTRACT', 'PURCHASE_ORDER'],
      category: 'GENERAL',
      steps: [
        {
          id: 'step-1',
          sequence: 1,
          approverType: 'ROLE',
          approverIds: ['procurement_manager'],
          isRequired: true,
          isParallel: false,
          timeoutHours: 24,
          escalationApproverIds: ['procurement_director'],
        },
        {
          id: 'step-2',
          sequence: 2,
          approverType: 'ROLE',
          approverIds: ['legal_team'],
          isRequired: true,
          isParallel: false,
          timeoutHours: 48,
          conditions: [
            {
              field: 'documentType',
              operator: 'equals',
              value: 'CONTRACT',
            },
          ],
        },
        {
          id: 'step-3',
          sequence: 3,
          approverType: 'ROLE',
          approverIds: ['finance_manager'],
          isRequired: true,
          isParallel: false,
          timeoutHours: 24,
          conditions: [
            {
              field: 'estimatedValue',
              operator: 'greater_than',
              value: 100000000, // 100M IDR
            },
          ],
        },
      ],
      isActive: true,
      autoStart: true,
      notificationSettings: {
        notifyOnSubmission: true,
        notifyOnApproval: true,
        notifyOnRejection: true,
        reminderIntervalHours: 12,
      },
    },
    {
      id: 'high-value-approval',
      name: 'High Value Document Approval',
      description: 'Approval workflow for high-value procurement documents',
      documentTypes: ['CONTRACT', 'PURCHASE_ORDER'],
      category: 'HIGH_VALUE',
      steps: [
        {
          id: 'step-1',
          sequence: 1,
          approverType: 'ROLE',
          approverIds: ['procurement_manager'],
          isRequired: true,
          isParallel: false,
          timeoutHours: 12,
        },
        {
          id: 'step-2',
          sequence: 2,
          approverType: 'ALL_OF_ROLE',
          approverIds: ['legal_team', 'finance_manager'],
          isRequired: true,
          isParallel: true,
          timeoutHours: 24,
        },
        {
          id: 'step-3',
          sequence: 3,
          approverType: 'ROLE',
          approverIds: ['procurement_director'],
          isRequired: true,
          isParallel: false,
          timeoutHours: 24,
        },
        {
          id: 'step-4',
          sequence: 4,
          approverType: 'ROLE',
          approverIds: ['ceo'],
          isRequired: true,
          isParallel: false,
          timeoutHours: 48,
          conditions: [
            {
              field: 'estimatedValue',
              operator: 'greater_than',
              value: 1000000000, // 1B IDR
            },
          ],
        },
      ],
      isActive: true,
      autoStart: false,
      notificationSettings: {
        notifyOnSubmission: true,
        notifyOnApproval: true,
        notifyOnRejection: true,
        reminderIntervalHours: 6,
      },
    },
  ];

  /**
   * Start approval workflow for a document
   */
  async startApprovalWorkflow(
    documentId: string,
    requestedBy: string,
    workflowTemplateId?: string,
    metadata: Record<string, any> = {}
  ): Promise<DocumentApprovalRequest> {
    try {
      // Get workflow template
      const template = workflowTemplateId 
        ? this.getWorkflowTemplate(workflowTemplateId)
        : await this.selectWorkflowTemplate(documentId, metadata);

      if (!template) {
        throw new Error('No suitable workflow template found');
      }

      // Create approval request
      const approvalRequest: DocumentApprovalRequest = {
        id: `approval-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
        documentId,
        workflowTemplateId: template.id,
        requestedBy,
        status: ApprovalStatus.PENDING,
        submittedAt: new Date(),
        metadata,
        steps: [],
      };

      // Initialize approval steps
      approvalRequest.steps = await this.initializeApprovalSteps(template, metadata);

      // Start first step
      await this.startNextStep(approvalRequest);

      // TODO: Save to database once schema is fixed
      console.log(`Started approval workflow ${template.name} for document ${documentId}`);

      // Send notifications
      if (template.notificationSettings.notifyOnSubmission) {
        await this.sendNotification('SUBMISSION', approvalRequest, template);
      }

      return approvalRequest;
    } catch (error) {
      console.error('Error starting approval workflow:', error);
      throw error;
    }
  }

  /**
   * Process approval action
   */
  async processApprovalAction(
    approvalRequestId: string,
    stepId: string,
    action: ApprovalAction,
    approverId: string,
    comments?: string
  ): Promise<DocumentApprovalRequest> {
    try {
      // TODO: Get approval request from database once schema is fixed
      // For now, create a mock approval request
      const approvalRequest = await this.getApprovalRequest(approvalRequestId);
      const template = this.getWorkflowTemplate(approvalRequest.workflowTemplateId);

      if (!template) {
        throw new Error('Workflow template not found');
      }

      // Find current step
      const currentStep = approvalRequest.steps.find(step => step.stepId === stepId);
      if (!currentStep) {
        throw new Error('Approval step not found');
      }

      // Validate approver
      if (!currentStep.assignedTo.includes(approverId)) {
        throw new Error('User not authorized to approve this step');
      }

      // Process action
      currentStep.action = action;
      currentStep.approvedBy = approverId;
      currentStep.comments = comments;
      currentStep.completedAt = new Date();

      switch (action) {
        case ApprovalAction.APPROVE:
          currentStep.status = ApprovalStatus.APPROVED;
          await this.handleStepApproval(approvalRequest, currentStep, template);
          break;

        case ApprovalAction.REJECT:
          currentStep.status = ApprovalStatus.REJECTED;
          approvalRequest.status = ApprovalStatus.REJECTED;
          approvalRequest.completedAt = new Date();
          break;

        case ApprovalAction.REQUEST_CHANGES:
          currentStep.status = ApprovalStatus.REJECTED;
          approvalRequest.status = ApprovalStatus.REJECTED;
          approvalRequest.completedAt = new Date();
          break;

        case ApprovalAction.DELEGATE:
          // TODO: Implement delegation logic
          break;

        case ApprovalAction.ESCALATE:
          await this.escalateStep(approvalRequest, currentStep, template);
          break;
      }

      // Send notifications
      if (template.notificationSettings.notifyOnApproval && action === ApprovalAction.APPROVE) {
        await this.sendNotification('APPROVAL', approvalRequest, template);
      } else if (template.notificationSettings.notifyOnRejection && action === ApprovalAction.REJECT) {
        await this.sendNotification('REJECTION', approvalRequest, template);
      }

      return approvalRequest;
    } catch (error) {
      console.error('Error processing approval action:', error);
      throw error;
    }
  }

  /**
   * Get approval request by ID
   */
  private async getApprovalRequest(approvalRequestId: string): Promise<DocumentApprovalRequest> {
    // TODO: Implement actual database retrieval once schema is fixed
    // For now, return a mock approval request
    return {
      id: approvalRequestId,
      documentId: 'mock-document-id',
      workflowTemplateId: 'standard-document-approval',
      requestedBy: 'mock-user-id',
      status: ApprovalStatus.IN_REVIEW,
      currentStepId: 'step-1',
      submittedAt: new Date(),
      metadata: {},
      steps: [
        {
          id: 'execution-1',
          stepId: 'step-1',
          sequence: 1,
          status: ApprovalStatus.IN_REVIEW,
          assignedTo: ['procurement_manager'],
          startedAt: new Date(),
        },
      ],
    };
  }

  /**
   * Select appropriate workflow template
   */
  private async selectWorkflowTemplate(
    documentId: string,
    metadata: Record<string, any>
  ): Promise<ApprovalWorkflowTemplate | null> {
    // TODO: Get document details from database once schema is fixed
    const documentType = metadata.documentType || 'GENERAL';
    const estimatedValue = metadata.estimatedValue || 0;

    // Select template based on document type and value
    if (estimatedValue > 500000000) { // 500M IDR
      return this.workflowTemplates.find(t => t.id === 'high-value-approval') || null;
    }

    return this.workflowTemplates.find(t => 
      t.documentTypes.includes(documentType) && t.category === 'GENERAL'
    ) || null;
  }

  /**
   * Get workflow template by ID
   */
  private getWorkflowTemplate(templateId: string): ApprovalWorkflowTemplate | null {
    return this.workflowTemplates.find(t => t.id === templateId) || null;
  }

  /**
   * Initialize approval steps
   */
  private async initializeApprovalSteps(
    template: ApprovalWorkflowTemplate,
    metadata: Record<string, any>
  ): Promise<ApprovalStepExecution[]> {
    const steps: ApprovalStepExecution[] = [];

    for (const stepTemplate of template.steps) {
      // Check if step conditions are met
      if (stepTemplate.conditions && !this.evaluateStepConditions(stepTemplate.conditions, metadata)) {
        continue; // Skip this step
      }

      // Get approvers for this step
      const assignedTo = await this.getStepApprovers(stepTemplate);

      const stepExecution: ApprovalStepExecution = {
        id: `execution-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
        stepId: stepTemplate.id,
        sequence: stepTemplate.sequence,
        status: ApprovalStatus.PENDING,
        assignedTo,
        timeoutAt: stepTemplate.timeoutHours 
          ? new Date(Date.now() + stepTemplate.timeoutHours * 60 * 60 * 1000)
          : undefined,
      };

      steps.push(stepExecution);
    }

    return steps.sort((a, b) => a.sequence - b.sequence);
  }

  /**
   * Start next approval step
   */
  private async startNextStep(approvalRequest: DocumentApprovalRequest): Promise<void> {
    const nextStep = approvalRequest.steps.find(step => step.status === ApprovalStatus.PENDING);
    
    if (nextStep) {
      nextStep.status = ApprovalStatus.IN_REVIEW;
      nextStep.startedAt = new Date();
      approvalRequest.currentStepId = nextStep.stepId;
      approvalRequest.status = ApprovalStatus.IN_REVIEW;

      console.log(`Started approval step ${nextStep.stepId} for document ${approvalRequest.documentId}`);
    } else {
      // All steps completed
      approvalRequest.status = ApprovalStatus.APPROVED;
      approvalRequest.completedAt = new Date();
      console.log(`All approval steps completed for document ${approvalRequest.documentId}`);
    }
  }

  /**
   * Handle step approval
   */
  private async handleStepApproval(
    approvalRequest: DocumentApprovalRequest,
    completedStep: ApprovalStepExecution,
    template: ApprovalWorkflowTemplate
  ): Promise<void> {
    // Check if there are more steps
    const hasMoreSteps = approvalRequest.steps.some(step => 
      step.sequence > completedStep.sequence && step.status === ApprovalStatus.PENDING
    );

    if (hasMoreSteps) {
      // Start next step
      await this.startNextStep(approvalRequest);
    } else {
      // All steps completed
      approvalRequest.status = ApprovalStatus.APPROVED;
      approvalRequest.completedAt = new Date();
      console.log(`Document ${approvalRequest.documentId} fully approved`);
    }
  }

  /**
   * Escalate approval step
   */
  private async escalateStep(
    approvalRequest: DocumentApprovalRequest,
    step: ApprovalStepExecution,
    template: ApprovalWorkflowTemplate
  ): Promise<void> {
    const stepTemplate = template.steps.find(s => s.id === step.stepId);
    
    if (stepTemplate?.escalationApproverIds) {
      step.assignedTo = stepTemplate.escalationApproverIds;
      console.log(`Escalated step ${step.stepId} to ${stepTemplate.escalationApproverIds.join(', ')}`);
    }
  }

  /**
   * Evaluate step conditions
   */
  private evaluateStepConditions(
    conditions: ApprovalStep['conditions'],
    metadata: Record<string, any>
  ): boolean {
    if (!conditions) return true;

    return conditions.every(condition => {
      const fieldValue = metadata[condition.field];
      
      switch (condition.operator) {
        case 'equals':
          return fieldValue === condition.value;
        case 'not_equals':
          return fieldValue !== condition.value;
        case 'greater_than':
          return Number(fieldValue) > Number(condition.value);
        case 'less_than':
          return Number(fieldValue) < Number(condition.value);
        case 'contains':
          return String(fieldValue).includes(String(condition.value));
        default:
          return true;
      }
    });
  }

  /**
   * Get approvers for a step
   */
  private async getStepApprovers(stepTemplate: ApprovalStep): Promise<string[]> {
    // TODO: Implement actual user/role resolution once schema is fixed
    // For now, return the approver IDs as-is
    return stepTemplate.approverIds;
  }

  /**
   * Send notification
   */
  private async sendNotification(
    type: 'SUBMISSION' | 'APPROVAL' | 'REJECTION',
    approvalRequest: DocumentApprovalRequest,
    template: ApprovalWorkflowTemplate
  ): Promise<void> {
    console.log(`Sending ${type} notification for approval request ${approvalRequest.id}`);
    // TODO: Implement actual notification sending once notification system is implemented
  }

  /**
   * Get workflow templates
   */
  getWorkflowTemplates(): ApprovalWorkflowTemplate[] {
    return this.workflowTemplates;
  }

  /**
   * Get pending approvals for user
   */
  async getPendingApprovals(userId: string): Promise<DocumentApprovalRequest[]> {
    // TODO: Implement actual database query once schema is fixed
    // For now, return empty array
    return [];
  }

  /**
   * Get approval history for document
   */
  async getDocumentApprovalHistory(documentId: string): Promise<DocumentApprovalRequest[]> {
    // TODO: Implement actual database query once schema is fixed
    // For now, return empty array
    return [];
  }
}

export const documentApprovalWorkflow = new DocumentApprovalWorkflow();
