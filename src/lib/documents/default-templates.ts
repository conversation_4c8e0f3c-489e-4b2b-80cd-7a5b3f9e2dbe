import { TemplateVariable, DocumentSettings, DocumentTemplate, DocumentType } from "@/lib/types";

export const DEFAULT_DOCUMENT_TEMPLATES: Partial<Record<DocumentType, Partial<DocumentTemplate>>> = {
  PURCHASE_ORDER: {
    name: "Standard Purchase Order",
    type: "PURCHASE_ORDER",
    description: "Standard purchase order template for approved procurements",
    htmlTemplate: `
      <div class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <h1 class="text-xl font-bold">PURCHASE ORDER</h1>
            <p>{{company.name}}</p>
            <p>{{company.address}}</p>
            <p>{{company.phone}} | {{company.email}}</p>
          </div>
          <div class="text-right">
            <p><strong>PO Number:</strong> {{poNumber}}</p>
            <p><strong>Date:</strong> {{formatDate orderDate}}</p>
            <p><strong>Due Date:</strong> {{formatDate dueDate}}</p>
          </div>
        </div>
      </div>

      <div class="mb-4">
        <h3 class="font-bold">Vendor Information:</h3>
        <p><strong>{{vendor.companyName}}</strong></p>
        <p>{{vendor.address}}</p>
        <p>Contact: {{vendor.contactPerson}}</p>
        <p>Phone: {{vendor.contactPhone}} | Email: {{vendor.contactEmail}}</p>
      </div>

      <table>
        <thead>
          <tr>
            <th style="width: 5%;">No</th>
            <th style="width: 40%;">Item Description</th>
            <th style="width: 10%;">Quantity</th>
            <th style="width: 10%;">Unit</th>
            <th style="width: 15%;">Unit Price</th>
            <th style="width: 20%;">Total Price</th>
          </tr>
        </thead>
        <tbody>
          {{#eachWithIndex items}}
          <tr>
            <td class="text-center">{{index}}</td>
            <td>
              <strong>{{item.name}}</strong>
              {{#if item.description}}<br><small>{{item.description}}</small>{{/if}}
            </td>
            <td class="text-center">{{formatNumber quantity}}</td>
            <td class="text-center">{{item.unit}}</td>
            <td class="text-right">{{formatCurrency price}}</td>
            <td class="text-right">{{formatCurrency (multiply quantity price)}}</td>
          </tr>
          {{/eachWithIndex}}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="5" class="text-right font-bold">Subtotal:</td>
            <td class="text-right font-bold">{{formatCurrency subtotal}}</td>
          </tr>
          {{#if taxAmount}}
          <tr>
            <td colspan="5" class="text-right">Tax ({{taxRate}}%):</td>
            <td class="text-right">{{formatCurrency taxAmount}}</td>
          </tr>
          {{/if}}
          <tr>
            <td colspan="5" class="text-right font-bold text-lg">Total Amount:</td>
            <td class="text-right font-bold text-lg">{{formatCurrency totalAmount}}</td>
          </tr>
        </tfoot>
      </table>

      <div class="mt-4">
        <h3 class="font-bold">Terms & Conditions:</h3>
        <ul>
          <li>Payment terms: {{paymentTerms}}</li>
          <li>Delivery terms: {{deliveryTerms}}</li>
          <li>Delivery address: {{deliveryAddress}}</li>
          {{#if notes}}
          <li>Notes: {{notes}}</li>
          {{/if}}
        </ul>
      </div>

      <div class="footer">
        <div style="display: flex; justify-content: space-between; margin-top: 40px;">
          <div class="text-center" style="width: 45%;">
            <p>Prepared by:</p>
            <br><br><br>
            <p>_____________________</p>
            <p>{{preparedBy.name}}</p>
            <p>{{preparedBy.title}}</p>
          </div>
          <div class="text-center" style="width: 45%;">
            <p>Approved by:</p>
            <br><br><br>
            <p>_____________________</p>
            <p>{{approvedBy.name}}</p>
            <p>{{approvedBy.title}}</p>
          </div>
        </div>
      </div>
    `,
    variables: [
      { name: "poNumber", type: "TEXT", required: true, description: "Purchase Order Number" },
      { name: "orderDate", type: "DATE", required: true, description: "Order Date" },
      { name: "dueDate", type: "DATE", required: true, description: "Due Date" },
      { name: "vendor", type: "OBJECT", required: true, description: "Vendor Information" },
      { name: "items", type: "ARRAY", required: true, description: "Order Items" },
      { name: "subtotal", type: "NUMBER", required: true, description: "Subtotal Amount" },
      { name: "taxAmount", type: "NUMBER", required: false, description: "Tax Amount" },
      { name: "taxRate", type: "NUMBER", required: false, description: "Tax Rate" },
      { name: "totalAmount", type: "NUMBER", required: true, description: "Total Amount" },
      { name: "paymentTerms", type: "TEXT", required: true, description: "Payment Terms" },
      { name: "deliveryTerms", type: "TEXT", required: true, description: "Delivery Terms" },
      { name: "deliveryAddress", type: "TEXT", required: true, description: "Delivery Address" },
      { name: "notes", type: "TEXT", required: false, description: "Additional Notes" },
      { name: "preparedBy", type: "OBJECT", required: true, description: "Prepared By" },
      { name: "approvedBy", type: "OBJECT", required: true, description: "Approved By" },
      { name: "company", type: "OBJECT", required: true, description: "Company Information" },
    ] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
      header: { enabled: false, height: 0 },
      footer: { enabled: true, height: 30, template: "<div style='text-align: center; font-size: 10px;'>Page {{pageNumber}} of {{totalPages}}</div>" },
    } as DocumentSettings,
  },

  BAST: {
    name: "Berita Acara Serah Terima (BAST)",
    type: "BAST",
    description: "Official handover document template",
    htmlTemplate: `
      <div class="header text-center">
        <h1 class="text-xl font-bold">BERITA ACARA SERAH TERIMA</h1>
        <h2 class="text-lg">(BAST)</h2>
        <p class="mt-4"><strong>No: {{bastNumber}}</strong></p>
      </div>

      <div class="mb-4">
        <p>Pada hari ini, {{formatDate handoverDate}}, telah dilaksanakan serah terima barang/jasa dengan rincian sebagai berikut:</p>
      </div>

      <div class="mb-4">
        <h3 class="font-bold">I. DATA KONTRAK/PURCHASE ORDER</h3>
        <table style="border: none;">
          <tr style="border: none;">
            <td style="border: none; width: 30%;">Nomor PO</td>
            <td style="border: none; width: 5%;">:</td>
            <td style="border: none;">{{po.poNumber}}</td>
          </tr>
          <tr style="border: none;">
            <td style="border: none;">Tanggal PO</td>
            <td style="border: none;">:</td>
            <td style="border: none;">{{formatDate po.orderDate}}</td>
          </tr>
          <tr style="border: none;">
            <td style="border: none;">Vendor</td>
            <td style="border: none;">:</td>
            <td style="border: none;">{{vendor.companyName}}</td>
          </tr>
          <tr style="border: none;">
            <td style="border: none;">Nilai Kontrak</td>
            <td style="border: none;">:</td>
            <td style="border: none;">{{formatCurrency po.totalAmount}}</td>
          </tr>
        </table>
      </div>

      <div class="mb-4">
        <h3 class="font-bold">II. RINCIAN BARANG/JASA YANG DISERAHTERIMAKAN</h3>
        <table>
          <thead>
            <tr>
              <th style="width: 5%;">No</th>
              <th style="width: 35%;">Nama Barang/Jasa</th>
              <th style="width: 15%;">Spesifikasi</th>
              <th style="width: 10%;">Satuan</th>
              <th style="width: 10%;">Jumlah Kontrak</th>
              <th style="width: 10%;">Jumlah Diterima</th>
              <th style="width: 15%;">Kondisi</th>
            </tr>
          </thead>
          <tbody>
            {{#eachWithIndex items}}
            <tr>
              <td class="text-center">{{index}}</td>
              <td>{{item.name}}</td>
              <td>{{item.description}}</td>
              <td class="text-center">{{item.unit}}</td>
              <td class="text-center">{{formatNumber contractQuantity}}</td>
              <td class="text-center">{{formatNumber receivedQuantity}}</td>
              <td class="text-center">{{condition}}</td>
            </tr>
            {{/eachWithIndex}}
          </tbody>
        </table>
      </div>

      <div class="mb-4">
        <h3 class="font-bold">III. CATATAN</h3>
        {{#if notes}}
        <p>{{notes}}</p>
        {{else}}
        <p>Barang/jasa telah diterima dalam kondisi baik dan sesuai dengan spesifikasi yang diminta.</p>
        {{/if}}
      </div>

      <div class="mb-4">
        <h3 class="font-bold">IV. KESIMPULAN</h3>
        <p>Dengan ditandatanganinya Berita Acara Serah Terima ini, maka penyerahan barang/jasa dari pihak vendor kepada pihak pembeli telah dilaksanakan dengan baik.</p>
      </div>

      <div class="footer">
        <div style="display: flex; justify-content: space-between; margin-top: 40px;">
          <div class="text-center" style="width: 45%;">
            <p><strong>PIHAK PERTAMA</strong></p>
            <p>(Vendor)</p>
            <br><br><br>
            <p>_____________________</p>
            <p>{{vendorRepresentative.name}}</p>
            <p>{{vendorRepresentative.title}}</p>
            <p>{{vendor.companyName}}</p>
          </div>
          <div class="text-center" style="width: 45%;">
            <p><strong>PIHAK KEDUA</strong></p>
            <p>(Pembeli)</p>
            <br><br><br>
            <p>_____________________</p>
            <p>{{internalRepresentative.name}}</p>
            <p>{{internalRepresentative.title}}</p>
            <p>{{company.name}}</p>
          </div>
        </div>
      </div>
    `,
    variables: [
      { name: "bastNumber", type: "TEXT", required: true, description: "BAST Number" },
      { name: "handoverDate", type: "DATE", required: true, description: "Handover Date" },
      { name: "po", type: "OBJECT", required: true, description: "Purchase Order Information" },
      { name: "vendor", type: "OBJECT", required: true, description: "Vendor Information" },
      { name: "items", type: "ARRAY", required: true, description: "Items Being Handed Over" },
      { name: "notes", type: "TEXT", required: false, description: "Additional Notes" },
      { name: "vendorRepresentative", type: "OBJECT", required: true, description: "Vendor Representative" },
      { name: "internalRepresentative", type: "OBJECT", required: true, description: "Internal Representative" },
      { name: "company", type: "OBJECT", required: true, description: "Company Information" },
    ] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 25, right: 25, bottom: 25, left: 25 },
      header: { enabled: false, height: 0 },
      footer: { enabled: true, height: 30, template: "<div style='text-align: center; font-size: 10px;'>BAST - Page {{pageNumber}} of {{totalPages}}</div>" },
    } as DocumentSettings,
  },

  VENDOR_EVALUATION: {
    name: "Vendor Evaluation Report",
    type: "VENDOR_EVALUATION",
    description: "Comprehensive vendor evaluation report with scoring",
    htmlTemplate: `
      <div class="header">
        <h1 class="text-xl font-bold text-center">VENDOR EVALUATION REPORT</h1>
        <div style="display: flex; justify-content: space-between; margin-top: 20px;">
          <div>
            <p><strong>Evaluation ID:</strong> {{evaluationId}}</p>
            <p><strong>Procurement:</strong> {{procurement.title}}</p>
            <p><strong>Procurement Number:</strong> {{procurement.procurementNumber}}</p>
          </div>
          <div class="text-right">
            <p><strong>Evaluation Date:</strong> {{formatDate evaluationDate}}</p>
            <p><strong>Evaluator:</strong> {{evaluator.name}}</p>
            <p><strong>Status:</strong> {{status}}</p>
          </div>
        </div>
      </div>

      <div class="mb-4">
        <h3 class="font-bold">VENDOR INFORMATION</h3>
        <table style="border: none;">
          <tr style="border: none;">
            <td style="border: none; width: 25%;">Company Name</td>
            <td style="border: none; width: 5%;">:</td>
            <td style="border: none;">{{vendor.companyName}}</td>
          </tr>
          <tr style="border: none;">
            <td style="border: none;">Contact Person</td>
            <td style="border: none;">:</td>
            <td style="border: none;">{{vendor.contactPerson}}</td>
          </tr>
          <tr style="border: none;">
            <td style="border: none;">Email</td>
            <td style="border: none;">:</td>
            <td style="border: none;">{{vendor.contactEmail}}</td>
          </tr>
          <tr style="border: none;">
            <td style="border: none;">Phone</td>
            <td style="border: none;">:</td>
            <td style="border: none;">{{vendor.contactPhone}}</td>
          </tr>
        </table>
      </div>

      <div class="mb-4">
        <h3 class="font-bold">EVALUATION CRITERIA & SCORES</h3>
        <table>
          <thead>
            <tr>
              <th style="width: 5%;">No</th>
              <th style="width: 40%;">Criteria</th>
              <th style="width: 15%;">Weight (%)</th>
              <th style="width: 15%;">Score</th>
              <th style="width: 15%;">Weighted Score</th>
              <th style="width: 10%;">Rating</th>
            </tr>
          </thead>
          <tbody>
            {{#eachWithIndex criteria}}
            <tr>
              <td class="text-center">{{index}}</td>
              <td>{{name}}</td>
              <td class="text-center">{{formatNumber weight 1}}</td>
              <td class="text-center">{{formatNumber score 1}}</td>
              <td class="text-center">{{formatNumber weightedScore 2}}</td>
              <td class="text-center">{{rating}}</td>
            </tr>
            {{/eachWithIndex}}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="4" class="text-right font-bold">Total Score:</td>
              <td class="text-center font-bold">{{formatNumber totalScore 2}}</td>
              <td class="text-center font-bold">{{overallRating}}</td>
            </tr>
          </tfoot>
        </table>
      </div>

      <div class="mb-4">
        <h3 class="font-bold">EVALUATION SUMMARY</h3>
        <table style="border: none;">
          <tr style="border: none;">
            <td style="border: none; width: 25%;">Total Score</td>
            <td style="border: none; width: 5%;">:</td>
            <td style="border: none;"><strong>{{formatNumber totalScore 2}} / 100</strong></td>
          </tr>
          <tr style="border: none;">
            <td style="border: none;">Overall Rating</td>
            <td style="border: none;">:</td>
            <td style="border: none;"><strong>{{overallRating}}</strong></td>
          </tr>
          <tr style="border: none;">
            <td style="border: none;">Recommendation</td>
            <td style="border: none;">:</td>
            <td style="border: none;"><strong>{{recommendation}}</strong></td>
          </tr>
        </table>
      </div>

      {{#if strengths}}
      <div class="mb-4">
        <h3 class="font-bold">STRENGTHS</h3>
        <ul>
          {{#each strengths}}
          <li>{{this}}</li>
          {{/each}}
        </ul>
      </div>
      {{/if}}

      {{#if weaknesses}}
      <div class="mb-4">
        <h3 class="font-bold">AREAS FOR IMPROVEMENT</h3>
        <ul>
          {{#each weaknesses}}
          <li>{{this}}</li>
          {{/each}}
        </ul>
      </div>
      {{/if}}

      {{#if comments}}
      <div class="mb-4">
        <h3 class="font-bold">ADDITIONAL COMMENTS</h3>
        <p>{{comments}}</p>
      </div>
      {{/if}}

      <div class="footer">
        <div style="display: flex; justify-content: space-between; margin-top: 40px;">
          <div class="text-center" style="width: 45%;">
            <p>Evaluated by:</p>
            <br><br><br>
            <p>_____________________</p>
            <p>{{evaluator.name}}</p>
            <p>{{evaluator.title}}</p>
          </div>
          <div class="text-center" style="width: 45%;">
            <p>Reviewed by:</p>
            <br><br><br>
            <p>_____________________</p>
            <p>{{reviewer.name}}</p>
            <p>{{reviewer.title}}</p>
          </div>
        </div>
      </div>
    `,
    variables: [
      { name: "evaluationId", type: "TEXT", required: true, description: "Evaluation ID" },
      { name: "evaluationDate", type: "DATE", required: true, description: "Evaluation Date" },
      { name: "procurement", type: "OBJECT", required: true, description: "Procurement Information" },
      { name: "vendor", type: "OBJECT", required: true, description: "Vendor Information" },
      { name: "evaluator", type: "OBJECT", required: true, description: "Evaluator Information" },
      { name: "reviewer", type: "OBJECT", required: true, description: "Reviewer Information" },
      { name: "criteria", type: "ARRAY", required: true, description: "Evaluation Criteria" },
      { name: "totalScore", type: "NUMBER", required: true, description: "Total Score" },
      { name: "overallRating", type: "TEXT", required: true, description: "Overall Rating" },
      { name: "recommendation", type: "TEXT", required: true, description: "Recommendation" },
      { name: "strengths", type: "ARRAY", required: false, description: "Vendor Strengths" },
      { name: "weaknesses", type: "ARRAY", required: false, description: "Areas for Improvement" },
      { name: "comments", type: "TEXT", required: false, description: "Additional Comments" },
      { name: "status", type: "TEXT", required: true, description: "Evaluation Status" },
    ] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
      header: { enabled: false, height: 0 },
      footer: { enabled: true, height: 30, template: "<div style='text-align: center; font-size: 10px;'>Vendor Evaluation Report - Page {{pageNumber}} of {{totalPages}}</div>" },
    } as DocumentSettings,
  },

  // Placeholder templates for other document types
  CONTRACT: {
    name: "Standard Contract Template",
    type: "CONTRACT",
    description: "Standard contract template",
    htmlTemplate: "<h1>Contract Template - To be implemented</h1>",
    variables: [] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
    } as DocumentSettings,
  },

  PURCHASE_REQUISITION: {
    name: "Purchase Requisition Document",
    type: "PURCHASE_REQUISITION",
    description: "Purchase requisition approval document",
    htmlTemplate: "<h1>Purchase Requisition Template - To be implemented</h1>",
    variables: [] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
    } as DocumentSettings,
  },

  GRN: {
    name: "Good Receipt Confirmation",
    type: "GRN",
    description: "Good receipt confirmation document",
    htmlTemplate: "<h1>Good Receipt Template - To be implemented</h1>",
    variables: [] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
    } as DocumentSettings,
  },

  INVOICE: {
    name: "Invoice Template",
    type: "INVOICE",
    description: "Standard invoice template",
    htmlTemplate: "<h1>Invoice Template - To be implemented</h1>",
    variables: [] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
    } as DocumentSettings,
  },

  QUOTATION: {
    name: "Quotation Template",
    type: "QUOTATION",
    description: "Standard quotation template",
    htmlTemplate: "<h1>Quotation Template - To be implemented</h1>",
    variables: [] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
    } as DocumentSettings,
  },

  CUSTOM: {
    name: "Custom Document Template",
    type: "CUSTOM",
    description: "Custom document template",
    htmlTemplate: "<h1>Custom Template - To be configured</h1>",
    variables: [] as TemplateVariable[],
    settings: {
      pageSize: "A4",
      orientation: "portrait",
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
    } as DocumentSettings,
  },
};
