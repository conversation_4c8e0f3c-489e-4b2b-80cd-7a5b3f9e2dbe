import { PrismaClient } from '@prisma/client';
import { documentSecurity, DocumentPermission, AccessLevel } from './security';

const prisma = new PrismaClient();

export interface ShareRequest {
  documentId: string;
  sharedBy: string;
  sharedWith: {
    type: 'USER' | 'ROLE' | 'EMAIL' | 'PUBLIC';
    identifier: string;
    name?: string;
  }[];
  permissions: DocumentPermission[];
  accessLevel: AccessLevel;
  expiresAt?: Date;
  message?: string;
  requiresApproval?: boolean;
  conditions?: {
    timeRestriction?: {
      startDate?: Date;
      endDate?: Date;
      allowedHours?: { start: number; end: number };
    };
    ipRestriction?: string[];
    locationRestriction?: string[];
  };
}

export interface ShareLink {
  id: string;
  documentId: string;
  token: string;
  permissions: DocumentPermission[];
  expiresAt?: Date;
  maxUses?: number;
  currentUses: number;
  createdBy: string;
  createdAt: Date;
  isActive: boolean;
}

export interface ShareNotification {
  id: string;
  shareRequestId: string;
  recipientType: 'USER' | 'EMAIL';
  recipientId: string;
  status: 'PENDING' | 'SENT' | 'VIEWED' | 'ACCEPTED' | 'DECLINED';
  sentAt?: Date;
  viewedAt?: Date;
  respondedAt?: Date;
}

export class DocumentSharing {
  /**
   * Share document with users, roles, or external parties
   */
  async shareDocument(shareRequest: ShareRequest): Promise<{
    success: boolean;
    shareId: string;
    notifications: ShareNotification[];
    errors?: string[];
  }> {
    try {
      // Validate share request
      const validation = await this.validateShareRequest(shareRequest);
      if (!validation.valid) {
        return {
          success: false,
          shareId: '',
          notifications: [],
          errors: validation.errors,
        };
      }

      const shareId = `share-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
      const notifications: ShareNotification[] = [];
      const errors: string[] = [];

      // Process each share target
      for (const target of shareRequest.sharedWith) {
        try {
          switch (target.type) {
            case 'USER':
              await this.shareWithUser(shareRequest, target.identifier, shareId);
              notifications.push(await this.createNotification(shareId, 'USER', target.identifier));
              break;
              
            case 'ROLE':
              await this.shareWithRole(shareRequest, target.identifier, shareId);
              // Get users in role and create notifications
              const roleUsers = await this.getUsersInRole(target.identifier);
              for (const userId of roleUsers) {
                notifications.push(await this.createNotification(shareId, 'USER', userId));
              }
              break;
              
            case 'EMAIL':
              await this.shareWithEmail(shareRequest, target.identifier, shareId);
              notifications.push(await this.createNotification(shareId, 'EMAIL', target.identifier));
              break;
              
            case 'PUBLIC':
              await this.createPublicShareLink(shareRequest, shareId);
              break;
          }
        } catch (error) {
          console.error(`Error sharing with ${target.type} ${target.identifier}:`, error);
          errors.push(`Failed to share with ${target.type} ${target.identifier}`);
        }
      }

      // Send notifications
      await this.sendShareNotifications(notifications, shareRequest);

      // Log audit event
      await documentSecurity.logAuditEvent({
        documentId: shareRequest.documentId,
        userId: shareRequest.sharedBy,
        action: 'SHARE',
        details: {
          shareId,
          sharedWith: shareRequest.sharedWith,
          permissions: shareRequest.permissions,
          accessLevel: shareRequest.accessLevel,
        },
        success: true,
      });

      return {
        success: errors.length === 0,
        shareId,
        notifications,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      console.error('Error sharing document:', error);
      throw error;
    }
  }

  /**
   * Create public share link
   */
  async createShareLink(
    documentId: string,
    createdBy: string,
    permissions: DocumentPermission[],
    options?: {
      expiresAt?: Date;
      maxUses?: number;
      requiresPassword?: boolean;
      password?: string;
    }
  ): Promise<ShareLink> {
    try {
      const token = documentSecurity.generateSharingToken(
        documentId,
        permissions,
        options?.expiresAt
      );

      const shareLink: ShareLink = {
        id: `link-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
        documentId,
        token,
        permissions,
        expiresAt: options?.expiresAt,
        maxUses: options?.maxUses,
        currentUses: 0,
        createdBy,
        createdAt: new Date(),
        isActive: true,
      };

      // TODO: Save to database once schema is fixed
      console.log('Created share link:', shareLink);

      // Log audit event
      await documentSecurity.logAuditEvent({
        documentId,
        userId: createdBy,
        action: 'SHARE',
        details: {
          type: 'public_link',
          permissions,
          expiresAt: options?.expiresAt,
          maxUses: options?.maxUses,
        },
        success: true,
      });

      return shareLink;
    } catch (error) {
      console.error('Error creating share link:', error);
      throw error;
    }
  }

  /**
   * Access document via share link
   */
  async accessViaShareLink(
    token: string,
    context?: {
      ipAddress?: string;
      userAgent?: string;
      userId?: string;
    }
  ): Promise<{
    allowed: boolean;
    documentId?: string;
    permissions?: DocumentPermission[];
    reason?: string;
  }> {
    try {
      // Validate token
      const tokenValidation = documentSecurity.validateSharingToken(token);
      if (!tokenValidation.valid) {
        return {
          allowed: false,
          reason: tokenValidation.expired ? 'Share link has expired' : 'Invalid share link',
        };
      }

      // TODO: Get share link from database and check usage limits
      // For now, just validate token
      
      // Log access attempt
      if (tokenValidation.documentId) {
        await documentSecurity.logAuditEvent({
          documentId: tokenValidation.documentId,
          userId: context?.userId || 'anonymous',
          action: 'VIEW',
          details: {
            accessMethod: 'share_link',
            token: token.substring(0, 20) + '...',
          },
          ipAddress: context?.ipAddress,
          userAgent: context?.userAgent,
          success: true,
        });
      }

      return {
        allowed: true,
        documentId: tokenValidation.documentId,
        permissions: tokenValidation.permissions,
      };
    } catch (error) {
      console.error('Error accessing via share link:', error);
      return {
        allowed: false,
        reason: 'Access validation failed',
      };
    }
  }

  /**
   * Revoke document share
   */
  async revokeShare(
    shareId: string,
    revokedBy: string,
    reason?: string
  ): Promise<void> {
    try {
      // TODO: Implement actual share revocation once schema is fixed
      console.log(`Revoke share ${shareId} by ${revokedBy}:`, reason);

      // Log audit event
      await documentSecurity.logAuditEvent({
        documentId: 'unknown', // Would get from share record
        userId: revokedBy,
        action: 'SHARE',
        details: {
          action: 'revoke',
          shareId,
          reason,
        },
        success: true,
      });
    } catch (error) {
      console.error('Error revoking share:', error);
      throw error;
    }
  }

  /**
   * Get document shares
   */
  async getDocumentShares(documentId: string): Promise<{
    userShares: any[];
    roleShares: any[];
    emailShares: any[];
    publicLinks: ShareLink[];
  }> {
    try {
      // TODO: Implement actual share retrieval once schema is fixed
      return {
        userShares: [],
        roleShares: [],
        emailShares: [],
        publicLinks: [],
      };
    } catch (error) {
      console.error('Error getting document shares:', error);
      throw error;
    }
  }

  /**
   * Get user's shared documents
   */
  async getUserSharedDocuments(
    userId: string,
    options?: {
      sharedBy?: boolean; // Documents shared by user
      sharedWith?: boolean; // Documents shared with user
      limit?: number;
      offset?: number;
    }
  ): Promise<any[]> {
    try {
      // TODO: Implement actual shared documents retrieval once schema is fixed
      return [];
    } catch (error) {
      console.error('Error getting user shared documents:', error);
      throw error;
    }
  }

  /**
   * Validate share request
   */
  private async validateShareRequest(shareRequest: ShareRequest): Promise<{
    valid: boolean;
    errors?: string[];
  }> {
    const errors: string[] = [];

    // Check if user has permission to share the document
    const sharePermission = await documentSecurity.checkPermission(
      shareRequest.documentId,
      shareRequest.sharedBy,
      DocumentPermission.SHARE
    );

    if (!sharePermission.allowed) {
      errors.push('You do not have permission to share this document');
    }

    // Validate share targets
    if (shareRequest.sharedWith.length === 0) {
      errors.push('At least one share target is required');
    }

    // Validate permissions
    if (shareRequest.permissions.length === 0) {
      errors.push('At least one permission is required');
    }

    // Validate expiration date
    if (shareRequest.expiresAt && shareRequest.expiresAt <= new Date()) {
      errors.push('Expiration date must be in the future');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * Share with specific user
   */
  private async shareWithUser(
    shareRequest: ShareRequest,
    userId: string,
    shareId: string
  ): Promise<void> {
    await documentSecurity.grantPermission(
      shareRequest.documentId,
      'USER',
      userId,
      shareRequest.permissions,
      shareRequest.accessLevel,
      shareRequest.sharedBy,
      shareRequest.conditions
    );
  }

  /**
   * Share with role
   */
  private async shareWithRole(
    shareRequest: ShareRequest,
    roleId: string,
    shareId: string
  ): Promise<void> {
    await documentSecurity.grantPermission(
      shareRequest.documentId,
      'ROLE',
      roleId,
      shareRequest.permissions,
      shareRequest.accessLevel,
      shareRequest.sharedBy,
      shareRequest.conditions
    );
  }

  /**
   * Share with email (external user)
   */
  private async shareWithEmail(
    shareRequest: ShareRequest,
    email: string,
    shareId: string
  ): Promise<void> {
    // Create temporary access token for external user
    const token = documentSecurity.generateSharingToken(
      shareRequest.documentId,
      shareRequest.permissions,
      shareRequest.expiresAt
    );

    // TODO: Store email share record in database
    console.log(`Share with email ${email}:`, { shareId, token });
  }

  /**
   * Create public share link
   */
  private async createPublicShareLink(
    shareRequest: ShareRequest,
    shareId: string
  ): Promise<void> {
    await this.createShareLink(
      shareRequest.documentId,
      shareRequest.sharedBy,
      shareRequest.permissions,
      {
        expiresAt: shareRequest.expiresAt,
      }
    );
  }

  /**
   * Create notification record
   */
  private async createNotification(
    shareRequestId: string,
    recipientType: 'USER' | 'EMAIL',
    recipientId: string
  ): Promise<ShareNotification> {
    return {
      id: `notif-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      shareRequestId,
      recipientType,
      recipientId,
      status: 'PENDING',
    };
  }

  /**
   * Send share notifications
   */
  private async sendShareNotifications(
    notifications: ShareNotification[],
    shareRequest: ShareRequest
  ): Promise<void> {
    for (const notification of notifications) {
      try {
        // TODO: Implement actual notification sending (email, in-app, etc.)
        console.log('Send share notification:', {
          notification,
          message: shareRequest.message,
        });
        
        notification.status = 'SENT';
        notification.sentAt = new Date();
      } catch (error) {
        console.error('Error sending notification:', error);
      }
    }
  }

  /**
   * Get users in role
   */
  private async getUsersInRole(roleId: string): Promise<string[]> {
    try {
      // TODO: Implement actual role user retrieval once schema is fixed
      return [];
    } catch (error) {
      console.error('Error getting users in role:', error);
      return [];
    }
  }
}

export const documentSharing = new DocumentSharing();
