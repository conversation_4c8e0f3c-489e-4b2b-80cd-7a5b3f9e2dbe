import { createHash } from 'crypto';
import { promises as fs } from 'fs';
import path from 'path';

export interface FileValidationOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  allowedExtensions?: string[];
  requireSignature?: boolean;
}

export interface FileMetadata {
  name: string;
  size: number;
  type: string;
  extension: string;
  checksum: string;
  isValid: boolean;
  validationErrors: string[];
}

export class DocumentUtils {
  /**
   * Validate uploaded file against security and business rules
   */
  static validateFile(
    file: { buffer: Buffer; originalname: string; mimetype: string },
    options: FileValidationOptions = {}
  ): FileMetadata {
    const errors: string[] = [];
    const extension = path.extname(file.originalname).toLowerCase();
    const checksum = createHash('sha256').update(file.buffer).digest('hex');

    // Default validation options
    const defaultOptions: FileValidationOptions = {
      maxSize: 50 * 1024 * 1024, // 50MB
      allowedTypes: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'image/gif',
        'text/plain',
        'text/csv',
      ],
      allowedExtensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.gif', '.txt', '.csv'],
      requireSignature: false,
    };

    const validationOptions = { ...defaultOptions, ...options };

    // Validate file size
    if (validationOptions.maxSize && file.buffer.length > validationOptions.maxSize) {
      errors.push(`File size exceeds maximum allowed size of ${this.formatFileSize(validationOptions.maxSize)}`);
    }

    // Validate MIME type
    if (validationOptions.allowedTypes && !validationOptions.allowedTypes.includes(file.mimetype)) {
      errors.push(`File type '${file.mimetype}' is not allowed`);
    }

    // Validate file extension
    if (validationOptions.allowedExtensions && !validationOptions.allowedExtensions.includes(extension)) {
      errors.push(`File extension '${extension}' is not allowed`);
    }

    // Validate filename
    if (!this.isValidFilename(file.originalname)) {
      errors.push('Invalid filename. Filename contains illegal characters');
    }

    // Check for malicious content
    if (this.containsMaliciousContent(file.buffer, file.mimetype)) {
      errors.push('File contains potentially malicious content');
    }

    return {
      name: file.originalname,
      size: file.buffer.length,
      type: file.mimetype,
      extension,
      checksum,
      isValid: errors.length === 0,
      validationErrors: errors,
    };
  }

  /**
   * Generate secure filename
   */
  static generateSecureFilename(originalName: string, prefix?: string): string {
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension);
    const sanitizedBaseName = this.sanitizeFilename(baseName);
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    
    const prefixPart = prefix ? `${prefix}_` : '';
    return `${prefixPart}${timestamp}_${random}_${sanitizedBaseName}${extension}`;
  }

  /**
   * Sanitize filename to remove dangerous characters
   */
  static sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace non-alphanumeric chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
      .substring(0, 100); // Limit length
  }

  /**
   * Check if filename is valid
   */
  static isValidFilename(filename: string): boolean {
    // Check for dangerous patterns
    const dangerousPatterns = [
      /\.\./,           // Directory traversal
      /[<>:"|?*]/,      // Windows reserved characters
      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i, // Windows reserved names
      /^\./,            // Hidden files
      /\.$|\.$/,        // Ending with dot
    ];

    return !dangerousPatterns.some(pattern => pattern.test(filename));
  }

  /**
   * Check for malicious content in file
   */
  static containsMaliciousContent(buffer: Buffer, mimeType: string): boolean {
    const content = buffer.toString('utf8', 0, Math.min(buffer.length, 1024));
    
    // Check for script injection patterns
    const maliciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i,
      /eval\(/i,
      /document\.write/i,
    ];

    // Only check text-based files
    if (mimeType.startsWith('text/') || mimeType.includes('xml') || mimeType.includes('html')) {
      return maliciousPatterns.some(pattern => pattern.test(content));
    }

    return false;
  }

  /**
   * Format file size in human readable format
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file icon based on file type
   */
  static getFileIcon(filename: string, mimeType: string): string {
    const extension = path.extname(filename).toLowerCase();
    
    // Icon mapping
    const iconMap: Record<string, string> = {
      '.pdf': 'file-pdf',
      '.doc': 'file-word',
      '.docx': 'file-word',
      '.xls': 'file-excel',
      '.xlsx': 'file-excel',
      '.ppt': 'file-powerpoint',
      '.pptx': 'file-powerpoint',
      '.txt': 'file-text',
      '.csv': 'file-csv',
      '.jpg': 'file-image',
      '.jpeg': 'file-image',
      '.png': 'file-image',
      '.gif': 'file-image',
      '.zip': 'file-archive',
      '.rar': 'file-archive',
    };

    return iconMap[extension] || 'file';
  }

  /**
   * Extract text content from file for indexing
   */
  static async extractTextContent(buffer: Buffer, mimeType: string): Promise<string> {
    try {
      // For text files, return content directly
      if (mimeType.startsWith('text/')) {
        return buffer.toString('utf8');
      }

      // For other file types, return empty string for now
      // TODO: Implement text extraction for PDF, Word, etc.
      return '';
    } catch (error) {
      console.error('Error extracting text content:', error);
      return '';
    }
  }

  /**
   * Generate document thumbnail
   */
  static async generateThumbnail(buffer: Buffer, mimeType: string): Promise<Buffer | null> {
    try {
      // For image files, generate thumbnail
      if (mimeType.startsWith('image/')) {
        // TODO: Implement image thumbnail generation
        // For now, return null
        return null;
      }

      // For PDF files, generate thumbnail of first page
      if (mimeType === 'application/pdf') {
        // TODO: Implement PDF thumbnail generation
        // For now, return null
        return null;
      }

      return null;
    } catch (error) {
      console.error('Error generating thumbnail:', error);
      return null;
    }
  }

  /**
   * Verify file integrity using checksum
   */
  static verifyFileIntegrity(buffer: Buffer, expectedChecksum: string): boolean {
    const actualChecksum = createHash('sha256').update(buffer).digest('hex');
    return actualChecksum === expectedChecksum;
  }

  /**
   * Create file backup
   */
  static async createBackup(filePath: string, backupDir: string): Promise<string> {
    try {
      const filename = path.basename(filePath);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFilename = `${timestamp}_${filename}`;
      const backupPath = path.join(backupDir, backupFilename);

      // Ensure backup directory exists
      await fs.mkdir(backupDir, { recursive: true });

      // Copy file to backup location
      await fs.copyFile(filePath, backupPath);

      return backupPath;
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  /**
   * Clean up old files based on retention policy
   */
  static async cleanupOldFiles(
    directory: string,
    retentionDays: number = 30
  ): Promise<{ deletedCount: number; freedSpace: number }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const files = await fs.readdir(directory);
      let deletedCount = 0;
      let freedSpace = 0;

      for (const file of files) {
        const filePath = path.join(directory, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime < cutoffDate) {
          freedSpace += stats.size;
          await fs.unlink(filePath);
          deletedCount++;
        }
      }

      return { deletedCount, freedSpace };
    } catch (error) {
      console.error('Error cleaning up old files:', error);
      throw error;
    }
  }

  /**
   * Get storage usage statistics
   */
  static async getStorageStats(directory: string): Promise<{
    totalFiles: number;
    totalSize: number;
    filesByType: Record<string, { count: number; size: number }>;
  }> {
    try {
      const files = await fs.readdir(directory);
      let totalFiles = 0;
      let totalSize = 0;
      const filesByType: Record<string, { count: number; size: number }> = {};

      for (const file of files) {
        const filePath = path.join(directory, file);
        const stats = await fs.stat(filePath);

        if (stats.isFile()) {
          totalFiles++;
          totalSize += stats.size;

          const extension = path.extname(file).toLowerCase();
          if (!filesByType[extension]) {
            filesByType[extension] = { count: 0, size: 0 };
          }
          filesByType[extension].count++;
          filesByType[extension].size += stats.size;
        }
      }

      return { totalFiles, totalSize, filesByType };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      throw error;
    }
  }
}

export default DocumentUtils;
