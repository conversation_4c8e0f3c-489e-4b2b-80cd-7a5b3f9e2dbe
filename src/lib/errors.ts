import { NextResponse } from "next/server";
import { z } from "zod";

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string = "Validation failed") {
    super(message, 400);
    this.name = "ValidationError";
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = "Unauthorized") {
    super(message, 401);
    this.name = "UnauthorizedError";
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = "Forbidden") {
    super(message, 403);
    this.name = "ForbiddenError";
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = "Not found") {
    super(message, 404);
    this.name = "NotFoundError";
  }
}

export class ConflictError extends AppError {
  constructor(message: string = "Resource already exists") {
    super(message, 409);
    this.name = "ConflictError";
  }
}

export function handleApiError(error: unknown): NextResponse {
  console.error("API Error:", error);

  // Handle Zod validation errors
  if (
    error &&
    typeof error === "object" &&
    "issues" in error &&
    Array.isArray((error as any).issues)
  ) {
    const zodError = error as {
      issues: Array<{ path: string[]; message: string }>;
    };
    return NextResponse.json(
      {
        error: "Validation failed",
        details:
          zodError.issues.map((err) => ({
            field: err.path.join(".") || "unknown",
            message: err.message || "Validation error",
          })) || [],
      },
      { status: 400 }
    );
  }

  // Alternative check for errors array property
  if (
    error &&
    typeof error === "object" &&
    "errors" in error &&
    Array.isArray((error as any).errors)
  ) {
    const zodError = error as {
      errors: Array<{ path: string[]; message: string }>;
    };
    return NextResponse.json(
      {
        error: "Validation failed",
        details:
          zodError.errors.map((err) => ({
            field: err.path.join(".") || "unknown",
            message: err.message || "Validation error",
          })) || [],
      },
      { status: 400 }
    );
  }

  // Handle custom app errors
  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: error.message,
      },
      { status: error.statusCode }
    );
  }

  // Handle Prisma errors
  if (error && typeof error === "object" && "code" in error) {
    const prismaError = error as { code: string; message: string };

    switch (prismaError.code) {
      case "P2002":
        return NextResponse.json(
          { error: "A record with this information already exists" },
          { status: 409 }
        );
      case "P2025":
        return NextResponse.json(
          { error: "Record not found" },
          { status: 404 }
        );
      default:
        return NextResponse.json(
          { error: "Database error occurred" },
          { status: 500 }
        );
    }
  }

  // Handle generic errors
  return NextResponse.json({ error: "Internal server error" }, { status: 500 });
}

export function createSuccessResponse<T>(
  data: T,
  message?: string,
  status: number = 200
) {
  return NextResponse.json(
    {
      success: true,
      message,
      data,
    },
    { status }
  );
}
