import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { documentService } from '@/lib/documents/document-service';

// Mock dependencies
jest.mock('@/lib/documents/document-manager');
jest.mock('@/lib/documents/template-engine');

describe('DocumentService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('uploadDocument', () => {
    it('should upload a document successfully', async () => {
      const mockFile = {
        buffer: Buffer.from('test content'),
        originalname: 'test.pdf',
        mimetype: 'application/pdf',
      };

      const mockOptions = {
        documentType: 'CONTRACT',
        description: 'Test document',
        tags: ['test'],
        isConfidential: false,
        requiresApproval: false,
      };

      const result = await documentService.uploadDocument('user-1', mockFile, mockOptions);

      expect(result).toBeDefined();
      expect(result.fileName).toBe('test.pdf');
      expect(result.documentType).toBe('CONTRACT');
    });

    it('should validate file type', async () => {
      const mockFile = {
        buffer: Buffer.from('test content'),
        originalname: 'test.exe',
        mimetype: 'application/x-executable',
      };

      const mockOptions = {
        documentType: 'CONTRACT',
        description: 'Test document',
      };

      await expect(
        documentService.uploadDocument('user-1', mockFile, mockOptions)
      ).rejects.toThrow('File type not allowed');
    });

    it('should validate file size', async () => {
      const largeBuffer = Buffer.alloc(100 * 1024 * 1024); // 100MB
      const mockFile = {
        buffer: largeBuffer,
        originalname: 'large.pdf',
        mimetype: 'application/pdf',
      };

      const mockOptions = {
        documentType: 'CONTRACT',
        description: 'Large document',
      };

      await expect(
        documentService.uploadDocument('user-1', mockFile, mockOptions)
      ).rejects.toThrow('File size exceeds maximum allowed');
    });
  });

  describe('searchDocuments', () => {
    it('should search documents with filters', async () => {
      const result = await documentService.searchDocuments('user-1', {
        query: 'contract',
        documentType: 'CONTRACT',
        status: 'ACTIVE',
        sortBy: 'uploadedAt',
        sortOrder: 'desc',
        limit: 10,
        offset: 0,
      });

      expect(result).toBeDefined();
      expect(Array.isArray(result.documents)).toBe(true);
      expect(result.pagination).toBeDefined();
    });

    it('should return empty results for no matches', async () => {
      const result = await documentService.searchDocuments('user-1', {
        query: 'nonexistent',
        limit: 10,
        offset: 0,
      });

      expect(result.documents).toHaveLength(0);
      expect(result.pagination.total).toBe(0);
    });
  });

  describe('generateDocument', () => {
    it('should generate document from template', async () => {
      const mockOptions = {
        templateId: 'template-1',
        name: 'Generated Contract',
        data: {
          contractNumber: 'CONT-2024-001',
          vendor: { name: 'Test Vendor' },
          amount: 1000000,
        },
        entityType: 'PROCUREMENT',
        entityId: 'proc-1',
        generatePdf: true,
      };

      const result = await documentService.generateDocument('user-1', mockOptions);

      expect(result).toBeDefined();
      expect(result.name).toBe('Generated Contract');
      expect(result.content).toBeDefined();
    });

    it('should validate template data', async () => {
      const mockOptions = {
        templateId: 'template-1',
        name: 'Invalid Contract',
        data: {}, // Missing required data
        entityType: 'PROCUREMENT',
        entityId: 'proc-1',
      };

      await expect(
        documentService.generateDocument('user-1', mockOptions)
      ).rejects.toThrow('Missing required variables');
    });
  });

  describe('getDocumentVersions', () => {
    it('should return document versions', async () => {
      const versions = await documentService.getDocumentVersions('doc-1', 'user-1');

      expect(Array.isArray(versions)).toBe(true);
    });
  });

  describe('createDocumentVersion', () => {
    it('should create new document version', async () => {
      const version = await documentService.createDocumentVersion(
        'doc-1',
        'user-1',
        'Updated content',
        'Fixed typos'
      );

      expect(version).toBeDefined();
      expect(version.changelog).toBe('Fixed typos');
    });
  });

  describe('bulkUpload', () => {
    it('should upload multiple documents', async () => {
      const mockFiles = [
        {
          buffer: Buffer.from('content 1'),
          originalname: 'doc1.pdf',
          mimetype: 'application/pdf',
        },
        {
          buffer: Buffer.from('content 2'),
          originalname: 'doc2.pdf',
          mimetype: 'application/pdf',
        },
      ];

      const mockOptions = {
        documentType: 'CONTRACT',
        description: 'Bulk upload test',
      };

      const result = await documentService.bulkUpload('user-1', mockFiles, mockOptions);

      expect(result.successful).toHaveLength(2);
      expect(result.failed).toHaveLength(0);
    });

    it('should handle partial failures in bulk upload', async () => {
      const mockFiles = [
        {
          buffer: Buffer.from('content 1'),
          originalname: 'doc1.pdf',
          mimetype: 'application/pdf',
        },
        {
          buffer: Buffer.from('content 2'),
          originalname: 'doc2.exe', // Invalid type
          mimetype: 'application/x-executable',
        },
      ];

      const mockOptions = {
        documentType: 'CONTRACT',
        description: 'Bulk upload test',
      };

      const result = await documentService.bulkUpload('user-1', mockFiles, mockOptions);

      expect(result.successful).toHaveLength(1);
      expect(result.failed).toHaveLength(1);
      expect(result.failed[0].error).toContain('File type not allowed');
    });
  });

  describe('getDocumentStats', () => {
    it('should return document statistics', async () => {
      const stats = await documentService.getDocumentStats('user-1');

      expect(stats).toBeDefined();
      expect(typeof stats.totalDocuments).toBe('number');
      expect(typeof stats.totalSize).toBe('number');
      expect(Array.isArray(stats.byType)).toBe(true);
      expect(Array.isArray(stats.byStatus)).toBe(true);
    });
  });

  describe('validateFileType', () => {
    it('should validate allowed file types', () => {
      expect(documentService.validateFileType('document.pdf')).toBe(true);
      expect(documentService.validateFileType('document.doc')).toBe(true);
      expect(documentService.validateFileType('document.docx')).toBe(true);
      expect(documentService.validateFileType('document.xls')).toBe(true);
      expect(documentService.validateFileType('document.xlsx')).toBe(true);
      expect(documentService.validateFileType('image.jpg')).toBe(true);
      expect(documentService.validateFileType('image.png')).toBe(true);
    });

    it('should reject disallowed file types', () => {
      expect(documentService.validateFileType('virus.exe')).toBe(false);
      expect(documentService.validateFileType('script.bat')).toBe(false);
      expect(documentService.validateFileType('archive.zip')).toBe(false);
    });
  });

  describe('validateFileSize', () => {
    it('should validate file size within limits', () => {
      const smallFile = Buffer.alloc(1024); // 1KB
      expect(documentService.validateFileSize(smallFile)).toBe(true);

      const mediumFile = Buffer.alloc(10 * 1024 * 1024); // 10MB
      expect(documentService.validateFileSize(mediumFile)).toBe(true);
    });

    it('should reject files exceeding size limit', () => {
      const largeFile = Buffer.alloc(100 * 1024 * 1024); // 100MB
      expect(documentService.validateFileSize(largeFile)).toBe(false);
    });
  });
});
