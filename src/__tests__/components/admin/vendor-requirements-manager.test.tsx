import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { VendorRequirementsManager } from '@/components/admin/vendor-requirements-manager';

// Mock fetch
global.fetch = vi.fn();

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockRequirements = [
  {
    id: 'req-1',
    name: 'Legal Compliance',
    description: 'Legal compliance requirements',
    category: 'LEGAL',
    type: 'MANDATORY',
    templateId: 'template-1',
    isActive: true,
    criteria: {
      description: 'Legal compliance criteria',
      requirements: [
        {
          item: 'Business License',
          description: 'Valid business license',
          required: true,
        },
      ],
      evaluation: {
        method: 'PASS_FAIL',
      },
    },
    requiredDocuments: [
      {
        name: 'Business License',
        type: 'PDF',
        required: true,
        format: ['pdf'],
        maxSize: 5,
      },
    ],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    creator: {
      id: 'user-1',
      name: 'Admin User',
      email: '<EMAIL>',
    },
    template: {
      id: 'template-1',
      name: 'Standard Procurement',
      type: 'GOODS',
      category: 'STANDARD',
    },
    _count: {
      procurementRequirements: 0,
    },
  },
];

const mockTemplates = [
  {
    id: 'template-1',
    name: 'Standard Procurement',
    type: 'GOODS',
    category: 'STANDARD',
  },
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('VendorRequirementsManager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful API responses
    (global.fetch as any).mockImplementation((url: string) => {
      if (url.includes('/api/admin/vendor-requirements')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            requirements: mockRequirements,
            pagination: {
              page: 1,
              limit: 10,
              total: 1,
              totalPages: 1,
            },
          }),
        });
      }
      
      if (url.includes('/api/admin/procurement-workflow-templates')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            templates: mockTemplates,
          }),
        });
      }
      
      return Promise.reject(new Error('Unknown URL'));
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should render vendor requirements manager', async () => {
    render(<VendorRequirementsManager />, { wrapper: createWrapper() });

    expect(screen.getByText('Vendor Requirements Management')).toBeInTheDocument();
    expect(screen.getByText('Define and manage vendor qualification requirements and eligibility criteria')).toBeInTheDocument();
    expect(screen.getByText('Create Requirement')).toBeInTheDocument();
  });

  it('should display vendor requirements list', async () => {
    render(<VendorRequirementsManager />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText('Legal Compliance')).toBeInTheDocument();
    });

    expect(screen.getByText('Legal compliance requirements')).toBeInTheDocument();
    expect(screen.getByText('LEGAL')).toBeInTheDocument();
    expect(screen.getByText('MANDATORY')).toBeInTheDocument();
    expect(screen.getByText('Standard Procurement')).toBeInTheDocument();
  });

  it('should open create dialog when create button is clicked', async () => {
    render(<VendorRequirementsManager />, { wrapper: createWrapper() });

    const createButton = screen.getByText('Create Requirement');
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(screen.getByText('Create New Vendor Requirement')).toBeInTheDocument();
    });

    expect(screen.getByLabelText('Name *')).toBeInTheDocument();
    expect(screen.getByLabelText('Category *')).toBeInTheDocument();
    expect(screen.getByLabelText('Type *')).toBeInTheDocument();
  });

  it('should filter requirements by category', async () => {
    render(<VendorRequirementsManager />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText('Legal Compliance')).toBeInTheDocument();
    });

    const categoryFilter = screen.getByDisplayValue('All categories');
    fireEvent.click(categoryFilter);

    const legalOption = screen.getByText('Legal');
    fireEvent.click(legalOption);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('category=LEGAL')
      );
    });
  });

  it('should search requirements', async () => {
    render(<VendorRequirementsManager />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText('Legal Compliance')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search requirements...');
    fireEvent.change(searchInput, { target: { value: 'legal' } });

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('search=legal')
      );
    });
  });

  it('should create new requirement', async () => {
    (global.fetch as any).mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockRequirements[0]),
      })
    );

    render(<VendorRequirementsManager />, { wrapper: createWrapper() });

    const createButton = screen.getByText('Create Requirement');
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(screen.getByText('Create New Vendor Requirement')).toBeInTheDocument();
    });

    // Fill form
    const nameInput = screen.getByLabelText('Name *');
    fireEvent.change(nameInput, { target: { value: 'Test Requirement' } });

    const categorySelect = screen.getByLabelText('Category *');
    fireEvent.click(categorySelect);
    fireEvent.click(screen.getByText('Legal'));

    const typeSelect = screen.getByLabelText('Type *');
    fireEvent.click(typeSelect);
    fireEvent.click(screen.getByText('Mandatory'));

    // Submit form
    const submitButton = screen.getByText('Create Requirement');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/admin/vendor-requirements',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('Test Requirement'),
        })
      );
    });
  });

  it('should handle API errors gracefully', async () => {
    (global.fetch as any).mockImplementationOnce(() =>
      Promise.resolve({
        ok: false,
        status: 500,
      })
    );

    render(<VendorRequirementsManager />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText('No vendor requirements found. Create your first requirement!')).toBeInTheDocument();
    });
  });

  it('should disable delete button for requirements in use', async () => {
    const requirementInUse = {
      ...mockRequirements[0],
      _count: {
        procurementRequirements: 5,
      },
    };

    (global.fetch as any).mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          requirements: [requirementInUse],
          pagination: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1,
          },
        }),
      })
    );

    render(<VendorRequirementsManager />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText('Legal Compliance')).toBeInTheDocument();
    });

    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(button => 
      button.querySelector('svg')?.getAttribute('data-testid') === 'trash-2-icon'
    );

    expect(deleteButton).toBeDisabled();
  });
});
