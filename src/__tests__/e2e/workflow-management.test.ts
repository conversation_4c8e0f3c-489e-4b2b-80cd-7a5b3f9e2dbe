import { test, expect } from '@playwright/test';

// Mock data setup
const mockAdmin = {
  email: '<EMAIL>',
  password: 'password123',
  name: 'Admin User',
};

const mockVendorRequirement = {
  name: 'Legal Compliance Test',
  description: 'Test legal compliance requirements',
  category: 'LEGAL',
  type: 'MANDATORY',
};

const mockScheduleTemplate = {
  name: 'Standard Procurement Schedule',
  description: 'Standard procurement timeline template',
};

test.describe('Workflow Management E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/login', async (route: any) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: mockAdmin,
          token: 'mock-jwt-token',
        }),
      });
    });

    // Mock workflow stats API
    await page.route('**/api/admin/dashboard/workflow-stats', async (route: any) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          totalWorkflows: 5,
          activeWorkflows: 3,
          totalTemplates: 8,
          activeTemplates: 6,
          totalRequirements: 12,
          activeRequirements: 10,
          totalSchedules: 4,
          activeSchedules: 3,
          avgProcessingTime: 7,
          completionRate: 85,
          overdueCount: 2,
          upcomingDeadlines: 5,
        }),
      });
    });

    // Mock vendor requirements API
    await page.route('**/api/admin/vendor-requirements*', async (route: any) => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            requirements: [
              {
                id: 'req-1',
                name: 'Legal Compliance',
                description: 'Legal compliance requirements',
                category: 'LEGAL',
                type: 'MANDATORY',
                isActive: true,
                creator: { id: 'user-1', name: 'Admin User', email: '<EMAIL>' },
                template: { id: 'template-1', name: 'Standard Procurement', type: 'GOODS' },
                _count: { procurementRequirements: 0 },
              },
            ],
            pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
          }),
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'req-new',
            ...mockVendorRequirement,
            isActive: true,
            creator: mockAdmin,
            _count: { procurementRequirements: 0 },
          }),
        });
      }
    });

    // Mock workflow templates API
    await page.route('**/api/admin/procurement-workflow-templates*', async (route: any) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          templates: [
            {
              id: 'template-1',
              name: 'Standard Procurement',
              type: 'GOODS',
              category: 'STANDARD',
            },
          ],
        }),
      });
    });

    // Mock schedule templates API
    await page.route('**/api/admin/procurement-schedule-templates*', async (route: any) => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            scheduleTemplates: [
              {
                id: 'schedule-1',
                name: 'Standard Schedule',
                description: 'Standard procurement schedule',
                isActive: true,
                creator: mockAdmin,
                template: { id: 'template-1', name: 'Standard Procurement', type: 'GOODS' },
                _count: { procurementSchedules: 0 },
                stages: [],
                milestones: [],
              },
            ],
            pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
          }),
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'schedule-new',
            ...mockScheduleTemplate,
            isActive: true,
            creator: mockAdmin,
            _count: { procurementSchedules: 0 },
          }),
        });
      }
    });

    // Login
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', mockAdmin.email);
    await page.fill('[data-testid="password-input"]', mockAdmin.password);
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('/dashboard');
  });

  test('should navigate to workflow management page', async ({ page }) => {
    await page.goto('/admin/workflows');
    
    await expect(page.locator('h1')).toContainText('Workflow Management');
    await expect(page.locator('text=Comprehensive procurement workflow management system')).toBeVisible();
    
    // Check tabs are present
    await expect(page.locator('[role="tab"]:has-text("Dashboard")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Requirements")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Schedules")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Builder")')).toBeVisible();
  });

  test('should display workflow dashboard with metrics', async ({ page }) => {
    await page.goto('/admin/workflows');
    
    // Dashboard should be the default tab
    await expect(page.locator('text=Total Workflows')).toBeVisible();
    await expect(page.locator('text=5')).toBeVisible(); // Total workflows count
    await expect(page.locator('text=3 active')).toBeVisible(); // Active workflows
    
    await expect(page.locator('text=Templates')).toBeVisible();
    await expect(page.locator('text=8')).toBeVisible(); // Total templates
    
    await expect(page.locator('text=Avg Processing Time')).toBeVisible();
    await expect(page.locator('text=7d')).toBeVisible(); // Processing time
    
    await expect(page.locator('text=Completion Rate')).toBeVisible();
    await expect(page.locator('text=85%')).toBeVisible(); // Completion rate
  });

  test('should manage vendor requirements', async ({ page }) => {
    await page.goto('/admin/workflows');
    
    // Navigate to requirements tab
    await page.click('[role="tab"]:has-text("Requirements")');
    
    await expect(page.locator('text=Vendor Requirements Management')).toBeVisible();
    await expect(page.locator('text=Legal Compliance')).toBeVisible();
    
    // Test creating new requirement
    await page.click('text=Create Requirement');
    await expect(page.locator('text=Create New Vendor Requirement')).toBeVisible();
    
    // Fill form
    await page.fill('[id="name"]', mockVendorRequirement.name);
    await page.fill('[id="description"]', mockVendorRequirement.description);
    
    // Select category
    await page.click('[data-testid="category-select"]');
    await page.click('text=Legal');
    
    // Select type
    await page.click('[data-testid="type-select"]');
    await page.click('text=Mandatory');
    
    // Submit form
    await page.click('text=Create Requirement');
    
    // Verify success (would show in real implementation)
    await expect(page.locator('text=Legal Compliance Test')).toBeVisible();
  });

  test('should manage schedule templates', async ({ page }) => {
    await page.goto('/admin/workflows');
    
    // Navigate to schedules tab
    await page.click('[role="tab"]:has-text("Schedules")');
    
    await expect(page.locator('text=Procurement Schedule Management')).toBeVisible();
    await expect(page.locator('text=Standard Schedule')).toBeVisible();
    
    // Test creating new schedule template
    await page.click('text=Create Schedule Template');
    await expect(page.locator('text=Create New Schedule Template')).toBeVisible();
    
    // Fill basic information
    await page.fill('[id="name"]', mockScheduleTemplate.name);
    await page.fill('[id="description"]', mockScheduleTemplate.description);
    
    // Select workflow template
    await page.click('[data-testid="template-select"]');
    await page.click('text=Standard Procurement (GOODS)');
    
    // Submit form
    await page.click('text=Create Template');
    
    // Verify success
    await expect(page.locator('text=Standard Procurement Schedule')).toBeVisible();
  });

  test('should filter and search requirements', async ({ page }) => {
    await page.goto('/admin/workflows');
    await page.click('[role="tab"]:has-text("Requirements")');
    
    // Test search functionality
    await page.fill('[placeholder="Search requirements..."]', 'legal');
    await page.waitForTimeout(500); // Debounce delay
    
    // Test category filter
    await page.click('[data-testid="category-filter"]');
    await page.click('text=Legal');
    
    // Test type filter
    await page.click('[data-testid="type-filter"]');
    await page.click('text=Mandatory');
    
    // Verify filtered results
    await expect(page.locator('text=Legal Compliance')).toBeVisible();
  });

  test('should access workflow builder', async ({ page }) => {
    await page.goto('/admin/workflows');
    
    // Navigate to builder tab
    await page.click('[role="tab"]:has-text("Builder")');
    
    await expect(page.locator('text=Workflow Builder')).toBeVisible();
    await expect(page.locator('text=Design and customize procurement workflows with drag-and-drop interface')).toBeVisible();
    
    // Check if workflow builder component is rendered
    await expect(page.locator('[data-testid="workflow-builder"]')).toBeVisible();
  });

  test('should navigate from admin dashboard to workflow management', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Check workflow overview widget
    await expect(page.locator('text=Workflow Overview')).toBeVisible();
    await expect(page.locator('text=Total Workflows')).toBeVisible();
    
    // Click on workflow management quick action
    await page.click('text=Requirements');
    
    await expect(page).toHaveURL('/admin/workflows?tab=requirements');
    await expect(page.locator('text=Vendor Requirements Management')).toBeVisible();
  });

  test('should handle workflow management errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/admin/vendor-requirements*', async (route: any) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' }),
      });
    });

    await page.goto('/admin/workflows');
    await page.click('[role="tab"]:has-text("Requirements")');
    
    // Should show empty state or error message
    await expect(page.locator('text=No vendor requirements found')).toBeVisible();
  });

  test('should show workflow integration in content management', async ({ page }) => {
    await page.goto('/admin/content');
    
    // Navigate to workflows tab in content management
    await page.click('[role="tab"]:has-text("Workflows")');
    
    await expect(page.locator('text=Workflow Management Integration')).toBeVisible();
    await expect(page.locator('text=Quick Access')).toBeVisible();
    
    // Test quick access links
    await expect(page.locator('text=Workflow Dashboard')).toBeVisible();
    await expect(page.locator('text=Vendor Requirements')).toBeVisible();
    await expect(page.locator('text=Schedule Templates')).toBeVisible();
    await expect(page.locator('text=Workflow Builder')).toBeVisible();
    
    // Click on a quick access link
    await page.click('text=Workflow Dashboard');
    await expect(page).toHaveURL('/admin/workflows?tab=dashboard');
  });
});
