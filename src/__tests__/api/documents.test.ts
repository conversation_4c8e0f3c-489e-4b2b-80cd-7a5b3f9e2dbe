import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET, POST } from '@/app/api/documents/route';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/lib/documents/document-service');

const mockGetCurrentUser = jest.fn();
const mockSearchDocuments = jest.fn();
const mockUploadDocument = jest.fn();

// Setup mocks
beforeEach(() => {
  jest.clearAllMocks();
  
  require('@/lib/auth').getCurrentUser = mockGetCurrentUser;
  require('@/lib/documents/document-service').documentService = {
    searchDocuments: mockSearchDocuments,
    uploadDocument: mockUploadDocument,
  };
});

describe('/api/documents', () => {
  describe('GET', () => {
    it('should return documents for authenticated user', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      const mockDocuments = {
        documents: [
          {
            id: 'doc-1',
            fileName: 'test.pdf',
            documentType: 'CONTRACT',
            uploadedAt: new Date(),
          },
        ],
        pagination: {
          total: 1,
          limit: 50,
          offset: 0,
        },
      };

      mockGetCurrentUser.mockResolvedValue(mockUser);
      mockSearchDocuments.mockResolvedValue(mockDocuments);

      const request = new NextRequest('http://localhost/api/documents');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.documents).toHaveLength(1);
      expect(data.pagination).toBeDefined();
      expect(mockSearchDocuments).toHaveBeenCalledWith('user-1', expect.any(Object));
    });

    it('should return 401 for unauthenticated user', async () => {
      mockGetCurrentUser.mockRejectedValue(new Error('Unauthorized'));

      const request = new NextRequest('http://localhost/api/documents');
      const response = await GET(request);

      expect(response.status).toBe(500); // Error handling converts to 500
    });

    it('should handle search parameters', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      const mockDocuments = { documents: [], pagination: { total: 0, limit: 10, offset: 0 } };

      mockGetCurrentUser.mockResolvedValue(mockUser);
      mockSearchDocuments.mockResolvedValue(mockDocuments);

      const url = 'http://localhost/api/documents?query=contract&documentType=CONTRACT&limit=10';
      const request = new NextRequest(url);
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(mockSearchDocuments).toHaveBeenCalledWith('user-1', {
        query: 'contract',
        documentType: 'CONTRACT',
        status: '',
        procurementId: '',
        sortBy: 'uploadedAt',
        sortOrder: 'desc',
        limit: 10,
        offset: 0,
        includeStats: true,
      });
    });
  });

  describe('POST', () => {
    it('should upload documents successfully', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      const mockUploadedDoc = {
        id: 'doc-1',
        fileName: 'test.pdf',
        documentType: 'CONTRACT',
        uploadedAt: new Date(),
      };

      mockGetCurrentUser.mockResolvedValue(mockUser);
      mockUploadDocument.mockResolvedValue(mockUploadedDoc);

      // Create mock FormData
      const formData = new FormData();
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      formData.append('files', file);
      formData.append('documentType', 'CONTRACT');
      formData.append('description', 'Test document');
      formData.append('tags', JSON.stringify(['test']));
      formData.append('isConfidential', 'false');
      formData.append('requiresApproval', 'true');

      const request = new NextRequest('http://localhost/api/documents', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.documents).toHaveLength(1);
      expect(mockUploadDocument).toHaveBeenCalledWith(
        'user-1',
        expect.any(Object),
        expect.objectContaining({
          documentType: 'CONTRACT',
          description: 'Test document',
          tags: ['test'],
          isConfidential: false,
          requiresApproval: true,
        })
      );
    });

    it('should return 400 for missing files', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      mockGetCurrentUser.mockResolvedValue(mockUser);

      const formData = new FormData();
      formData.append('documentType', 'CONTRACT');

      const request = new NextRequest('http://localhost/api/documents', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('No files provided');
    });

    it('should return 400 for missing document type', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      mockGetCurrentUser.mockResolvedValue(mockUser);

      const formData = new FormData();
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      formData.append('files', file);

      const request = new NextRequest('http://localhost/api/documents', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Document type is required');
    });

    it('should handle upload errors', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      mockGetCurrentUser.mockResolvedValue(mockUser);
      mockUploadDocument.mockRejectedValue(new Error('Upload failed'));

      const formData = new FormData();
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      formData.append('files', file);
      formData.append('documentType', 'CONTRACT');

      const request = new NextRequest('http://localhost/api/documents', {
        method: 'POST',
        body: formData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to upload documents');
    });
  });
});

describe('/api/documents/[id]', () => {
  const { GET: getDocument, PUT: updateDocument, DELETE: deleteDocument } = 
    require('@/app/api/documents/[id]/route');

  describe('GET', () => {
    it('should return document details', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      const mockDocument = {
        id: 'doc-1',
        fileName: 'test.pdf',
        documentType: 'CONTRACT',
        uploadedAt: new Date(),
      };

      mockGetCurrentUser.mockResolvedValue(mockUser);
      require('@/lib/documents/document-manager').documentManager = {
        getDocument: jest.fn().mockResolvedValue(mockDocument),
      };

      const request = new NextRequest('http://localhost/api/documents/doc-1');
      const response = await getDocument(request, { params: { id: 'doc-1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe('doc-1');
    });

    it('should return 404 for non-existent document', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      mockGetCurrentUser.mockResolvedValue(mockUser);
      
      require('@/lib/documents/document-manager').documentManager = {
        getDocument: jest.fn().mockRejectedValue(new Error('Document not found')),
      };

      const request = new NextRequest('http://localhost/api/documents/non-existent');
      const response = await getDocument(request, { params: { id: 'non-existent' } });

      expect(response.status).toBe(404);
    });
  });

  describe('PUT', () => {
    it('should update document successfully', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      mockGetCurrentUser.mockResolvedValue(mockUser);

      const updateData = {
        description: 'Updated description',
        tags: ['updated', 'test'],
        isConfidential: true,
        requiresApproval: false,
      };

      const request = new NextRequest('http://localhost/api/documents/doc-1', {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await updateDocument(request, { params: { id: 'doc-1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });

  describe('DELETE', () => {
    it('should delete document successfully', async () => {
      const mockUser = { id: 'user-1', name: 'Test User' };
      mockGetCurrentUser.mockResolvedValue(mockUser);

      const request = new NextRequest('http://localhost/api/documents/doc-1', {
        method: 'DELETE',
      });

      const response = await deleteDocument(request, { params: { id: 'doc-1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });
});

describe('/api/documents/generate', () => {
  const { POST: generateDocument } = require('@/app/api/documents/generate/route');

  it('should generate document from template', async () => {
    const mockUser = { id: 'user-1', name: 'Test User' };
    const mockGeneratedDoc = {
      id: 'generated-doc-1',
      name: 'Generated Contract',
      content: 'Generated content',
    };

    mockGetCurrentUser.mockResolvedValue(mockUser);
    require('@/lib/documents/document-service').documentService = {
      generateDocument: jest.fn().mockResolvedValue(mockGeneratedDoc),
    };

    const generateData = {
      templateId: 'template-1',
      name: 'Generated Contract',
      data: {
        contractNumber: 'CONT-001',
        vendor: { name: 'Test Vendor' },
      },
      entityType: 'PROCUREMENT',
      entityId: 'proc-1',
      generatePdf: true,
    };

    const request = new NextRequest('http://localhost/api/documents/generate', {
      method: 'POST',
      body: JSON.stringify(generateData),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await generateDocument(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.document).toBeDefined();
  });

  it('should return 400 for missing required fields', async () => {
    const mockUser = { id: 'user-1', name: 'Test User' };
    mockGetCurrentUser.mockResolvedValue(mockUser);

    const incompleteData = {
      templateId: 'template-1',
      // Missing name and data
    };

    const request = new NextRequest('http://localhost/api/documents/generate', {
      method: 'POST',
      body: JSON.stringify(incompleteData),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await generateDocument(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toContain('Missing required fields');
  });
});
