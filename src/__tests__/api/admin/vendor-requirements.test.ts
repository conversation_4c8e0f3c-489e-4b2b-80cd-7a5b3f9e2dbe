import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import { GET, POST } from '@/app/api/admin/vendor-requirements/route';
import { prisma } from '@/lib/db';
import { getCurrentUser } from '@/lib/auth';

// Mock dependencies
vi.mock('@/lib/db', () => ({
  prisma: {
    vendorRequirementTemplate: {
      findMany: vi.fn(),
      count: vi.fn(),
      create: vi.fn(),
    },
    procurementWorkflowTemplate: {
      findUnique: vi.fn(),
    },
  },
}));

vi.mock('@/lib/auth', () => ({
  getCurrentUser: vi.fn(),
}));

const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  name: 'Admin User',
  roles: ['ADMIN'],
};

const mockVendorRequirement = {
  id: 'req-1',
  name: 'Legal Compliance',
  description: 'Legal compliance requirements',
  category: 'LEGAL',
  type: 'MANDATORY',
  templateId: 'template-1',
  weight: null,
  criteria: {
    description: 'Legal compliance criteria',
    requirements: [
      {
        item: 'Business License',
        description: 'Valid business license',
        required: true,
      },
    ],
    evaluation: {
      method: 'PASS_FAIL',
    },
  },
  requiredDocuments: [
    {
      name: 'Business License',
      type: 'PDF',
      required: true,
      format: ['pdf'],
      maxSize: 5,
    },
  ],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  creator: {
    id: 'user-1',
    name: 'Admin User',
    email: '<EMAIL>',
  },
  template: {
    id: 'template-1',
    name: 'Standard Procurement',
    type: 'GOODS',
    category: 'STANDARD',
  },
  _count: {
    procurementRequirements: 0,
  },
};

describe('/api/admin/vendor-requirements', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getCurrentUser as any).mockResolvedValue(mockUser);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('GET', () => {
    it('should fetch vendor requirements successfully', async () => {
      const mockRequirements = [mockVendorRequirement];
      (prisma.vendorRequirementTemplate.findMany as any).mockResolvedValue(mockRequirements);
      (prisma.vendorRequirementTemplate.count as any).mockResolvedValue(1);

      const request = new NextRequest('http://localhost/api/admin/vendor-requirements');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.requirements).toEqual(mockRequirements);
      expect(data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      });
    });

    it('should filter by category', async () => {
      const mockRequirements = [mockVendorRequirement];
      (prisma.vendorRequirementTemplate.findMany as any).mockResolvedValue(mockRequirements);
      (prisma.vendorRequirementTemplate.count as any).mockResolvedValue(1);

      const request = new NextRequest('http://localhost/api/admin/vendor-requirements?category=LEGAL');
      await GET(request);

      expect(prisma.vendorRequirementTemplate.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { category: 'LEGAL' },
        })
      );
    });

    it('should filter by search term', async () => {
      const mockRequirements = [mockVendorRequirement];
      (prisma.vendorRequirementTemplate.findMany as any).mockResolvedValue(mockRequirements);
      (prisma.vendorRequirementTemplate.count as any).mockResolvedValue(1);

      const request = new NextRequest('http://localhost/api/admin/vendor-requirements?search=legal');
      await GET(request);

      expect(prisma.vendorRequirementTemplate.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            OR: [
              { name: { contains: 'legal', mode: 'insensitive' } },
              { description: { contains: 'legal', mode: 'insensitive' } },
            ],
          },
        })
      );
    });

    it('should return 401 if user is not authenticated', async () => {
      (getCurrentUser as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/admin/vendor-requirements');
      const response = await GET(request);

      expect(response.status).toBe(401);
    });
  });

  describe('POST', () => {
    const validRequirementData = {
      name: 'Technical Requirements',
      description: 'Technical compliance requirements',
      category: 'TECHNICAL',
      type: 'MANDATORY',
      templateId: 'template-1',
      criteria: {
        description: 'Technical criteria',
        requirements: [
          {
            item: 'ISO Certification',
            description: 'Valid ISO certification',
            required: true,
          },
        ],
        evaluation: {
          method: 'PASS_FAIL',
        },
      },
      requiredDocuments: [
        {
          name: 'ISO Certificate',
          type: 'PDF',
          required: true,
          format: ['pdf'],
          maxSize: 5,
        },
      ],
    };

    it('should create vendor requirement successfully', async () => {
      (prisma.procurementWorkflowTemplate.findUnique as any).mockResolvedValue({
        id: 'template-1',
        name: 'Standard Procurement',
      });
      (prisma.vendorRequirementTemplate.create as any).mockResolvedValue(mockVendorRequirement);

      const request = new NextRequest('http://localhost/api/admin/vendor-requirements', {
        method: 'POST',
        body: JSON.stringify(validRequirementData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual(mockVendorRequirement);
      expect(prisma.vendorRequirementTemplate.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            ...validRequirementData,
            creatorId: mockUser.id,
          }),
        })
      );
    });

    it('should return 400 for invalid data', async () => {
      const invalidData = {
        name: '', // Invalid: empty name
        category: 'INVALID_CATEGORY',
      };

      const request = new NextRequest('http://localhost/api/admin/vendor-requirements', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it('should return 404 if template not found', async () => {
      (prisma.procurementWorkflowTemplate.findUnique as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/admin/vendor-requirements', {
        method: 'POST',
        body: JSON.stringify(validRequirementData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);

      expect(response.status).toBe(404);
    });

    it('should return 401 if user is not authenticated', async () => {
      (getCurrentUser as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/admin/vendor-requirements', {
        method: 'POST',
        body: JSON.stringify(validRequirementData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);

      expect(response.status).toBe(401);
    });
  });
});
