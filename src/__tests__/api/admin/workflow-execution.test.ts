import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import { GET, POST, PUT } from '@/app/api/admin/workflow-execution/route';
import { prisma } from '@/lib/db';
import { getCurrentUser } from '@/lib/auth';

// Mock dependencies
vi.mock('@/lib/db', () => ({
  prisma: {
    approvalInstance: {
      findMany: vi.fn(),
      count: vi.fn(),
      create: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
    },
    approvalWorkflow: {
      findUnique: vi.fn(),
    },
    approvalStep: {
      create: vi.fn(),
    },
  },
}));

vi.mock('@/lib/auth', () => ({
  getCurrentUser: vi.fn(),
}));

const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  name: 'Admin User',
  roles: ['ADMIN'],
};

const mockWorkflow = {
  id: 'workflow-1',
  name: 'Procurement Approval',
  entityType: 'PROCUREMENT',
  isActive: true,
  stages: [
    {
      id: 'stage-1',
      name: 'Initial Review',
      order: 1,
      approverId: 'approver-1',
      timeoutHours: 24,
    },
    {
      id: 'stage-2',
      name: 'Final Approval',
      order: 2,
      approverId: 'approver-2',
      timeoutHours: 48,
    },
  ],
};

const mockInstance = {
  id: 'instance-1',
  workflowId: 'workflow-1',
  entityId: 'procurement-1',
  entityType: 'PROCUREMENT',
  status: 'PENDING',
  priority: 'MEDIUM',
  dueDate: new Date('2024-12-31'),
  metadata: {},
  createdAt: new Date(),
  createdById: 'user-1',
  workflow: {
    id: 'workflow-1',
    name: 'Procurement Approval',
    entityType: 'PROCUREMENT',
    stages: mockWorkflow.stages,
  },
  createdBy: {
    id: 'user-1',
    name: 'Admin User',
    email: '<EMAIL>',
  },
  steps: [
    {
      id: 'step-1',
      instanceId: 'instance-1',
      stageId: 'stage-1',
      stepOrder: 1,
      status: 'PENDING',
      approverId: 'approver-1',
      approver: {
        id: 'approver-1',
        name: 'Approver One',
        email: '<EMAIL>',
      },
    },
    {
      id: 'step-2',
      instanceId: 'instance-1',
      stageId: 'stage-2',
      stepOrder: 2,
      status: 'WAITING',
      approverId: 'approver-2',
      approver: {
        id: 'approver-2',
        name: 'Approver Two',
        email: '<EMAIL>',
      },
    },
  ],
};

describe('/api/admin/workflow-execution', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getCurrentUser as any).mockResolvedValue(mockUser);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('GET', () => {
    it('should fetch workflow instances successfully', async () => {
      const mockInstances = [mockInstance];
      (prisma.approvalInstance.findMany as any).mockResolvedValue(mockInstances);
      (prisma.approvalInstance.count as any).mockResolvedValue(1);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.instances).toEqual(mockInstances);
      expect(data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      });
    });

    it('should filter by status', async () => {
      const mockInstances = [mockInstance];
      (prisma.approvalInstance.findMany as any).mockResolvedValue(mockInstances);
      (prisma.approvalInstance.count as any).mockResolvedValue(1);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution?status=PENDING');
      await GET(request);

      expect(prisma.approvalInstance.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { status: 'PENDING' },
        })
      );
    });

    it('should return 401 if user is not authenticated', async () => {
      (getCurrentUser as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution');
      const response = await GET(request);

      expect(response.status).toBe(401);
    });
  });

  describe('POST', () => {
    const validExecutionData = {
      workflowId: 'workflow-1',
      entityId: 'procurement-1',
      entityType: 'PROCUREMENT',
      priority: 'HIGH',
      dueDate: '2024-12-31T23:59:59Z',
      metadata: { source: 'admin' },
    };

    it('should execute workflow successfully', async () => {
      (prisma.approvalWorkflow.findUnique as any).mockResolvedValue(mockWorkflow);
      (prisma.approvalInstance.create as any).mockResolvedValue({
        id: 'instance-1',
        ...validExecutionData,
        status: 'PENDING',
        createdById: mockUser.id,
      });
      (prisma.approvalStep.create as any)
        .mockResolvedValueOnce({ id: 'step-1' })
        .mockResolvedValueOnce({ id: 'step-2' });
      (prisma.approvalInstance.findUnique as any).mockResolvedValue(mockInstance);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution', {
        method: 'POST',
        body: JSON.stringify(validExecutionData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual(mockInstance);
      expect(prisma.approvalInstance.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            workflowId: validExecutionData.workflowId,
            entityId: validExecutionData.entityId,
            entityType: validExecutionData.entityType,
            status: 'PENDING',
            priority: validExecutionData.priority,
            createdById: mockUser.id,
          }),
        })
      );
    });

    it('should return 404 if workflow not found', async () => {
      (prisma.approvalWorkflow.findUnique as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution', {
        method: 'POST',
        body: JSON.stringify(validExecutionData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);

      expect(response.status).toBe(404);
    });

    it('should return 400 if workflow is not active', async () => {
      (prisma.approvalWorkflow.findUnique as any).mockResolvedValue({
        ...mockWorkflow,
        isActive: false,
      });

      const request = new NextRequest('http://localhost/api/admin/workflow-execution', {
        method: 'POST',
        body: JSON.stringify(validExecutionData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it('should return 400 for invalid data', async () => {
      const invalidData = {
        workflowId: '', // Invalid: empty workflow ID
        entityId: 'procurement-1',
        entityType: 'PROCUREMENT',
      };

      const request = new NextRequest('http://localhost/api/admin/workflow-execution', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it('should return 401 if user is not authenticated', async () => {
      (getCurrentUser as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution', {
        method: 'POST',
        body: JSON.stringify(validExecutionData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);

      expect(response.status).toBe(401);
    });
  });

  describe('PUT', () => {
    const validUpdateData = {
      status: 'COMPLETED',
      comment: 'Workflow completed successfully',
      metadata: { completedBy: 'admin' },
    };

    it('should update workflow status successfully', async () => {
      const updatedInstance = {
        ...mockInstance,
        status: 'COMPLETED',
        completedAt: new Date(),
      };

      (prisma.approvalInstance.findUnique as any).mockResolvedValue(mockInstance);
      (prisma.approvalInstance.update as any).mockResolvedValue(updatedInstance);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution?instanceId=instance-1', {
        method: 'PUT',
        body: JSON.stringify(validUpdateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(updatedInstance);
      expect(prisma.approvalInstance.update).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: 'instance-1' },
          data: expect.objectContaining({
            status: 'COMPLETED',
            completedAt: expect.any(Date),
          }),
        })
      );
    });

    it('should return 400 if instanceId is missing', async () => {
      const request = new NextRequest('http://localhost/api/admin/workflow-execution', {
        method: 'PUT',
        body: JSON.stringify(validUpdateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await PUT(request);

      expect(response.status).toBe(400);
    });

    it('should return 404 if instance not found', async () => {
      (prisma.approvalInstance.findUnique as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution?instanceId=nonexistent', {
        method: 'PUT',
        body: JSON.stringify(validUpdateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await PUT(request);

      expect(response.status).toBe(404);
    });

    it('should return 401 if user is not authenticated', async () => {
      (getCurrentUser as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/admin/workflow-execution?instanceId=instance-1', {
        method: 'PUT',
        body: JSON.stringify(validUpdateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await PUT(request);

      expect(response.status).toBe(401);
    });
  });
});
