// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// -----------------------------
// ENUMS
// -----------------------------

/// Defines the roles a user can have in the system.
enum UserRoleType {
  ADMIN
  PROCUREMENT_USER /// User Pengadaan / Panitia
  APPROVER
  VENDOR
}

/// Status for vendor company profile verification.
enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
}

/// The main type of procurement event.
enum ProcurementType {
  TENDER
  RFQ
}

/// Overall status of the procurement lifecycle.
enum ProcurementStatus {
  DRAFT
  PUBLISHED
  AANWIJZING
  SUBMISSION
  EVALUATION
  NEGOTIATION
  WINNER_ANNOUNCEMENT
  AWARDED
  COMPLETED
  CANCELED
}

/// Status for individual stages within a procurement.
enum StageStatus {
  PENDING
  ONGOING
  COMPLETED
}

/// Status of a vendor's offer throughout the evaluation process.
enum OfferStatus {
  DRAFT
  SUBMITTED
  WITHDRAWN
  EVALUATING_ADMIN
  PASSED_ADMIN
  FAILED_ADMIN
  EVALUATING_TECH
  PASSED_TECH
  FAILED_TECH
  EVALUATING_PRICE
  PASSED_PRICE
  FAILED_PRICE
  NEGOTIATING
  WINNER
  LOSER
  BACKUP_1 /// Cadangan 1
  BACKUP_2 /// Cadangan 2
}

/// Status for any approval workflow step.
enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

// -----------------------------
// CORE MODELS: USERS & VENDORS
// -----------------------------

model User {
  id                      String   @id @default(cuid())
  email                   String   @unique
  name                    String
  password                String
  roles                   UserRoleType[] // Legacy field, will be migrated to UserRole
  notificationPreferences Json?    // Email notification preferences
  isActive                Boolean  @default(true)
  lastLoginAt             DateTime?
  failedLoginAttempts     Int      @default(0)
  lockedUntil             DateTime?
  twoFactorEnabled        Boolean  @default(false)
  twoFactorSecret         String?
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  vendor               Vendor?
  committeeAssignments ProcurementCommitteeMember[]
  approvals            Approval[]
  blacklistEntries     BlacklistEntry[]
  notifications        Notification[]

  // Document template relations
  createdTemplates     DocumentTemplate[]        @relation("TemplateCreator")
  updatedTemplates     DocumentTemplate[]        @relation("TemplateUpdater")
  approvedTemplates    DocumentTemplate[]        @relation("TemplateApprover")
  templateVersions     DocumentTemplateVersion[]
  generatedDocuments   GeneratedDocument[]

  // RBAC relations
  userRoles            UserRole[]
  assignedRoles        UserRole[]                @relation("UserRoleAssigner")
  resourcePermissions  ResourcePermission[]      @relation("UserResourcePermissions")
  grantedPermissions   ResourcePermission[]      @relation("ResourcePermissionGranter")

  // Audit trail relations
  auditLogs            AuditLog[]
  securityEvents       SecurityEvent[]       @relation("SecurityEventUser")
  resolvedSecurityEvents SecurityEvent[]     @relation("SecurityEventResolver")

  // Tax management relations
  createdTaxRates      TaxRate[]             @relation("TaxRateCreator")
  approvedTaxRates     TaxRate[]             @relation("TaxRateApprover")
  createdTaxExemptions TaxExemption[]        @relation("TaxExemptionCreator")
  approvedTaxExemptions TaxExemption[]       @relation("TaxExemptionApprover")

  // Vendor requirement relations
  createdVendorRequirementTemplates VendorRequirementTemplate[] @relation("VendorRequirementTemplateCreator")
  updatedVendorRequirementTemplates VendorRequirementTemplate[] @relation("VendorRequirementTemplateUpdater")
  reviewedVendorRequirements        VendorRequirementSubmission[] @relation("VendorRequirementReviewer")

  // Password reset relations
  passwordResetTokens               PasswordResetToken[]

  // Evaluation relations
  offerEvaluations                  OfferEvaluation[]

  // Content management relations
  newsArticles                      NewsArticle[]
  contentApprovalsRequested         ContentApproval[] @relation("ContentApprovalRequester")
  contentApprovalsReviewed          ContentApproval[] @relation("ContentApprovalReviewer")

  // Approval workflow relations
  createdWorkflows                  ApprovalWorkflow[] @relation("WorkflowCreator")
  initiatedApprovals                ApprovalInstance[] @relation("ApprovalInitiator")
  approvalActions                   ApprovalAction[] @relation("ApprovalActionPerformer")
  delegatedApprovals                ApprovalAction[] @relation("ApprovalActionDelegate")
  approvalComments                  ApprovalComment[] @relation("ApprovalCommentAuthor")

  // Post-award workflow relations
  createdPOs                        PurchaseOrder[] @relation("POCreator")
  approvedPOs                       PurchaseOrder[] @relation("POApprover")
  createdDeliveries                 Delivery[] @relation("DeliveryCreator")
  inspectedDeliveries               Delivery[] @relation("DeliveryInspector")
  createdGRNs                       GoodsReceiptNote[] @relation("GRNCreator")
  approvedGRNs                      GoodsReceiptNote[] @relation("GRNApprover")
  submittedInvoices                 Invoice[] @relation("InvoiceSubmitter")
  approvedInvoices                  Invoice[] @relation("InvoiceApprover")

  // Discussion & negotiation relations
  createdDiscussions                ProcurementDiscussion[] @relation("DiscussionCreator")
  discussionParticipations          DiscussionParticipant[] @relation("DiscussionParticipants")
  discussionMessages                DiscussionMessage[] @relation("MessageAuthor")
  messageReactions                  MessageReaction[] @relation("MessageReactions")
  uploadedAttachments               DiscussionAttachment[] @relation("AttachmentUploader")
  uploadedMessageAttachments        MessageAttachment[] @relation("MessageAttachmentUploader")
  initiatedNegotiations             NegotiationSession[] @relation("NegotiationInitiator")
  proposedRounds                    NegotiationRound[] @relation("RoundProposer")
  respondedRounds                   NegotiationRound[] @relation("RoundResponder")

  // KPI & Performance relations
  createdKpiTemplates               VendorKpiTemplate[] @relation("KpiTemplateCreator")
  kpiEvaluations                    VendorKpiEvaluation[] @relation("KpiEvaluator")
  approvedKpiEvaluations            VendorKpiEvaluation[] @relation("KpiEvaluationApprover")
  recordedMetrics                   VendorKpiMetric[] @relation("MetricRecorder")
  createdBlacklists                 BlacklistEntry[] @relation("BlacklistCreator")
  appealDecisions                   BlacklistEntry[] @relation("AppealDecider")
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token     String   @unique
  expiresAt DateTime
  used      Boolean  @default(false)
  usedAt    DateTime?
  createdAt DateTime @default(now())

  @@index([token])
  @@index([userId])
  @@index([expiresAt])
}

model Vendor {
  id                 String             @id @default(cuid())
  userId             String             @unique
  user               User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  companyName        String
  npwpNumber         String             @unique
  address            String
  npwpAddress        String
  phone              String
  picName            String
  picEmail           String
  picPhone           String
  verificationStatus VerificationStatus @default(PENDING)
  verifiedAt         DateTime?
  rejectionReason    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  documents        Document[]
  offers           VendorOffer[]
  kpis             VendorKpi[]
  purchaseOrders   PurchaseOrder[]
  blacklistEntries BlacklistEntry[]
  negotiations     NegotiationSession[]

  // KPI & Performance relations
  kpiEvaluations   VendorKpiEvaluation[]
  performanceHistory VendorPerformanceHistory[]
  kpiMetrics       VendorKpiMetric[]

  // Vendor requirements
  requirementTemplateId String?
  requirementTemplate   VendorRequirementTemplate? @relation(fields: [requirementTemplateId], references: [id])
  requirementSubmissions VendorRequirementSubmission[]

  @@index([verificationStatus])
  @@index([requirementTemplateId])
}



// -----------------------------
// PROCUREMENT FLOW MODELS
// -----------------------------

model Procurement {
  id                        String            @id @default(cuid())
  title                     String
  procurementNumber         String            @unique @default(cuid())
  type                      ProcurementType
  status                    ProcurementStatus @default(DRAFT)
  ownerEstimate             Float /// HPS (Harga Perkiraan Sendiri)
  showOwnerEstimateToVendor Boolean           @default(false)
  evaluationMethod          String /// e.g., "Sistem Nilai", "Pass/Fail"

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  stages    ProcurementStage[]
  items     ProcurementItem[]
  offers    VendorOffer[]
  committee ProcurementCommitteeMember[]
  po        PurchaseOrder?
  taxes     ProcurementTax[]

  @@index([status, type])
}

/// Represents a member of the procurement committee for a specific tender.
model ProcurementCommitteeMember {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  userId        String
  user          User        @relation(fields: [userId], references: [id], onDelete: Restrict)
  committeeRole String /// e.g., "Ketua", "Anggota"

  @@unique([procurementId, userId])
}

model ProcurementStage {
  id               String            @id @default(cuid())
  procurementId    String
  procurement      Procurement       @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  name             String /// e.g., "Undangan RFQ", "Pemasukan Penawaran"
  status           StageStatus       @default(PENDING)
  startDate        DateTime
  endDate          DateTime
  sequence         Int
  discussionThread DiscussionThread?

  @@unique([procurementId, sequence])
  @@index([procurementId])
}

model ProcurementItem {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  name          String
  description   String?
  quantity      Float
  unit          String
  ownerEstimate Float /// OE per item

  vendorOfferItems VendorOfferItem[]
  poItems          PurchaseOrderItem[]

  @@index([procurementId])
}

model VendorOffer {
  id                String      @id @default(cuid())
  procurementId     String
  procurement       Procurement @relation(fields: [procurementId], references: [id], onDelete: Restrict)
  vendorId          String
  vendor            Vendor      @relation(fields: [vendorId], references: [id], onDelete: Restrict)
  status            OfferStatus @default(DRAFT)
  offerNumber       String?
  totalOfferedPrice Float
  negotiatedPrice   Float?
  submissionDate    DateTime    @default(now())

  items       VendorOfferItem[]
  documents   Document[]
  taxes       OfferTax[]
  evaluations OfferEvaluation[]

  @@index([procurementId])
  @@index([vendorId])
  @@index([status])
}

model VendorOfferItem {
  id            String          @id @default(cuid())
  vendorOfferId String
  vendorOffer   VendorOffer     @relation(fields: [vendorOfferId], references: [id], onDelete: Cascade)
  itemId        String
  item          ProcurementItem @relation(fields: [itemId], references: [id], onDelete: Cascade)
  offeredPrice  Float

  @@unique([vendorOfferId, itemId])
}

model OfferEvaluation {
  id              String      @id @default(cuid())
  offerId         String
  offer           VendorOffer @relation(fields: [offerId], references: [id], onDelete: Cascade)
  evaluatedBy     String
  evaluatedByUser User        @relation(fields: [evaluatedBy], references: [id], onDelete: Restrict)
  technicalScore  Float       // 0-100
  commercialScore Float       // 0-100
  totalScore      Float       // Calculated weighted score
  notes           String?
  evaluatedAt     DateTime    @default(now())

  @@unique([offerId, evaluatedBy])
  @@index([offerId])
  @@index([evaluatedBy])
}

// -----------------------------
// POST-AWARD & FINANCIAL MODELS
// -----------------------------

model PurchaseOrder {
  id              String         @id @default(cuid())
  poNumber        String         @unique
  procurementId   String         @unique
  procurement     Procurement    @relation(fields: [procurementId], references: [id], onDelete: Restrict)
  vendorId        String
  vendor          Vendor         @relation(fields: [vendorId], references: [id], onDelete: Restrict)
  status          ApprovalStatus @default(PENDING)
  poDate          DateTime
  totalValue      Float
  deliveryAddress String
  termsOfPayment  String

  documents Document[]
  items     PurchaseOrderItem[]
  grn       GoodReceiptNote?
  invoices  Invoice[]
  basts     BAST[]
  approvals Approval[]

  @@index([vendorId, status])
}

model PurchaseOrderItem {
  id       String          @id @default(cuid())
  poId     String
  po       PurchaseOrder   @relation(fields: [poId], references: [id], onDelete: Cascade)
  itemId   String
  item     ProcurementItem @relation(fields: [itemId], references: [id], onDelete: Restrict)
  quantity Float
  price    Float

  receivedItems GoodReceiptItem[]
}

model GoodReceiptNote {
  id            String            @id @default(cuid())
  grnNumber     String            @unique
  poId          String            @unique
  po            PurchaseOrder     @relation(fields: [poId], references: [id], onDelete: Cascade)
  receivedDate  DateTime
  notes         String?
  receivedItems GoodReceiptItem[]
}

model GoodReceiptItem {
  id                  String            @id @default(cuid())
  grnId               String
  grn                 GoodReceiptNote   @relation(fields: [grnId], references: [id], onDelete: Cascade)
  purchaseOrderItemId String
  purchaseOrderItem   PurchaseOrderItem @relation(fields: [purchaseOrderItemId], references: [id], onDelete: Restrict)
  receivedQuantity    Float
  notes               String?

  @@unique([grnId, purchaseOrderItemId])
}

model Invoice {
  id            String         @id @default(cuid())
  invoiceNumber String         @unique
  poId          String
  po            PurchaseOrder  @relation(fields: [poId], references: [id], onDelete: Restrict)
  invoiceDate   DateTime
  amount        Float
  status        ApprovalStatus @default(PENDING)

  documents Document[]
  approvals Approval[]

  @@index([poId, status])
}

model BAST {
  id           String         @id @default(cuid())
  bastNumber   String         @unique
  poId         String
  po           PurchaseOrder  @relation(fields: [poId], references: [id], onDelete: Restrict)
  handoverDate DateTime
  status       ApprovalStatus @default(PENDING)

  documents Document[]
  checklist BastChecklistItem[]
  approvals Approval[]

  @@index([poId, status])
}

model BastChecklistItem {
  id       String  @id @default(cuid())
  bastId   String
  bast     BAST    @relation(fields: [bastId], references: [id], onDelete: Cascade)
  itemName String
  isOk     Boolean @default(true)
  notes    String?
}

// -----------------------------
// GENERIC & SUPPORTING MODELS
// -----------------------------

/// Polymorphic model for approval workflows.
model Approval {
  id          String         @id @default(cuid())
  sequence    Int
  status      ApprovalStatus @default(PENDING)
  approverId  String
  approver    User           @relation(fields: [approverId], references: [id], onDelete: Restrict)
  comments    String?
  processedAt DateTime?

  // Polymorphic relation fields
  poId          String?
  purchaseOrder PurchaseOrder? @relation(fields: [poId], references: [id], onDelete: Cascade)
  invoiceId     String?
  invoice       Invoice?       @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  bastId        String?
  bast          BAST?          @relation(fields: [bastId], references: [id], onDelete: Cascade)

  @@index([approverId, status])
}

model Document {
  id          String   @id @default(cuid())
  fileName    String
  fileUrl     String   @unique
  fileType    String /// Mime type
  description String?
  uploadedAt  DateTime @default(now())

  // Polymorphic relation fields
  vendorId      String?
  vendor        Vendor?        @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorOfferId String?
  vendorOffer   VendorOffer?   @relation(fields: [vendorOfferId], references: [id], onDelete: Cascade)
  poId          String?
  purchaseOrder PurchaseOrder? @relation(fields: [poId], references: [id], onDelete: Cascade)
  invoiceId     String?
  invoice       Invoice?       @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  bastId        String?
  bast          BAST?          @relation(fields: [bastId], references: [id], onDelete: Cascade)
}

model DiscussionThread {
  id                 String           @id @default(cuid())
  procurementStageId String           @unique
  procurementStage   ProcurementStage @relation(fields: [procurementStageId], references: [id], onDelete: Cascade)
  type               String /// "AANWIJZING", "NEGOTIATION", "SANGGAHAN"

  messages DiscussionMessage[]
}



model VendorKpi {
  id            String   @id @default(cuid())
  vendorId      String
  vendor        Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  procurementId String /// The procurement this KPI relates to
  period        String
  qualityScore  Int
  timeScore     Int
  costScore     Int
  serviceScore  Int
  overallScore  Float
  remarks       String?
  createdAt     DateTime @default(now())

  @@index([vendorId])
}

// -----------------------------
// DOCUMENT TEMPLATE MODELS
// -----------------------------

model DocumentTemplate {
  id          String                    @id @default(cuid())
  name        String
  description String?
  type        DocumentTemplateType
  category    String
  content     Json // Template content with components and layout
  variables   Json // Available variables/placeholders
  version     Int                       @default(1)
  status      DocumentTemplateStatus    @default(DRAFT)
  isActive    Boolean                   @default(false)
  createdBy   String
  updatedBy   String?
  approvedBy  String?
  approvedAt  DateTime?
  createdAt   DateTime                  @default(now())
  updatedAt   DateTime                  @updatedAt

  creator   User  @relation("TemplateCreator", fields: [createdBy], references: [id])
  updater   User? @relation("TemplateUpdater", fields: [updatedBy], references: [id])
  approver  User? @relation("TemplateApprover", fields: [approvedBy], references: [id])

  versions DocumentTemplateVersion[]
  usages   GeneratedDocument[]

  @@map("document_templates")
}

model DocumentTemplateVersion {
  id         String   @id @default(cuid())
  templateId String
  version    Int
  content    Json
  variables  Json
  changelog  String?
  createdBy  String
  createdAt  DateTime @default(now())

  template DocumentTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  creator  User             @relation(fields: [createdBy], references: [id])

  @@unique([templateId, version])
  @@map("document_template_versions")
}

model GeneratedDocument {
  id         String   @id @default(cuid())
  templateId String
  name       String
  data       Json // Data used to populate the template
  content    Json // Final rendered content
  pdfUrl     String?
  createdBy  String
  createdAt  DateTime @default(now())

  template DocumentTemplate @relation(fields: [templateId], references: [id])
  creator  User             @relation(fields: [createdBy], references: [id])

  @@map("generated_documents")
}

enum DocumentTemplateType {
  RFQ
  CONTRACT
  PURCHASE_ORDER
  INVOICE
  BAST
  AANWIJZING
  EVALUATION_REPORT
  AWARD_LETTER
  CUSTOM
}

enum DocumentTemplateStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  REJECTED
  ARCHIVED
}

// -----------------------------
// NOTIFICATION MODELS
// -----------------------------

model Notification {
  id        String            @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType  @default(INFO)
  read      Boolean           @default(false)
  metadata  Json?             // Additional data for the notification
  createdAt DateTime          @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
}

// -----------------------------
// CONTENT MANAGEMENT MODELS
// -----------------------------

model NewsCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  articles    NewsArticle[]

  @@index([slug])
  @@index([isActive])
}

model NewsArticle {
  id             String   @id @default(cuid())
  title          String
  slug           String   @unique
  excerpt        String?
  content        String   @db.Text
  featuredImage  String?
  status         ArticleStatus @default(DRAFT)
  publishedAt    DateTime?
  scheduledAt    DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  authorId       String
  author         User     @relation(fields: [authorId], references: [id], onDelete: Restrict)
  categoryId     String
  category       NewsCategory @relation(fields: [categoryId], references: [id], onDelete: Restrict)

  // Content approval workflow
  approvals      ContentApproval[]

  @@index([slug])
  @@index([status])
  @@index([publishedAt])
  @@index([authorId])
  @@index([categoryId])
}

enum ArticleStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  PUBLISHED
  ARCHIVED
}

model ContentApproval {
  id          String   @id @default(cuid())
  contentType String   // "news_article", "announcement", etc.
  contentId   String
  status      ApprovalStatus @default(PENDING)
  comments    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  requestedById String
  requestedBy   User   @relation("ContentApprovalRequester", fields: [requestedById], references: [id], onDelete: Restrict)
  reviewedById  String?
  reviewedBy    User?  @relation("ContentApprovalReviewer", fields: [reviewedById], references: [id], onDelete: Restrict)

  // Polymorphic relations
  newsArticle   NewsArticle? @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@index([contentType, contentId])
  @@index([status])
  @@index([requestedById])
  @@index([reviewedById])
}



// -----------------------------
// DYNAMIC APPROVAL WORKFLOW MODELS
// -----------------------------

model ApprovalWorkflow {
  id          String   @id @default(cuid())
  name        String
  description String?
  entityType  String   // "procurement", "vendor", "contract", "payment", etc.
  isActive    Boolean  @default(true)
  isDefault   Boolean  @default(false)
  version     Int      @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Conditions for when this workflow applies
  conditions  Json?    // Dynamic conditions like value thresholds, categories, etc.

  // Relations
  createdById String
  createdBy   User     @relation("WorkflowCreator", fields: [createdById], references: [id], onDelete: Restrict)

  steps       ApprovalStep[]
  instances   ApprovalInstance[]

  @@index([entityType])
  @@index([isActive])
  @@index([isDefault])
}

model ApprovalStep {
  id          String   @id @default(cuid())
  workflowId  String
  workflow    ApprovalWorkflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  name        String
  description String?
  sequence    Int      // Order of execution
  stepType    ApprovalStepType
  isRequired  Boolean  @default(true)

  // Dynamic configuration
  config      Json?    // Step-specific configuration

  // Approval requirements
  approverType     ApproverType
  approverConfig   Json?        // Configuration for approver selection
  requiredCount    Int @default(1)  // Number of approvals needed
  allowDelegation  Boolean @default(false)
  timeoutHours     Int?         // Auto-escalation timeout

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  stepApprovals ApprovalStepInstance[]

  @@unique([workflowId, sequence])
  @@index([workflowId])
  @@index([sequence])
}

enum ApprovalStepType {
  APPROVAL        // Standard approval
  REVIEW          // Review only (no approval power)
  NOTIFICATION    // Notification only
  CONDITIONAL     // Conditional step based on data
  PARALLEL        // Parallel approval (multiple approvers)
  SEQUENTIAL      // Sequential approval (one after another)
  ESCALATION      // Escalation step
}

enum ApproverType {
  SPECIFIC_USER   // Specific user(s)
  ROLE_BASED      // Users with specific role(s)
  DEPARTMENT      // Department head/members
  HIERARCHY       // Based on organizational hierarchy
  DYNAMIC         // Dynamically determined based on conditions
  COMMITTEE       // Committee-based approval
  EXTERNAL        // External approver (email notification)
}

model ApprovalInstance {
  id          String   @id @default(cuid())
  workflowId  String
  workflow    ApprovalWorkflow @relation(fields: [workflowId], references: [id], onDelete: Restrict)

  entityType  String   // Same as workflow entityType
  entityId    String   // ID of the entity being approved

  status      ApprovalInstanceStatus @default(PENDING)
  priority    ApprovalPriority @default(NORMAL)

  // Metadata
  title       String?
  description String?
  metadata    Json?    // Additional data about the approval

  // Timing
  startedAt   DateTime @default(now())
  completedAt DateTime?
  dueDate     DateTime?

  // Relations
  initiatedById String
  initiatedBy   User   @relation("ApprovalInitiator", fields: [initiatedById], references: [id], onDelete: Restrict)

  stepInstances ApprovalStepInstance[]
  comments      ApprovalComment[]

  @@index([entityType, entityId])
  @@index([status])
  @@index([initiatedById])
  @@index([dueDate])
}

enum ApprovalInstanceStatus {
  PENDING
  IN_PROGRESS
  APPROVED
  REJECTED
  CANCELLED
  EXPIRED
}

enum ApprovalPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

model ApprovalStepInstance {
  id          String   @id @default(cuid())
  instanceId  String
  instance    ApprovalInstance @relation(fields: [instanceId], references: [id], onDelete: Cascade)
  stepId      String
  step        ApprovalStep @relation(fields: [stepId], references: [id], onDelete: Restrict)

  status      ApprovalStepStatus @default(PENDING)
  sequence    Int      // Copied from step for performance

  // Timing
  startedAt   DateTime?
  completedAt DateTime?
  dueDate     DateTime?

  // Results
  decision    ApprovalDecision?
  comments    String?
  metadata    Json?

  // Relations
  approvals   ApprovalAction[]

  @@index([instanceId])
  @@index([stepId])
  @@index([status])
  @@index([sequence])
}

enum ApprovalStepStatus {
  PENDING
  IN_PROGRESS
  APPROVED
  REJECTED
  SKIPPED
  EXPIRED
}

enum ApprovalDecision {
  APPROVED
  REJECTED
  DELEGATED
  ESCALATED
}

model ApprovalAction {
  id              String   @id @default(cuid())
  stepInstanceId  String
  stepInstance    ApprovalStepInstance @relation(fields: [stepInstanceId], references: [id], onDelete: Cascade)

  action          ApprovalActionType
  decision        ApprovalDecision?
  comments        String?
  metadata        Json?

  createdAt       DateTime @default(now())

  // Relations
  performedById   String
  performedBy     User     @relation("ApprovalActionPerformer", fields: [performedById], references: [id], onDelete: Restrict)
  delegatedToId   String?
  delegatedTo     User?    @relation("ApprovalActionDelegate", fields: [delegatedToId], references: [id], onDelete: Restrict)

  @@index([stepInstanceId])
  @@index([performedById])
  @@index([action])
}

enum ApprovalActionType {
  APPROVE
  REJECT
  DELEGATE
  REQUEST_INFO
  ESCALATE
  COMMENT
}

model ApprovalComment {
  id          String   @id @default(cuid())
  instanceId  String
  instance    ApprovalInstance @relation(fields: [instanceId], references: [id], onDelete: Cascade)

  content     String   @db.Text
  isInternal  Boolean  @default(false)  // Internal comments vs public
  createdAt   DateTime @default(now())

  // Relations
  authorId    String
  author      User     @relation("ApprovalCommentAuthor", fields: [authorId], references: [id], onDelete: Restrict)

  @@index([instanceId])
  @@index([authorId])
  @@index([createdAt])
}





model Delivery {
  id              String   @id @default(cuid())
  deliveryNumber  String   @unique
  poId            String
  po              PurchaseOrder @relation(fields: [poId], references: [id], onDelete: Restrict)

  // Delivery details
  deliveryDate    DateTime
  receivedDate    DateTime?

  // Status
  status          DeliveryStatus @default(SCHEDULED)

  // Location
  deliveryAddress String
  receivedBy      String?

  // Quality control
  inspectionDate  DateTime?
  inspectionResult QualityResult?
  inspectionNotes String?

  // BAST (Berita Acara Serah Terima)
  bastNumber      String?
  bastDate        DateTime?
  bastSignedBy    String?
  bastDocument    String?  // File path to BAST document

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  createdById     String
  createdBy       User     @relation("DeliveryCreator", fields: [createdById], references: [id], onDelete: Restrict)
  inspectedById   String?
  inspectedBy     User?    @relation("DeliveryInspector", fields: [inspectedById], references: [id], onDelete: Restrict)

  items           DeliveryItem[]
  grn             GoodsReceiptNote?

  @@index([deliveryNumber])
  @@index([poId])
  @@index([status])
  @@index([deliveryDate])
}

enum DeliveryStatus {
  SCHEDULED
  IN_TRANSIT
  DELIVERED
  INSPECTING
  ACCEPTED
  REJECTED
  PARTIALLY_ACCEPTED
}

enum QualityResult {
  PASSED
  FAILED
  CONDITIONAL
}

model DeliveryItem {
  id              String   @id @default(cuid())
  deliveryId      String
  delivery        Delivery @relation(fields: [deliveryId], references: [id], onDelete: Cascade)
  poItemId        String
  poItem          PurchaseOrderItem   @relation(fields: [poItemId], references: [id], onDelete: Restrict)

  // Delivered quantities
  deliveredQty    Float
  acceptedQty     Float    @default(0)
  rejectedQty     Float    @default(0)

  // Quality notes
  qualityNotes    String?
  rejectionReason String?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([deliveryId])
  @@index([poItemId])
}

model GoodsReceiptNote {
  id              String   @id @default(cuid())
  grnNumber       String   @unique
  deliveryId      String   @unique
  delivery        Delivery @relation(fields: [deliveryId], references: [id], onDelete: Restrict)

  // GRN details
  receiptDate     DateTime @default(now())
  totalValue      Float

  // Status
  status          GRNStatus @default(DRAFT)

  // Approval
  approvedAt      DateTime?
  approvedById    String?
  approvedBy      User?    @relation("GRNApprover", fields: [approvedById], references: [id], onDelete: Restrict)

  // Notes
  notes           String?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  createdById     String
  createdBy       User     @relation("GRNCreator", fields: [createdById], references: [id], onDelete: Restrict)

  @@index([grnNumber])
  @@index([deliveryId])
  @@index([status])
}

enum GRNStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  POSTED
}





// -----------------------------
// DISCUSSION & NEGOTIATION MODELS
// -----------------------------

model ProcurementDiscussion {
  id              String   @id @default(cuid())
  procurementId   String
  procurement     Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)

  // Discussion details
  title           String
  description     String?
  type            DiscussionType
  status          DiscussionStatus @default(ACTIVE)

  // Aanwijzing (Pre-bid conference) specific
  meetingDate     DateTime?
  meetingLocation String?
  meetingType     MeetingType?
  maxParticipants Int?

  // Access control
  isPublic        Boolean  @default(true)
  allowAnonymous  Boolean  @default(false)

  // Timing
  startDate       DateTime @default(now())
  endDate         DateTime?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  createdById     String
  createdBy       User     @relation("DiscussionCreator", fields: [createdById], references: [id], onDelete: Restrict)

  messages        DiscussionMessage[]
  participants    DiscussionParticipant[]
  attachments     DiscussionAttachment[]

  @@index([procurementId])
  @@index([type])
  @@index([status])
  @@index([createdById])
}

enum DiscussionType {
  AANWIJZING          // Pre-bid conference
  QA_SESSION          // Question & Answer
  CLARIFICATION       // Clarification requests
  NEGOTIATION         // Price/terms negotiation
  TECHNICAL_DISCUSSION // Technical discussions
  GENERAL             // General discussion
}

enum DiscussionStatus {
  ACTIVE
  CLOSED
  ARCHIVED
}

enum MeetingType {
  PHYSICAL
  VIRTUAL
  HYBRID
}

model DiscussionMessage {
  id              String   @id @default(cuid())
  discussionId    String
  discussion      ProcurementDiscussion @relation(fields: [discussionId], references: [id], onDelete: Cascade)

  // Message content
  content         String   @db.Text
  messageType     MessageType @default(MESSAGE)

  // Threading
  parentId        String?
  parent          DiscussionMessage? @relation("MessageReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies         DiscussionMessage[] @relation("MessageReplies")

  // Status
  isEdited        Boolean  @default(false)
  isDeleted       Boolean  @default(false)
  isOfficial      Boolean  @default(false)  // Official response from procurement team

  // Visibility
  isPublic        Boolean  @default(true)
  isAnonymous     Boolean  @default(false)

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  authorId        String
  author          User     @relation("MessageAuthor", fields: [authorId], references: [id], onDelete: Restrict)

  attachments     MessageAttachment[]
  reactions       MessageReaction[]

  @@index([discussionId])
  @@index([authorId])
  @@index([parentId])
  @@index([createdAt])
}

enum MessageType {
  MESSAGE
  QUESTION
  ANSWER
  CLARIFICATION
  ANNOUNCEMENT
  SYSTEM
}

model DiscussionParticipant {
  id              String   @id @default(cuid())
  discussionId    String
  discussion      ProcurementDiscussion @relation(fields: [discussionId], references: [id], onDelete: Cascade)
  userId          String
  user            User     @relation("DiscussionParticipants", fields: [userId], references: [id], onDelete: Cascade)

  // Participation details
  role            ParticipantRole @default(PARTICIPANT)
  joinedAt        DateTime @default(now())
  lastSeenAt      DateTime?

  // Permissions
  canPost         Boolean  @default(true)
  canModerate     Boolean  @default(false)

  // Status
  isActive        Boolean  @default(true)

  @@unique([discussionId, userId])
  @@index([discussionId])
  @@index([userId])
}

enum ParticipantRole {
  MODERATOR
  FACILITATOR
  PARTICIPANT
  OBSERVER
}

model DiscussionAttachment {
  id              String   @id @default(cuid())
  discussionId    String
  discussion      ProcurementDiscussion @relation(fields: [discussionId], references: [id], onDelete: Cascade)

  // File details
  fileName        String
  originalName    String
  fileSize        Int
  mimeType        String
  filePath        String

  // Metadata
  description     String?
  isPublic        Boolean  @default(true)

  createdAt       DateTime @default(now())

  // Relations
  uploadedById    String
  uploadedBy      User     @relation("AttachmentUploader", fields: [uploadedById], references: [id], onDelete: Restrict)

  @@index([discussionId])
  @@index([uploadedById])
}

model MessageAttachment {
  id              String   @id @default(cuid())
  messageId       String
  message         DiscussionMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)

  // File details
  fileName        String
  originalName    String
  fileSize        Int
  mimeType        String
  filePath        String

  // Metadata
  description     String?

  createdAt       DateTime @default(now())

  // Relations
  uploadedById    String
  uploadedBy      User     @relation("MessageAttachmentUploader", fields: [uploadedById], references: [id], onDelete: Restrict)

  @@index([messageId])
  @@index([uploadedById])
}

model MessageReaction {
  id              String   @id @default(cuid())
  messageId       String
  message         DiscussionMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  userId          String
  user            User     @relation("MessageReactions", fields: [userId], references: [id], onDelete: Cascade)

  // Reaction details
  emoji           String   // Unicode emoji
  createdAt       DateTime @default(now())

  @@unique([messageId, userId, emoji])
  @@index([messageId])
  @@index([userId])
}

// Negotiation-specific models
model NegotiationSession {
  id              String   @id @default(cuid())
  procurementId   String
  procurement     Procurement @relation(fields: [procurementId], references: [id], onDelete: Restrict)
  vendorId        String
  vendor          Vendor   @relation(fields: [vendorId], references: [id], onDelete: Restrict)

  // Session details
  title           String
  description     String?
  status          NegotiationStatus @default(PENDING)

  // Timing
  startDate       DateTime?
  endDate         DateTime?
  scheduledAt     DateTime?

  // Results
  finalPrice      Float?
  agreedTerms     Json?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  initiatedById   String
  initiatedBy     User     @relation("NegotiationInitiator", fields: [initiatedById], references: [id], onDelete: Restrict)

  rounds          NegotiationRound[]

  @@index([procurementId])
  @@index([vendorId])
  @@index([status])
}

enum NegotiationStatus {
  PENDING
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

model NegotiationRound {
  id              String   @id @default(cuid())
  sessionId       String
  session         NegotiationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  // Round details
  roundNumber     Int
  proposedPrice   Float?
  proposedTerms   Json?
  counterPrice    Float?
  counterTerms    Json?

  // Status
  status          RoundStatus @default(PENDING)

  // Notes
  vendorNotes     String?
  buyerNotes      String?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  proposedById    String
  proposedBy      User     @relation("RoundProposer", fields: [proposedById], references: [id], onDelete: Restrict)
  respondedById   String?
  respondedBy     User?    @relation("RoundResponder", fields: [respondedById], references: [id], onDelete: Restrict)

  @@index([sessionId])
  @@index([roundNumber])
}

enum RoundStatus {
  PENDING
  RESPONDED
  ACCEPTED
  REJECTED
  EXPIRED
}

// -----------------------------
// VENDOR KPI & PERFORMANCE MODELS
// -----------------------------

model VendorKpiTemplate {
  id              String   @id @default(cuid())
  name            String
  description     String?
  category        KpiCategory
  isActive        Boolean  @default(true)

  // KPI Configuration
  metrics         Json     // Array of metric definitions
  weights         Json     // Weights for each metric
  thresholds      Json     // Performance thresholds (excellent, good, fair, poor)

  // Calculation settings
  calculationMethod CalculationMethod @default(WEIGHTED_AVERAGE)
  evaluationPeriod  EvaluationPeriod @default(QUARTERLY)

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  createdById     String
  createdBy       User     @relation("KpiTemplateCreator", fields: [createdById], references: [id], onDelete: Restrict)

  evaluations     VendorKpiEvaluation[]

  @@index([category])
  @@index([isActive])
}

enum KpiCategory {
  QUALITY
  DELIVERY
  COST
  SERVICE
  COMPLIANCE
  INNOVATION
  OVERALL
}

enum CalculationMethod {
  WEIGHTED_AVERAGE
  SIMPLE_AVERAGE
  MINIMUM_SCORE
  MAXIMUM_SCORE
  CUSTOM_FORMULA
}

enum EvaluationPeriod {
  MONTHLY
  QUARTERLY
  SEMI_ANNUAL
  ANNUAL
  PROJECT_BASED
}

model VendorKpiEvaluation {
  id              String   @id @default(cuid())
  vendorId        String
  vendor          Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  templateId      String
  template        VendorKpiTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)

  // Evaluation period
  evaluationPeriod String  // e.g., "2024-Q1", "2024-01", "PROJECT-123"
  startDate       DateTime
  endDate         DateTime

  // Scores
  overallScore    Float
  categoryScores  Json     // Scores by category
  metricScores    Json     // Individual metric scores

  // Performance rating
  rating          PerformanceRating

  // Comments and feedback
  strengths       String?
  weaknesses      String?
  recommendations String?

  // Status
  status          EvaluationStatus @default(DRAFT)

  // Approval
  approvedAt      DateTime?
  approvedById    String?
  approvedBy      User?    @relation("KpiEvaluationApprover", fields: [approvedById], references: [id], onDelete: Restrict)

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  evaluatedById   String
  evaluatedBy     User     @relation("KpiEvaluator", fields: [evaluatedById], references: [id], onDelete: Restrict)

  @@unique([vendorId, templateId, evaluationPeriod])
  @@index([vendorId])
  @@index([templateId])
  @@index([evaluationPeriod])
  @@index([rating])
}

enum PerformanceRating {
  EXCELLENT
  GOOD
  SATISFACTORY
  NEEDS_IMPROVEMENT
  POOR
}

enum EvaluationStatus {
  DRAFT
  PENDING_REVIEW
  APPROVED
  PUBLISHED
}

model VendorPerformanceHistory {
  id              String   @id @default(cuid())
  vendorId        String
  vendor          Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  // Performance data
  period          String   // e.g., "2024-Q1"
  overallScore    Float
  qualityScore    Float?
  deliveryScore   Float?
  costScore       Float?
  serviceScore    Float?
  complianceScore Float?

  // Ranking
  rank            Int?
  totalVendors    Int?

  // Trend analysis
  previousScore   Float?
  scoreChange     Float?
  trend           PerformanceTrend?

  createdAt       DateTime @default(now())

  @@unique([vendorId, period])
  @@index([vendorId])
  @@index([period])
  @@index([overallScore])
}

enum PerformanceTrend {
  IMPROVING
  STABLE
  DECLINING
}

model VendorKpiMetric {
  id              String   @id @default(cuid())
  vendorId        String
  vendor          Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  // Metric details
  metricName      String
  metricType      MetricType
  category        KpiCategory

  // Values
  targetValue     Float?
  actualValue     Float
  achievementRate Float?   // Percentage of target achieved

  // Scoring
  score           Float    // Normalized score (0-100)
  weight          Float    // Weight in overall calculation

  // Period
  evaluationPeriod String
  recordedDate    DateTime @default(now())

  // Source
  sourceType      MetricSource
  sourceId        String?  // ID of source record (procurement, contract, etc.)

  // Metadata
  notes           String?
  isAutoCalculated Boolean @default(false)

  createdAt       DateTime @default(now())

  // Relations
  recordedById    String
  recordedBy      User     @relation("MetricRecorder", fields: [recordedById], references: [id], onDelete: Restrict)

  @@index([vendorId])
  @@index([metricName])
  @@index([category])
  @@index([evaluationPeriod])
}

enum MetricType {
  PERCENTAGE
  RATIO
  COUNT
  DURATION
  CURRENCY
  SCORE
  BOOLEAN
}

enum MetricSource {
  MANUAL_ENTRY
  PROCUREMENT_DATA
  CONTRACT_DATA
  DELIVERY_DATA
  INVOICE_DATA
  SURVEY_DATA
  SYSTEM_CALCULATED
}

// Vendor blacklist management
model BlacklistEntry {
  id              String   @id @default(cuid())
  vendorId        String
  vendor          Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  // Blacklist details
  reason          String
  description     String?
  severity        BlacklistSeverity
  category        BlacklistCategory

  // Duration
  startDate       DateTime @default(now())
  endDate         DateTime?
  isPermanent     Boolean  @default(false)

  // Status
  status          BlacklistStatus @default(ACTIVE)

  // Appeal process
  appealSubmitted Boolean  @default(false)
  appealDate      DateTime?
  appealReason    String?
  appealStatus    AppealStatus?
  appealDecision  String?
  appealDecidedAt DateTime?
  appealDecidedById String?
  appealDecidedBy User?    @relation("AppealDecider", fields: [appealDecidedById], references: [id], onDelete: Restrict)

  // Evidence and documentation
  evidenceFiles   Json?    // Array of file paths
  relatedContracts Json?   // Array of contract IDs

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  createdById     String
  createdBy       User     @relation("BlacklistCreator", fields: [createdById], references: [id], onDelete: Restrict)

  @@index([vendorId])
  @@index([status])
  @@index([severity])
  @@index([startDate])
  @@index([endDate])
}

enum BlacklistSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum BlacklistCategory {
  QUALITY_ISSUES
  DELIVERY_DELAYS
  CONTRACT_BREACH
  FRAUD
  CORRUPTION
  NON_COMPLIANCE
  POOR_PERFORMANCE
  LEGAL_ISSUES
  OTHER
}

enum BlacklistStatus {
  ACTIVE
  SUSPENDED
  EXPIRED
  APPEALED
  REVOKED
}

enum AppealStatus {
  PENDING
  UNDER_REVIEW
  APPROVED
  REJECTED
}

// -----------------------------
// AUDIT TRAIL MODELS
// -----------------------------

model AuditLog {
  id            String          @id @default(cuid())
  userId        String?         // Null for system actions
  sessionId     String?         // Session identifier
  action        AuditAction
  resource      String          // e.g., "procurement", "vendor", "user"
  resourceId    String?         // ID of the affected resource
  description   String          // Human-readable description
  metadata      Json            // Additional context data
  oldValues     Json?           // Previous values (for updates)
  newValues     Json?           // New values (for updates/creates)
  ipAddress     String?
  userAgent     String?
  timestamp     DateTime        @default(now())
  severity      AuditSeverity   @default(LOW)
  category      AuditCategory   @default(GENERAL)
  checksum      String?         // For integrity verification

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
  @@index([severity])
  @@index([category])
  @@map("audit_logs")
}

model SecurityEvent {
  id          String              @id @default(cuid())
  userId      String?
  eventType   SecurityEventType
  description String
  metadata    Json
  ipAddress   String?
  userAgent   String?
  severity    SecuritySeverity    @default(LOW)
  resolved    Boolean             @default(false)
  resolvedBy  String?
  resolvedAt  DateTime?
  timestamp   DateTime            @default(now())

  user       User? @relation("SecurityEventUser", fields: [userId], references: [id], onDelete: SetNull)
  resolvedByUser User? @relation("SecurityEventResolver", fields: [resolvedBy], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([eventType])
  @@index([severity])
  @@index([timestamp])
  @@index([resolved])
  @@map("security_events")
}

enum AuditAction {
  CREATE
  READ
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  APPROVE
  REJECT
  SUBMIT
  PUBLISH
  AWARD
  NEGOTIATE
  EVALUATE
  UPLOAD
  DOWNLOAD
  EXPORT
  IMPORT
  CONFIGURE
  BACKUP
  RESTORE
  SYSTEM_START
  SYSTEM_STOP
}

enum AuditSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AuditCategory {
  GENERAL
  AUTHENTICATION
  AUTHORIZATION
  DATA_ACCESS
  DATA_MODIFICATION
  SYSTEM_CONFIGURATION
  PROCUREMENT_PROCESS
  VENDOR_MANAGEMENT
  FINANCIAL_TRANSACTION
  SECURITY_INCIDENT
}

enum SecurityEventType {
  FAILED_LOGIN
  ACCOUNT_LOCKED
  SUSPICIOUS_ACTIVITY
  UNAUTHORIZED_ACCESS
  DATA_BREACH_ATTEMPT
  PRIVILEGE_ESCALATION
  MALICIOUS_FILE_UPLOAD
  SQL_INJECTION_ATTEMPT
  XSS_ATTEMPT
  CSRF_ATTEMPT
  RATE_LIMIT_EXCEEDED
  UNUSUAL_ACTIVITY_PATTERN
  SECURITY_POLICY_VIOLATION
}

enum SecuritySeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// -----------------------------
// TAX MASTER MODELS
// -----------------------------

model TaxType {
  id          String   @id @default(cuid())
  code        String   @unique // e.g., "PPN", "PPH21", "PPH23", "PPH4_2"
  name        String   // e.g., "Pajak Pertambahan Nilai", "PPh Pasal 21"
  description String?
  category    TaxCategory
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  taxRates TaxRate[]

  @@map("tax_types")
}

model TaxRate {
  id          String    @id @default(cuid())
  taxTypeId   String
  rate        Decimal   @db.Decimal(5, 4) // e.g., 0.1100 for 11%
  description String?   // e.g., "Standard rate", "Reduced rate for certain goods"
  effectiveFrom DateTime
  effectiveTo   DateTime?
  isActive    Boolean   @default(true)
  createdBy   String
  approvedBy  String?
  approvedAt  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  taxType     TaxType @relation(fields: [taxTypeId], references: [id], onDelete: Cascade)
  creator     User    @relation("TaxRateCreator", fields: [createdBy], references: [id])
  approver    User?   @relation("TaxRateApprover", fields: [approvedBy], references: [id])

  // Relations to transactions
  procurementTaxes ProcurementTax[]
  offerTaxes       OfferTax[]

  @@index([taxTypeId])
  @@index([effectiveFrom])
  @@index([effectiveTo])
  @@map("tax_rates")
}

model TaxExemption {
  id            String    @id @default(cuid())
  taxTypeId     String
  exemptionCode String    @unique // e.g., "GOVT_AGENCY", "EDUCATION", "HEALTHCARE"
  name          String    // e.g., "Government Agency Exemption"
  description   String?
  conditions    Json      // Conditions for exemption eligibility
  effectiveFrom DateTime
  effectiveTo   DateTime?
  isActive      Boolean   @default(true)
  createdBy     String
  approvedBy    String?
  approvedAt    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  taxType  TaxType @relation(fields: [taxTypeId], references: [id], onDelete: Cascade)
  creator  User    @relation("TaxExemptionCreator", fields: [createdBy], references: [id])
  approver User?   @relation("TaxExemptionApprover", fields: [approvedBy], references: [id])

  // Relations to tax applications
  procurementTaxes ProcurementTax[]
  offerTaxes       OfferTax[]

  @@index([taxTypeId])
  @@index([exemptionCode])
  @@map("tax_exemptions")
}

model ProcurementTax {
  id            String     @id @default(cuid())
  procurementId String
  taxRateId     String
  baseAmount    Decimal    @db.Decimal(15, 2) // Amount before tax
  taxAmount     Decimal    @db.Decimal(15, 2) // Calculated tax amount
  exemptionId   String?    // If tax exemption applies
  notes         String?
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  procurement Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  taxRate     TaxRate     @relation(fields: [taxRateId], references: [id])
  exemption   TaxExemption? @relation(fields: [exemptionId], references: [id])

  @@unique([procurementId, taxRateId])
  @@map("procurement_taxes")
}

model OfferTax {
  id          String  @id @default(cuid())
  offerId     String
  taxRateId   String
  baseAmount  Decimal @db.Decimal(15, 2)
  taxAmount   Decimal @db.Decimal(15, 2)
  exemptionId String?
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  offer     VendorOffer  @relation(fields: [offerId], references: [id], onDelete: Cascade)
  taxRate   TaxRate      @relation(fields: [taxRateId], references: [id])
  exemption TaxExemption? @relation(fields: [exemptionId], references: [id])

  @@unique([offerId, taxRateId])
  @@map("offer_taxes")
}

enum TaxCategory {
  VAT          // Pajak Pertambahan Nilai (PPN)
  INCOME_TAX   // Pajak Penghasilan (PPh)
  LUXURY_TAX   // Pajak Penjualan Barang Mewah (PPnBM)
  IMPORT_DUTY  // Bea Masuk
  EXCISE_TAX   // Cukai
  REGIONAL_TAX // Pajak Daerah
  OTHER        // Lainnya
}

// -----------------------------
// VENDOR REQUIREMENTS MODELS
// -----------------------------

model VendorRequirementTemplate {
  id          String   @id @default(cuid())
  name        String   // e.g., "Standard Vendor Requirements", "Construction Vendor Requirements"
  description String?
  category    String   // e.g., "GENERAL", "CONSTRUCTION", "IT_SERVICES", "CONSULTING"
  isDefault   Boolean  @default(false)
  isActive    Boolean  @default(true)
  createdBy   String
  updatedBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  creator     User @relation("VendorRequirementTemplateCreator", fields: [createdBy], references: [id])
  updater     User? @relation("VendorRequirementTemplateUpdater", fields: [updatedBy], references: [id])

  requirements VendorRequirement[]
  vendors      Vendor[] // Vendors using this template

  @@map("vendor_requirement_templates")
}

model VendorRequirement {
  id                String                    @id @default(cuid())
  templateId        String
  fieldName         String                    // e.g., "npwp", "siup", "company_profile"
  fieldLabel        String                    // e.g., "NPWP", "SIUP/NIB", "Company Profile"
  fieldType         VendorRequirementType
  description       String?                   // Help text for the field
  isRequired        Boolean                   @default(true)
  validationRules   Json?                     // Validation rules (file types, size limits, etc.)
  displayOrder      Int                       @default(0)
  groupName         String?                   // Group related fields together
  conditionalLogic  Json?                     // Show/hide based on other fields
  isActive          Boolean                   @default(true)
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt

  template VendorRequirementTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  submissions VendorRequirementSubmission[]

  @@unique([templateId, fieldName])
  @@index([templateId, displayOrder])
  @@map("vendor_requirements")
}

model VendorRequirementSubmission {
  id            String   @id @default(cuid())
  vendorId      String
  requirementId String
  value         Json     // The submitted value (text, file URL, etc.)
  status        VendorRequirementStatus @default(PENDING)
  reviewNotes   String?  // Admin review comments
  reviewedBy    String?
  reviewedAt    DateTime?
  submittedAt   DateTime @default(now())
  updatedAt     DateTime @updatedAt

  vendor      Vendor            @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  requirement VendorRequirement @relation(fields: [requirementId], references: [id], onDelete: Cascade)
  reviewer    User?             @relation("VendorRequirementReviewer", fields: [reviewedBy], references: [id])

  @@unique([vendorId, requirementId])
  @@index([vendorId])
  @@index([status])
  @@map("vendor_requirement_submissions")
}

enum VendorRequirementType {
  TEXT          // Simple text input
  TEXTAREA      // Multi-line text
  EMAIL         // Email address
  PHONE         // Phone number
  NUMBER        // Numeric input
  DATE          // Date picker
  SELECT        // Dropdown selection
  MULTISELECT   // Multiple selection
  CHECKBOX      // Boolean checkbox
  FILE          // File upload
  MULTIFILE     // Multiple file uploads
  URL           // Website URL
  ADDRESS       // Address with components
  CURRENCY      // Currency amount
  PERCENTAGE    // Percentage value
}

enum VendorRequirementStatus {
  PENDING       // Waiting for review
  APPROVED      // Approved by admin
  REJECTED      // Rejected, needs resubmission
  INCOMPLETE    // Missing or incomplete submission
}

// -----------------------------
// RBAC SECURITY MODELS
// -----------------------------

model Permission {
  id          String   @id @default(cuid())
  name        String   @unique // e.g., "procurement.create", "vendor.approve"
  description String
  resource    String   // e.g., "procurement", "vendor", "user"
  action      String   // e.g., "create", "read", "update", "delete", "approve"
  conditions  Json?    // Additional conditions for permission (e.g., own resources only)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rolePermissions RolePermission[]

  @@map("permissions")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String
  level       Int      @default(0) // For hierarchy (higher = more permissions)
  parentId    String?  // For role inheritance
  isSystem    Boolean  @default(false) // System roles cannot be deleted
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  parent       Role?  @relation("RoleHierarchy", fields: [parentId], references: [id])
  children     Role[] @relation("RoleHierarchy")

  permissions  RolePermission[]
  userRoles    UserRole[]

  @@map("roles")
}

model RolePermission {
  id           String   @id @default(cuid())
  roleId       String
  permissionId String
  conditions   Json?    // Override or additional conditions
  createdAt    DateTime @default(now())

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserRole {
  id         String    @id @default(cuid())
  userId     String
  roleId     String
  assignedBy String
  assignedAt DateTime  @default(now())
  expiresAt  DateTime?
  isActive   Boolean   @default(true)
  conditions Json?     // User-specific conditions

  user       User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role       Role @relation(fields: [roleId], references: [id], onDelete: Cascade)
  assignedByUser User @relation("UserRoleAssigner", fields: [assignedBy], references: [id])

  @@unique([userId, roleId])
  @@map("user_roles")
}

model ResourcePermission {
  id         String   @id @default(cuid())
  userId     String
  resource   String   // e.g., "procurement", "vendor"
  resourceId String   // Specific resource ID
  permission String   // e.g., "read", "write", "approve"
  grantedBy  String
  grantedAt  DateTime @default(now())
  expiresAt  DateTime?

  user      User @relation("UserResourcePermissions", fields: [userId], references: [id], onDelete: Cascade)
  grantedByUser User @relation("ResourcePermissionGranter", fields: [grantedBy], references: [id])

  @@unique([userId, resource, resourceId, permission])
  @@map("resource_permissions")
}
