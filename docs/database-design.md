### Key Refinements and Design Decisions:

1.  **Normalization of JSON Fields:** The previous design used `Json` fields for `procurementCommittee`, `BAST.checklist`, and `GRN.receivedItems`. This is a major weakness as it prevents structured queries and referential integrity.

    - **Solution:** These have been replaced with proper relational models: `ProcurementCommitteeMember`, `BastChecklistItem`, and `GoodReceiptItem`. You can now properly query who was on a committee or what specific items were received.

2.  **Explicit Approval Workflow Model:** The original design had simple `status` enums on POs, Invoices, etc. Real-world systems require a multi-step approval history.

    - **Solution:** A new `Approval` model has been introduced. This is a polymorphic model that can attach to a `PurchaseOrder`, `Invoice`, or `BAST`. It tracks each step, the approver, their decision, and comments, providing a full audit trail.

3.  **Detailed Vendor Blacklisting:** The `isBlacklisted` boolean was too simplistic.

    - **Solution:** A dedicated `BlacklistEntry` model now captures the reason, the admin who initiated it, and start/end dates, allowing for both temporary suspensions and permanent blacklisting.

4.  **Stronger Data Integrity & Relationships:**

    - **`onDelete` Rules:** Added explicit `onDelete` rules. For example, deleting a `Procurement` will `Cascade` and delete all its related items and stages. However, deleting a `Vendor` is `Restrict`ed to prevent accidental loss of historical procurement data. This is a critical safety feature.
    - **`onUpdate: Cascade`:** Ensures that if a primary key changes (which is rare with CUIDs but good practice), the change propagates to all related records.

5.  **Performance Indexes:** Added `@@index` to columns that will be frequently used in `WHERE` clauses or for sorting (e.g., `status`, foreign keys like `procurementId`, `vendorId`). This will significantly speed up database queries as the application scales.

6.  **Clearer Naming and Comments:** Added `///` doc comments to explain the purpose of models and enums, making the schema a self-documenting artifact for the development team.

---

### Refined `prisma/schema.prisma`

```prisma
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// -----------------------------
// ENUMS
// -----------------------------

/// Defines the roles a user can have in the system.
enum Role {
  ADMIN
  PROCUREMENT_USER /// User Pengadaan / Panitia
  APPROVER
  VENDOR
}

/// Status for vendor company profile verification.
enum VerificationStatus {
  PENDING
  VERIFIED
  REJECTED
}

/// The main type of procurement event.
enum ProcurementType {
  TENDER
  RFQ
}

/// Overall status of the procurement lifecycle.
enum ProcurementStatus {
  DRAFT
  PUBLISHED
  AANWIJZING
  SUBMISSION
  EVALUATION
  NEGOTIATION
  WINNER_ANNOUNCEMENT
  AWARDED
  COMPLETED
  CANCELED
}

/// Status for individual stages within a procurement.
enum StageStatus {
  PENDING
  ONGOING
  COMPLETED
}

/// Status of a vendor's offer throughout the evaluation process.
enum OfferStatus {
  DRAFT
  SUBMITTED
  WITHDRAWN
  EVALUATING_ADMIN
  PASSED_ADMIN
  FAILED_ADMIN
  EVALUATING_TECH
  PASSED_TECH
  FAILED_TECH
  EVALUATING_PRICE
  PASSED_PRICE
  FAILED_PRICE
  NEGOTIATING
  WINNER
  LOSER
  BACKUP_1 /// Cadangan 1
  BACKUP_2 /// Cadangan 2
}

/// Status for any approval workflow step.
enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

// -----------------------------
// CORE MODELS: USERS & VENDORS
// -----------------------------

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  roles     Role[]   @default([VENDOR])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  vendor                Vendor?
  committeeAssignments  ProcurementCommitteeMember[]
  discussionMessages    DiscussionMessage[]
  approvals             Approval[]
  blacklistEntries      BlacklistEntry[]
}

model Vendor {
  id                 String             @id @default(cuid())
  userId             String             @unique
  user               User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  companyName        String
  npwpNumber         String             @unique
  address            String
  npwpAddress        String
  phone              String
  picName            String
  picEmail           String
  picPhone           String
  verificationStatus VerificationStatus @default(PENDING)
  verifiedAt         DateTime?
  rejectionReason    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  documents        Document[]
  offers           VendorOffer[]
  kpis             VendorKpi[]
  purchaseOrders   PurchaseOrder[]
  blacklistEntries BlacklistEntry[]

  @@index([verificationStatus])
}

/// Models vendor blacklisting with history and reason.
model BlacklistEntry {
  id              String    @id @default(cuid())
  vendorId        String
  vendor          Vendor    @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  reason          String
  startDate       DateTime  @default(now())
  endDate         DateTime? /// Null for permanent blacklist
  blacklistedById String
  blacklistedBy   User      @relation(fields: [blacklistedById], references: [id], onDelete: Restrict)

  @@index([vendorId])
}

// -----------------------------
// PROCUREMENT FLOW MODELS
// -----------------------------

model Procurement {
  id                      String   @id @default(cuid())
  title                   String
  procurementNumber       String   @unique @default(cuid())
  type                    ProcurementType
  status                  ProcurementStatus @default(DRAFT)
  ownerEstimate           Float /// HPS (Harga Perkiraan Sendiri)
  showOwnerEstimateToVendor Boolean  @default(false)
  evaluationMethod        String /// e.g., "Sistem Nilai", "Pass/Fail"

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  stages    ProcurementStage[]
  items     ProcurementItem[]
  offers    VendorOffer[]
  committee ProcurementCommitteeMember[]
  po        PurchaseOrder?

  @@index([status, type])
}

/// Represents a member of the procurement committee for a specific tender.
model ProcurementCommitteeMember {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  userId        String
  user          User        @relation(fields: [userId], references: [id], onDelete: Restrict)
  committeeRole String /// e.g., "Ketua", "Anggota"

  @@unique([procurementId, userId])
}

model ProcurementStage {
  id               String            @id @default(cuid())
  procurementId    String
  procurement      Procurement       @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  name             String /// e.g., "Undangan RFQ", "Pemasukan Penawaran"
  status           StageStatus       @default(PENDING)
  startDate        DateTime
  endDate          DateTime
  sequence         Int
  discussionThread DiscussionThread?

  @@unique([procurementId, sequence])
  @@index([procurementId])
}

model ProcurementItem {
  id            String      @id @default(cuid())
  procurementId String
  procurement   Procurement @relation(fields: [procurementId], references: [id], onDelete: Cascade)
  name          String
  description   String?
  quantity      Float
  unit          String
  ownerEstimate Float /// OE per item

  vendorOfferItems VendorOfferItem[]
  poItems          PurchaseOrderItem[]

  @@index([procurementId])
}

model VendorOffer {
  id                String      @id @default(cuid())
  procurementId     String
  procurement       Procurement @relation(fields: [procurementId], references: [id], onDelete: Restrict)
  vendorId          String
  vendor            Vendor      @relation(fields: [vendorId], references: [id], onDelete: Restrict)
  status            OfferStatus @default(DRAFT)
  offerNumber       String?
  totalOfferedPrice Float
  negotiatedPrice   Float?
  submissionDate    DateTime    @default(now())

  items     VendorOfferItem[]
  documents Document[]

  @@index([procurementId])
  @@index([vendorId])
  @@index([status])
}

model VendorOfferItem {
  id            String      @id @default(cuid())
  vendorOfferId String
  vendorOffer   VendorOffer @relation(fields: [vendorOfferId], references: [id], onDelete: Cascade)
  itemId        String
  item          ProcurementItem @relation(fields: [itemId], references: [id], onDelete: Cascade)
  offeredPrice  Float

  @@unique([vendorOfferId, itemId])
}

// -----------------------------
// POST-AWARD & FINANCIAL MODELS
// -----------------------------

model PurchaseOrder {
  id              String   @id @default(cuid())
  poNumber        String   @unique
  procurementId   String   @unique
  procurement     Procurement @relation(fields: [procurementId], references: [id], onDelete: Restrict)
  vendorId        String
  vendor          Vendor   @relation(fields: [vendorId], references: [id], onDelete: Restrict)
  status          ApprovalStatus @default(PENDING)
  poDate          DateTime
  totalValue      Float
  deliveryAddress String
  termsOfPayment  String

  documents Document[]
  items     PurchaseOrderItem[]
  grn       GoodReceiptNote?
  invoices  Invoice[]
  basts     BAST[]
  approvals Approval[]

  @@index([vendorId, status])
}

model PurchaseOrderItem {
  id        String          @id @default(cuid())
  poId      String
  po        PurchaseOrder   @relation(fields: [poId], references: [id], onDelete: Cascade)
  itemId    String
  item      ProcurementItem @relation(fields: [itemId], references: [id], onDelete: Restrict)
  quantity  Float
  price     Float

  receivedItems GoodReceiptItem[]
}

model GoodReceiptNote {
  id            String   @id @default(cuid())
  grnNumber     String   @unique
  poId          String   @unique
  po            PurchaseOrder @relation(fields: [poId], references: [id], onDelete: Cascade)
  receivedDate  DateTime
  notes         String?
  receivedItems GoodReceiptItem[]
}

model GoodReceiptItem {
  id                  String            @id @default(cuid())
  grnId               String
  grn                 GoodReceiptNote   @relation(fields: [grnId], references: [id], onDelete: Cascade)
  purchaseOrderItemId String
  purchaseOrderItem   PurchaseOrderItem @relation(fields: [purchaseOrderItemId], references: [id], onDelete: Restrict)
  receivedQuantity    Float
  notes               String?

  @@unique([grnId, purchaseOrderItemId])
}

model Invoice {
  id            String         @id @default(cuid())
  invoiceNumber String         @unique
  poId          String
  po            PurchaseOrder  @relation(fields: [poId], references: [id], onDelete: Restrict)
  invoiceDate   DateTime
  amount        Float
  status        ApprovalStatus @default(PENDING)

  documents Document[]
  approvals Approval[]

  @@index([poId, status])
}

model BAST {
  id           String         @id @default(cuid())
  bastNumber   String         @unique
  poId         String
  po           PurchaseOrder  @relation(fields: [poId], references: [id], onDelete: Restrict)
  handoverDate DateTime
  status       ApprovalStatus @default(PENDING)

  documents Document[]
  checklist BastChecklistItem[]
  approvals Approval[]

  @@index([poId, status])
}

model BastChecklistItem {
  id       String  @id @default(cuid())
  bastId   String
  bast     BAST    @relation(fields: [bastId], references: [id], onDelete: Cascade)
  itemName String
  isOk     Boolean @default(true)
  notes    String?
}


// -----------------------------
// GENERIC & SUPPORTING MODELS
// -----------------------------

/// Polymorphic model for approval workflows.
model Approval {
  id           String         @id @default(cuid())
  sequence     Int
  status       ApprovalStatus @default(PENDING)
  approverId   String
  approver     User           @relation(fields: [approverId], references: [id], onDelete: Restrict)
  comments     String?
  processedAt  DateTime?

  // Polymorphic relation fields
  poId      String?
  purchaseOrder PurchaseOrder? @relation(fields: [poId], references: [id], onDelete: Cascade)
  invoiceId String?
  invoice   Invoice?       @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  bastId    String?
  bast      BAST?          @relation(fields: [bastId], references: [id], onDelete: Cascade)

  @@index([approverId, status])
}

model Document {
  id           String    @id @default(cuid())
  fileName     String
  fileUrl      String    @unique
  fileType     String /// Mime type
  description  String?
  uploadedAt   DateTime  @default(now())

  // Polymorphic relation fields
  vendorId      String?
  vendor        Vendor?      @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorOfferId String?
  vendorOffer   VendorOffer? @relation(fields: [vendorOfferId], references: [id], onDelete: Cascade)
  poId          String?
  purchaseOrder PurchaseOrder? @relation(fields: [poId], references: [id], onDelete: Cascade)
  invoiceId     String?
  invoice       Invoice?     @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  bastId        String?
  bast          BAST?        @relation(fields: [bastId], references: [id], onDelete: Cascade)
}

model DiscussionThread {
  id               String             @id @default(cuid())
  procurementStageId String           @unique
  procurementStage ProcurementStage @relation(fields: [procurementStageId], references: [id], onDelete: Cascade)
  type             String /// "AANWIJZING", "NEGOTIATION", "SANGGAHAN"

  messages DiscussionMessage[]
}

model DiscussionMessage {
  id        String           @id @default(cuid())
  threadId  String
  thread    DiscussionThread @relation(fields: [threadId], references: [id], onDelete: Cascade)
  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Restrict)
  content   String
  createdAt DateTime         @default(now())
}

model VendorKpi {
  id            String   @id @default(cuid())
  vendorId      String
  vendor        Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  procurementId String /// The procurement this KPI relates to
  period        String
  qualityScore  Int
  timeScore     Int
  costScore     Int
  serviceScore  Int
  overallScore  Float
  remarks       String?
  createdAt     DateTime @default(now())

  @@index([vendorId])
}

```

#### **1. Vendor Lifecycle (From "Vendor Nasional" & "Verifikasi Data Vendor" manuals)**

- **Pendaftaran (Registration):**

  - **Process:** A new company registers with its profile, PIC, and legal documents.
  - **Coverage:** Handled by creating a `User` (with `VENDOR` role) and a `Vendor` record. The `Document` model stores their NPWP, SIUP, etc. The `verificationStatus` is initially set to `PENDING`.
  - **Models Used:** `User`, `Vendor`, `Document`, `VerificationStatus` enum.

- **Verifikasi (Verification):**

  - **Process:** An internal Admin/Committee reviews the submitted data and documents, then approves or rejects the vendor. A reason is required for rejection.
  - **Coverage:** The `verificationStatus` on the `Vendor` model is updated to `VERIFIED` or `REJECTED`. The `rejectionReason` field captures why. The `verifiedAt` timestamp provides an audit trail.
  - **Models Used:** `Vendor`.

- **Blacklisting:**
  - **Process:** An admin can blacklist a vendor, either temporarily or permanently, with a stated reason.
  - **Coverage:** The `BlacklistEntry` model perfectly captures this. It's superior to a simple boolean because it tracks the _history_, `reason`, `startDate`, `endDate` (for temporary bans), and the `User` who initiated it.
  - **Models Used:** `BlacklistEntry`, `Vendor`, `User`.

#### **2. Procurement Lifecycle (From "e-Tender", "RFQ", and "Panitia" manuals)**

- **Procurement Setup:**

  - **Process:** A committee (Panitia) creates a new Tender/RFQ, defines the items, sets the budget (HPS), and configures the evaluation method.
  - **Coverage:** A `Procurement` record is created. `ProcurementItem`s are added. The committee members are stored in `ProcurementCommitteeMember`, linking `User`s to the `Procurement`.
  - **Models Used:** `Procurement`, `ProcurementItem`, `ProcurementCommitteeMember`.

- **Tahapan (Staging & Timeline):**

  - **Process:** The committee defines and schedules all stages: Pengumuman, Undangan, Pendaftaran, Aanwijzing, Penawaran, Evaluasi, etc.
  - **Coverage:** The `ProcurementStage` model handles this perfectly, with `sequence`, `startDate`, and `endDate` for each stage.
  - **Models Used:** `ProcurementStage`.

- **Aanwijzing / Negosiasi (Discussions):**

  - **Process:** A structured Q&A or negotiation chat occurs during specific stages.
  - **Coverage:** The `DiscussionThread` is linked to a specific `ProcurementStage` (e.g., the "Aanwijzing" stage). All messages from vendors and the committee are stored as `DiscussionMessage` records within that thread, providing a complete, auditable conversation history.
  - **Models Used:** `DiscussionThread`, `DiscussionMessage`.

- **Offer Submission:**

  - **Process:** Verified vendors submit their administrative, technical, and price documents/offers.
  - **Coverage:** A `VendorOffer` is created for each submitting vendor, linked to the `Procurement`. `VendorOfferItem` holds the price for each line item. The polymorphic `Document` model holds all uploaded files.
  - **Models Used:** `VendorOffer`, `VendorOfferItem`, `Document`.

- **Evaluasi (Evaluation):**

  - **Process:** The committee evaluates each offer in stages (Admin, Teknis, Harga), marking them as pass/fail and assigning scores.
  - **Coverage:** The `OfferStatus` enum on the `VendorOffer` model tracks the progress precisely (e.g., `EVALUATING_ADMIN`, `PASSED_ADMIN`, `FAILED_TECH`). The results of the evaluation can be stored in the application logic, with the final status being the source of truth in the DB.
  - **Models Used:** `VendorOffer`, `OfferStatus` enum.

- **Penetapan Pemenang (Winner Selection):**
  - **Process:** The committee ranks vendors and declares a winner, plus potential backups (Cadangan 1, 2).
  - **Coverage:** The `OfferStatus` enum handles this with `WINNER`, `LOSER`, `BACKUP_1`, `BACKUP_2` states. This is a clear and unambiguous way to represent the final result.
  - **Models Used:** `VendorOffer`, `OfferStatus` enum.

#### **3. Post-Award & Financial Lifecycle (From "Purchase Order" & "Invoice" manuals)**

- **Purchase Order (PO) Creation:**

  - **Process:** A PO is generated based on the winning offer.
  - **Coverage:** The `PurchaseOrder` and `PurchaseOrderItem` models are created, referencing the parent `Procurement` and winning `Vendor`.
  - **Models Used:** `PurchaseOrder`, `PurchaseOrderItem`.

- **Approval Workflows (PO, Invoice, BAST):**

  - **Process:** Key documents like POs, Invoices, and BASTs require multi-step approval from internal users.
  - **Coverage:** This is a major strength of the refined design. The polymorphic `Approval` model creates a full audit trail for _any_ document. It tracks the `sequence` of approval, the `approver`, their `status` (Approved/Rejected), and `comments`.
  - **Models Used:** `Approval`, `PurchaseOrder`, `Invoice`, `BAST`.

- **Good Receipt (GR) & Handover (BAST):**

  - **Process:** The receipt of goods is recorded (GR). A formal handover document (BAST) with a checklist is created and signed off.
  - **Coverage:** The `GoodReceiptNote` and `GoodReceiptItem` models allow for tracking partial deliveries. The `BAST` and `BastChecklistItem` models fully digitize the handover process, making the checklist queryable.
  - **Models Used:** `GoodReceiptNote`, `GoodReceiptItem`, `BAST`, `BastChecklistItem`.

- **Invoicing & Reversal:**
  - **Process:** A vendor submits an `Invoice` against a PO. It goes through an approval process. In special cases, it can be reversed.
  - **Coverage:** The `Invoice` model captures the core data. Its approval is handled by the `Approval` model. The `status` on the `Invoice` (or `PurchaseOrder`) can be set to a reversed state, with the application logic handling the business rules for what that entails.
  - **Models Used:** `Invoice`, `Approval`.

---

### **Potential Gaps & Areas for Clarification (The 2-5%)**

The remaining uncertainty lies in areas that are typically defined by business stakeholders, not just found in user manuals. This design _enables_ them, but they need to be built out in the application logic.

1.  **Master Data Management:** The manuals imply the existence of "Master Data" but don't detail how it's managed. For example:

    - **Approval Flows:** The `Approval` model tracks an approval _instance_. The system will need a "Master Approval Flow" table/logic to define _who_ needs to approve a PO for >$10,000 vs. <$10,000. This is an application-layer or a new `ApprovalMatrix` model concern.
    - **Evaluation Templates:** How are evaluation criteria (e.g., "Detail Teknis > Spesifikasi Teknis") and their weights (`30%`) created and managed? This would likely require `EvaluationCriteriaMaster` tables.

2.  **Complex Reversal Business Logic:** The manual mentions "Reversal Invoice." The database can mark an invoice as reversed. But what does that _mean_?

    - Does the PO become available for a new invoice?
    - Is the GRN also nullified?
    - This is pure business logic that needs clarification from the client, but the database schema is flexible enough to support the outcome.

3.  **User & Role Management:** The schema defines `User` and `Role`. The application will need an admin interface to create/manage internal users and assign them roles like `APPROVER` or `PROCUREMENT_USER`. This is an application feature, not a database schema gap.
