# Public Asset Security Documentation

## Overview

The e-procurement system implements a secure, isolated storage system for public assets used on the landing page and public portal. This separation ensures that public-facing content is properly secured and isolated from sensitive application data.

## Security Architecture

### 1. **Storage Separation**

**Private Files (Sensitive)**
- Location: `./uploads/` (local) or dedicated private bucket
- Access: Authenticated users only
- Content: Vendor documents, procurement files, contracts
- Security: Full access control, audit logging

**Public Assets (Landing Page)**
- Location: `./public/assets/public/` (local) or dedicated public bucket
- Access: Public read access
- Content: Images, banners, news attachments, logos
- Security: Content validation, size limits, type restrictions

### 2. **File Type Restrictions**

**Allowed Public Asset Types:**
```typescript
ALLOWED_MIME_TYPES: [
  "image/jpeg",     // .jpg, .jpeg
  "image/png",      // .png
  "image/webp",     // .webp
  "image/svg+xml",  // .svg
  "image/gif",      // .gif
  "application/pdf" // .pdf (documents only)
]
```

**Blocked Types:**
- Executable files (.exe, .bat, .cmd, .sh)
- Script files (.js, .php, .asp, .jsp, .py, .rb)
- HTML files (.html, .htm)
- Any file with path traversal attempts

### 3. **Security Validations**

**File Header Validation:**
- JPEG: Validates 0xFF 0xD8 header
- PNG: Validates 0x89 0x50 0x4E 0x47 header
- GIF: Validates GIF87a/GIF89a header
- WebP: Validates RIFF header
- PDF: Validates %PDF- header

**Content Security:**
- Maximum file size: 10MB
- File name sanitization
- Path traversal prevention
- Malicious pattern detection

### 4. **Storage Configuration**

**Environment Variables:**
```bash
# Public Assets Storage (Separate from private files)
PUBLIC_ASSETS_PROVIDER="local"              # or "aws-s3", "cloudflare-r2"
PUBLIC_ASSETS_LOCAL_PATH="./public/assets"
PUBLIC_ASSETS_LOCAL_URL="/assets"
PUBLIC_ASSETS_BUCKET="eproc-public-assets"
PUBLIC_ASSETS_REGION="us-east-1"
PUBLIC_ASSETS_CDN_URL="https://cdn.yourcompany.com"
PUBLIC_ASSETS_ACCESS_KEY_ID="your-public-assets-key"
PUBLIC_ASSETS_SECRET_ACCESS_KEY="your-public-assets-secret"
```

### 5. **Security Headers**

**Automatic Security Headers:**
```typescript
SECURITY_HEADERS: {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Cache-Control": "public, max-age=31536000, immutable"
}
```

**Content Security Policy for Images:**
```
default-src 'none'; img-src 'self'; style-src 'unsafe-inline';
```

## Implementation Details

### 1. **Upload Process**

```typescript
// Admin uploads public asset
const result = await publicAssetStorage.uploadPublicAsset(file, "news");

// Security validations applied:
// 1. File type validation
// 2. Size limit check
// 3. Header validation
// 4. Content scanning
// 5. Secure filename generation
```

### 2. **File Access**

**Public Access:**
```
GET /assets/public/news/timestamp-uuid-filename.jpg
```

**Security Middleware:**
- Validates file path
- Applies security headers
- Sets appropriate Content-Type
- Prevents directory traversal

### 3. **Admin Management**

**Upload API (Admin Only):**
```
POST /api/public-assets/upload
Authorization: Bearer <admin-token>
Content-Type: multipart/form-data

{
  file: <file>,
  category: "news" | "announcements" | "banners" | "logos" | "documents"
}
```

**Delete API (Admin Only):**
```
DELETE /api/public-assets/{encoded-key}
Authorization: Bearer <admin-token>
```

## Production Recommendations

### 1. **CDN Configuration**

**Benefits:**
- Global content delivery
- Reduced server load
- Better performance
- DDoS protection

**Setup:**
```bash
PUBLIC_ASSETS_CDN_URL="https://cdn.yourcompany.com"
```

### 2. **Object Storage**

**AWS S3 Configuration:**
```bash
PUBLIC_ASSETS_PROVIDER="aws-s3"
PUBLIC_ASSETS_BUCKET="eproc-public-assets"
PUBLIC_ASSETS_REGION="us-east-1"
```

**Cloudflare R2 Configuration:**
```bash
PUBLIC_ASSETS_PROVIDER="cloudflare-r2"
PUBLIC_ASSETS_ENDPOINT="https://your-account.r2.cloudflarestorage.com"
```

### 3. **Security Monitoring**

**Audit Logging:**
- All upload attempts logged
- Failed validations tracked
- Admin actions audited
- Security threats recorded

**Monitoring Metrics:**
- Upload success rate
- File type distribution
- Security violations
- Storage usage

### 4. **Backup Strategy**

**Automated Backups:**
- Daily backup of public assets
- Version control for critical assets
- Cross-region replication
- Disaster recovery plan

## Security Best Practices

### 1. **Access Control**

- Only admins can upload public assets
- Public read access for approved content only
- No write access from public endpoints
- Regular access review

### 2. **Content Validation**

- Strict file type enforcement
- Content scanning for malware
- Size limit enforcement
- Regular security updates

### 3. **Infrastructure Security**

- Separate storage buckets/paths
- Network isolation
- Regular security audits
- Incident response plan

### 4. **Compliance**

- Data retention policies
- Privacy compliance
- Audit trail maintenance
- Regular security assessments

## Troubleshooting

### Common Issues

**Upload Failures:**
1. Check file type and size
2. Verify admin permissions
3. Check storage configuration
4. Review security logs

**Access Issues:**
1. Verify file exists
2. Check security headers
3. Validate CDN configuration
4. Review middleware logs

**Performance Issues:**
1. Enable CDN
2. Optimize image sizes
3. Check cache headers
4. Monitor bandwidth usage

## API Reference

### Upload Public Asset
```
POST /api/public-assets/upload
Content-Type: multipart/form-data
Authorization: Bearer <admin-token>

Body:
- file: File (required)
- category: string (required) - "news"|"announcements"|"banners"|"logos"|"documents"

Response:
{
  "success": true,
  "data": {
    "url": "/assets/public/news/timestamp-uuid-filename.jpg",
    "key": "public/news/timestamp-uuid-filename.jpg",
    "filename": "original-filename.jpg",
    "size": 1024000,
    "contentType": "image/jpeg"
  }
}
```

### List Public Assets
```
GET /api/public-assets?category=news
Authorization: Bearer <admin-token>

Response:
{
  "success": true,
  "data": {
    "assets": ["public/news/file1.jpg", "public/news/file2.png"],
    "count": 2
  }
}
```

### Delete Public Asset
```
DELETE /api/public-assets/{encoded-key}
Authorization: Bearer <admin-token>

Response:
{
  "success": true,
  "message": "Public asset deleted successfully"
}
```
