# Gap Analysis & Enhancement Blueprint for E-Procurement System

#### 1. Executive Summary

This document synthesizes a multi-pass, in-depth analysis of the provided e-procurement manuals against the existing codebase. While the current system has a solid architectural foundation, it has significant functional and user-experience gaps when compared to the documented operational workflows. This blueprint provides a comprehensive plan to bridge these gaps and introduce strategic enhancements for a superior system.

The most critical areas for implementation are:

1.  **Pre-Procurement Workflow:** The entire Purchase Requisition (PR) module, including consolidation, is missing.
2.  **Post-Award Closure:** The Good Receipt (GR) and Berita Acara Serah Terima (BAST) workflows are non-functional, breaking the Procure-to-Pay cycle.
3.  **Vendor Management & Compliance:** The vendor verification, data update ("Buka Lock"), and blacklisting processes are too simplistic and do not meet the detailed risk management requirements.
4.  **UI/UX and Business Logic Mismatch:** Key UI components, terminology, and financial logic (like HPS/PPN calculation) described in the manuals are absent, creating a disconnect for users.

This document outlines the necessary database schema changes, API endpoints, business logic, and UI components to not only implement these missing features but also to enhance the overall business process with intelligent automation, robust risk management, and strategic sourcing capabilities.

---

### Part 2: Consolidated Gap Analysis

This section provides a unified summary of all identified discrepancies.

| Module                        | Gap Description                                                                                                                                                                                                                               | Impact Level |
| :---------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------- |
| **Purchase Requisition**      | The entire module is missing. The system cannot handle initial procurement requests, routine payment PRs based on existing contracts, or the consolidation of multiple PRs into a single procurement package.                                 | **Critical** |
| **Good Receipt**              | The system has no functionality to record the physical receipt of goods against a Purchase Order, a fundamental step for tracking delivery and enabling subsequent BAST/payment processes.                                                    | **Critical** |
| **BAST (Handover)**           | Though models exist, the workflow is not implemented. Vendors cannot create BASTs, and internal users cannot approve them. The crucial quality control checklist within the BAST is also missing.                                             | **High**     |
| **Vendor Verification**       | The current single-status verification is a stark contrast to the manual's granular, multi-step, department-aware approval process for different sections of a vendor's profile.                                                              | **High**     |
| **Vendor Profile Updates**    | The secure, approval-based "Buka Lock" mechanism for verified vendors to update their data is non-existent, leading to potential data staleness and compliance risks.                                                                         | **High**     |
| **Procurement Setup**         | Key configuration UIs and logic are missing, including HPS vs. PPN calculation, `Satuan Waktu Pekerjaan` (Work Time Unit), and the use of reusable, complex evaluation templates.                                                             | **High**     |
| **e-Tender Process**          | The system lacks the "Koreksi Aritmatik" feature for the committee to correct bid calculation errors. It also lacks a "Master Tim Pengadaan" concept for efficient team assignment and a temporary replacement feature for committee members. | **Medium**   |
| **Blacklisting**              | The blacklisting model is too simplistic; it cannot sanction specific individuals associated with a vendor, creating a significant risk management loophole.                                                                                  | **High**     |
| **Notifications & Approvals** | The notification system is generic and does not group pending tasks by type as shown in the manual. The approval history view lacks the required departmental context for full transparency.                                                  | **Medium**   |

---

### Part 3: Strategic Enhancements & Business Process Improvements

This section outlines proposals to elevate the system beyond the manual's requirements.

- **Intelligent Automation:**

  1.  **Automated Document Generation:** Automatically generate POs, Contracts, and BASTs from system data using `DocumentTemplate` models.
  2.  **Workflow Triggers:** Chain events automatically (e.g., BAST approval triggers a payment draft).
  3.  **Automated Vendor Scoring:** Pre-populate KPI evaluation drafts with objective data (e.g., On-Time Delivery rates calculated from PO and GRN dates).

- **Data Integrity & Risk Management:**

  1.  **Immutable Ledgers:** For critical actions (bidding, price corrections), create append-only log tables to ensure an unchallengeable audit trail.
  2.  **Version-Controlled Documents:** Archive old vendor documents upon update instead of overwriting, maintaining a complete history.
  3.  **3-Way Matching Engine:** Automatically cross-reference `PurchaseOrder`, `GoodReceipt`, and `Invoice` data, flagging discrepancies to prevent incorrect payments.
  4.  **Sanctioned Individuals Ledger:** A global, cross-vendor list of sanctioned individuals to prevent them from re-registering under new entities.

- **Strategic Sourcing & User Experience:**
  1.  **Item Master Catalog:** Standardize procurement items to enable powerful spend analytics and facilitate strategic sourcing for high-volume goods/services.
  2.  **Actionable Dashboards:** Transform dashboards from informational to actionable, with clickable stats that lead to filtered work queues.
  3.  **Visual Workflow Trackers:** Provide a clear, visual timeline for each procurement, accessible to both internal users and vendors.

---

### Part 4: The Ultimate Implementation Blueprint

This is the final, hyper-detailed design incorporating all analyses and enhancements.

#### 4.1. Core Database Schema (`prisma/schema.prisma`)

This is a holistic view of all necessary new models and modifications.

```prisma
// Enums for Statuses and Types
enum PurchaseRequisitionStatus { DRAFT, PENDING_APPROVAL, APPROVED, REJECTED, CONSOLIDATED, CONVERTED, CANCELLED }
enum PurchaseRequisitionType { INTERNAL, EXTERNAL_ROUTINE, EXTERNAL_NON_ROUTINE }
enum GoodReceiptStatus { DRAFT, COMPLETED, CANCELLED }
enum BastStatus { DRAFT, PENDING_APPROVAL, APPROVED, REJECTED }

// --- New Models ---
model Department {
  id    String @id @default(cuid())
  name  String @unique
  users User[]
}

model ItemMaster {
  id              String   @id @default(cuid())
  itemCode        String   @unique
  name            String
  description     String?
  unit            String
  category        String
  specifications  Json?

  prItems         PurchaseRequisitionItem[]

  @@index([category])
}

model PurchaseRequisition {
  id               String                      @id @default(cuid())
  prNumber         String                      @unique @default(cuid())
  title            String
  type             PurchaseRequisitionType
  status           PurchaseRequisitionStatus   @default(DRAFT)
  requesterId      String
  requester        User                        @relation("PRRequester", fields: [requesterId], references: [id])
  totalValue       Float                       @default(0)
  isConsolidated   Boolean                     @default(false)
  packageId        String?
  package          ProcurementPackage?         @relation(fields: [packageId], references: [id])
  procurementId    String?                     @unique
  procurement      Procurement?                @relation(fields: [procurementId], references: [id])
  sourceContractId String?
  sourceContract   Contract?                   @relation(fields: [sourceContractId], references: [id])
  createdAt        DateTime                    @default(now())
  updatedAt        DateTime                    @updatedAt

  items            PurchaseRequisitionItem[]
  documents        Document[]                  @relation("PRDocuments")
}

model PurchaseRequisitionItem {
  id             String                @id @default(cuid())
  prId           String
  pr             PurchaseRequisition   @relation(fields: [prId], references: [id], onDelete:Cascade)
  itemMasterId   String?               // Link to standardized item
  itemMaster     ItemMaster?           @relation(fields: [itemMasterId], references: [id])
  name           String                // Name if not from master
  description    String?
  quantity       Float
  unit           String
  estimatedPrice Float
}

model ProcurementPackage {
  id           String                @id @default(cuid())
  name         String
  createdById  String
  createdBy    User                  @relation(fields: [createdById], references: [id])
  procurementId String?               @unique
  procurement   Procurement?
  requisitions PurchaseRequisition[]
}

model Contract {
  id                   String                  @id @default(cuid())
  contractNumber       String                  @unique
  vendorId             String
  vendor               Vendor                  @relation(fields: [vendorId], references: [id])
  title                String
  startDate            DateTime
  endDate              DateTime
  totalValue           Float
  purchaseRequisitions PurchaseRequisition[]
}

model GoodReceipt {
  id          String            @id @default(cuid())
  grNumber    String            @unique
  poId        String
  po          PurchaseOrder     @relation(fields: [poId], references: [id])
  status      GoodReceiptStatus @default(DRAFT)
  receivedDate DateTime
  createdById String
  createdBy   User              @relation(fields: [createdById], references: [id])
  items       GoodReceiptItem[]
}

model GoodReceiptItem {
  id                  String             @id @default(cuid())
  grId                String
  gr                  GoodReceipt        @relation(fields: [grId], references: [id], onDelete: Cascade)
  purchaseOrderItemId String
  purchaseOrderItem   PurchaseOrderItem  @relation(fields: [purchaseOrderItemId], references: [id])
  quantityReceived    Float              // This will be a calculated field (sum of logs)
  receiptLogs         ReceiptLog[]
}

model ReceiptLog {
  id                String          @id @default(cuid())
  goodReceiptItemId String
  goodReceiptItem   GoodReceiptItem @relation(fields: [goodReceiptItemId], references: [id])
  quantityChange    Float
  logDate           DateTime        @default(now())
  notes             String?
  loggedById        String
  loggedBy          User            @relation(fields: [loggedById], references: [id])
}

model BastChecklistItem {
  id          String   @id @default(cuid())
  bastId      String
  bast        BAST     @relation(fields: [bastId], references: [id], onDelete: Cascade)
  description String
  defectNotes String?
  targetDate  DateTime?
}

model SanctionedIndividual {
  id                String         @id @default(cuid())
  name              String
  identityNumber    String         @unique
  reason            String
  sourceBlacklistId String?
  sourceBlacklist   BlacklistEntry? @relation(fields: [sourceBlacklistId], references: [id])
}

model EvaluationTemplate {
  id           String        @id @default(cuid())
  name         String        @unique
  method       String
  passGrade    Float?
  criteria     Json
  procurements Procurement[]
}

model PriceCorrectionLog {
  id               String          @id @default(cuid())
  offerItemId      String
  offerItem        VendorOfferItem @relation(fields: [offerItemId], references: [id])
  originalPrice    Float
  correctedPrice   Float
  reason           String
  correctedAt      DateTime        @default(now())
  correctedById    String
  correctedBy      User            @relation(fields: [correctedById], references: [id])
}


// --- Modifications to Existing Models ---
model User {
  // ... existing fields
  departmentId String?
  department   Department? @relation(fields: [departmentId], references: [id])

  // Relations to new models
  requestedPRs      PurchaseRequisition[]      @relation("PRRequester")
  createdPackages   ProcurementPackage[]
  createdGRs        GoodReceipt[]              @relation("GRCreator")
  receiptLogs       ReceiptLog[]
  createdBASTs      BAST[]                     @relation("BASTCreator")
  priceCorrections  VendorOfferItem[]          @relation("PriceCorrector")
  verifiedVendorSteps VendorVerificationStep[]
}

model Vendor {
  // ... existing fields
  isLocked            Boolean                  @default(true)
  unlockExpiryDate    DateTime?
  contracts           Contract[]
  verificationSteps   VendorVerificationStep[]
}

model VendorVerificationStep {
  id           String         @id @default(cuid())
  vendorId     String
  vendor       Vendor         @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  stepName     String
  status       ApprovalStatus @default(PENDING)
  verifiedById String?
  verifiedBy   User?          @relation(fields: [verifiedById], references: [id])
  verifiedAt   DateTime?
  comments     String?
  @@unique([vendorId, stepName])
}

model Procurement {
  // ... existing fields
  workTimeUnit         String?
  hpsIncludesVat       Boolean               @default(false)
  vatRate              Float?
  evaluationTemplateId String?
  evaluationTemplate   EvaluationTemplate?   @relation(fields: [evaluationTemplateId], references: [id])
}

model VendorOfferItem {
  // ... existing fields
  correctedPrice   Float?
  correctionReason String?
  correctedAt      DateTime?
  correctedById    String?
  correctedBy      User?                   @relation("PriceCorrector", fields: [correctedById], references: [id])
  correctionLogs   PriceCorrectionLog[]
}

model BAST {
  // ... existing fields
  status             BastStatus @default(DRAFT)
  summary            String?    @db.Text
  createdById        String
  createdBy          User       @relation("BASTCreator", fields: [createdById], references: [id])
  approvalInstanceId String?
  documents          Document[] @relation("BASTDocuments")
}

model BlacklistEntry {
  // ... existing fields
  sanctionedIndividuals SanctionedIndividual[]
}

model Notification {
  // ... existing fields
  notificationType String // e.g., "BAST_APPROVAL_REQUEST"
}

model Document {
  // ... existing fields
  prId   String?
  pr     PurchaseRequisition? @relation("PRDocuments", fields: [prId], references: [id], onDelete: Cascade)
  bastId String?
  bast   BAST?                @relation("BASTDocuments", fields: [bastId], references: [id], onDelete: Cascade)
}
```

#### 4.2. API Endpoints & Business Logic

| Module     | Method | Endpoint                                  | Business Logic                                                                                                                                                                              | Roles               |
| :--------- | :----- | :---------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------------------ |
| **PR**     | `POST` | `/api/purchase-requisitions`              | Creates a new PR. If `type` is `EXTERNAL_ROUTINE` and `sourceContractId` is present, pre-fills data from the specified contract.                                                            | `PROCUREMENT_USER`  |
|            | `POST` | `/api/purchase-requisitions/[id]/release` | Submits PR to the approval workflow engine.                                                                                                                                                 | `PROCUREMENT_USER`  |
|            | `POST` | `/api/procurement-packages`               | Creates a `ProcurementPackage` and links multiple `APPROVED` PRs to it, marking them as `CONSOLIDATED`.                                                                                     | `PROCUREMENT_USER`  |
|            | `POST` | `/api/procurement-packages/[id]/convert`  | Creates a single `Procurement` from a package, aggregating all items.                                                                                                                       | `PROCUREMENT_USER`  |
| **GR**     | `POST` | `/api/good-receipts`                      | Creates a `GoodReceipt` against a PO. For each item, creates a `ReceiptLog` entry. `quantityReceived` is calculated, not directly set.                                                      | `PROCUREMENT_USER`  |
|            | `POST` | `/api/good-receipts/[id]/complete`        | Finalizes a GR. **Enhancement:** Triggers an in-app and email notification to the vendor that they may now submit a BAST.                                                                   | `PROCUREMENT_USER`  |
| **BAST**   | `POST` | `/api/bast`                               | Vendor creates a BAST, including the dynamic `checklist` items. Submitting triggers the `BAST` approval workflow.                                                                           | `VENDOR`            |
|            | `GET`  | `/api/bast/[id]/approval-history`         | Fetches the structured approval timeline, joining with `User` and `Department` models for a rich view.                                                                                      | `All Internal`      |
| **Vendor** | `PUT`  | `/api/admin/vendors/[id]/verify-step`     | Admin/Approver approves a specific verification step (e.g., `stepName: "TAX"`). The logic checks if all steps are now `APPROVED`; if so, it updates the main `Vendor.status` to `VERIFIED`. | `ADMIN`, `APPROVER` |
|            | `PUT`  | `/api/admin/vendors/[id]/grant-unlock`    | Admin sets `isLocked = false` and `unlockExpiryDate`. Logs this action in the `AuditLog`.                                                                                                   | `ADMIN`             |
|            | `POST` | `/api/vendors/[id]/blacklist`             | Admin blacklists a vendor. The request can include individuals, which creates `SanctionedIndividual` records.                                                                               | `ADMIN`             |
| **Tender** | `PUT`  | `/api/procurements/[id]`                  | Enhanced to handle `hpsIncludesVat`, `vatRate`, and `evaluationTemplateId`.                                                                                                                 | `PROCUREMENT_USER`  |
|            | `PUT`  | `/api/offers/[id]/correct-arithmetic`     | Committee action. Creates a `PriceCorrectionLog` entry and updates the `correctedPrice` on the `VendorOfferItem`. All future price comparisons for this offer must use `correctedPrice`.    | `COMMITTEE`         |

#### 4.3. User Interface (UI/UX) Components

- **`PurchaseRequisitionDashboard.tsx`**: A new page at `/purchase-requisitions`.
  - Displays PRs in tabs: "Draft", "Pending Approval", "Approved", "Rejected".
  - The "Approved" tab will have checkboxes next to each PR and a "Konsolidasi PR" button that becomes active when multiple PRs are selected.
- **`VendorVerificationDashboard.tsx` (Refactored):**
  - The main view will be a list of vendors pending verification.
  - Clicking "Review" on a vendor opens a detail modal with tabs as seen in the manual: "Surat Pernyataan", "Administrasi", "Izin Usaha", "Pajak", etc.
  - Each tab shows the relevant data and documents. An approver with the correct permissions sees an "Approve Step" button at the bottom of each tab.
- **`ProcurementSetupWizard.tsx`**: A multi-step component that guides the user through creating a procurement package.
  - **Step 1:** General Info (Title, Type).
  - **Step 2: "Atur Paket"** - Implements the HPS/PPN calculation UI, Work Time Unit dropdown, and Evaluation Template selection.
  - **Step 3: "Susunan Tim"** - Uses a `CommitteeManager` component that allows adding members from a predefined master list or searching for users. Includes the temporary replacement feature.
- **`VendorOfferEvaluation.tsx`:**
  - This component will display the vendor's submitted prices.
  - A "Koreksi Aritmatik" button will be present next to each line item. Clicking it opens a modal where the committee member can input the `correctedPrice` and `correctionReason`. The UI will then display both the original and corrected price, with a small icon indicating a correction was made.
- **`VendorBlacklistForm.tsx`**:
  - A modal for admins.
  - In addition to the reason, it will have a section to "Add Sanctioned Individual". This will allow the admin to input Name and Identity Number, which will be checked against the `SanctionedIndividual` ledger.
- **`ThreeWayMatchDashboard.tsx`**: A new admin/finance page.
  - Lists POs that have associated GRs and Invoices.
  - Displays a table with columns: `PO Number`, `PO Amount`, `GR Total Received Value`, `Invoice Amount`, `Variance`.
  - Rows with variances are highlighted in red. Clicking a row shows a detailed breakdown of the item-level discrepancies.

This ultimate design document provides a comprehensive and actionable plan. It addresses every identified nuance from the manuals and integrates strategic enhancements to ensure the final product is not just a compliant e-procurement system, but a truly excellent one.
