# Redis Queue System

The E-Procurement system uses a robust Redis-based queue system for handling asynchronous tasks like email notifications, SMS messages, push notifications, and webhook deliveries.

## Architecture

### Components

1. **Redis Client** (`src/lib/queue/redis-client.ts`)
   - Manages Redis connections
   - Defines queue keys and data structures
   - Provides health check functionality

2. **Queue Manager** (`src/lib/queue/queue-manager.ts`)
   - Core queue operations (add, get, complete, fail jobs)
   - Priority-based job processing
   - Retry logic with exponential backoff
   - Queue statistics and monitoring

3. **Queue Services**
   - **Email Queue** (`src/lib/queue/email-queue.ts`)
   - **SMS Queue** (`src/lib/queue/sms-queue.ts`)
   - **Push Queue** (`src/lib/queue/push-queue.ts`)
   - **Webhook Queue** (`src/lib/queue/webhook-queue.ts`)

4. **Workers** (`src/lib/queue/workers/`)
   - **Email Worker** - Processes email jobs using <PERSON><PERSON><PERSON><PERSON>
   - **SMS Worker** - Processes SMS jobs (Twilio, Nexmo, AWS SNS)
   - **Push Worker** - Processes push notifications (FCM, APNS)
   - **Webhook Worker** - Processes webhook deliveries

## Features

### Priority Queues
- **High Priority**: Urgent notifications, OTP messages
- **Medium Priority**: Standard notifications
- **Low Priority**: Marketing emails, newsletters

### Retry Logic
- Exponential backoff for failed jobs
- Configurable retry attempts
- Dead letter queue for permanently failed jobs

### Scheduling
- Delayed job execution
- Scheduled notifications
- Recurring tasks support

### Monitoring
- Real-time queue statistics
- Worker health monitoring
- Performance metrics
- Redis health checks

## Setup

### Prerequisites

1. **Redis Server**
   ```bash
   # Install Redis (Ubuntu/Debian)
   sudo apt update
   sudo apt install redis-server
   
   # Start Redis
   sudo systemctl start redis-server
   sudo systemctl enable redis-server
   
   # Verify Redis is running
   redis-cli ping
   ```

2. **Environment Variables**
   ```env
   # Redis Configuration
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_PASSWORD=your_redis_password
   REDIS_DB=0
   
   # Email Configuration (Production)
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_SECURE=true
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your_app_password
   SMTP_FROM=<EMAIL>
   
   # SMS Configuration (Optional)
   SMS_PROVIDER=twilio
   TWILIO_ACCOUNT_SID=your_account_sid
   TWILIO_AUTH_TOKEN=your_auth_token
   TWILIO_FROM_NUMBER=+**********
   
   # Or use Nexmo/Vonage
   SMS_PROVIDER=nexmo
   NEXMO_API_KEY=your_api_key
   NEXMO_API_SECRET=your_api_secret
   NEXMO_FROM_NUMBER=YourBrand
   ```

### Installation

1. **Install Dependencies**
   ```bash
   npm install ioredis nodemailer
   npm install --save-dev @types/nodemailer
   ```

2. **Generate Prisma Client**
   ```bash
   npm run db:generate
   ```

3. **Start Queue Workers**
   ```bash
   # Production
   npm run queue:start
   
   # Development (with auto-restart)
   npm run queue:dev
   ```

## Usage

### Adding Jobs to Queue

#### Email Notifications
```typescript
import { emailQueue } from '@/lib/queue/email-queue';

// Basic email
await emailQueue.addEmail({
  to: '<EMAIL>',
  subject: 'Welcome to E-Procurement',
  body: 'Thank you for registering!',
});

// High priority email
await emailQueue.addUrgentEmail({
  to: '<EMAIL>',
  subject: 'Critical System Alert',
  body: 'Immediate attention required!',
});

// Scheduled email
await emailQueue.scheduleEmail(
  {
    to: '<EMAIL>',
    subject: 'Reminder',
    body: 'Don\'t forget about your meeting tomorrow.',
  },
  new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
);
```

#### SMS Notifications
```typescript
import { smsQueue } from '@/lib/queue/sms-queue';

// Basic SMS
await smsQueue.addSms({
  to: '+628**********',
  message: 'Your procurement request has been approved.',
});

// OTP SMS
await smsQueue.addOtpSms('+628**********', '123456');

// Urgent SMS
await smsQueue.addUrgentSms({
  to: '+628**********',
  message: 'URGENT: System maintenance in 30 minutes.',
});
```

#### Push Notifications
```typescript
import { pushQueue } from '@/lib/queue/push-queue';

// Basic push notification
await pushQueue.addPushNotification({
  userId: 'user123',
  title: 'New Message',
  body: 'You have a new procurement update.',
});

// Broadcast notification
await pushQueue.addBroadcastNotification(
  'System Maintenance',
  'The system will be down for maintenance from 2-4 AM.'
);
```

#### Webhooks
```typescript
import { webhookQueue } from '@/lib/queue/webhook-queue';

// Basic webhook
await webhookQueue.addWebhook({
  url: 'https://external-system.com/webhook',
  payload: {
    event: 'procurement_created',
    data: { id: 'proc123', title: 'Office Supplies' },
  },
});

// Slack notification
await webhookQueue.addSlackWebhook(
  'https://hooks.slack.com/services/...',
  'New procurement request submitted!',
  '#procurement'
);
```

### Monitoring

#### Queue Statistics
```typescript
import { queueManager } from '@/lib/queue/queue-manager';

// Get email queue stats
const emailStats = await queueManager.getStats('email');
console.log(emailStats);
// {
//   pending: 5,
//   processing: 2,
//   completed: 100,
//   failed: 3,
//   delayed: 1,
//   total: 111
// }
```

#### Worker Health
```typescript
import { workerManager } from '@/lib/queue/workers/base-worker';

const health = workerManager.getHealthStatus();
console.log(health);
// {
//   email: true,
//   sms: true,
//   push: false,
//   webhook: true
// }
```

### API Endpoints

#### Queue Management
```bash
# Get queue statistics
GET /api/admin/queue/stats

# Pause a queue
POST /api/admin/queue/stats
{
  "action": "pause",
  "queueType": "email"
}

# Resume a queue
POST /api/admin/queue/stats
{
  "action": "resume",
  "queueType": "email"
}

# Clean up old jobs
POST /api/admin/queue/stats
{
  "action": "cleanup",
  "queueType": "email",
  "olderThanHours": 24
}
```

#### Worker Management
```bash
# Get worker status
GET /api/admin/queue/workers

# Test email worker
POST /api/admin/queue/workers
{
  "action": "test_email",
  "email": "<EMAIL>"
}

# Test SMS worker
POST /api/admin/queue/workers
{
  "action": "test_sms",
  "phone": "+628**********"
}
```

## Production Deployment

### Docker Compose
```yaml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    
  app:
    build: .
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
      
  queue-workers:
    build: .
    command: npm run queue:start
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
      
volumes:
  redis_data:
```

### Scaling Workers
```bash
# Run multiple worker instances
docker-compose up --scale queue-workers=3
```

### Monitoring with Redis CLI
```bash
# Monitor queue lengths
redis-cli llen queue:email:high
redis-cli llen queue:email:medium
redis-cli llen queue:email:low

# Monitor processing queues
redis-cli hlen processing:email

# View failed jobs
redis-cli lrange failed:email 0 -1
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check Redis server is running
   - Verify connection credentials
   - Check network connectivity

2. **Workers Not Processing Jobs**
   - Check worker health status
   - Verify queue is not paused
   - Check Redis memory usage

3. **High Failed Job Count**
   - Check external service credentials
   - Verify network connectivity
   - Review error logs

### Debugging
```bash
# Check Redis logs
sudo journalctl -u redis-server

# Monitor Redis operations
redis-cli monitor

# Check queue worker logs
npm run queue:start 2>&1 | tee queue.log
```
