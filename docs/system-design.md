**1. System Overview**
This document outlines the design for the e-Procurement system for PT Bank BPD Sulteng. The system manages the entire procurement lifecycle, from vendor onboarding to financial settlement, for both RFQ and e-Tender processes. The architecture is based on a Next.js frontend, a PostgreSQL database managed by Prisma, and a set of well-defined API endpoints.

**2. Guiding Principles**

- **Component-Based:** The UI is broken down into logical, reusable components (using Shadcn UI).
- **State-Driven:** Frontend state is managed by Tanstack Query, ensuring data is always fresh and synchronized with the backend.
- **Role-Based Access Control (RBAC):** Every feature and API endpoint is protected and tailored to the user's role (`VENDOR`, `ADMIN`, `PROCUREMENT_USER`, `APPROVER`).
- **Single Source of Truth:** The Prisma schema is the definitive source for all data structures.

---

### **Module 1: Vendor Onboarding & Verification**

#### **Flow 1.1: New Vendor Registration**

- **User Story:** As a new potential supplier, I want to register my company on the portal by providing my company details, PIC information, and legal documents, so I can be considered for future procurements.

- **UI Screen:** `app/(auth)/register/page.tsx`

  - **Layout:** A public, single-column layout centered on the page.
  - **Main Component:** `VendorRegistrationForm.tsx`
    - **UI Elements:**
      - `<Card>` with `<CardHeader>` ("Form Pendaftaran Akun Eprocurement PT Bank BPD Sulteng").
      - `<Tabs>` with two tabs: "Vendor Nasional" (default) and "Vendor Internasional".
      - **Form Sections (inside "Vendor Nasional" tab):**
        1.  **"Data Perusahaan"**:
            - `<Input name="npwpNumber">` (with a button to check for uniqueness).
            - `<FileUpload name="npwpDocument">` for the NPWP file.
            - `<Select name="businessEntityType">` (e.g., PT, CV).
            - `<Input name="companyName">`.
            - `<Input name="email" type="email">`.
            - `<Input name="password" type="password">`.
            - `<Input name="confirmPassword" type="password">`.
            - `<Textarea name="address">`.
        2.  **"Data PIC"**:
            - `<Input name="picName">`.
            - `<Input name="picEmail" type="email">`.
            - `<Input name="picPhone">`.
        3.  **"Legal Documents"**:
            - `<FileUpload name="siupDocument">` (SIUP).
            - `<FileUpload name="tdpDocument">` (TDP/NIB).
        4.  **Finalization**:
            - `<Checkbox name="termsAndConditions">` ("Saya telah membaca dan menyetujui...").
            - `<Input name="captcha">`.
            - `<Button type="submit" disabled={!formState.isValid}>` ("DAFTAR").
    - **State Management:** `react-hook-form` with a `zod` schema for validation (e.g., password match, required fields, valid email format).

- **User Actions & API Calls:**
  1.  **User fills the form.** The `FileUpload` component immediately uploads the file to cloud storage (via a pre-signed URL fetched from `/api/upload-url`) and stores the returned URL in the form state.
  2.  **User clicks "DAFTAR".**
      - **Frontend Action:** The form's `onSubmit` handler triggers a Tanstack `useMutation`.
      - **API Call:** `POST /api/auth/register`
      - **Request Payload:**
        ```json
        {
          "email": "<EMAIL>",
          "password": "hashed_password",
          "name": "PIC Name",
          "companyName": "PT Sejahtera",
          "npwpNumber": "1234567890123456",
          "address": "Jl. Sudirman No. 1",
          // ...other fields
          "documents": [
            {
              "documentType": "NPWP",
              "fileUrl": "https://storage.cloud/npwp.pdf"
            },
            {
              "documentType": "SIUP",
              "fileUrl": "https://storage.cloud/siup.pdf"
            }
          ]
        }
        ```
  3.  **Backend Processing:**
      - **Endpoint:** `app/api/auth/register/route.ts`
      - **Logic:**
        1.  Validate the payload.
        2.  Check if `email` or `npwpNumber` already exist. If so, return 409 Conflict.
        3.  Start a Prisma transaction (`prisma.$transaction`).
        4.  Hash the password.
        5.  `prisma.user.create()` with `role: 'VENDOR'`.
        6.  `prisma.vendor.create()` linked to the new user, `verificationStatus: 'PENDING'`.
        7.  `prisma.document.createMany()` for all uploaded documents, linked to the new vendor.
        8.  Commit transaction.
        9.  Send a "Registration Successful, Awaiting Verification" email.
      - **Response:** `201 Created` with `{ "message": "Registration successful. Please wait for verification." }`
  4.  **Frontend displays a success message** in a `<Dialog>` and redirects to the login page.

---

#### **Flow 1.2: Admin Verifies Vendor**

- **User Story:** As an Admin, I want to review the list of newly registered vendors, check their submitted documents, and either approve their registration to make them active or reject it with a clear reason.

- **UI Screen:** `app/(main)/admin/vendors/page.tsx` (Admin role required)

  - **Layout:** Main app layout with sidebar.
  - **Main Component:** `VendorVerificationDashboard.tsx`
    - **UI Elements:**
      - `<Card>` with `<CardHeader>` ("Verifikasi Data Vendor").
      - `<Tabs>` with tabs: "Menunggu Verifikasi" (Pending), "Terverifikasi" (Verified), "Ditolak" (Rejected).
      - **`VendorVerificationTable.tsx` (under "Menunggu Verifikasi" tab):**
        - Powered by Tanstack Table. Fetches data for pending vendors.
        - **Columns:** `companyName`, `picName`, `registrationDate`, `action`.
        - The `action` column contains a `<Button>`: "Review".
  - **Dialog Component:** `VendorDetailDialog.tsx`
    - **Trigger:** Opens when the "Review" button is clicked.
    - **UI Elements:**
      - `<DialogHeader>` with the vendor's company name.
      - Read-only display of all `Vendor` and `User` fields.
      - A section "Dokumen Terlampir" that lists all submitted `Document`s. Each document is a link that opens the file in a new tab.
      - `<DialogFooter>` with action buttons.
  - **Action Component:** `VerificationActions.tsx` (inside the dialog footer)
    - **UI Elements:**
      - `<Button variant="destructive">` ("Tolak Verifikasi").
      - `<Button>` ("Setujui Verifikasi").
    - A `<Popover>` attached to the "Tolak" button, containing a `<Textarea name="rejectionReason">` and a final "Kirim Penolakan" button.

- **User Actions & API Calls:**
  1.  **Admin navigates to the verification page.**
      - **Frontend Action:** The `VendorVerificationTable` component's `useQuery` hook fires.
      - **API Call:** `GET /api/admin/vendors?status=PENDING`
      - **Backend Response:** An array of `Vendor` objects.
  2.  **Admin clicks "Review" on a vendor.**
      - **Frontend Action:** The `VendorDetailDialog` opens, populated with the data for that specific vendor. No new API call is needed as the data is already fetched.
  3.  **Admin clicks "Setujui Verifikasi".**
      - **Frontend Action:** Triggers a `useMutation`.
      - **API Call:** `PUT /api/admin/vendors/{vendorId}/status`
      - **Request Payload:** `{ "status": "VERIFIED" }`
      - **Backend Processing:**
        1.  `prisma.vendor.update({ where: { id: vendorId }, data: { verificationStatus: 'VERIFIED', verifiedAt: new Date() } })`.
        2.  Send a "Your Account is Verified" email to the vendor.
      - **Frontend Action:** The mutation's `onSuccess` callback invalidates the `['vendors', 'PENDING']` query key, causing the table to refetch and the approved vendor to disappear from the list. A success toast is shown.
  4.  **Admin clicks "Tolak Verifikasi", fills reason, and confirms.**
      - **Frontend Action:** Triggers the same `useMutation`.
      - **API Call:** `PUT /api/admin/vendors/{vendorId}/status`
      - **Request Payload:** `{ "status": "REJECTED", "rejectionReason": "NPWP document is not legible." }`
      - **Backend Processing:**
        1.  `prisma.vendor.update({ where: { id: vendorId }, data: { verificationStatus: 'REJECTED', rejectionReason: "..." } })`.
        2.  Send a "Registration Rejected" email to the vendor, including the reason.
      - **Frontend Action:** Same as approval: invalidate query, show toast.

---

### **Module 2: RFQ Procurement & Offer**

#### **Flow 2.1: Procurement User Creates and Publishes an RFQ**

- **User Story:** As a Procurement User, I want to create an RFQ for office supplies, specify the items and quantities needed, set the timeline, and publish it so that verified vendors can submit their offers.

- **UI Screen:** `app/(main)/procurements/page.tsx` and `app/(main)/procurements/new/page.tsx`

  - **Layout:** Main app layout. The index page shows a table of existing procurements with a "Buat Pengadaan Baru" button.
  - **Main Component:** `CreateProcurementForm.tsx` on the `/new` page.
    - **UI Elements:**
      - `<Input name="title">` (e.g., "Pengadaan Alat Tulis Kantor Q4 2023").
      - `<Select name="type">` (defaulted to `RFQ`).
      - `<Input name="ownerEstimate" type="number">` (HPS).
      - **`ProcurementItemsManager.tsx` (Dynamic Form Array):**
        - A component managed by `react-hook-form`'s `useFieldArray`.
        - Each row is a line item with `<Input name={`items.${index}.name`}>`, `<Input name={`items.${index}.quantity`} type="number">`, `<Input name={`items.${index}.unit`}>`.
        - A button "Tambah Item" adds a new row. Each row has a "Hapus" button.
      - **`StageScheduler.tsx`:**
        - A series of `<DatePicker>` components for `submissionEndDate`, `evaluationEndDate`, etc.
      - `<Button type="submit">` ("Simpan & Publikasikan").

- **User Actions & API Calls:**
  1.  **User fills the form.**
  2.  **User clicks "Simpan & Publikasikan".**
      - **Frontend Action:** Triggers `useMutation`.
      - **API Call:** `POST /api/procurements`
      - **Request Payload:**
        ```json
        {
          "title": "Pengadaan ATK Q4 2023",
          "type": "RFQ",
          "status": "PUBLISHED",
          "ownerEstimate": 50000000,
          "items": [
            {
              "name": "Joyko Binder Clips 200",
              "quantity": 1500,
              "unit": "Box"
            },
            {
              "name": "Joyko Binder Clips 155",
              "quantity": 1500,
              "unit": "Box"
            }
          ],
          "stages": [
            {
              "name": "Pemasukan Penawaran",
              "sequence": 1,
              "startDate": "...",
              "endDate": "..."
            },
            {
              "name": "Evaluasi dan Negosiasi",
              "sequence": 2,
              "startDate": "...",
              "endDate": "..."
            }
          ]
        }
        ```
  3.  **Backend Processing:**
      - **Endpoint:** `app/api/procurements/route.ts`
      - **Logic:**
        1.  Validate payload.
        2.  Create the `Procurement`, `ProcurementItem`, and `ProcurementStage` records in a single transaction.
        3.  Optionally, trigger notifications to all `VERIFIED` vendors.
      - **Response:** `201 Created` with the full new `Procurement` object.
  4.  **Frontend redirects** to the detail page for the newly created procurement: `/procurements/{newId}`.

---

#### **Flow 2.2: Vendor Submits an Offer**

- **User Story:** As a verified Vendor, I see a new RFQ for office supplies. I want to submit my prices for each item and upload my company profile as a technical document.

- **UI Screen:** `app/(main)/procurements/{id}/page.tsx`

  - **Layout:** A detailed view of the procurement.
  - **Main Component:** `OfferSubmissionWrapper.tsx`
    - **Logic:** This component checks if the current user is a `VENDOR` and if they have already submitted an offer for this procurement. If not, it renders the `OfferSubmissionForm.tsx`. Otherwise, it shows their submitted offer details.
  - **Component:** `OfferSubmissionForm.tsx`
    - **UI Elements:**
      - A read-only table listing the `ProcurementItem`s.
      - Next to each item, an `<Input name={`offerItems.${index}.offeredPrice`} type="number">`.
      - A `FileUpload` component for "Dokumen Penawaran".
      - A `<Button type="submit">` ("Kirim Penawaran").

- **User Actions & API Calls:**
  1.  **Vendor navigates to the procurement detail page.**
  2.  **Vendor fills in their prices and uploads a document.**
  3.  **Vendor clicks "Kirim Penawaran".**
      - **Frontend Action:** Triggers `useMutation`.
      - **API Call:** `POST /api/procurements/{id}/offers`
      - **Request Payload:**
        ```json
        {
          "offerItems": [
            { "itemId": "item_id_1", "offeredPrice": 28500 },
            { "itemId": "item_id_2", "offeredPrice": 1100 }
          ],
          "documents": [
            {
              "documentType": "TECHNICAL_OFFER",
              "fileUrl": "https://storage.cloud/profile.pdf"
            }
          ]
        }
        ```
  4.  **Backend Processing:**
      - **Endpoint:** `app/api/procurements/[id]/offers/route.ts`
      - **Logic:**
        1.  Verify the user is a `VENDOR` and the submission stage is active.
        2.  Check if this vendor has already submitted an offer.
        3.  Create the `VendorOffer`, `VendorOfferItem`, and `Document` records in a transaction. Set `status: 'SUBMITTED'`.
      - **Response:** `201 Created`.
  5.  **Frontend updates the UI** to show a "Penawaran Terkirim" status, hiding the form.

---

### **Module 3: Evaluation, Award & PO Generation**

This module condenses several steps from the manuals into a single workflow for the Procurement Committee.

#### **Flow 3.1: Committee Evaluates, Negotiates, and Awards a Winner**

- **User Story:** As a committee member, I want to see a side-by-side comparison of all submitted offers, evaluate them, negotiate with the top candidate, and officially award the procurement to the winner.

- **UI Screen:** `app/(main)/procurements/{id}/evaluation/page.tsx`

  - **Layout:** A master-detail view.
  - **Main Component:** `EvaluationDashboard.tsx`
    - **UI Elements:**
      - **`OfferComparisonTable.tsx`:** A Tanstack Table where each row is a `Vendor`. Columns include `vendorName`, `totalOfferedPrice`, `negotiatedPrice`, and `status`. This provides the "Tampilkan Berdasarkan Kategori" view.
      - **`NegotiationPanel.tsx`:** When a vendor row is selected for negotiation, this panel appears. It contains the `NegotiationChat.tsx` component.
      - **Action Buttons:** In the table, each vendor row has buttons like "Lakukan Negosiasi" and "Tetapkan Pemenang".

- **User Actions & API Calls:**
  1.  **Committee reviews offers.** They see all vendors in the table.
  2.  **Committee decides to negotiate with "PT KARYA SEHATI".** They click "Lakukan Negosiasi".
      - **Frontend Action:** Sets the state to show the `NegotiationPanel` for this vendor.
      - **API Call (for chat):** The chat component establishes a connection or starts polling `GET /api/discussions/{threadId}/messages`.
  3.  **During negotiation, the committee agrees on a final price.**
      - **Frontend Action:** An `<Input>` appears for the committee to enter the final price.
      - **API Call:** `PUT /api/offers/{offerId}/negotiate`
      - **Request Payload:** `{ "negotiatedPrice": 42000000 }`
      - **Backend Processing:** `prisma.vendorOffer.update({ where: { id: offerId }, data: { negotiatedPrice: ... } })`.
  4.  **Committee clicks "Tetapkan Pemenang".**
      - **Frontend Action:** Triggers a `useMutation`.
      - **API Call:** `POST /api/procurements/{id}/award`
      - **Request Payload:** `{ "winningOfferId": "offer_id_pt_karya" }`
      - **Backend Processing:**
        1.  In a transaction, update the winning `VendorOffer` status to `WINNER`.
        2.  Update all other `VendorOffer`s for this procurement to `LOSER`.
        3.  Update the parent `Procurement` status to `AWARDED`.
      - **Response:** `200 OK`.
  5.  **Frontend UI updates.** A prominent "Buat Purchase Order" button now appears at the top of the page.

#### **Flow 3.2: Generating the Purchase Order and Invoicing**

- **User Story (Procurement User):** Now that a winner is awarded, I want to generate the official Purchase Order.
- **User Story (Vendor):** After delivering the goods and getting a BAST, I want to submit my invoice through the system.
- **User Story (Approver):** I want to review and approve the submitted invoice so the vendor can be paid.

- **This flow directly follows the design from the previous response's Module 5 and 6.** It involves:
  1.  **PO Creation:** `POST /api/po`
  2.  **BAST Creation & Approval:** `POST /api/bast`, `PUT /api/bast/{id}/process`
  3.  **Invoice Submission:** `POST /api/invoices`
  4.  **Invoice Approval:** `PUT /api/invoices/{id}/process`

---

### **3. Core System Components (New Section)**

#### **3.1 Document Generation Engine**

- **Objective:** To dynamically generate standardized PDF documents (e.g., Surat Undangan, Berita Acara, Purchase Order) populated with data from the database.
- **Technology Choice:** A server-side PDF generation library. `Puppeteer` (which runs a headless Chromium instance) is a powerful choice for rendering HTML/CSS into a pixel-perfect PDF. A lighter alternative could be `pdf-lib` if templates are simple. We will design for Puppeteer due to its flexibility.
- **Implementation:**
  1.  **Templates:** Create a directory `app/lib/document-templates/` containing React components for each document type (e.g., `PurchaseOrderTemplate.tsx`, `BastTemplate.tsx`). These components will accept props corresponding to the data they need to display (e.g., `poData`, `vendorData`).
  2.  **Generation Endpoint:** Create a dedicated API endpoint: `POST /api/documents/generate`
      - **Request Payload:**
        ```json
        {
          "templateName": "PurchaseOrder",
          "data": { "poId": "..." }
        }
        ```
      - **Backend Logic:**
        a. The endpoint fetches all required data from the database based on the payload (e.g., `prisma.purchaseOrder.findUnique({ where: { id: poId }, include: { ... } })`).
        b. It renders the corresponding React template component to an HTML string on the server (`ReactDOMServer.renderToStaticMarkup(<PurchaseOrderTemplate data={...} />)`).
        c. It launches a Puppeteer instance, sets the content to the rendered HTML, and generates a PDF buffer.
        d. It uploads this PDF buffer to the cloud storage.
        e. It returns the URL of the newly generated PDF.
  3.  **Usage:** When a user clicks a "Print PO" or "Download Berita Acara" button, the frontend calls this endpoint. The returned URL is then used to either open the PDF in a new tab or trigger a download.

#### **3.2 Notification System**

- **Objective:** To proactively inform users (Vendors, Admins, Approvers) about critical events via email and in-app notifications.
- **Technology Choice:**
  - **Email:** `Nodemailer` or a transactional email service like `Resend`, `Postmark`, or `SendGrid`. We'll design for `Resend` as it integrates well with React (using `react-email` for templates).
  - **In-App:** A new `Notification` model in Prisma.
- **Implementation:**
  1.  **Email Templates:** Create a directory `emails/` with React components for each email type (e.g., `VendorVerifiedEmail.tsx`, `NewProcurementEmail.tsx`).
  2.  **Notification Service:** Create a service file `app/lib/notificationService.ts`.
      - `export async function sendNotification(params: { userId, title, message, emailData?: {...} })`
      - **Logic:**
        a. Creates a record in the `Notification` table in the database linked to the `userId`.
        b. If `emailData` is provided, it fetches the user's email, renders the appropriate `react-email` template, and sends it via the chosen email service.
  3.  **Trigger Points:** This service will be called from various API endpoints after a successful database transaction. For example, after an admin verifies a vendor, the `PUT /api/admin/vendors/{vendorId}/status` endpoint will call `notificationService.sendNotification(...)`.

---

### **4. Utility & Security Flows (New Section)**

#### **4.1 Password Management**

- **User Story (Forgot Password):** As a registered user who has forgotten their password, I want to request a password reset link to be sent to my registered email address.
- **User Story (Reset Password):** After receiving the link, I want to be able to set a new password for my account.

- **Database Changes:**

  - Add fields to the `User` model:
    ```prisma
    model User {
      // ... existing fields
      passwordResetToken       String?   @unique
      passwordResetTokenExpiry DateTime?
    }
    ```

- **UI Screens & Components:**

  - **`app/(auth)/forgot-password/page.tsx`**: A simple page with a single `<Input name="email">` and a "Kirim Link Reset" `<Button>`.
  - **`app/(auth)/reset-password/page.tsx`**: This page expects a `token` in the URL query params (`?token=...`). It contains two `<Input>` fields: `newPassword` and `confirmPassword`, and a "Setel Ulang Password" `<Button>`.

- **User Actions & API Calls:**
  1.  **User enters email and requests reset link.**
      - **API Call:** `POST /api/auth/forgot-password`
      - **Request Payload:** `{ "email": "<EMAIL>" }`
      - **Backend Logic:**
        a. Find the user by email. If not found, return a generic success message to prevent email enumeration.
        b. Generate a secure, random token (e.g., `crypto.randomBytes(32).toString('hex')`).
        c. Hash the token before storing it in the database for security.
        d. `prisma.user.update()` to set `passwordResetToken` (hashed) and `passwordResetTokenExpiry` (e.g., 1 hour from now).
        e. Send a password reset email (using the Notification System) containing a link like `https://yourapp.com/reset-password?token={unhashed_token}`.
  2.  **User clicks the link in the email and sets a new password.**
      - **API Call:** `POST /api/auth/reset-password`
      - **Request Payload:** `{ "token": "...", "password": "..." }`
      - **Backend Logic:**
        a. Hash the incoming `token`.
        b. Find a user with a matching `passwordResetToken` where `passwordResetTokenExpiry` is in the future.
        c. If no user is found or the token is expired, return a 400 Bad Request error.
        d. If valid, hash the new password.
        e. `prisma.user.update()` to set the new `password` and nullify the `passwordResetToken` and `passwordResetTokenExpiry` fields.
      - **Frontend Action:** On success, redirect to the login page with a success message.

---

### **5. Detailed Business Flows (Enhanced)**

Now, we integrate these new components into the main flows.

#### **Flow 1.1: New Vendor Registration (Enhanced)**

- **... (steps 1-3 remain the same)**
- **Backend Processing (Step 3, enhanced):**
  - ... (after transaction commit)
  - **Trigger Notification:** `notificationService.sendNotification({ userId: newAdminUser.id, title: "Vendor Baru Mendaftar", message: `${vendor.companyName} telah mendaftar dan menunggu verifikasi.` })`.
- **...**

#### **Flow 1.2: Admin Verifies Vendor (Enhanced)**

- **... (steps 1-2 remain the same)**
- **Admin clicks "Setujui Verifikasi" (Step 3, enhanced):**
  - **... (backend processing)**
  - **Trigger Notification:** `notificationService.sendNotification({ userId: vendor.userId, title: "Akun Anda Telah Diverifikasi", message: "Selamat! Akun Anda telah disetujui. Anda sekarang dapat mengikuti proses pengadaan.", emailData: { template: 'vendorVerified', ... } })`.
- **Admin clicks "Tolak Verifikasi" (Step 4, enhanced):**
  - **... (backend processing)**
  - **Trigger Notification:** `notificationService.sendNotification({ userId: vendor.userId, title: "Verifikasi Akun Ditolak", message: `Verifikasi akun Anda ditolak. Alasan: ${rejectionReason}`, emailData: { template: 'vendorRejected', ... } })`.

#### **Flow 2.1: Procurement User Creates and Publishes an RFQ (Enhanced)**

- **... (steps 1-3 remain the same)**
- **Backend Processing (Step 3, enhanced):**
  - ... (after transaction commit)
  - **Trigger Notification:**
    - Fetch all `VERIFIED` vendors.
    - Loop through them and call `notificationService.sendNotification({ userId: vendor.userId, title: `Pengadaan Baru: ${procurement.title}`, ..., emailData: { ... } })`. This is a background job candidate.

#### **Flow 3.2: Generating the Purchase Order (Enhanced)**

- **User Story:** After a winner is awarded, the Procurement User generates the official PO, which should be a standardized PDF document.

- **UI Screen:** `app/(main)/procurements/{id}/evaluation/page.tsx`
- **User Action:** User clicks the "Buat Purchase Order" button.
  - **Frontend Action:** A dialog opens confirming the action. On confirmation, a `useMutation` is triggered.
  - **API Call 1 (Create PO Record):** `POST /api/po`
    - **Request Payload:** `{ "procurementId": "...", "vendorId": "..." }`
    - **Backend Processing:** Creates the `PurchaseOrder` and `PurchaseOrderItem` records in the database. Returns the new `poId`.
  - **Frontend Action (onSuccess of API Call 1):**
    - A loading state is shown ("Generating PDF...").
    - A second API call is made.
  - **API Call 2 (Generate PDF):** `POST /api/documents/generate`
    - **Request Payload:** `{ "templateName": "PurchaseOrder", "data": { "poId": "{newPoId}" } }`
  - **Backend Processing:**
    - The Document Generation Engine runs, fetches the PO data, creates the PDF, and saves it to cloud storage.
    - It then updates the `PurchaseOrder` record by creating a linked `Document` record with the PDF's URL.
    - Returns the final `Document` object with the file URL.
  - **Frontend Action (onSuccess of API Call 2):**
    - The loading state is removed.
    - A "Download PO" button appears, linked to the returned file URL.
  - **Trigger Notification:** The backend, after successful PO creation, calls `notificationService.sendNotification({ userId: winningVendor.userId, title: `Anda Menerima Purchase Order Baru #${po.poNumber}`, ... })`.
