# Storage Configuration Guide

This guide explains how to configure file storage for public assets in the e-procurement system. The system supports both local file storage and S3-compatible object storage (including AWS S3, Cloudflare R2, MinIO, and other S3-compatible services).

## Environment Variables

### Storage Type Selection

```bash
# Choose storage type: 'local' or 's3'
STORAGE_TYPE=local  # or 's3'
```

### Local Storage Configuration

When using `STORAGE_TYPE=local`:

```bash
# Local file storage settings
LOCAL_UPLOAD_PATH=./public/uploads          # Directory to store uploaded files
LOCAL_PUBLIC_URL=/uploads                   # Public URL prefix for accessing files
```

**Important Notes for Local Storage:**
- Ensure the upload directory has proper write permissions
- The directory will be created automatically if it doesn't exist
- Files are served directly from the filesystem
- Consider using a reverse proxy (nginx) for better performance in production

### S3-Compatible Storage Configuration

When using `STORAGE_TYPE=s3`:

```bash
# S3-compatible storage settings
S3_ENDPOINT=                                # Optional: Custom endpoint for S3-compatible services
S3_REGION=us-east-1                        # AWS region or 'auto' for some services
S3_BUCKET=public-assets                     # Bucket name for storing files
S3_ACCESS_KEY_ID=your_access_key           # Access key ID
S3_SECRET_ACCESS_KEY=your_secret_key       # Secret access key
S3_PUBLIC_URL=                             # Optional: Custom domain for public access
S3_FORCE_PATH_STYLE=false                  # Set to 'true' for path-style URLs
```

## Service-Specific Configurations

### AWS S3

```bash
STORAGE_TYPE=s3
S3_REGION=us-east-1
S3_BUCKET=your-bucket-name
S3_ACCESS_KEY_ID=AKIA...
S3_SECRET_ACCESS_KEY=...
# S3_ENDPOINT is not needed for AWS S3
# S3_PUBLIC_URL can be set if using CloudFront
```

### Cloudflare R2

```bash
STORAGE_TYPE=s3
S3_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
S3_REGION=auto
S3_BUCKET=your-bucket-name
S3_ACCESS_KEY_ID=your-r2-access-key
S3_SECRET_ACCESS_KEY=your-r2-secret-key
S3_PUBLIC_URL=https://your-custom-domain.com  # If using custom domain
S3_FORCE_PATH_STYLE=true
```

### MinIO (Self-hosted)

```bash
STORAGE_TYPE=s3
S3_ENDPOINT=https://your-minio-server.com
S3_REGION=us-east-1
S3_BUCKET=public-assets
S3_ACCESS_KEY_ID=minioadmin
S3_SECRET_ACCESS_KEY=minioadmin
S3_FORCE_PATH_STYLE=true
```

### DigitalOcean Spaces

```bash
STORAGE_TYPE=s3
S3_ENDPOINT=https://nyc3.digitaloceanspaces.com
S3_REGION=nyc3
S3_BUCKET=your-space-name
S3_ACCESS_KEY_ID=your-spaces-key
S3_SECRET_ACCESS_KEY=your-spaces-secret
S3_PUBLIC_URL=https://your-space-name.nyc3.cdn.digitaloceanspaces.com
```

### Wasabi

```bash
STORAGE_TYPE=s3
S3_ENDPOINT=https://s3.wasabisys.com
S3_REGION=us-east-1
S3_BUCKET=your-bucket-name
S3_ACCESS_KEY_ID=your-wasabi-key
S3_SECRET_ACCESS_KEY=your-wasabi-secret
```

## Security Considerations

### File Validation
- All uploaded files are validated for type and size
- File content is scanned for security threats
- Only allowed file types are accepted (images, PDFs)
- Maximum file size limits are enforced

### Access Control
- Files are stored with public read access for public assets
- Secure filename generation prevents path traversal attacks
- File metadata is stored separately in the database

### Storage Security
- Use IAM roles with minimal required permissions for S3
- Enable bucket versioning and logging where possible
- Consider enabling server-side encryption
- Use HTTPS endpoints for all S3-compatible services

## Bucket Permissions (S3-Compatible Services)

### Required S3 Permissions

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:PutObjectAcl",
                "s3:GetObject",
                "s3:DeleteObject"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name"
        }
    ]
}
```

### Bucket Policy for Public Read Access

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        }
    ]
}
```

## Testing Storage Configuration

The system provides utilities to test your storage configuration:

```typescript
import { testStorageConnection, getStorageInfo } from '@/lib/storage/public-assets-storage';

// Test if storage is properly configured
const connectionTest = await testStorageConnection();
console.log('Storage connection:', connectionTest);

// Get current storage configuration
const storageInfo = getStorageInfo();
console.log('Storage info:', storageInfo);
```

## File Organization

Files are organized in the following structure:

```
/uploads (or S3 bucket)
├── news/
│   ├── 1640995200000_a1b2c3d4_announcement.jpg
│   └── 1640995300000_e5f6g7h8_update.pdf
├── announcements/
│   └── 1640995400000_i9j0k1l2_notice.png
└── banners/
    └── 1640995500000_m3n4o5p6_banner.jpg
```

## Monitoring and Maintenance

### Cleanup Expired Assets

```typescript
import { cleanupExpiredAssets } from '@/lib/storage/public-assets-storage';

// Clean up expired assets
const result = await cleanupExpiredAssets();
console.log(`Deleted ${result.deletedCount} expired assets`);
if (result.errors.length > 0) {
    console.error('Cleanup errors:', result.errors);
}
```

### Storage Statistics

```typescript
import { getPublicAssetStats } from '@/lib/storage/public-assets-storage';

const stats = await getPublicAssetStats();
console.log('Storage stats:', stats);
```

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check IAM permissions and bucket policies
2. **Endpoint Not Found**: Verify S3_ENDPOINT URL format
3. **Access Denied**: Ensure bucket exists and credentials are correct
4. **Path Style Issues**: Try setting S3_FORCE_PATH_STYLE=true
5. **Local Storage Issues**: Check directory permissions and disk space

### Debug Mode

Enable debug logging by setting:

```bash
DEBUG=storage:*
```

This will provide detailed logging for storage operations.
