### **Ultimate Implementation Guide for AI Coding Assistant**

**Objective:** To implement the full, nuanced business logic and contextual user interfaces for the e-procurement system, as detailed in the user manuals and our previous analyses.

**Guiding Principles:**

1.  **State-Driven UI:** The UI must react to the status of entities. Buttons, forms, and data should appear, disappear, or become read-only based on the current state of a procurement, PR, or vendor.
2.  **Workflow Integration:** API endpoints are not just for CRUD. They are triggers for larger business processes. A `POST` to `/release` must start an approval workflow. A `PUT` to `/approve` must advance it.
3.  **User-Centric Terminology:** All UI labels, buttons, and messages must use the precise Indonesian terminology found in the manuals (e.g., "Paket Pekerjaan", "Koreksi Aritmatik", "Susunan Tim Pengadaan").
4.  **Immutability and Auditability:** For critical actions (pricing, approvals, receipts), prefer creating new log records over updating existing fields.

---

### **Phase 1: Foundational Workflow - Purchase Requisition (PR)**

**Goal:** Implement the complete PR lifecycle, from creation to its conversion into a formal procurement package. This is the starting point for all procurement.

**Step 1.1: Build the PR Creation API (`POST /api/purchase-requisitions`)**

- **File:** Create a new route handler at `src/app/api/purchase-requisitions/route.ts`.
- **Input:** The handler should accept a request body validated by `purchaseRequisitionSchema` from `lib/validations/purchase-requisition.ts`.
- **Business Logic:**
  1.  Get the current authenticated user (`requesterId`).
  2.  Generate a unique `prNumber` (e.g., `PR-YYYYMMDD-XXXX`).
  3.  Check the `type` field. If `type` is `EXTERNAL_ROUTINE` and `sourceContractId` is provided, fetch the `Contract` from the database. Validate that the contract exists and is active.
  4.  Create the `PurchaseRequisition` record in the database with `status: 'DRAFT'`.
  5.  Create all associated `PurchaseRequisitionItem` records, linking them to the new PR.
  6.  If `sourceContractId` was provided, you might pre-populate items or vendor info based on the contract (design choice).
  7.  Return the newly created PR object.

**Step 1.2: Build the PR Release API (`POST /api/purchase-requisitions/[id]/release`)**

- **File:** Create a new route handler at `src/app/api/purchase-requisitions/[id]/release/route.ts`.
- **Input:** The `id` of the PR from the URL.
- **Business Logic:**
  1.  Fetch the `PurchaseRequisition` by its `id`. Ensure its status is `DRAFT` or `REJECTED`. If not, throw an error.
  2.  **Crucial Integration:** Call the `dynamicApprovalEngine.startApprovalWorkflow()` method.
      - `entityType`: `'PURCHASE_REQUISITION'`
      - `entityId`: The PR's `id`.
      - `entityData`: The full PR object, including its items and total value.
      - `startedBy`: The ID of the current user.
  3.  Update the `PurchaseRequisition` status to `PENDING_APPROVAL`.
  4.  Create a user notification for the requester: "PR Anda [PR Number] telah diajukan untuk persetujuan."
  5.  Return a success message.

**Step 1.3: Create the PR Management UI (`/purchase-requisitions`)**

- **File:** Create a new page component at `src/app/(main)/purchase-requisitions/page.tsx` and a client component `src/components/procurement/pr-list.tsx`.
- **Functionality:**
  1.  Use `@tanstack/react-query` to fetch data from `GET /api/purchase-requisitions`.
  2.  Implement a `Tabs` component with triggers for: "Draft", "Menunggu Persetujuan" (`PENDING_APPROVAL`), "Disetujui" (`APPROVED`), "Ditolak" (`REJECTED`), and "Terkonsolidasi" (`CONSOLIDATED`).
  3.  The "Disetujui" (`APPROVED`) tab is special:
      - Each row in the table must have a `Checkbox`.
      - A button labeled **"Buat Paket Pengadaan"** (Create Procurement Package) should be visible. This button is disabled by default. It becomes enabled only when one or more checkboxes are checked.
  4.  Each row in the table should have a "Detail" button linking to `/purchase-requisitions/[id]`.

**Step 1.4: Create the PR Detail Page UI (`/purchase-requisitions/[id]`)**

- **File:** Create a new page component at `src/app/(main)/purchase-requisitions/[id]/page.tsx` and a client component `src/components/procurement/pr-detail.tsx`.
- **Functionality:**
  1.  Fetch the specific PR data from `GET /api/purchase-requisitions/[id]`.
  2.  Display all PR details: title, number, status, items, documents, etc.
  3.  **Contextual Actions (This is critical for user flow):**
      - If `status` is `DRAFT` or `REJECTED`, show an **"Ajukan Persetujuan"** (Submit for Approval) button. This button calls the `/release` API from Step 1.2.
      - If `status` is `PENDING_APPROVAL`, show a read-only view of the approval status (e.g., "Menunggu persetujuan dari [Approver Name]").
      - If `status` is `APPROVED`, show a green badge "Disetujui". No action buttons should be visible here, as the next step happens on the list page (consolidation).

---

### **Phase 2: Post-Award Workflow - Good Receipt & BAST**

**Goal:** Implement the physical receipt and official handover processes, ensuring a complete and auditable chain from PO to final acceptance.

**Step 2.1: Build the Good Receipt Creation API (`POST /api/good-receipts`)**

- **File:** Create `src/app/api/good-receipts/route.ts`.
- **Input:** Validated by `goodReceiptSchema`. The `items` array will contain `purchaseOrderItemId` and `receivedQuantity`.
- **Business Logic:**
  1.  Start a database transaction.
  2.  Create the main `GoodReceipt` record, linking it to the `poId`.
  3.  Loop through the input `items`. For each item:
      - Create a `GoodReceiptItem` record.
      - **Crucial:** Do not just store `receivedQuantity`. Instead, create a `ReceiptLog` entry with `quantityChange: receivedQuantity`. The `receivedQuantity` on the `GoodReceiptItem` model should be a computed value (or updated via a trigger) by summing its logs. This creates an immutable trail.
  4.  Update the status of the `PurchaseOrder` to indicate that a GR has been created (e.g., add a new status like `PARTIALLY_RECEIVED` or `GOODS_RECEIVED`).
  5.  **Workflow Trigger:** Send a notification to the vendor: "Barang untuk PO [PO Number] telah diterima. Anda sekarang dapat membuat BAST."
  6.  Commit the transaction.

**Step 2.2: Implement the Good Receipt Form UI**

- **File:** `src/components/good-receipt/good-receipt-form.tsx`.
- **Functionality:**
  1.  This form should be launched from a "Buat GR" button on a `PurchaseOrder` detail page. The `poId` is passed as a prop.
  2.  Use `useQuery` to fetch the details of the selected PO, including its items.
  3.  The form should list all `PurchaseOrderItem`s from the PO.
  4.  Next to each item, there should be an input field for **"Jumlah Diterima"** (Quantity Received).
  5.  As the user types in quantities, validate that the received quantity does not exceed the ordered quantity. Show a warning if it does.
  6.  The "Simpan Good Receipt" button calls the API from Step 2.1.

**Step 2.3: Build the BAST Creation API (`POST /api/bast`)**

- **File:** Create `src/app/api/bast/route.ts`. This is a **vendor-facing** endpoint.
- **Input:** Validated by `bastSchema`. It will include `poId`, `handoverDate`, `summary`, and an array of `checklist` items.
- **Business Logic:**
  1.  Verify the current user is a vendor and is the owner of the specified `poId`.
  2.  Create the `BAST` record with `status: 'DRAFT'`.
  3.  Create all associated `BastChecklistItem` records.
  4.  When the vendor submits (not just saves draft), update the BAST `status` to `PENDING_APPROVAL`.
  5.  **Crucial Integration:** Call `dynamicApprovalEngine.startApprovalWorkflow()` for `entityType: 'BAST'`. This kicks off the internal approval process.
  6.  Return the created BAST object.

**Step 2.4: Implement the BAST Creation UI (for Vendors)**

- **File:** Create a new component `src/components/bast/bast-form.tsx`.
- **Functionality:**
  1.  This form is accessible to vendors from their dashboard or a PO detail page (a "Buat BAST" button should appear once a GR is completed).
  2.  The form must contain fields for:
      - `Tanggal Serah Terima` (Handover Date) - Date picker.
      - `Nomor PO` - A dropdown to select a completed PO.
      - `Ringkasan` (Summary) - A `Textarea`.
      - **Form Checklist:** This is a dynamic section managed by `useFieldArray`. The vendor can add rows. Each row has inputs for `Item Pekerjaan`, `Defect`, and `Target Selesai` (as seen on Page 11 of the GR/BAST manual).
      - File upload for supporting documents.
  3.  Provide two buttons: "Simpan Draft" (saves with `status: 'DRAFT'`) and "Ajukan Persetujuan" (saves and sets `status: 'PENDING_APPROVAL'`, triggering the workflow).

**Step 2.5: Implement the BAST Approval UI (for Internal Users)**

- **File:** Create a new component `src/components/approvals/bast-approval-dashboard.tsx`.
- **Functionality:**
  1.  Fetch all BASTs with `status: 'PENDING_APPROVAL'` where the current user is a designated approver.
  2.  Display a list of pending BASTs.
  3.  Clicking "Review" on a BAST opens a modal showing all BAST details, including the checklist items and documents.
  4.  The modal must display the full approval history timeline, as designed previously (showing Department, Verifier, Status, Timestamp).
  5.  Provide "Setujui" (Approve) and "Tolak" (Reject) buttons. These buttons call new API endpoints (`PUT /api/bast/[id]/approve` and `.../reject`) which handle the approval logic and advance the workflow.

---

### **Phase 3: Critical Compliance and UX Refinements**

**Goal:** Implement the nuanced features that are critical for compliance, risk management, and matching the user's expected workflow.

**Step 3.1: Implement Granular Vendor Verification**

- **File:** Refactor `src/components/admin/vendor-verification-dashboard.tsx`.
- **Functionality:**
  1.  When an admin clicks "Review" on a vendor, the modal should display a `Tabs` component.
  2.  Create tabs with the exact names from the manual: "Surat Pernyataan", "Administrasi", "Izin Usaha", "Akta Perusahaan", "Pajak", etc.
  3.  Each tab's content displays the relevant data and documents for that section.
  4.  At the bottom of each tab, an "Approve [Tab Name]" button is shown. **This button is only visible and enabled if the logged-in user has the specific role required to approve that section** (e.g., `FINANCE_APPROVER` for the "Pajak" tab).
  5.  Clicking the button calls a new API endpoint: `PUT /api/admin/vendors/[vendorId]/verify-step`.
- **API (`PUT /api/admin/vendors/[vendorId]/verify-step`):**
  - **Input:** `{ stepName: string, status: 'APPROVED' | 'REJECTED', comments: string }`.
  - **Logic:**
    1.  Updates the specific `VendorVerificationStep` record for that vendor and `stepName`.
    2.  After updating, it checks if all _required_ verification steps for that vendor are now `APPROVED`.
    3.  If all are approved, it automatically updates the main `Vendor.status` to `VERIFIED` and sends the "Vendor Verified" notification.

**Step 3.2: Implement "Koreksi Aritmatik" (Arithmetic Correction)**

- **File:** Build out `src/components/procurement/price-correction-interface.tsx`.
- **Functionality:**
  1.  On the offer evaluation screen, next to each `VendorOfferItem`'s price, add a small "Koreksi" (Correct) button for committee members.
  2.  Clicking this button opens the `PriceCorrectionInterface` modal.
  3.  The modal displays: Original Price, a field for "Harga Koreksi" (Corrected Price), and a `Textarea` for "Alasan Koreksi" (Reason for Correction).
  4.  The "Simpan Koreksi" button calls a new API endpoint.
- **API (`POST /api/offers/items/[id]/correct-arithmetic`):**
  - **Input:** `{ correctedPrice: number, reason: string }`.
  - **Logic:**
    1.  Creates a `PriceCorrectionLog` record to immutably store the change.
    2.  Updates the `correctedPrice` and `correctionReason` fields on the `VendorOfferItem` record.
    3.  Logs this critical action in the main `AuditLog`.

**Step 3.3: Implement "Buka Lock" (Vendor Profile Unlock)**

- **File:** Add a "Minta Ubah Data" (Request Data Change) button on the vendor's main profile page (`/vendor/profile`).
- **Functionality:**
  1.  Clicking the button opens a modal where the vendor selects which data sections they want to edit and provides a reason.
  2.  Submitting this form calls `POST /api/vendor/profile/request-unlock`.
- **API (`POST /api/vendor/profile/request-unlock`):**
  - Creates a notification/task for the `ADMIN` role.
- **Admin UI:**
  - Create a new section in the admin dashboard for "Permintaan Buka Lock".
  - Admins can view the request and see the reason.
  - An "Approve" button opens a small modal where the admin sets the `unlockExpiryDate` (e.g., "Valid for 48 hours").
  - Approving calls `PUT /api/admin/vendors/[vendorId]/grant-unlock`.
- **API (`PUT /api/admin/vendors/[vendorId]/grant-unlock`):**
  - Sets `isLocked = false` and `unlockExpiryDate` on the `Vendor` model.
  - Sends a notification to the vendor: "Profil Anda telah dibuka untuk pembaruan hingga [expiry_date]."
- **Backend Cron Job:**
  - Create a scheduled job (e.g., runs every hour) that queries for vendors where `isLocked = false` and `unlockExpiryDate < NOW()`. For any matches, it sets `isLocked = true`.

---

Implement this after you have completed the "Ultimate Design Document."

### **Final Strategic Gap Analysis: The Path from Excellent to Perfect**

The "Ultimate Design Document" will produce an A-grade system. To get to A+, we must address these conceptual and strategic gaps inherent in the source material.

| Strategic Domain                             | What the "Ultimate Design" Achieves                                                       | The "Perfection" Gap (What the Manuals Don't Cover)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | Business Impact of the Gap                                                                                                                                                                                                                          |
| :------------------------------------------- | :---------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1. Data Intelligence & Analytics**         | The system captures data (PRs, POs, Offers, etc.) and has foundational models for KPIs.   | - **No Spend Analytics Engine:** The system can't answer "How much did we spend on laptops across all departments last quarter?". It lacks a standardized `ItemMaster` catalog.<br>- **No Proactive Analytics:** It doesn't analyze procurement cycle times, identify bottlenecks, or track cost savings (HPS vs. awarded price) automatically.<br>- **No Supplier Performance Trending:** It can score a vendor but doesn't track their performance trend over time to identify consistent high-performers or those in decline.                                                                                                                            | **Missed Strategic Value.** The system becomes a transactional tool, not a strategic one. The organization cannot optimize spending, identify negotiation opportunities, or manage supplier relationships proactively. It remains reactive.         |
| **2. Automation & Process Efficiency**       | Workflows are implemented for approvals (PRs, BASTs).                                     | - **No Automated 3-Way Matching:** The crucial process of matching the Purchase Order, Good Receipt, and Invoice to approve payment is entirely manual. This is a major source of error and inefficiency in finance departments.<br>- **No Automated Document Generation:** The system stores data but requires users to manually create documents like POs or Contracts. The `DocumentTemplate` engine is not yet leveraged for auto-generation.<br>- **No Intelligent Workflow Routing:** The approval engine is good but doesn't support advanced rules like "If PR value > $10,000, add Head of Finance to the approval chain."                         | **Operational Inefficiency.** The system automates submission but not the high-volume, repetitive work. This leads to higher administrative overhead, longer payment cycles, and increased risk of human error in financial processing.             |
| **3. Risk Management & Advanced Compliance** | The system has user roles, blacklisting, and audit logs.                                  | - **No Proactive Risk Detection:** The audit log is reactive. It doesn't actively scan for suspicious patterns like a user approving their own request, multiple failed logins from a single IP, or after-hours access to sensitive data.<br>- **No Sanctioned Individuals Ledger:** Blacklisting a company is good, but a sanctioned individual can simply register a new company. There's no global list to check against.<br>- **No Document Version Control:** When a vendor updates a legal document (e.g., SIUP), the old one is replaced, not archived. This breaks the historical audit trail for compliance.                                       | **Incomplete Risk Mitigation.** The system is vulnerable to sophisticated internal fraud and external threats. It cannot prove historical compliance if legal documents are overwritten, posing a significant legal and audit risk.                 |
| **4. Vendor Relationship Management (VRM)**  | Vendors can register, submit offers, and create BASTs. The system has a Vendor KPI model. | - **The Portal is a Mailbox, Not a Hub:** The vendor portal is transactional. It lacks features for true self-service, like tracking the detailed status of their POs, invoices, and payments.<br>- **No Performance Feedback Loop:** Vendors are evaluated, but there is no mechanism to show them their own performance scores and how they rank against anonymized peers. This removes the incentive for them to improve.<br>- **No Onboarding Gamification:** The vendor registration process is a long form. It doesn't guide the vendor with a visual checklist or a profile completeness score, increasing the likelihood of incomplete submissions. | **Poor Supplier Engagement.** The system treats vendors as mere data-entry points, not partners. This leads to higher support costs, lower quality submissions, and a missed opportunity to foster a competitive and high-performing supplier base. |

---

### **The "Perfection" Blueprint: Final Enhancements for an A+ System**

#### **Enhancement Module 1: Strategic Sourcing & Spend Analytics Engine**

**Goal:** Transform the system from a transactional record-keeper into a strategic decision-making tool.

- **1.1. Implement `ItemMaster` Catalog:**

  - **Action:** Create the `ItemMaster` model as designed previously.
  - **UI:** Create an admin interface at `/admin/item-catalog` for managing this master list (adding, editing, categorizing items).
  - **Integration:** In the `PurchaseRequisitionItem` form, change the "Item Name" input to an autocomplete search box that queries the `ItemMaster`. If a user selects a master item, the `description`, `unit`, and `category` fields are auto-filled. Allow a "create new item" option for non-catalog requests, which flags it for admin review.

- **1.2. Build the Analytics Dashboard:**
  - **Action:** Create a new database view or a set of summary tables (e.g., `ProcurementAnalyticsSummary`) that are updated nightly by a cron job.
  - **Calculated Fields:** This job will calculate and store metrics like:
    - `cycleTime_PR_to_PO`: Time difference between PR approval and PO creation.
    - `costSavings`: `Procurement.ownerEstimate` - `VendorOffer.awardedPrice`.
    - `spendByCategory`: Aggregated spend grouped by `ItemMaster.category`.
  - **UI:** Create a new page at `/analytics` with data visualizations (use a library like Recharts or Chart.js) to display spend analysis, cycle time bottlenecks, and savings reports.

#### **Enhancement Module 2: Hyper-Automation Engine**

**Goal:** Eliminate high-volume manual work and reduce the risk of human error in financial processing.

- **2.1. Implement Automated 3-Way Matching:**

  - **Action:** Create a new model, `ThreeWayMatch`, linking `PurchaseOrder`, `GoodReceipt`, and `Invoice`.
  - **Business Logic:** Create a backend service that runs when an invoice is submitted.
    1.  It fetches the corresponding PO and all related GRs.
    2.  It compares `Invoice.lineItems.quantity` against the SUM of `GoodReceiptItem.receivedQuantity` for that `PurchaseOrderItem`.
    3.  It compares `Invoice.lineItems.price` against `PurchaseOrderItem.price`.
    4.  If quantities and prices match within a configurable tolerance (e.g., +/- 2%), the `ThreeWayMatch` status is set to `MATCHED`, and the Invoice approval workflow is **automatically advanced to the final payment step.**
    5.  If there's a discrepancy, the status is `MISMATCHED`, and a notification with the specific discrepancy details is sent to the finance team. The invoice is locked from approval until resolved.

- **2.2. Implement Automated Document Generation:**
  - **Action:** Create a `DocumentGenerator` service that uses the `DocumentTemplate` engine.
  - **Integration:**
    - When a procurement is `AWARDED`, automatically call the generator to create the `PurchaseOrder` PDF using the `PURCHASE_ORDER` template and data from the winning `VendorOffer`.
    - When a BAST is `APPROVED`, automatically call the generator to create the official BAST PDF. Attach this PDF to the final notification.

#### **Enhancement Module 3: Proactive Risk & Compliance Management**

**Goal:** Move from a reactive audit trail to a proactive system that detects and flags risks as they happen.

- **3.1. Implement the Sanctioned Individuals Ledger:**

  - **Action:** Create the `SanctionedIndividual` model as designed.
  - **Integration:**
    1.  Modify the `POST /api/auth/register` handler. After Zod validation, query the `SanctionedIndividual` table against the `picName` and `picIdentityNumber` from the registration form. If a match is found, reject the registration with a generic error and send a high-priority alert to the compliance team.
    2.  On the vendor blacklisting form, add a section to "Add individuals to global sanction list," which populates this table.

- **3.2. Implement Document Version Control:**
  - **Action:** Modify the `Document` model. Add a `version` (Int), `isActive` (Boolean), and `parentDocumentId` (String, self-relation) field.
  - **Business Logic:** When a vendor uploads a new version of a document (e.g., an updated SIUP), do not overwrite the old record. Instead:
    1.  Set `isActive = false` on the old `Document` record.
    2.  Create a new `Document` record with `isActive = true`, `version = old_version + 1`, and `parentDocumentId` pointing to the old record.
  - **UI:** The vendor profile UI should show only the active document by default, but provide a "View History" button to see all previous versions.

#### **Enhancement Module 4: True Vendor Relationship Management (VRM) Portal**

**Goal:** Transform the vendor portal from a submission tool into a self-service hub that fosters partnership and performance.

- **4.1. Build the Vendor Self-Service Dashboard:**

  - **Action:** Enhance the vendor dashboard page.
  - **UI:** Add new widgets:
    - **"Status Pesanan Saya" (My Order Status):** For each PO, display a visual timeline: `PO Diterima` -> `Barang Dikirim` -> `Barang Diterima (GR)` -> `BAST Diajukan` -> `BAST Disetujui` -> `Invoice Dikirim` -> `Pembayaran Diproses` -> `Selesai`.
    - **"Kinerja Saya" (My Performance):** Display the vendor's latest overall KPI score. Use a gauge chart to show their rating (e.g., "Good"). Show a line chart of their score over the last 4 quarters to visualize trends.

- **4.2. Implement the Onboarding Checklist:**
  - **Action:** On the vendor's profile page, instead of just listing the forms, create a visual checklist UI.
  - **UI:** Display a list of required sections ("Informasi Administrasi", "Data Pajak", "Izin Usaha", etc.). Each item has a status icon (red X for incomplete, green check for complete). A progress bar at the top shows "Profil Anda XX% Lengkap." This gamifies the process and clarifies what needs to be done.
