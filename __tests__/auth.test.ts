import { describe, expect, test } from "@jest/globals";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";

import { vendorRegistrationSchema, loginSchema } from "../src/lib/validations/auth";

// Mock auth functions for testing without Next.js dependencies
const JWT_SECRET = "test-secret-key";

async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

function generateToken(payload: { userId: string; email: string; roles: string[] }): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: "7d" });
}

function verifyToken(token: string): { userId: string; email: string; roles: string[] } {
  return jwt.verify(token, JWT_SECRET) as { userId: string; email: string; roles: string[] };
}

describe("Authentication Utilities", () => {
  describe("Password Hashing", () => {
    test("should hash password correctly", async () => {
      const password = "TestPassword123!";
      const hashedPassword = await hashPassword(password);
      
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(50);
    });

    test("should verify password correctly", async () => {
      const password = "TestPassword123!";
      const hashedPassword = await hashPassword(password);
      
      const isValid = await verifyPassword(password, hashedPassword);
      expect(isValid).toBe(true);
      
      const isInvalid = await verifyPassword("WrongPassword", hashedPassword);
      expect(isInvalid).toBe(false);
    });
  });

  describe("JWT Token", () => {
    test("should generate and verify token correctly", () => {
      const payload = {
        userId: "test-user-id",
        email: "<EMAIL>",
        roles: ["VENDOR"],
      };

      const token = generateToken(payload);
      expect(token).toBeDefined();
      expect(typeof token).toBe("string");

      const decoded = verifyToken(token);
      expect(decoded.userId).toBe(payload.userId);
      expect(decoded.email).toBe(payload.email);
      expect(decoded.roles).toEqual(payload.roles);
    });

    test("should throw error for invalid token", () => {
      expect(() => {
        verifyToken("invalid-token");
      }).toThrow();
    });
  });
});

describe("Authentication Validation", () => {
  describe("Vendor Registration Schema", () => {
    test("should validate correct vendor registration data", () => {
      const validData = {
        email: "<EMAIL>",
        password: "Password123!",
        confirmPassword: "Password123!",
        companyName: "PT Test Company",
        npwpNumber: "123456789012345",
        address: "Jl. Test No. 123",
        npwpAddress: "Jl. Test No. 123",
        phone: "+6281234567890",
        picName: "John Doe",
        picEmail: "<EMAIL>",
        picPhone: "+6281234567890",
        businessEntityType: "PT" as const,
        termsAndConditions: true,
      };

      const result = vendorRegistrationSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    test("should fail validation for mismatched passwords", () => {
      const invalidData = {
        email: "<EMAIL>",
        password: "Password123!",
        confirmPassword: "DifferentPassword!",
        companyName: "PT Test Company",
        npwpNumber: "123456789012345",
        address: "Jl. Test No. 123",
        npwpAddress: "Jl. Test No. 123",
        phone: "+6281234567890",
        picName: "John Doe",
        picEmail: "<EMAIL>",
        picPhone: "+6281234567890",
        businessEntityType: "PT" as const,
        termsAndConditions: true,
      };

      const result = vendorRegistrationSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Passwords do not match");
      }
    });

    test("should fail validation for invalid NPWP number", () => {
      const invalidData = {
        email: "<EMAIL>",
        password: "Password123!",
        confirmPassword: "Password123!",
        companyName: "PT Test Company",
        npwpNumber: "12345", // Too short
        address: "Jl. Test No. 123",
        npwpAddress: "Jl. Test No. 123",
        phone: "+6281234567890",
        picName: "John Doe",
        picEmail: "<EMAIL>",
        picPhone: "+6281234567890",
        businessEntityType: "PT" as const,
        termsAndConditions: true,
      };

      const result = vendorRegistrationSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("NPWP number must be exactly 15 digits");
      }
    });

    test("should fail validation when terms not accepted", () => {
      const invalidData = {
        email: "<EMAIL>",
        password: "Password123!",
        confirmPassword: "Password123!",
        companyName: "PT Test Company",
        npwpNumber: "123456789012345",
        address: "Jl. Test No. 123",
        npwpAddress: "Jl. Test No. 123",
        phone: "+6281234567890",
        picName: "John Doe",
        picEmail: "<EMAIL>",
        picPhone: "+6281234567890",
        businessEntityType: "PT" as const,
        termsAndConditions: false,
      };

      const result = vendorRegistrationSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("You must accept the terms and conditions");
      }
    });
  });

  describe("Login Schema", () => {
    test("should validate correct login data", () => {
      const validData = {
        email: "<EMAIL>",
        password: "password123",
      };

      const result = loginSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    test("should fail validation for invalid email", () => {
      const invalidData = {
        email: "invalid-email",
        password: "password123",
      };

      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Invalid email address");
      }
    });

    test("should fail validation for empty password", () => {
      const invalidData = {
        email: "<EMAIL>",
        password: "",
      };

      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Password is required");
      }
    });
  });
});
