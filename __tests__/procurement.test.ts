import { describe, expect, test } from "@jest/globals";

import { procurementSchema, vendorOfferSchema } from "../src/lib/validations/procurement";

describe("Procurement Validation", () => {
  describe("Procurement Schema", () => {
    test("should validate correct procurement data", () => {
      const validData = {
        title: "Pengadaan Laptop",
        type: "RFQ" as const,
        status: "DRAFT" as const,
        ownerEstimate: 100000000,
        showOwnerEstimateToVendor: false,
        evaluationMethod: "Lowest Price",
        items: [
          {
            name: "Laptop Dell",
            description: "Laptop untuk kantor",
            quantity: 10,
            unit: "Unit",
            ownerEstimate: 10000000,
          },
        ],
        stages: [
          {
            name: "<PERSON><PERSON><PERSON>",
            sequence: 1,
            startDate: new Date("2024-01-01"),
            endDate: new Date("2024-01-07"),
          },
          {
            name: "Penawaran",
            sequence: 2,
            startDate: new Date("2024-01-08"),
            endDate: new Date("2024-01-15"),
          },
        ],
        committee: [
          {
            userId: "user-1",
            committeeRole: "Ketua",
          },
        ],
      };

      const result = procurementSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    test("should fail validation for empty title", () => {
      const invalidData = {
        title: "",
        type: "RFQ" as const,
        status: "DRAFT" as const,
        ownerEstimate: 100000000,
        showOwnerEstimateToVendor: false,
        evaluationMethod: "Lowest Price",
        items: [
          {
            name: "Laptop Dell",
            quantity: 10,
            unit: "Unit",
            ownerEstimate: 10000000,
          },
        ],
        stages: [
          {
            name: "Pengumuman",
            sequence: 1,
            startDate: new Date("2024-01-01"),
            endDate: new Date("2024-01-07"),
          },
        ],
        committee: [
          {
            userId: "user-1",
            committeeRole: "Ketua",
          },
        ],
      };

      const result = procurementSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Title is required");
      }
    });

    test("should fail validation for overlapping stages", () => {
      const invalidData = {
        title: "Pengadaan Laptop",
        type: "RFQ" as const,
        status: "DRAFT" as const,
        ownerEstimate: 100000000,
        showOwnerEstimateToVendor: false,
        evaluationMethod: "Lowest Price",
        items: [
          {
            name: "Laptop Dell",
            quantity: 10,
            unit: "Unit",
            ownerEstimate: 10000000,
          },
        ],
        stages: [
          {
            name: "Pengumuman",
            sequence: 1,
            startDate: new Date("2024-01-01"),
            endDate: new Date("2024-01-10"), // Overlaps with next stage
          },
          {
            name: "Penawaran",
            sequence: 2,
            startDate: new Date("2024-01-05"), // Starts before previous ends
            endDate: new Date("2024-01-15"),
          },
        ],
        committee: [
          {
            userId: "user-1",
            committeeRole: "Ketua",
          },
        ],
      };

      const result = procurementSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Stages cannot overlap in time");
      }
    });

    test("should fail validation for end date before start date", () => {
      const invalidData = {
        title: "Pengadaan Laptop",
        type: "RFQ" as const,
        status: "DRAFT" as const,
        ownerEstimate: 100000000,
        showOwnerEstimateToVendor: false,
        evaluationMethod: "Lowest Price",
        items: [
          {
            name: "Laptop Dell",
            quantity: 10,
            unit: "Unit",
            ownerEstimate: 10000000,
          },
        ],
        stages: [
          {
            name: "Pengumuman",
            sequence: 1,
            startDate: new Date("2024-01-10"),
            endDate: new Date("2024-01-05"), // End before start
          },
        ],
        committee: [
          {
            userId: "user-1",
            committeeRole: "Ketua",
          },
        ],
      };

      const result = procurementSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("End date must be after start date for all stages");
      }
    });
  });

  describe("Vendor Offer Schema", () => {
    test("should validate correct vendor offer data", () => {
      const validData = {
        procurementId: "proc-1",
        offerItems: [
          {
            itemId: "item-1",
            offeredPrice: 9500000,
          },
        ],
        documents: [
          {
            documentType: "Proposal",
            fileUrl: "https://example.com/proposal.pdf",
            fileName: "proposal.pdf",
            fileType: "application/pdf",
            description: "Technical proposal",
          },
        ],
      };

      const result = vendorOfferSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    test("should fail validation for empty offer items", () => {
      const invalidData = {
        procurementId: "proc-1",
        offerItems: [],
      };

      const result = vendorOfferSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("At least one offer item is required");
      }
    });

    test("should fail validation for negative offered price", () => {
      const invalidData = {
        procurementId: "proc-1",
        offerItems: [
          {
            itemId: "item-1",
            offeredPrice: -1000, // Negative price
          },
        ],
      };

      const result = vendorOfferSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Offered price must be a positive number");
      }
    });

    test("should fail validation for invalid file URL", () => {
      const invalidData = {
        procurementId: "proc-1",
        offerItems: [
          {
            itemId: "item-1",
            offeredPrice: 9500000,
          },
        ],
        documents: [
          {
            documentType: "Proposal",
            fileUrl: "invalid-url", // Invalid URL
            fileName: "proposal.pdf",
            fileType: "application/pdf",
          },
        ],
      };

      const result = vendorOfferSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Invalid file URL");
      }
    });
  });
});
