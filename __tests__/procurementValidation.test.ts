import { z } from "zod";
import { describe, expect, test } from "@jest/globals";

// Define the Zod schema for procurement creation (e.g., RFQ)
const procurementSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  type: z.enum(["TENDER", "RFQ"], { message: "Invalid procurement type" }),
  status: z
    .enum(
      [
        "DRAFT",
        "PUBLISHED",
        "AANWIJZING",
        "SUBMISSION",
        "EVALUATION",
        "NEGOTIATION",
        "WINNER_ANNOUNCEMENT",
        "AWARDED",
        "COMPLETED",
        "CANCELED",
      ],
      { message: "Invalid status" }
    )
    .default("DRAFT"),
  ownerEstimate: z
    .number()
    .positive({ message: "Owner estimate must be a positive number" }),
  showOwnerEstimateToVendor: z.boolean().default(false),
  evaluationMethod: z
    .string()
    .min(1, { message: "Evaluation method is required" }),
  items: z
    .array(
      z.object({
        name: z.string().min(1, { message: "Item name is required" }),
        quantity: z
          .number()
          .positive({ message: "Quantity must be a positive number" }),
        unit: z.string().min(1, { message: "Unit is required" }),
        ownerEstimate: z
          .number()
          .positive({
            message: "Owner estimate per item must be a positive number",
          }),
      })
    )
    .min(1, { message: "At least one item is required" }),
  stages: z
    .array(
      z.object({
        name: z.string().min(1, { message: "Stage name is required" }),
        sequence: z
          .number()
          .int()
          .positive({ message: "Sequence must be a positive integer" }),
        startDate: z.date({ message: "Start date must be a valid date" }),
        endDate: z.date({ message: "End date must be a valid date" }),
      })
    )
    .min(1, { message: "At least one stage is required" })
    .refine((data) => data.every((stage) => stage.endDate > stage.startDate), {
      message: "End date must be after start date",
      path: ["stages"],
    }),
});

describe("Procurement Creation Validation", () => {
  test("validates a correct procurement form", () => {
    const validData = {
      title: "Pengadaan Alat Tulis Kantor Q4 2023",
      type: "RFQ",
      status: "DRAFT",
      ownerEstimate: 50000000,
      showOwnerEstimateToVendor: false,
      evaluationMethod: "Sistem Nilai",
      items: [
        {
          name: "Joyko Binder Clips 200",
          quantity: 1500,
          unit: "Box",
          ownerEstimate: 30000,
        },
        {
          name: "Joyko Binder Clips 155",
          quantity: 1500,
          unit: "Box",
          ownerEstimate: 20000,
        },
      ],
      stages: [
        {
          name: "Pemasukan Penawaran",
          sequence: 1,
          startDate: new Date("2023-10-01"),
          endDate: new Date("2023-10-07"),
        },
        {
          name: "Evaluasi dan Negosiasi",
          sequence: 2,
          startDate: new Date("2023-10-08"),
          endDate: new Date("2023-10-14"),
        },
      ],
    };
    const result = procurementSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  test("fails validation on empty title", () => {
    const invalidData = {
      title: "",
      type: "RFQ",
      status: "DRAFT",
      ownerEstimate: 50000000,
      showOwnerEstimateToVendor: false,
      evaluationMethod: "Sistem Nilai",
      items: [
        {
          name: "Joyko Binder Clips 200",
          quantity: 1500,
          unit: "Box",
          ownerEstimate: 30000,
        },
      ],
      stages: [
        {
          name: "Pemasukan Penawaran",
          sequence: 1,
          startDate: new Date("2023-10-01"),
          endDate: new Date("2023-10-07"),
        },
      ],
    };
    const result = procurementSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe("Title is required");
    }
  });

  test("fails validation on invalid owner estimate", () => {
    const invalidData = {
      title: "Pengadaan Alat Tulis Kantor Q4 2023",
      type: "RFQ",
      status: "DRAFT",
      ownerEstimate: -50000000, // Negative value
      showOwnerEstimateToVendor: false,
      evaluationMethod: "Sistem Nilai",
      items: [
        {
          name: "Joyko Binder Clips 200",
          quantity: 1500,
          unit: "Box",
          ownerEstimate: 30000,
        },
      ],
      stages: [
        {
          name: "Pemasukan Penawaran",
          sequence: 1,
          startDate: new Date("2023-10-01"),
          endDate: new Date("2023-10-07"),
        },
      ],
    };
    const result = procurementSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe(
        "Owner estimate must be a positive number"
      );
    }
  });

  test("fails validation on empty items array", () => {
    const invalidData = {
      title: "Pengadaan Alat Tulis Kantor Q4 2023",
      type: "RFQ",
      status: "DRAFT",
      ownerEstimate: 50000000,
      showOwnerEstimateToVendor: false,
      evaluationMethod: "Sistem Nilai",
      items: [], // Empty array
      stages: [
        {
          name: "Pemasukan Penawaran",
          sequence: 1,
          startDate: new Date("2023-10-01"),
          endDate: new Date("2023-10-07"),
        },
      ],
    };
    const result = procurementSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe(
        "At least one item is required"
      );
    }
  });

  test("fails validation on invalid stage dates", () => {
    const invalidData = {
      title: "Pengadaan Alat Tulis Kantor Q4 2023",
      type: "RFQ",
      status: "DRAFT",
      ownerEstimate: 50000000,
      showOwnerEstimateToVendor: false,
      evaluationMethod: "Sistem Nilai",
      items: [
        {
          name: "Joyko Binder Clips 200",
          quantity: 1500,
          unit: "Box",
          ownerEstimate: 30000,
        },
      ],
      stages: [
        {
          name: "Pemasukan Penawaran",
          sequence: 1,
          startDate: new Date("2023-10-07"),
          endDate: new Date("2023-10-01"),
        }, // End date before start date
      ],
    };
    const result = procurementSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe(
        "End date must be after start date"
      );
    }
  });
});
