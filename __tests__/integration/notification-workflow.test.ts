import { jest } from '@jest/globals';

// Mock external dependencies
const mockSendMail = jest.fn();
const mockCreateTransport = jest.fn(() => ({
  sendMail: mockSendMail,
}));

jest.mock('nodemailer', () => ({
  createTransport: mockCreateTransport,
}));

const mockCompile = jest.fn();
jest.mock('handlebars', () => ({
  compile: mockCompile,
}));

// Mock database
const mockPrismaNotificationCreate = jest.fn();
const mockPrismaVendorFindUnique = jest.fn();
const mockPrismaVendorUpdate = jest.fn();
const mockPrismaUserFindUnique = jest.fn();
const mockPrismaPasswordResetTokenCreate = jest.fn();
const mockPrismaAuditLogCreate = jest.fn();

jest.mock('@/lib/db', () => ({
  prisma: {
    notification: {
      create: mockPrismaNotificationCreate,
    },
    vendor: {
      findUnique: mockPrismaVendorFindUnique,
      update: mockPrismaVendorUpdate,
    },
    user: {
      findUnique: mockPrismaUserFindUnique,
    },
    passwordResetToken: {
      create: mockPrismaPasswordResetTokenCreate,
    },
    auditLog: {
      create: mockPrismaAuditLogCreate,
    },
  },
}));

import {
  createNotification,
  sendEmailNotification,
  sendCombinedNotification,
} from '@/lib/notifications/notification-service';

describe('Notification Workflow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.NODE_ENV = 'test';
    process.env.SMTP_HOST = 'localhost';
    process.env.SMTP_PORT = '587';
    process.env.FROM_EMAIL = '<EMAIL>';
  });

  afterEach(() => {
    delete process.env.SMTP_HOST;
    delete process.env.SMTP_PORT;
    delete process.env.FROM_EMAIL;
  });

  describe('Vendor Status Change Workflow', () => {
    it('should complete vendor verification notification workflow', async () => {
      // Mock database responses
      const mockNotification = {
        id: 'notification-123',
        userId: 'user-123',
        title: 'Akun Anda Telah Diverifikasi',
        message: 'Selamat! Akun Anda telah disetujui.',
        type: 'SUCCESS',
        read: false,
        createdAt: new Date(),
      };

      mockPrismaNotificationCreate.mockResolvedValue(mockNotification);

      // Mock email template compilation
      const mockCompiledHtml = jest.fn(() => `
        <div style="font-family: Arial, sans-serif;">
          <h2>Status Vendor Diperbarui</h2>
          <p>Halo Test Vendor,</p>
          <p>Status vendor Anda telah diperbarui menjadi: <strong>DIVERIFIKASI</strong></p>
        </div>
      `);
      const mockCompiledText = jest.fn(() => 'Status Vendor Diperbarui\n\nHalo Test Vendor,\n\nStatus vendor Anda telah diperbarui menjadi: DIVERIFIKASI');

      mockCompile
        .mockReturnValueOnce(mockCompiledHtml)
        .mockReturnValueOnce(mockCompiledText);

      // Mock email sending
      mockSendMail.mockResolvedValue({
        messageId: 'test-message-id-123',
      });

      // Execute the workflow
      const notificationData = {
        userId: 'user-123',
        title: 'Akun Anda Telah Diverifikasi',
        message: 'Selamat! Akun Anda telah disetujui. Anda sekarang dapat mengikuti proses pengadaan.',
        type: 'SUCCESS' as const,
      };

      const emailData = {
        to: '<EMAIL>',
        subject: 'Akun Vendor Telah Diverifikasi',
        template: 'vendor-status-change',
        data: {
          vendorName: 'Test Vendor',
          status: 'DIVERIFIKASI',
        },
      };

      await sendCombinedNotification(notificationData, emailData);

      // Verify in-app notification was created
      expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
        data: {
          userId: 'user-123',
          title: 'Akun Anda Telah Diverifikasi',
          message: 'Selamat! Akun Anda telah disetujui. Anda sekarang dapat mengikuti proses pengadaan.',
          type: 'SUCCESS',
          read: false,
        },
      });

      // Verify email was sent
      expect(mockCreateTransport).toHaveBeenCalledWith({
        host: 'localhost',
        port: 587,
        secure: false,
        auth: undefined,
      });

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Akun Vendor Telah Diverifikasi',
        html: expect.stringContaining('Status Vendor Diperbarui'),
        text: expect.stringContaining('Status Vendor Diperbarui'),
      });

      // Verify template compilation
      expect(mockCompile).toHaveBeenCalledTimes(2); // HTML and text templates
      expect(mockCompiledHtml).toHaveBeenCalledWith({
        vendorName: 'Test Vendor',
        status: 'DIVERIFIKASI',
      });
      expect(mockCompiledText).toHaveBeenCalledWith({
        vendorName: 'Test Vendor',
        status: 'DIVERIFIKASI',
      });
    });

    it('should handle vendor rejection notification workflow', async () => {
      const mockNotification = {
        id: 'notification-124',
        userId: 'user-124',
        title: 'Verifikasi Akun Ditolak',
        message: 'Verifikasi akun Anda ditolak.',
        type: 'ERROR',
        read: false,
        createdAt: new Date(),
      };

      mockPrismaNotificationCreate.mockResolvedValue(mockNotification);

      const mockCompiledHtml = jest.fn(() => `
        <div style="font-family: Arial, sans-serif;">
          <h2>Status Vendor Diperbarui</h2>
          <p>Halo Test Vendor,</p>
          <p>Status vendor Anda telah diperbarui menjadi: <strong>DITOLAK</strong></p>
          <p><strong>Alasan:</strong> Dokumen tidak lengkap</p>
        </div>
      `);
      const mockCompiledText = jest.fn(() => 'Status Vendor Diperbarui\n\nHalo Test Vendor,\n\nStatus vendor Anda telah diperbarui menjadi: DITOLAK\n\nAlasan: Dokumen tidak lengkap');

      mockCompile
        .mockReturnValueOnce(mockCompiledHtml)
        .mockReturnValueOnce(mockCompiledText);

      mockSendMail.mockResolvedValue({
        messageId: 'test-message-id-124',
      });

      const notificationData = {
        userId: 'user-124',
        title: 'Verifikasi Akun Ditolak',
        message: 'Verifikasi akun Anda ditolak. Alasan: Dokumen tidak lengkap',
        type: 'ERROR' as const,
      };

      const emailData = {
        to: '<EMAIL>',
        subject: 'Verifikasi Akun Vendor Ditolak',
        template: 'vendor-status-change',
        data: {
          vendorName: 'Test Vendor',
          status: 'DITOLAK',
          reason: 'Dokumen tidak lengkap',
        },
      };

      await sendCombinedNotification(notificationData, emailData);

      expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
        data: {
          userId: 'user-124',
          title: 'Verifikasi Akun Ditolak',
          message: 'Verifikasi akun Anda ditolak. Alasan: Dokumen tidak lengkap',
          type: 'ERROR',
          read: false,
        },
      });

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Verifikasi Akun Vendor Ditolak',
        html: expect.stringContaining('DITOLAK'),
        text: expect.stringContaining('DITOLAK'),
      });
    });
  });

  describe('Password Reset Workflow', () => {
    it('should complete password reset notification workflow', async () => {
      const mockCompiledHtml = jest.fn(() => `
        <div style="font-family: Arial, sans-serif;">
          <h2>Reset Password</h2>
          <p>Halo Test User,</p>
          <p>Anda telah meminta reset password untuk akun Anda.</p>
          <a href="http://localhost:3000/reset-password?token=test-token">Reset Password</a>
        </div>
      `);
      const mockCompiledText = jest.fn(() => 'Reset Password\n\nHalo Test User,\n\nAnda telah meminta reset password untuk akun Anda.\n\nKlik link berikut untuk reset password:\nhttp://localhost:3000/reset-password?token=test-token');

      mockCompile
        .mockReturnValueOnce(mockCompiledHtml)
        .mockReturnValueOnce(mockCompiledText);

      mockSendMail.mockResolvedValue({
        messageId: 'test-message-id-125',
      });

      const emailData = {
        to: '<EMAIL>',
        subject: 'Reset Password - E-Procurement System',
        template: 'password-reset',
        data: {
          userName: 'Test User',
          resetUrl: 'http://localhost:3000/reset-password?token=test-token',
        },
      };

      await sendEmailNotification(emailData);

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Reset Password - E-Procurement System',
        html: expect.stringContaining('Reset Password'),
        text: expect.stringContaining('Reset Password'),
      });

      expect(mockCompiledHtml).toHaveBeenCalledWith({
        userName: 'Test User',
        resetUrl: 'http://localhost:3000/reset-password?token=test-token',
      });
    });
  });

  describe('Offer Evaluation Workflow', () => {
    it('should complete offer evaluation notification workflow', async () => {
      const mockNotification = {
        id: 'notification-126',
        userId: 'vendor-user-123',
        title: 'Evaluasi Penawaran',
        message: 'Penawaran Anda untuk Pengadaan Test telah dievaluasi.',
        type: 'INFO',
        read: false,
        createdAt: new Date(),
      };

      mockPrismaNotificationCreate.mockResolvedValue(mockNotification);

      const mockCompiledHtml = jest.fn(() => `
        <div style="font-family: Arial, sans-serif;">
          <h2>Evaluasi Penawaran</h2>
          <p>Halo Test Vendor,</p>
          <p>Penawaran Anda untuk tender "Pengadaan Test" telah dievaluasi.</p>
          <p><strong>Status:</strong> PEMENANG</p>
          <p><strong>Skor:</strong> 95</p>
        </div>
      `);
      const mockCompiledText = jest.fn(() => 'Evaluasi Penawaran\n\nHalo Test Vendor,\n\nPenawaran Anda untuk tender "Pengadaan Test" telah dievaluasi.\n\nStatus: PEMENANG\n\nSkor: 95');

      mockCompile
        .mockReturnValueOnce(mockCompiledHtml)
        .mockReturnValueOnce(mockCompiledText);

      mockSendMail.mockResolvedValue({
        messageId: 'test-message-id-126',
      });

      const notificationData = {
        userId: 'vendor-user-123',
        title: 'Selamat! Anda Memenangkan Pengadaan',
        message: 'Penawaran Anda untuk Pengadaan Test terpilih sebagai pemenang.',
        type: 'SUCCESS' as const,
      };

      const emailData = {
        to: '<EMAIL>',
        subject: 'Selamat! Penawaran Anda Menang',
        template: 'offer-evaluation',
        data: {
          vendorName: 'Test Vendor',
          procurementTitle: 'Pengadaan Test',
          status: 'PEMENANG',
          score: '95',
        },
      };

      await sendCombinedNotification(notificationData, emailData);

      expect(mockPrismaNotificationCreate).toHaveBeenCalledWith({
        data: {
          userId: 'vendor-user-123',
          title: 'Selamat! Anda Memenangkan Pengadaan',
          message: 'Penawaran Anda untuk Pengadaan Test terpilih sebagai pemenang.',
          type: 'SUCCESS',
          read: false,
        },
      });

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Selamat! Penawaran Anda Menang',
        html: expect.stringContaining('PEMENANG'),
        text: expect.stringContaining('PEMENANG'),
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle email sending failure gracefully', async () => {
      const mockNotification = {
        id: 'notification-127',
        userId: 'user-127',
        title: 'Test Notification',
        message: 'Test message',
        type: 'INFO',
        read: false,
        createdAt: new Date(),
      };

      mockPrismaNotificationCreate.mockResolvedValue(mockNotification);
      mockSendMail.mockRejectedValue(new Error('SMTP connection failed'));

      const notificationData = {
        userId: 'user-127',
        title: 'Test Notification',
        message: 'Test message',
        type: 'INFO' as const,
      };

      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        template: 'vendor-status-change',
        data: { vendorName: 'Test', status: 'TEST' },
      };

      // In development mode, this should not throw
      process.env.NODE_ENV = 'development';
      await expect(sendCombinedNotification(notificationData, emailData)).resolves.not.toThrow();

      // In production mode, this should throw
      process.env.NODE_ENV = 'production';
      await expect(sendCombinedNotification(notificationData, emailData)).rejects.toThrow();

      // Verify in-app notification was still created
      expect(mockPrismaNotificationCreate).toHaveBeenCalledTimes(2);
    });
  });
});
