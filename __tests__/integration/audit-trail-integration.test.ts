import { auditLogger } from '@/lib/audit/comprehensive-audit-logger';
import { prisma } from '@/lib/db';

// Mock dependencies
jest.mock('@/lib/db');
jest.mock('@/lib/audit/comprehensive-audit-logger');

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockAuditLogger = auditLogger as jest.Mocked<typeof auditLogger>;

describe('Audit Trail Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Comprehensive Audit Logger Integration', () => {
    it('should log CRUD operations with complete context', async () => {
      const mockAuditLog = {
        id: 'audit-1',
        userId: 'user-1',
        action: 'CREATE',
        entityType: 'PURCHASE_REQUISITION',
        entityId: 'pr-1',
        oldValues: null,
        newValues: { title: 'New PR', amount: 1000 },
        details: { reason: 'New procurement request' },
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        createdAt: new Date(),
      };

      mockPrisma.auditLog.create.mockResolvedValue(mockAuditLog as any);

      const mockRequest = {
        headers: {
          get: jest.fn()
            .mockReturnValueOnce('***********') // x-forwarded-for
            .mockReturnValueOnce('Mozilla/5.0'), // user-agent
        },
      };

      await auditLogger.logCrudOperation(
        'user-1',
        'CREATE',
        'PURCHASE_REQUISITION',
        'pr-1',
        undefined,
        { title: 'New PR', amount: 1000 },
        mockRequest as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'user-1',
        'CREATE',
        'PURCHASE_REQUISITION',
        'pr-1',
        null,
        { title: 'New PR', amount: 1000 },
        mockRequest
      );
    });

    it('should log authentication events', async () => {
      const mockRequest = {
        headers: {
          get: jest.fn()
            .mockReturnValueOnce('10.0.0.1') // x-forwarded-for
            .mockReturnValueOnce('Chrome/91.0'), // user-agent
        },
      };

      await auditLogger.logAuthEvent(
        'user-1',
        'LOGIN_SUCCESS',
        { email: '<EMAIL>', loginMethod: 'email' },
        mockRequest as any
      );

      expect(mockAuditLogger.logAuthEvent).toHaveBeenCalledWith(
        'user-1',
        'LOGIN_SUCCESS',
        { email: '<EMAIL>', loginMethod: 'email' },
        mockRequest
      );
    });

    it('should log workflow state changes', async () => {
      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditLogger.logWorkflowStateChange(
        'user-1',
        'workflow-1',
        'APPROVAL_WORKFLOW',
        'PENDING',
        'IN_PROGRESS',
        'PURCHASE_REQUISITION',
        'pr-1',
        'Workflow started'
      );

      expect(mockAuditLogger.logWorkflowStateChange).toHaveBeenCalledWith(
        'user-1',
        'workflow-1',
        'APPROVAL_WORKFLOW',
        'PENDING',
        'IN_PROGRESS',
        'PURCHASE_REQUISITION',
        'pr-1',
        'Workflow started'
      );
    });

    it('should handle batch logging efficiently', async () => {
      const batchLogs = [
        {
          userId: 'user-1',
          action: 'VIEW',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
        },
        {
          userId: 'user-1',
          action: 'VIEW',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-2',
        },
      ];

      mockPrisma.auditLog.createMany.mockResolvedValue({ count: 2 });

      await auditLogger.batchLog(batchLogs);

      expect(mockAuditLogger.batchLog).toHaveBeenCalledWith(batchLogs);
    });

    it('should retrieve audit logs with filtering', async () => {
      const mockLogs = [
        {
          id: 'audit-1',
          userId: 'user-1',
          action: 'CREATE',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
          createdAt: new Date(),
          user: { id: 'user-1', name: 'Test User', email: '<EMAIL>' },
        },
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(mockLogs as any);
      mockPrisma.auditLog.count.mockResolvedValue(1);

      const filters = {
        userId: 'user-1',
        action: 'CREATE',
        entityType: 'PURCHASE_REQUISITION',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        page: 1,
        limit: 50,
      };

      mockAuditLogger.getAuditLogs.mockResolvedValue({
        logs: mockLogs,
        total: 1,
        page: 1,
        totalPages: 1,
      });

      const result = await auditLogger.getAuditLogs(filters);

      expect(result).toEqual({
        logs: mockLogs,
        total: 1,
        page: 1,
        totalPages: 1,
      });

      expect(mockAuditLogger.getAuditLogs).toHaveBeenCalledWith(filters);
    });
  });

  describe('Audit System Integration', () => {
    it('should handle API request logging', async () => {
      const mockApiData = {
        method: 'POST',
        endpoint: '/api/purchase-requisitions',
        userId: 'user-1',
        requestData: { title: 'Test PR', amount: 5000 },
        responseStatus: 201,
      };

      await auditLogger.logCrudOperation(
        mockApiData.userId,
        'CREATE',
        'API_ENDPOINT',
        mockApiData.endpoint,
        undefined,
        mockApiData,
        undefined as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'user-1',
        'CREATE',
        'API_ENDPOINT',
        '/api/purchase-requisitions',
        null,
        mockApiData,
        null
      );
    });

    it('should handle page view logging', async () => {
      const pageViewData = {
        userId: 'user-1',
        page: '/dashboard/purchase-requisitions',
        method: 'GET',
        userAgent: 'Mozilla/5.0',
        ipAddress: '********',
      };

      await auditLogger.logCrudOperation(
        pageViewData.userId,
        'READ',
        'PAGE',
        pageViewData.page,
        undefined,
        pageViewData,
        undefined as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'user-1',
        'READ',
        'PAGE',
        '/dashboard/purchase-requisitions',
        null,
        pageViewData,
        null
      );
    });

    it('should handle error logging gracefully', async () => {
      mockAuditLogger.logCrudOperation.mockRejectedValue(new Error('Database connection failed'));

      try {
        await auditLogger.logCrudOperation(
          'user-1',
          'CREATE',
          'SYSTEM',
          'error-1',
          undefined,
          { error: 'Test error' },
          undefined as any
        );
      } catch (error) {
        expect((error as Error).message).toBe('Database connection failed');
      }

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'user-1',
        'CREATE',
        'SYSTEM',
        'error-1',
        null,
        { error: 'Test error' },
        null
      );
    });
  });

  describe('Audit Log Search and Analytics', () => {
    it('should provide comprehensive search capabilities', async () => {
      const mockSearchResults = [
        {
          id: 'audit-1',
          userId: 'user-1',
          action: 'CREATE',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
          details: { title: 'Office Supplies', amount: 1500 },
          createdAt: new Date(),
        },
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(mockSearchResults as any);
      mockPrisma.auditLog.count.mockResolvedValue(1);

      const searchFilters = {
        searchTerm: 'Office Supplies',
        actions: ['CREATE', 'UPDATE'],
        entityTypes: ['PURCHASE_REQUISITION'],
        userIds: ['user-1'],
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
        page: 1,
        limit: 20,
      };

      mockAuditLogger.searchAuditLogs.mockResolvedValue({
        logs: mockSearchResults,
        total: 1,
        page: 1,
        totalPages: 1,
      });

      const result = await auditLogger.searchAuditLogs(searchFilters);

      expect(result.logs).toEqual(mockSearchResults);
      expect(result.total).toBe(1);
      expect(mockAuditLogger.searchAuditLogs).toHaveBeenCalledWith(searchFilters);
    });

    it('should generate audit statistics and reports', async () => {
      mockPrisma.auditLog.count.mockResolvedValue(1000);
      mockPrisma.auditLog.aggregate.mockResolvedValue({ _avg: { id: 85.5 } } as any);
      mockPrisma.auditLog.groupBy
        .mockResolvedValueOnce([{ action: 'CREATE', _count: { action: 300 } }])
        .mockResolvedValueOnce([{ severity: 'HIGH', _count: { severity: 50 } }])
        .mockResolvedValueOnce([{ category: 'procurement', _count: { category: 600 } }])
        .mockResolvedValueOnce([{ userEmail: '<EMAIL>', _count: { userEmail: 150 } }]);

      mockAuditLogger.getAuditStatistics.mockResolvedValue({
        totalLogs: 1000,
        averageScore: 85.5,
        actionStats: [{ action: 'CREATE', _count: { action: 300 } }],
        severityStats: [{ severity: 'HIGH', _count: { severity: 50 } }],
        categoryStats: [{ category: 'procurement', _count: { category: 600 } }],
        userStats: [{ userEmail: '<EMAIL>', _count: { userEmail: 150 } }],
      });

      const statistics = await auditLogger.getAuditStatistics();

      expect(statistics).toEqual({
        totalLogs: 1000,
        averageScore: 85.5,
        actionStats: [{ action: 'CREATE', _count: { action: 300 } }],
        severityStats: [{ severity: 'HIGH', _count: { severity: 50 } }],
        categoryStats: [{ category: 'procurement', _count: { category: 600 } }],
        userStats: [{ userEmail: '<EMAIL>', _count: { userEmail: 150 } }],
      });
    });
  });

  describe('Audit Log Archival and Cleanup', () => {
    it('should archive old audit logs', async () => {
      const oldLogs = [
        {
          id: 'old-audit-1',
          userId: 'user-1',
          action: 'CREATE',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-old',
          createdAt: new Date('2023-01-01'),
        },
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(oldLogs as any);
      mockPrisma.auditLogArchive.createMany.mockResolvedValue({ count: 1 });
      mockPrisma.auditLog.deleteMany.mockResolvedValue({ count: 1 });

      const archiveDate = new Date('2023-12-31');
      mockAuditLogger.archiveOldLogs.mockResolvedValue({
        archivedCount: 1,
        deletedCount: 1,
      });

      const result = await auditLogger.archiveOldLogs(archiveDate);

      expect(result.archivedCount).toBe(1);
      expect(result.deletedCount).toBe(1);
      expect(mockAuditLogger.archiveOldLogs).toHaveBeenCalledWith(archiveDate);
    });

    it('should clean up archived logs based on retention policy', async () => {
      mockPrisma.auditLogArchive.deleteMany.mockResolvedValue({ count: 50 });

      const retentionDate = new Date('2022-01-01');
      mockAuditLogger.cleanupArchivedLogs.mockResolvedValue({
        deletedCount: 50,
      });

      const result = await auditLogger.cleanupArchivedLogs(retentionDate);

      expect(result.deletedCount).toBe(50);
      expect(mockAuditLogger.cleanupArchivedLogs).toHaveBeenCalledWith(retentionDate);
    });
  });
});
