import { prisma } from '@/lib/db';
import { auditLogger } from '@/lib/audit/comprehensive-audit-logger';
import { documentTemplateEngine } from '@/lib/documents/document-template-engine';
import { enhancedWorkflowEngine } from '@/lib/approval/enhanced-workflow-engine';
import { auditSystem } from '@/lib/audit/comprehensive-audit-system';
import { enhancedDocumentGenerator } from '@/lib/documents/enhanced-document-generator';

jest.mock('@/lib/db');
jest.mock('@/lib/audit/comprehensive-audit-logger');
jest.mock('@/lib/documents/document-template-engine');
jest.mock('@/lib/approval/enhanced-workflow-engine');
jest.mock('@/lib/audit/comprehensive-audit-system');
jest.mock('@/lib/documents/enhanced-document-generator');

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockAuditLogger = auditLogger as jest.Mocked<typeof auditLogger>;
const mockDocumentTemplateEngine = documentTemplateEngine as jest.Mocked<typeof documentTemplateEngine>;
const mockEnhancedWorkflowEngine = enhancedWorkflowEngine as jest.Mocked<typeof enhancedWorkflowEngine>;
const mockAuditSystem = auditSystem as jest.Mocked<typeof auditSystem>;
const mockEnhancedDocumentGenerator = enhancedDocumentGenerator as jest.Mocked<typeof enhancedDocumentGenerator>;

describe('Workflow Approval Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Approval Workflow with Document Generation', () => {
    const mockWorkflow = {
      id: 'workflow123',
      name: 'Purchase Order Approval',
      entityType: 'purchase_order',
      stage: 'po_approval',
      steps: [
        {
          id: 'step1',
          sequence: 1,
          stepType: 'REVIEW',
          name: 'Initial Review',
          timeoutHours: 24,
        },
        {
          id: 'step2',
          sequence: 2,
          stepType: 'APPROVAL',
          name: 'Manager Approval',
          timeoutHours: 48,
        },
      ],
    };

    const mockTemplate = {
      id: 'template123',
      name: 'Purchase Order Template',
      type: 'PURCHASE_ORDER',
      htmlTemplate: '<html><body>PO: {{poNumber}}</body></html>',
      variables: [
        { name: 'poNumber', type: 'text', required: true },
        { name: 'totalAmount', type: 'currency', required: true },
      ],
    };

    it('should complete full workflow with document generation and audit trail', async () => {
      // Mock workflow retrieval
      mockEnhancedWorkflowEngine.getWorkflowForStage.mockResolvedValue(mockWorkflow as any);
      
      // Mock approval instance creation
      const mockInstance = {
        id: 'instance123',
        workflowId: 'workflow123',
        entityType: 'purchase_order',
        entityId: 'po123',
        status: 'PENDING',
      };
      
      mockPrisma.approvalInstance.create.mockResolvedValue(mockInstance as any);
      mockPrisma.approvalStepInstance.create.mockResolvedValue({} as any);
      mockPrisma.approvalAction.create.mockResolvedValue({} as any);
      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      // Mock document template
      mockPrisma.documentTemplate.findUnique.mockResolvedValue({
        id: 'template123',
        content: {
          htmlTemplate: mockTemplate.htmlTemplate,
          signatureFields: [],
        },
        variables: mockTemplate.variables,
      } as any);

      // Start approval workflow
      const approvalData = {
        entityType: 'purchase_order' as const,
        entityId: 'po123',
        stage: 'po_approval' as const,
        initiatedById: 'user123',
        title: 'Purchase Order Approval',
        description: 'Approval for PO-2024-001',
        priority: 'NORMAL' as const,
      };

      mockEnhancedWorkflowEngine.processNextStep.mockResolvedValue();
      mockEnhancedWorkflowEngine.startStageApproval.mockResolvedValue(mockInstance as any);

      const instance = await mockEnhancedWorkflowEngine.startStageApproval(approvalData);

      expect(instance).toEqual(mockInstance);
      expect(mockEnhancedWorkflowEngine.startStageApproval).toHaveBeenCalledWith(approvalData);

      // Simulate approval actions
      const approvalActionData = {
        instanceId: 'instance123',
        stepInstanceId: 'stepInstance123',
        userId: 'approver123',
        action: 'APPROVE' as const,
        comments: 'Approved with conditions',
        metadata: { conditions: 'Delivery within 30 days' },
      };

      mockEnhancedWorkflowEngine.processApprovalAction.mockResolvedValue({
        success: true,
        nextStep: 'step2',
        workflowCompleted: false,
      } as any);

      const actionResult = await mockEnhancedWorkflowEngine.processApprovalAction(approvalActionData);

      expect(actionResult.success).toBe(true);
      expect(actionResult.nextStep).toBe('step2');

      // Generate document with approval signatures
      const documentData = {
        templateId: 'template123',
        data: {
          poNumber: 'PO-2024-001',
          totalAmount: 1000000,
        },
        outputFormat: 'pdf' as const,
        fileName: 'po-2024-001.pdf',
        approvalWorkflowId: 'workflow123',
        entityId: 'po123',
        entityType: 'purchase_order',
        digitalSignatures: [
          {
            signerName: 'John Doe',
            signerTitle: 'Procurement Manager',
            timestamp: new Date(),
            stepSequence: 3,
            approved: true,
          },
        ],
      };

      mockEnhancedDocumentGenerator.generateDocument.mockResolvedValue({
        success: true,
        fileName: 'po-2024-001.pdf',
        filePath: '/generated/po-2024-001.pdf',
        fileSize: 1024,
        signatureFields: [
          {
            id: 'sig1',
            name: 'Manager Signature',
            position: { x: 400, y: 700 },
            size: { width: 150, height: 80 },
            required: true,
          },
        ],
        pendingSignatures: [],
      });

      const documentResult = await mockEnhancedDocumentGenerator.generateDocument(documentData);

      expect(documentResult.success).toBe(true);
      expect(documentResult.fileName).toBe('po-2024-001.pdf');
      expect(documentResult.pendingSignatures).toHaveLength(0);

      // Verify audit trail
      mockAuditSystem.logApprovalAction.mockResolvedValue();

      await mockAuditSystem.logApprovalAction({
        userId: 'approver123',
        userEmail: '<EMAIL>',
        userRole: 'APPROVER',
        action: 'APPROVE',
        resourceId: 'instance123',
        procurementId: 'po123',
        workflowStage: 'po_approval',
        approvalStep: 'manager_approval',
        metadata: { comments: 'Approved with conditions' },
      });

      expect(mockAuditSystem.logApprovalAction).toHaveBeenCalled();
    });

    it('should handle workflow rejection and document regeneration', async () => {
      // Mock rejection scenario
      mockEnhancedWorkflowEngine.processApprovalAction.mockResolvedValue({
        success: true,
        workflowCompleted: true,
        finalStatus: 'REJECTED',
      } as any);

      const rejectionData = {
        instanceId: 'instance123',
        stepInstanceId: 'stepInstance123',
        userId: 'approver123',
        action: 'REJECT' as const,
        comments: 'Budget exceeds limit',
        metadata: { reason: 'BUDGET_EXCEEDED' },
      };

      const result = await mockEnhancedWorkflowEngine.processApprovalAction(rejectionData);

      expect(result.finalStatus).toBe('REJECTED');

      // Generate rejection document
      const rejectionDocData = {
        templateId: 'rejection_template',
        data: {
          poNumber: 'PO-2024-001',
          rejectionReason: 'Budget exceeds limit',
          rejectedBy: 'John Doe',
          rejectedAt: new Date(),
        },
        outputFormat: 'pdf' as const,
        fileName: 'po-2024-001-rejection.pdf',
      };

      mockEnhancedDocumentGenerator.generateDocument.mockResolvedValue({
        success: true,
        fileName: 'po-2024-001-rejection.pdf',
        filePath: '/generated/po-2024-001-rejection.pdf',
        fileSize: 512,
      });

      const rejectionDoc = await mockEnhancedDocumentGenerator.generateDocument(rejectionDocData);

      expect(rejectionDoc.success).toBe(true);
      expect(rejectionDoc.fileName).toContain('rejection');
    });

    it('should handle workflow timeout and escalation', async () => {
      // Mock timeout scenario
      const timeoutDate = new Date();
      timeoutDate.setHours(timeoutDate.getHours() - 25); // 25 hours ago

      mockPrisma.approvalStepInstance.findMany.mockResolvedValue([
        {
          id: 'stepInstance123',
          status: 'IN_PROGRESS',
          dueDate: timeoutDate,
          step: {
            id: 'step1',
            name: 'Initial Review',
            escalationConfig: {
              escalateToIds: ['manager123'],
              escalationMessage: 'Review timeout - please escalate',
            },
          },
        },
      ] as any);

      mockEnhancedWorkflowEngine.handleTimeouts.mockResolvedValue([
        {
          stepInstanceId: 'stepInstance123',
          escalatedTo: ['manager123'],
          escalationReason: 'TIMEOUT',
        },
      ]);

      const timeoutResults = await mockEnhancedWorkflowEngine.handleTimeouts();

      expect(timeoutResults).toHaveLength(1);
      expect(timeoutResults[0].escalationReason).toBe('TIMEOUT');

      // Verify escalation audit log
      mockAuditSystem.logAudit.mockResolvedValue();

      await mockAuditSystem.logAudit({
        action: 'ESCALATE',
        resource: 'approval_step',
        resourceId: 'stepInstance123',
        severity: 'HIGH',
        category: 'APPROVAL_WORKFLOW',
        description: 'Approval step escalated due to timeout',
        metadata: {
          originalAssignees: ['reviewer123'],
          escalatedTo: ['manager123'],
          reason: 'TIMEOUT',
        },
      });

      expect(mockAuditSystem.logAudit).toHaveBeenCalled();
    });
  });

  describe('Multi-Stage Workflow Integration', () => {
    it('should handle sequential workflow stages', async () => {
      const stages = [
        'vendor_registration',
        'rfq_creation',
        'rfq_publication',
        'offer_evaluation',
        'contract_award',
      ];

      for (const stage of stages) {
        const mockWorkflow = {
          id: `workflow_${stage}`,
          stage,
          entityType: 'procurement',
          steps: [
            {
              id: `step_${stage}`,
              sequence: 1,
              stepType: 'APPROVAL',
              name: `${stage} approval`,
            },
          ],
        };

        mockEnhancedWorkflowEngine.getWorkflowForStage.mockResolvedValue(mockWorkflow as any);

        const workflow = await mockEnhancedWorkflowEngine.getWorkflowForStage('procurement', stage as any);
        
        expect(workflow).toBeDefined();
        expect(workflow?.stage).toBe(stage);
      }
    });

    it('should maintain audit trail across multiple stages', async () => {
      const procurementId = 'proc123';
      const stages = ['rfq_creation', 'offer_evaluation', 'contract_award'];

      for (let i = 0; i < stages.length; i++) {
        const stage = stages[i];
        
        mockAuditSystem.logAudit.mockResolvedValue();

        await mockAuditSystem.logAudit({
          action: 'APPROVE',
          resource: 'procurement_stage',
          resourceId: `${procurementId}_${stage}`,
          procurementId,
          workflowStage: stage,
          severity: 'MEDIUM',
          category: 'APPROVAL_WORKFLOW',
          description: `Stage ${stage} completed`,
          metadata: {
            stageNumber: i + 1,
            totalStages: stages.length,
            nextStage: stages[i + 1] || 'COMPLETED',
          },
        });
      }

      expect(mockAuditSystem.logAudit).toHaveBeenCalledTimes(stages.length);
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle database failures gracefully', async () => {
      mockPrisma.approvalInstance.create.mockRejectedValue(new Error('Database connection failed'));

      const approvalData = {
        entityType: 'procurement' as const,
        entityId: 'proc123',
        stage: 'vendor_registration' as const,
        initiatedById: 'user123',
        title: 'Test Approval',
        priority: 'NORMAL' as const,
      };

      mockEnhancedWorkflowEngine.getWorkflowForStage.mockResolvedValue({
        id: 'workflow123',
        steps: [],
      } as any);

      mockEnhancedWorkflowEngine.startStageApproval.mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(mockEnhancedWorkflowEngine.startStageApproval(approvalData))
        .rejects.toThrow('Database connection failed');

      // Verify error is logged
      mockAuditSystem.logAudit.mockResolvedValue();

      await mockAuditSystem.logAudit({
        action: 'CREATE',
        resource: 'approval_instance',
        severity: 'CRITICAL',
        category: 'SYSTEM',
        description: 'Failed to create approval instance',
        metadata: {
          error: 'Database connection failed',
          entityType: 'procurement',
          entityId: 'proc123',
        },
      });

      expect(mockAuditSystem.logAudit).toHaveBeenCalled();
    });

    it('should handle document generation failures', async () => {
      mockEnhancedDocumentGenerator.generateDocument.mockResolvedValue({
        success: false,
        error: 'Template not found',
      });

      const documentData = {
        templateId: 'nonexistent',
        data: { test: 'data' },
        outputFormat: 'pdf' as const,
        fileName: 'test.pdf',
      };

      const result = await mockEnhancedDocumentGenerator.generateDocument(documentData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Template not found');

      // Verify error audit
      mockAuditSystem.logAudit.mockResolvedValue();

      await mockAuditSystem.logAudit({
        action: 'GENERATE_DOCUMENT',
        resource: 'document',
        severity: 'HIGH',
        category: 'DOCUMENT_MANAGEMENT',
        description: 'Document generation failed',
        metadata: {
          templateId: 'nonexistent',
          error: 'Template not found',
        },
      });

      expect(mockAuditSystem.logAudit).toHaveBeenCalled();
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent approvals', async () => {
      const concurrentApprovals = Array.from({ length: 10 }, (_, i) => ({
        entityType: 'procurement' as const,
        entityId: `proc${i}`,
        stage: 'vendor_registration' as const,
        initiatedById: 'user123',
        title: `Approval ${i}`,
        priority: 'NORMAL' as const,
      }));

      mockEnhancedWorkflowEngine.getWorkflowForStage.mockResolvedValue({
        id: 'workflow123',
        steps: [{ id: 'step1', sequence: 1, timeoutHours: 24 }],
      } as any);

      mockPrisma.approvalInstance.create.mockResolvedValue({} as any);
      mockPrisma.approvalStepInstance.create.mockResolvedValue({} as any);
      mockEnhancedWorkflowEngine.processNextStep.mockResolvedValue();
      mockEnhancedWorkflowEngine.startStageApproval.mockResolvedValue({
        id: 'instance-123',
        workflowId: 'workflow123',
        entityType: 'procurement',
        entityId: 'proc-123',
        status: 'PENDING',
      } as any);

      const promises = concurrentApprovals.map(approval =>
        mockEnhancedWorkflowEngine.startStageApproval(approval)
      );

      const results = await Promise.allSettled(promises);

      expect(results.every(result => result.status === 'fulfilled')).toBe(true);
      expect(mockEnhancedWorkflowEngine.startStageApproval).toHaveBeenCalledTimes(10);
    });

    it('should handle bulk document generation', async () => {
      const bulkDocuments = Array.from({ length: 5 }, (_, i) => ({
        templateId: 'template123',
        data: { poNumber: `PO-2024-${String(i + 1).padStart(3, '0')}` },
        outputFormat: 'pdf' as const,
        fileName: `po-2024-${String(i + 1).padStart(3, '0')}.pdf`,
      }));

      mockEnhancedDocumentGenerator.generateDocument.mockResolvedValue({
        success: true,
        fileName: 'test.pdf',
        filePath: '/generated/test.pdf',
        fileSize: 1024,
      });

      const promises = bulkDocuments.map(doc =>
        mockEnhancedDocumentGenerator.generateDocument(doc)
      );

      const results = await Promise.allSettled(promises);

      expect(results.every(result => result.status === 'fulfilled')).toBe(true);
      expect(mockEnhancedDocumentGenerator.generateDocument).toHaveBeenCalledTimes(5);
    });
  });
});
