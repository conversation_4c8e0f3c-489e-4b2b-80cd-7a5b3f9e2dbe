import { prisma } from '@/lib/db';
import { auditLogger } from '@/lib/audit/comprehensive-audit-logger';
import { documentTemplateEngine } from '@/lib/documents/document-template-engine';
import { databasePerformanceOptimizer } from '@/lib/database/performance-optimizer';

// Mock dependencies
jest.mock('@/lib/db');
jest.mock('@/lib/audit/comprehensive-audit-logger');
jest.mock('@/lib/documents/document-template-engine');
jest.mock('@/lib/database/performance-optimizer');

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockAuditLogger = auditLogger as jest.Mocked<typeof auditLogger>;
const mockDocumentTemplateEngine = documentTemplateEngine as jest.Mocked<typeof documentTemplateEngine>;
const mockDatabasePerformanceOptimizer = databasePerformanceOptimizer as jest.Mocked<typeof databasePerformanceOptimizer>;

describe('Cross-Module Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Audit Trail Integration Across All Procurement Processes', () => {
    it('should log audit trail for complete procurement lifecycle', async () => {
      const mockUser = {
        id: 'user-1',
        name: 'Test User',
        email: '<EMAIL>',
      };

      const mockRequest = {
        headers: {
          get: jest.fn()
            .mockReturnValueOnce('192.168.1.1') // x-forwarded-for
            .mockReturnValueOnce('Mozilla/5.0'), // user-agent
        },
      };

      // Mock audit log creation
      mockPrisma.auditLog.create.mockResolvedValue({
        id: 'audit-1',
        userId: 'user-1',
        action: 'CREATE',
        entityType: 'PURCHASE_REQUISITION',
        entityId: 'pr-1',
      } as any);

      // Test audit logging for PR creation
      await auditLogger.logCrudOperation(
        'user-1',
        'CREATE',
        'PURCHASE_REQUISITION',
        'pr-1',
        null,
        { title: 'Office Supplies', amount: 5000000 },
        mockRequest as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'user-1',
        'CREATE',
        'PURCHASE_REQUISITION',
        'pr-1',
        null,
        { title: 'Office Supplies', amount: 5000000 },
        mockRequest
      );

      // Test audit logging for approval workflow
      await auditLogger.logWorkflowStateChange(
        'user-1',
        'workflow-1',
        'APPROVAL_WORKFLOW',
        'PENDING',
        'APPROVED',
        'PURCHASE_REQUISITION',
        'pr-1',
        'Approved by supervisor'
      );

      expect(mockAuditLogger.logWorkflowStateChange).toHaveBeenCalledWith(
        'user-1',
        'workflow-1',
        'APPROVAL_WORKFLOW',
        'PENDING',
        'APPROVED',
        'PURCHASE_REQUISITION',
        'pr-1',
        'Approved by supervisor'
      );

      // Test audit logging for procurement creation
      await auditLogger.logCrudOperation(
        'procurement-officer-1',
        'CREATE',
        'PROCUREMENT',
        'proc-1',
        null,
        { purchaseRequisitionId: 'pr-1', method: 'DIRECT_APPOINTMENT' },
        mockRequest as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'procurement-officer-1',
        'CREATE',
        'PROCUREMENT',
        'proc-1',
        null,
        { purchaseRequisitionId: 'pr-1', method: 'DIRECT_APPOINTMENT' },
        mockRequest
      );

      // Test audit logging for vendor verification
      await auditLogger.logCrudOperation(
        'procurement-manager-1',
        'UPDATE',
        'VENDOR',
        'vendor-1',
        { status: 'PENDING_VERIFICATION' },
        { status: 'VERIFIED' },
        mockRequest as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'procurement-manager-1',
        'UPDATE',
        'VENDOR',
        'vendor-1',
        { status: 'PENDING_VERIFICATION' },
        { status: 'VERIFIED' },
        mockRequest
      );

      // Verify all audit operations were called
      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledTimes(3);
      expect(mockAuditLogger.logWorkflowStateChange).toHaveBeenCalledTimes(1);
    });

    it('should provide comprehensive audit search across all entities', async () => {
      const mockAuditLogs = [
        {
          id: 'audit-1',
          userId: 'user-1',
          action: 'CREATE',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
          createdAt: new Date(),
        },
        {
          id: 'audit-2',
          userId: 'user-1',
          action: 'UPDATE',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
          createdAt: new Date(),
        },
        {
          id: 'audit-3',
          userId: 'procurement-officer-1',
          action: 'CREATE',
          entityType: 'PROCUREMENT',
          entityId: 'proc-1',
          createdAt: new Date(),
        },
      ];

      mockAuditLogger.getAuditLogs.mockResolvedValue({
        logs: mockAuditLogs,
        total: 3,
        page: 1,
        totalPages: 1,
      });

      const searchFilters = {
        entityTypes: ['PURCHASE_REQUISITION', 'PROCUREMENT'],
        actions: ['CREATE', 'UPDATE'],
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        page: 1,
        limit: 50,
      };

      const result = await auditLogger.getAuditLogs(searchFilters);

      expect(result.logs).toHaveLength(3);
      expect(result.total).toBe(3);
      expect(mockAuditLogger.getAuditLogs).toHaveBeenCalledWith(searchFilters);
    });
  });

  describe('Document Generation Triggered by Workflow State Changes', () => {
    it('should generate documents when workflow reaches specific states', async () => {
      // Mock template
      const mockTemplate = {
        id: 'template-1',
        name: 'Purchase Order Template',
        type: 'PURCHASE_ORDER',
        htmlTemplate: '<h1>Purchase Order</h1><p>{{orderNumber}}</p>',
        variables: [
          { name: 'orderNumber', type: 'TEXT', required: true },
          { name: 'vendorName', type: 'TEXT', required: true },
          { name: 'totalAmount', type: 'CURRENCY', required: true },
        ],
      };

      mockDocumentTemplateEngine.getTemplate.mockResolvedValue(mockTemplate as any);

      // Mock document generation
      const mockGeneratedDocument = {
        id: 'doc-1',
        templateId: 'template-1',
        fileName: 'PO-2024-001.pdf',
        filePath: '/generated/PO-2024-001.pdf',
        fileSize: 1024000,
        entityType: 'PURCHASE_ORDER',
        entityId: 'po-1',
        generatedAt: new Date(),
      };

      mockPrisma.generatedDocument.create.mockResolvedValue(mockGeneratedDocument as any);

      // Simulate workflow state change triggering document generation
      const workflowData = {
        entityType: 'PURCHASE_ORDER',
        entityId: 'po-1',
        newState: 'APPROVED',
        data: {
          orderNumber: 'PO-2024-001',
          vendorName: 'ABC Supplies Ltd',
          totalAmount: 5000000,
        },
      };

      // Test template retrieval
      const template = await documentTemplateEngine.getTemplate('template-1');
      expect(template).toEqual(mockTemplate);

      // Test document generation
      const generatedDoc = await mockPrisma.generatedDocument.create({
        data: {
          templateId: 'template-1',
          fileName: 'PO-2024-001.pdf',
          filePath: '/generated/PO-2024-001.pdf',
          fileSize: 1024000,
          entityType: 'PURCHASE_ORDER',
          entityId: 'po-1',
          generatedAt: new Date(),
        },
      });

      expect(generatedDoc.fileName).toBe('PO-2024-001.pdf');
      expect(generatedDoc.entityType).toBe('PURCHASE_ORDER');

      // Test audit logging for document generation
      await auditLogger.logCrudOperation(
        'system',
        'CREATE',
        'GENERATED_DOCUMENT',
        'doc-1',
        null,
        {
          templateId: 'template-1',
          fileName: 'PO-2024-001.pdf',
          entityType: 'PURCHASE_ORDER',
          entityId: 'po-1',
        },
        null as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'system',
        'CREATE',
        'GENERATED_DOCUMENT',
        'doc-1',
        null,
        expect.objectContaining({
          templateId: 'template-1',
          fileName: 'PO-2024-001.pdf',
        }),
        null
      );

      // Verify all operations
      expect(mockDocumentTemplateEngine.getTemplate).toHaveBeenCalledWith('template-1');
      expect(mockPrisma.generatedDocument.create).toHaveBeenCalledTimes(1);
      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledTimes(1);
    });

    it('should handle multiple document types for different workflow states', async () => {
      const documentConfigs = [
        {
          workflowState: 'APPROVED',
          templateId: 'po-template',
          documentType: 'PURCHASE_ORDER',
        },
        {
          workflowState: 'COMPLETED',
          templateId: 'bast-template',
          documentType: 'BAST',
        },
        {
          workflowState: 'RECEIVED',
          templateId: 'grn-template',
          documentType: 'GRN',
        },
      ];

      for (const config of documentConfigs) {
        const mockTemplate = {
          id: config.templateId,
          type: config.documentType,
          name: `${config.documentType} Template`,
        };

        mockDocumentTemplateEngine.getTemplate.mockResolvedValue(mockTemplate as any);

        const template = await documentTemplateEngine.getTemplate(config.templateId);
        expect(template.type).toBe(config.documentType);
      }

      expect(mockDocumentTemplateEngine.getTemplate).toHaveBeenCalledTimes(3);
    });
  });

  describe('Approval Workflow Integration with Notification System', () => {
    it('should send notifications for workflow state changes', async () => {
      // Mock notification creation
      const mockNotification = {
        id: 'notif-1',
        userId: 'supervisor-1',
        type: 'APPROVAL_REQUEST',
        title: 'Purchase Requisition Approval Required',
        message: 'A new purchase requisition requires your approval',
        entityType: 'PURCHASE_REQUISITION',
        entityId: 'pr-1',
        isRead: false,
        createdAt: new Date(),
      };

      mockPrisma.notification.create.mockResolvedValue(mockNotification as any);

      // Test notification creation for approval request
      const notification = await mockPrisma.notification.create({
        data: {
          userId: 'supervisor-1',
          type: 'APPROVAL_REQUEST',
          title: 'Purchase Requisition Approval Required',
          message: 'A new purchase requisition requires your approval',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
          isRead: false,
        },
      });

      expect(notification.type).toBe('APPROVAL_REQUEST');
      expect(notification.entityType).toBe('PURCHASE_REQUISITION');

      // Test audit logging for notification
      await auditLogger.logCrudOperation(
        'system',
        'CREATE',
        'NOTIFICATION',
        'notif-1',
        null,
        {
          userId: 'supervisor-1',
          type: 'APPROVAL_REQUEST',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
        },
        null as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'system',
        'CREATE',
        'NOTIFICATION',
        'notif-1',
        null,
        expect.objectContaining({
          type: 'APPROVAL_REQUEST',
          entityType: 'PURCHASE_REQUISITION',
        }),
        null
      );

      // Verify operations
      expect(mockPrisma.notification.create).toHaveBeenCalledTimes(1);
      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledTimes(1);
    });

    it('should handle notification escalation for overdue approvals', async () => {
      // Mock overdue approval scenario
      const overdueApproval = {
        id: 'approval-1',
        workflowId: 'workflow-1',
        entityType: 'PURCHASE_REQUISITION',
        entityId: 'pr-1',
        assignedTo: 'supervisor-1',
        dueDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day overdue
        status: 'PENDING',
      };

      // Mock escalation notification
      const escalationNotification = {
        id: 'notif-2',
        userId: 'manager-1',
        type: 'ESCALATION',
        title: 'Overdue Approval Escalation',
        message: 'An approval request is overdue and requires immediate attention',
        entityType: 'PURCHASE_REQUISITION',
        entityId: 'pr-1',
        priority: 'HIGH',
        isRead: false,
        createdAt: new Date(),
      };

      mockPrisma.notification.create.mockResolvedValue(escalationNotification as any);

      const escalation = await mockPrisma.notification.create({
        data: {
          userId: 'manager-1',
          type: 'ESCALATION',
          title: 'Overdue Approval Escalation',
          message: 'An approval request is overdue and requires immediate attention',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
          priority: 'HIGH',
          isRead: false,
        },
      });

      expect(escalation.type).toBe('ESCALATION');
      expect(escalation.priority).toBe('HIGH');
      expect(mockPrisma.notification.create).toHaveBeenCalledTimes(1);
    });
  });

  describe('Performance Monitoring Integration', () => {
    it('should monitor database performance across all procurement operations', async () => {
      // Mock performance logging
      mockDatabasePerformanceOptimizer.logQueryPerformance.mockResolvedValue(undefined);

      // Test performance monitoring for different operations
      const operations = [
        { operation: 'CREATE', tableName: 'purchase_requisitions', executionTime: 150 },
        { operation: 'UPDATE', tableName: 'vendors', executionTime: 75 },
        { operation: 'SELECT', tableName: 'procurements', executionTime: 200 },
        { operation: 'INSERT', tableName: 'audit_logs', executionTime: 50 },
      ];

      for (const op of operations) {
        await databasePerformanceOptimizer.logQueryPerformance(
          op.operation,
          op.tableName,
          op.executionTime
        );
      }

      expect(mockDatabasePerformanceOptimizer.logQueryPerformance).toHaveBeenCalledTimes(4);

      // Test performance metrics retrieval
      const mockMetrics = [
        {
          tableName: 'purchase_requisitions',
          operation: 'CREATE',
          avgExecutionTime: 150,
          maxExecutionTime: 200,
          minExecutionTime: 100,
          totalQueries: 10,
          slowQueries: 2,
          lastUpdated: new Date(),
        },
      ];

      mockDatabasePerformanceOptimizer.getPerformanceMetrics.mockResolvedValue(mockMetrics);

      const metrics = await databasePerformanceOptimizer.getPerformanceMetrics(
        'purchase_requisitions',
        new Date('2024-01-01'),
        new Date('2024-01-31')
      );

      expect(metrics).toHaveLength(1);
      expect(metrics[0].tableName).toBe('purchase_requisitions');
      expect(mockDatabasePerformanceOptimizer.getPerformanceMetrics).toHaveBeenCalledWith(
        'purchase_requisitions',
        new Date('2024-01-01'),
        new Date('2024-01-31')
      );
    });
  });
});
