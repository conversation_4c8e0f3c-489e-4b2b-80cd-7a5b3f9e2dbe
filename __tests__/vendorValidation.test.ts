import { z } from "zod";
import { describe, expect, test } from "@jest/globals";

// Define the Zod schema for vendor registration
const vendorRegistrationSchema = z
  .object({
    email: z.string().email({ message: "Invalid email address" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" }),
    confirmPassword: z.string(),
    companyName: z.string().min(1, { message: "Company name is required" }),
    npwpNumber: z
      .string()
      .regex(/^\d{16}$/, { message: "NPWP number must be 16 digits" }),
    address: z.string().min(1, { message: "Address is required" }),
    picName: z.string().min(1, { message: "PIC name is required" }),
    picEmail: z.string().email({ message: "Invalid PIC email address" }),
    picPhone: z
      .string()
      .regex(/^\+?\d{10,14}$/, { message: "Invalid phone number format" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

describe("Vendor Registration Validation", () => {
  test("validates a correct vendor registration form", () => {
    const validData = {
      email: "<EMAIL>",
      password: "Password123!",
      confirmPassword: "Password123!",
      companyName: "PT Sejahtera",
      npwpNumber: "1234567890123456",
      address: "Jl. Sudirman No. 1",
      picName: "John Doe",
      picEmail: "<EMAIL>",
      picPhone: "+6281234567890",
    };
    const result = vendorRegistrationSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  test("fails validation on invalid email", () => {
    const invalidData = {
      email: "invalid-email",
      password: "Password123!",
      confirmPassword: "Password123!",
      companyName: "PT Sejahtera",
      npwpNumber: "1234567890123456",
      address: "Jl. Sudirman No. 1",
      picName: "John Doe",
      picEmail: "<EMAIL>",
      picPhone: "+6281234567890",
    };
    const result = vendorRegistrationSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe("Invalid email address");
    }
  });

  test("fails validation on password mismatch", () => {
    const invalidData = {
      email: "<EMAIL>",
      password: "Password123!",
      confirmPassword: "DifferentPassword!",
      companyName: "PT Sejahtera",
      npwpNumber: "1234567890123456",
      address: "Jl. Sudirman No. 1",
      picName: "John Doe",
      picEmail: "<EMAIL>",
      picPhone: "+6281234567890",
    };
    const result = vendorRegistrationSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe("Passwords do not match");
    }
  });

  test("fails validation on invalid NPWP number", () => {
    const invalidData = {
      email: "<EMAIL>",
      password: "Password123!",
      confirmPassword: "Password123!",
      companyName: "PT Sejahtera",
      npwpNumber: "12345", // Too short
      address: "Jl. Sudirman No. 1",
      picName: "John Doe",
      picEmail: "<EMAIL>",
      picPhone: "+6281234567890",
    };
    const result = vendorRegistrationSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe(
        "NPWP number must be 16 digits"
      );
    }
  });
});
