import { ComprehensiveAuditSystem } from '@/lib/audit/comprehensive-audit-system';
import { prisma } from '@/lib/db';
import { NextRequest } from 'next/server';

jest.mock('@/lib/db');

describe('ComprehensiveAuditSystem', () => {
  let auditSystem: ComprehensiveAuditSystem;
  const mockPrisma = prisma as jest.Mocked<typeof prisma>;

  beforeEach(() => {
    auditSystem = new ComprehensiveAuditSystem();
    jest.clearAllMocks();
  });

  describe('logAudit', () => {
    const mockAuditData = {
      userId: 'user123',
      userEmail: '<EMAIL>',
      userRole: 'ADMIN',
      action: 'CREATE' as const,
      resource: 'procurement',
      resourceId: 'proc123',
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0',
      severity: 'MEDIUM' as const,
      category: 'DATA_CHANGE' as const,
      description: 'Created new procurement',
    };

    it('should log audit event successfully', async () => {
      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logAudit(mockAuditData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: {
          userId: 'user123',
          userEmail: '<EMAIL>',
          userRole: 'ADMIN',
          action: 'CREATE',
          resource: 'procurement',
          resourceId: 'proc123',
          oldValues: undefined,
          newValues: undefined,
          changedFields: undefined,
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0',
          sessionId: undefined,
          requestId: undefined,
          procurementId: undefined,
          workflowStage: undefined,
          approvalStep: undefined,
          severity: 'MEDIUM',
          category: 'DATA_CHANGE',
          metadata: undefined,
          description: 'Created new procurement',
          timestamp: expect.any(Date),
          partitionDate: expect.any(Date),
        },
      });
    });

    it('should handle database errors gracefully', async () => {
      mockPrisma.auditLog.create.mockRejectedValue(new Error('Database error'));

      // Should not throw error
      await expect(auditSystem.logAudit(mockAuditData)).resolves.toBeUndefined();
    });

    it('should use default values when not provided', async () => {
      const minimalData = {
        action: 'READ' as const,
        resource: 'user',
      };

      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logAudit(minimalData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'READ',
          resource: 'user',
          severity: 'LOW',
          category: 'GENERAL',
          timestamp: expect.any(Date),
          partitionDate: expect.any(Date),
        }),
      });
    });
  });

  describe('logAuthentication', () => {
    it('should log successful login', async () => {
      const authData = {
        userId: 'user123',
        userEmail: '<EMAIL>',
        action: 'LOGIN' as const,
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        sessionId: 'session123',
      };

      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logAuthentication(authData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'user123',
          userEmail: '<EMAIL>',
          action: 'LOGIN',
          resource: 'authentication',
          severity: 'LOW',
          category: 'AUTHENTICATION',
          description: 'User login',
        }),
      });
    });

    it('should log failed login with higher severity', async () => {
      const authData = {
        userEmail: '<EMAIL>',
        action: 'LOGIN_FAILED' as const,
        ipAddress: '***********',
      };

      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logAuthentication(authData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'LOGIN_FAILED',
          severity: 'MEDIUM',
          description: 'User login failed',
        }),
      });
    });
  });

  describe('logDataChange', () => {
    const mockRequest = {
      headers: new Map([
        ['x-forwarded-for', '***********'],
        ['user-agent', 'Mozilla/5.0'],
        ['x-request-id', 'req123'],
      ]),
    } as unknown as NextRequest;

    it('should log data change with old and new values', async () => {
      const changeData = {
        userId: 'user123',
        userEmail: '<EMAIL>',
        userRole: 'ADMIN',
        action: 'UPDATE' as const,
        resource: 'procurement',
        resourceId: 'proc123',
        oldValues: { status: 'DRAFT', title: 'Old Title' },
        newValues: { status: 'PUBLISHED', title: 'New Title' },
        request: mockRequest,
      };

      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logDataChange(changeData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'UPDATE',
          resource: 'procurement',
          oldValues: { status: 'DRAFT', title: 'Old Title' },
          newValues: { status: 'PUBLISHED', title: 'New Title' },
          changedFields: ['status', 'title'],
          category: 'DATA_CHANGE',
        }),
      });
    });

    it('should calculate changed fields for UPDATE operations', async () => {
      const changeData = {
        action: 'UPDATE' as const,
        resource: 'vendor',
        resourceId: 'vendor123',
        oldValues: { name: 'Old Name', status: 'PENDING' },
        newValues: { name: 'New Name', status: 'PENDING' },
      };

      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logDataChange(changeData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          changedFields: ['name'], // Only name changed
        }),
      });
    });

    it('should not calculate changed fields for CREATE operations', async () => {
      const changeData = {
        action: 'CREATE' as const,
        resource: 'vendor',
        resourceId: 'vendor123',
        newValues: { name: 'New Vendor', status: 'PENDING' },
      };

      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logDataChange(changeData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          changedFields: [],
        }),
      });
    });
  });

  describe('logApprovalAction', () => {
    const mockRequest = {
      headers: new Map([
        ['x-forwarded-for', '***********'],
        ['user-agent', 'Mozilla/5.0'],
      ]),
    } as unknown as NextRequest;

    it('should log approval action', async () => {
      const approvalData = {
        userId: 'user123',
        userEmail: '<EMAIL>',
        userRole: 'APPROVER',
        action: 'APPROVE' as const,
        procurementId: 'proc123',
        workflowStage: 'vendor_registration',
        approvalStep: 'final_approval',
        resourceId: 'approval123',
        metadata: { comments: 'Approved with conditions' },
        request: mockRequest,
      };

      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logApprovalAction(approvalData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'APPROVE',
          resource: 'approval',
          procurementId: 'proc123',
          workflowStage: 'vendor_registration',
          approvalStep: 'final_approval',
          severity: 'MEDIUM',
          category: 'APPROVAL_WORKFLOW',
          metadata: { comments: 'Approved with conditions' },
        }),
      });
    });
  });

  describe('logSecurityEvent', () => {
    const mockRequest = {
      headers: new Map([
        ['x-forwarded-for', '***********'],
        ['user-agent', 'Mozilla/5.0'],
      ]),
    } as unknown as NextRequest;

    it('should log security event', async () => {
      const securityData = {
        userId: 'user123',
        userEmail: '<EMAIL>',
        action: 'LOGIN' as const,
        resource: 'authentication',
        severity: 'HIGH' as const,
        description: 'Multiple failed login attempts detected',
        metadata: { attemptCount: 5 },
        request: mockRequest,
      };

      mockPrisma.auditLog.create.mockResolvedValue({} as any);

      await auditSystem.logSecurityEvent(securityData);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          action: 'LOGIN',
          resource: 'authentication',
          severity: 'HIGH',
          category: 'SECURITY',
          description: 'Multiple failed login attempts detected',
          metadata: { attemptCount: 5 },
        }),
      });
    });
  });

  describe('searchAuditLogs', () => {
    it('should search audit logs with filters', async () => {
      const filters = {
        userId: 'user123',
        action: 'CREATE' as const,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        searchText: 'procurement',
      };

      const mockLogs = [
        {
          id: 'log1',
          userId: 'user123',
          action: 'CREATE',
          resource: 'procurement',
          timestamp: new Date(),
        },
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(mockLogs as any);
      mockPrisma.auditLog.count.mockResolvedValue(1);

      const result = await auditSystem.searchAuditLogs(filters, 1, 10);

      expect(mockPrisma.auditLog.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user123',
          action: 'CREATE',
          timestamp: {
            gte: new Date('2024-01-01'),
            lte: new Date('2024-01-31'),
          },
          OR: [
            { userEmail: { contains: 'procurement', mode: 'insensitive' } },
            { description: { contains: 'procurement', mode: 'insensitive' } },
            { resource: { contains: 'procurement', mode: 'insensitive' } },
          ],
        },
        orderBy: { timestamp: 'desc' },
        skip: 0,
        take: 10,
      });

      expect(result).toEqual({
        logs: mockLogs,
        totalCount: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it('should handle empty search results', async () => {
      mockPrisma.auditLog.findMany.mockResolvedValue([]);
      mockPrisma.auditLog.count.mockResolvedValue(0);

      const result = await auditSystem.searchAuditLogs({}, 1, 10);

      expect(result).toEqual({
        logs: [],
        totalCount: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      });
    });

    it('should calculate pagination correctly', async () => {
      mockPrisma.auditLog.findMany.mockResolvedValue([]);
      mockPrisma.auditLog.count.mockResolvedValue(25);

      const result = await auditSystem.searchAuditLogs({}, 2, 10);

      expect(mockPrisma.auditLog.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: { timestamp: 'desc' },
        skip: 10, // (page - 1) * limit
        take: 10,
      });

      expect(result.totalPages).toBe(3); // Math.ceil(25 / 10)
    });
  });

  describe('getAuditStatistics', () => {
    it('should return comprehensive audit statistics', async () => {
      const mockStats = {
        totalLogs: 100,
        actionStats: [
          { action: 'CREATE', _count: { action: 30 } },
          { action: 'UPDATE', _count: { action: 25 } },
        ],
        severityStats: [
          { severity: 'LOW', _count: { severity: 50 } },
          { severity: 'MEDIUM', _count: { severity: 30 } },
        ],
        categoryStats: [
          { category: 'DATA_CHANGE', _count: { category: 40 } },
          { category: 'AUTHENTICATION', _count: { category: 20 } },
        ],
        userStats: [
          { userEmail: '<EMAIL>', _count: { userEmail: 15 } },
          { userEmail: '<EMAIL>', _count: { userEmail: 10 } },
        ],
      };

      mockPrisma.auditLog.count.mockResolvedValue(100);
      mockPrisma.auditLog.aggregate.mockResolvedValue({ _avg: { id: 85.5 } } as any);
      mockPrisma.auditLog.groupBy
        .mockResolvedValueOnce(mockStats.actionStats as any)
        .mockResolvedValueOnce(mockStats.severityStats as any)
        .mockResolvedValueOnce(mockStats.categoryStats as any)
        .mockResolvedValueOnce(mockStats.userStats as any);

      const result = await auditSystem.getAuditStatistics();

      expect(result).toEqual({
        totalLogs: 100,
        averageScore: 85.5,
        actionStats: mockStats.actionStats,
        severityStats: mockStats.severityStats,
        categoryStats: mockStats.categoryStats,
        userStats: mockStats.userStats,
      });
    });

    it('should apply date filters when provided', async () => {
      const filters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        procurementId: 'proc123',
      };

      mockPrisma.auditLog.count.mockResolvedValue(50);
      mockPrisma.auditLog.aggregate.mockResolvedValue({ _avg: { id: 75.0 } } as any);
      mockPrisma.auditLog.groupBy.mockResolvedValue([]);

      await auditSystem.getAuditStatistics(filters);

      const expectedWhere = {
        timestamp: {
          gte: new Date('2024-01-01'),
          lte: new Date('2024-01-31'),
        },
        procurementId: 'proc123',
      };

      expect(mockPrisma.auditLog.count).toHaveBeenCalledWith({ where: expectedWhere });
      expect(mockPrisma.auditLog.groupBy).toHaveBeenCalledWith(
        expect.objectContaining({ where: expectedWhere })
      );
    });
  });

  describe('archiveOldLogs', () => {
    it('should archive logs older than specified days', async () => {
      const oldLogs = [
        {
          id: 'log1',
          userId: 'user123',
          action: 'CREATE',
          timestamp: new Date('2023-01-01'),
        },
        {
          id: 'log2',
          userId: 'user456',
          action: 'UPDATE',
          timestamp: new Date('2023-01-02'),
        },
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(oldLogs as any);
      mockPrisma.auditLogArchive.createMany.mockResolvedValue({ count: 2 } as any);
      mockPrisma.auditLog.deleteMany.mockResolvedValue({ count: 2 } as any);

      const result = await auditSystem.archiveOldLogs(365);

      expect(mockPrisma.auditLog.findMany).toHaveBeenCalledWith({
        where: {
          timestamp: { lt: expect.any(Date) },
        },
      });

      expect(mockPrisma.auditLogArchive.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            originalId: 'log1',
            archiveReason: 'Archived after 365 days',
          }),
          expect.objectContaining({
            originalId: 'log2',
            archiveReason: 'Archived after 365 days',
          }),
        ]),
      });

      expect(mockPrisma.auditLog.deleteMany).toHaveBeenCalledWith({
        where: {
          timestamp: { lt: expect.any(Date) },
        },
      });

      expect(result).toBe(2);
    });

    it('should return 0 when no logs to archive', async () => {
      mockPrisma.auditLog.findMany.mockResolvedValue([]);

      const result = await auditSystem.archiveOldLogs(365);

      expect(result).toBe(0);
      expect(mockPrisma.auditLogArchive.createMany).not.toHaveBeenCalled();
      expect(mockPrisma.auditLog.deleteMany).not.toHaveBeenCalled();
    });
  });

  describe('detectSuspiciousActivities', () => {
    it('should detect multiple failed logins', async () => {
      const failedLogins = [
        {
          userEmail: '<EMAIL>',
          ipAddress: '***********',
          _count: { action: 5 },
        },
      ];

      const highVolumeUsers = [
        {
          userId: 'user123',
          _count: { action: 150 },
        },
      ];

      const afterHoursActions = [
        {
          id: 'log1',
          action: 'DELETE',
          severity: 'CRITICAL',
          timestamp: new Date(),
        },
      ];

      mockPrisma.auditLog.groupBy
        .mockResolvedValueOnce(failedLogins as any)
        .mockResolvedValueOnce(highVolumeUsers as any);
      mockPrisma.auditLog.findMany.mockResolvedValue(afterHoursActions as any);

      const result = await auditSystem.detectSuspiciousActivities(24);

      expect(result).toEqual([
        {
          type: 'MULTIPLE_FAILED_LOGINS',
          userEmail: '<EMAIL>',
          ipAddress: '***********',
          count: 5,
        },
        {
          type: 'HIGH_VOLUME_ACTIVITY',
          userId: 'user123',
          count: 150,
        },
        {
          type: 'AFTER_HOURS_CRITICAL_ACTION',
          id: 'log1',
          action: 'DELETE',
          severity: 'CRITICAL',
          timestamp: expect.any(Date),
        },
      ]);
    });

    it('should return empty array when no suspicious activities found', async () => {
      mockPrisma.auditLog.groupBy.mockResolvedValue([]);
      mockPrisma.auditLog.findMany.mockResolvedValue([]);

      const result = await auditSystem.detectSuspiciousActivities(24);

      expect(result).toEqual([]);
    });
  });

  describe('Private helper methods', () => {
    describe('getChangedFields', () => {
      it('should identify changed fields between old and new values', () => {
        const oldValues = { name: 'Old Name', status: 'DRAFT', value: 100 };
        const newValues = { name: 'New Name', status: 'DRAFT', value: 200 };

        const result = (auditSystem as any).getChangedFields(oldValues, newValues);

        expect(result).toEqual(['name', 'value']);
      });

      it('should return empty array when no changes', () => {
        const oldValues = { name: 'Same Name', status: 'DRAFT' };
        const newValues = { name: 'Same Name', status: 'DRAFT' };

        const result = (auditSystem as any).getChangedFields(oldValues, newValues);

        expect(result).toEqual([]);
      });
    });

    describe('getClientIP', () => {
      it('should extract IP from x-forwarded-for header', () => {
        const mockRequest = {
          headers: {
            get: jest.fn().mockImplementation((header) => {
              if (header === 'x-forwarded-for') return '***********, ********';
              return null;
            }),
          },
        } as unknown as NextRequest;

        const result = (auditSystem as any).getClientIP(mockRequest);

        expect(result).toBe('***********');
      });

      it('should extract IP from x-real-ip header when x-forwarded-for not available', () => {
        const mockRequest = {
          headers: {
            get: jest.fn().mockImplementation((header) => {
              if (header === 'x-real-ip') return '***********';
              return null;
            }),
          },
        } as unknown as NextRequest;

        const result = (auditSystem as any).getClientIP(mockRequest);

        expect(result).toBe('***********');
      });

      it('should return "unknown" when no IP headers available', () => {
        const mockRequest = {
          headers: {
            get: jest.fn().mockReturnValue(null),
          },
        } as unknown as NextRequest;

        const result = (auditSystem as any).getClientIP(mockRequest);

        expect(result).toBe('unknown');
      });
    });

    describe('determineSeverity', () => {
      it('should return CRITICAL for user deletion', () => {
        const result = (auditSystem as any).determineSeverity('user', 'DELETE');
        expect(result).toBe('CRITICAL');
      });

      it('should return HIGH for approval actions', () => {
        const result = (auditSystem as any).determineSeverity('approval', 'APPROVE');
        expect(result).toBe('HIGH');
      });

      it('should return MEDIUM for procurement creation', () => {
        const result = (auditSystem as any).determineSeverity('procurement', 'CREATE');
        expect(result).toBe('MEDIUM');
      });

      it('should return LOW for other actions', () => {
        const result = (auditSystem as any).determineSeverity('document', 'READ');
        expect(result).toBe('LOW');
      });
    });
  });
});
