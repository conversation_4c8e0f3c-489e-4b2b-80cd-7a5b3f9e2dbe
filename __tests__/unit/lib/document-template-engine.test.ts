import { jest } from '@jest/globals';

// Mock AWS S3
const mockS3Send = jest.fn();
const mockS3Client = jest.fn(() => ({
  send: mockS3Send,
}));

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: mockS3Client,
  PutObjectCommand: jest.fn(),
}));

// Mock fs/promises
const mockWriteFile = jest.fn();
const mockMkdir = jest.fn();

jest.mock('fs/promises', () => ({
  writeFile: mockWriteFile,
  mkdir: mockMkdir,
}));

// Mock path
jest.mock('path', () => ({
  join: jest.fn((...args) => args.join('/')),
}));

// Mock process.cwd
const originalCwd = process.cwd;
const mockCwd = jest.fn(() => '/test/workspace');

// Mock jsPDF
const mockJsPDFSave = jest.fn();
const mockJsPDFHtml = jest.fn();
const mockJsPDF = jest.fn(() => ({
  html: mockJsPDFHtml,
  save: mockJsPDFSave,
  output: jest.fn(() => Buffer.from('test-pdf-content')),
}));

jest.mock('jspdf', () => ({
  jsPDF: mockJsPDF,
}));

// Mock handlebars
const mockHandlebarsCompile = jest.fn();
const mockHandlebarsRegisterHelper = jest.fn();

jest.mock('handlebars', () => ({
  compile: mockHandlebarsCompile,
  registerHelper: mockHandlebarsRegisterHelper,
}));

import { documentTemplateEngine } from '@/lib/documents/document-template-engine';

describe('Document Template Engine', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.cwd = mockCwd;
    
    // Set up default environment
    process.env.NODE_ENV = 'test';
    delete process.env.AWS_S3_BUCKET;
    delete process.env.AWS_REGION;
    delete process.env.AWS_ACCESS_KEY_ID;
    delete process.env.AWS_SECRET_ACCESS_KEY;
  });

  afterEach(() => {
    process.cwd = originalCwd;
    // Clean up environment variables
    delete process.env.AWS_S3_BUCKET;
    delete process.env.AWS_REGION;
    delete process.env.AWS_ACCESS_KEY_ID;
    delete process.env.AWS_SECRET_ACCESS_KEY;
  });

  describe('generateDocument', () => {
    const mockTemplate = {
      id: 'template-123',
      name: 'Test Template',
      content: '<h1>{{title}}</h1><p>{{description}}</p>',
      variables: [
        {
          name: 'title',
          type: 'TEXT',
          required: true,
          description: 'Document title',
        },
        {
          name: 'description',
          type: 'TEXT',
          required: false,
          description: 'Document description',
        },
      ],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockData = {
      title: 'Test Document',
      description: 'This is a test document',
    };

    it('should generate document successfully with local storage', async () => {
      const mockCompiledTemplate = jest.fn(() => '<h1>Test Document</h1><p>This is a test document</p>');
      mockHandlebarsCompile.mockReturnValue(mockCompiledTemplate);

      mockJsPDFHtml.mockImplementation((content, options) => {
        options.callback();
      });

      mockMkdir.mockResolvedValue(undefined);
      mockWriteFile.mockResolvedValue(undefined);

      const result = await documentTemplateEngine.generateDocument(mockTemplate, mockData);

      expect(mockHandlebarsCompile).toHaveBeenCalledWith(mockTemplate.content);
      expect(mockCompiledTemplate).toHaveBeenCalledWith(mockData);
      expect(mockJsPDFHtml).toHaveBeenCalled();
      expect(mockMkdir).toHaveBeenCalledWith('/test/workspace/uploads/documents', { recursive: true });
      expect(mockWriteFile).toHaveBeenCalled();
      expect(result).toMatch(/^\/uploads\/documents\/.*\.pdf$/);
    });

    it('should generate document successfully with S3 storage', async () => {
      process.env.AWS_S3_BUCKET = 'test-bucket';
      process.env.AWS_REGION = 'us-east-1';
      process.env.AWS_ACCESS_KEY_ID = 'test-key';
      process.env.AWS_SECRET_ACCESS_KEY = 'test-secret';

      const mockCompiledTemplate = jest.fn(() => '<h1>Test Document</h1><p>This is a test document</p>');
      mockHandlebarsCompile.mockReturnValue(mockCompiledTemplate);

      mockJsPDFHtml.mockImplementation((content, options) => {
        options.callback();
      });

      mockS3Send.mockResolvedValue({});

      const result = await documentTemplateEngine.generateDocument(mockTemplate, mockData);

      expect(mockS3Client).toHaveBeenCalledWith({
        region: 'us-east-1',
        credentials: {
          accessKeyId: 'test-key',
          secretAccessKey: 'test-secret',
        },
      });

      expect(mockS3Send).toHaveBeenCalled();
      expect(result).toMatch(/^https:\/\/test-bucket\.s3\.us-east-1\.amazonaws\.com\/documents\/.*\.pdf$/);
    });

    it('should validate required variables', async () => {
      const invalidData = {
        description: 'This is a test document',
        // missing required 'title' field
      };

      await expect(
        documentTemplateEngine.generateDocument(mockTemplate, invalidData)
      ).rejects.toThrow("Required variable 'title' is missing");
    });

    it('should validate variable types', async () => {
      const invalidData = {
        title: 123, // should be string
        description: 'This is a test document',
      };

      await expect(
        documentTemplateEngine.generateDocument(mockTemplate, invalidData)
      ).rejects.toThrow("Variable 'title' must be a string");
    });

    it('should handle number validation', async () => {
      const numberTemplate = {
        ...mockTemplate,
        variables: [
          {
            name: 'amount',
            type: 'NUMBER',
            required: true,
            description: 'Amount',
            validation: { min: 0, max: 1000 },
          },
        ],
      };

      const invalidData = { amount: -10 };

      await expect(
        documentTemplateEngine.generateDocument(numberTemplate, invalidData)
      ).rejects.toThrow("Variable 'amount' must be at least 0");

      const invalidData2 = { amount: 1500 };

      await expect(
        documentTemplateEngine.generateDocument(numberTemplate, invalidData2)
      ).rejects.toThrow("Variable 'amount' must be at most 1000");
    });

    it('should handle array validation', async () => {
      const arrayTemplate = {
        ...mockTemplate,
        variables: [
          {
            name: 'items',
            type: 'ARRAY',
            required: true,
            description: 'Items list',
          },
        ],
      };

      const invalidData = { items: 'not an array' };

      await expect(
        documentTemplateEngine.generateDocument(arrayTemplate, invalidData)
      ).rejects.toThrow("Variable 'items' must be an array");
    });

    it('should handle object validation', async () => {
      const objectTemplate = {
        ...mockTemplate,
        variables: [
          {
            name: 'metadata',
            type: 'OBJECT',
            required: true,
            description: 'Metadata object',
          },
        ],
      };

      const invalidData = { metadata: ['not', 'an', 'object'] };

      await expect(
        documentTemplateEngine.generateDocument(objectTemplate, invalidData)
      ).rejects.toThrow("Variable 'metadata' must be an object");
    });

    it('should handle boolean validation', async () => {
      const booleanTemplate = {
        ...mockTemplate,
        variables: [
          {
            name: 'isActive',
            type: 'BOOLEAN',
            required: true,
            description: 'Active status',
          },
        ],
      };

      const invalidData = { isActive: 'yes' };

      await expect(
        documentTemplateEngine.generateDocument(booleanTemplate, invalidData)
      ).rejects.toThrow("Variable 'isActive' must be a boolean");
    });

    it('should handle date validation', async () => {
      const dateTemplate = {
        ...mockTemplate,
        variables: [
          {
            name: 'createdAt',
            type: 'DATE',
            required: true,
            description: 'Creation date',
          },
        ],
      };

      const invalidData = { createdAt: 123 };

      await expect(
        documentTemplateEngine.generateDocument(dateTemplate, invalidData)
      ).rejects.toThrow("Variable 'createdAt' must be a date");
    });

    it('should handle S3 upload failure', async () => {
      process.env.AWS_S3_BUCKET = 'test-bucket';
      process.env.AWS_REGION = 'us-east-1';
      process.env.AWS_ACCESS_KEY_ID = 'test-key';
      process.env.AWS_SECRET_ACCESS_KEY = 'test-secret';

      const mockCompiledTemplate = jest.fn(() => '<h1>Test Document</h1>');
      mockHandlebarsCompile.mockReturnValue(mockCompiledTemplate);

      mockJsPDFHtml.mockImplementation((content, options) => {
        options.callback();
      });

      mockS3Send.mockRejectedValue(new Error('S3 Upload Failed'));

      await expect(
        documentTemplateEngine.generateDocument(mockTemplate, mockData)
      ).rejects.toThrow('Failed to save document to cloud storage');
    });

    it('should handle local filesystem failure', async () => {
      const mockCompiledTemplate = jest.fn(() => '<h1>Test Document</h1>');
      mockHandlebarsCompile.mockReturnValue(mockCompiledTemplate);

      mockJsPDFHtml.mockImplementation((content, options) => {
        options.callback();
      });

      mockMkdir.mockResolvedValue(undefined);
      mockWriteFile.mockRejectedValue(new Error('File write failed'));

      await expect(
        documentTemplateEngine.generateDocument(mockTemplate, mockData)
      ).rejects.toThrow('Failed to save document to local storage');
    });
  });
});
