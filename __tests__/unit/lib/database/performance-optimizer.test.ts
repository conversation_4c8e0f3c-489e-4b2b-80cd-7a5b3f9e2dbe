import { DatabasePerformanceOptimizer } from '@/lib/database/performance-optimizer';
import { prisma } from '@/lib/db';

jest.mock('@/lib/db');

describe('DatabasePerformanceOptimizer', () => {
  let optimizer: DatabasePerformanceOptimizer;
  const mockPrisma = prisma as jest.Mocked<typeof prisma>;

  beforeEach(() => {
    optimizer = new DatabasePerformanceOptimizer();
    jest.clearAllMocks();
  });

  describe('initializePartitioning', () => {
    it('should initialize partitioning for all configured tables', async () => {
      mockPrisma.$queryRawUnsafe.mockResolvedValue([{ count: 0 }]);
      mockPrisma.$executeRawUnsafe.mockResolvedValue(undefined);

      // Mock private methods
      jest.spyOn(optimizer as any, 'isTablePartitioned').mockResolvedValue(false);
      jest.spyOn(optimizer as any, 'createPartitionedTable').mockResolvedValue(undefined);
      jest.spyOn(optimizer as any, 'createInitialPartitions').mockResolvedValue(undefined);

      await optimizer.initializePartitioning();

      expect(optimizer['createPartitionedTable']).toHaveBeenCalledTimes(3); // audit_logs, notifications, db_performance_logs
      expect(optimizer['createInitialPartitions']).toHaveBeenCalledTimes(3);
    });

    it('should skip already partitioned tables', async () => {
      jest.spyOn(optimizer as any, 'isTablePartitioned').mockResolvedValue(true);
      jest.spyOn(optimizer as any, 'createPartitionedTable').mockResolvedValue(undefined);
      jest.spyOn(optimizer as any, 'createInitialPartitions').mockResolvedValue(undefined);

      await optimizer.initializePartitioning();

      expect(optimizer['createPartitionedTable']).not.toHaveBeenCalled();
      expect(optimizer['createInitialPartitions']).toHaveBeenCalledTimes(3);
    });

    it('should handle errors gracefully', async () => {
      jest.spyOn(optimizer as any, 'isTablePartitioned').mockRejectedValue(new Error('DB error'));
      jest.spyOn(optimizer as any, 'createPartitionedTable').mockResolvedValue(undefined);
      jest.spyOn(optimizer as any, 'createInitialPartitions').mockResolvedValue(undefined);

      // Should not throw
      await expect(optimizer.initializePartitioning()).resolves.toBeUndefined();
    });
  });

  describe('isTablePartitioned', () => {
    it('should return true for partitioned table', async () => {
      mockPrisma.$queryRawUnsafe.mockResolvedValue([{ count: 1 }]);

      const result = await (optimizer as any).isTablePartitioned('audit_logs');

      expect(result).toBe(true);
      expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('pg_partitioned_table'),
        'audit_logs'
      );
    });

    it('should return false for non-partitioned table', async () => {
      mockPrisma.$queryRawUnsafe.mockResolvedValue([{ count: 0 }]);

      const result = await (optimizer as any).isTablePartitioned('audit_logs');

      expect(result).toBe(false);
    });

    it('should return false on error', async () => {
      mockPrisma.$queryRawUnsafe.mockRejectedValue(new Error('DB error'));

      const result = await (optimizer as any).isTablePartitioned('audit_logs');

      expect(result).toBe(false);
    });
  });

  describe('createFuturePartitions', () => {
    it('should create future partitions for auto-enabled tables', async () => {
      jest.spyOn(optimizer as any, 'getPartitionDate').mockImplementation((date, interval, offset) => {
        const newDate = new Date(date);
        newDate.setMonth(newDate.getMonth() + offset);
        return newDate;
      });
      jest.spyOn(optimizer as any, 'getPartitionName').mockReturnValue('audit_logs_y2024m04');
      jest.spyOn(optimizer as any, 'partitionExists').mockResolvedValue(false);
      jest.spyOn(optimizer as any, 'createPartition').mockResolvedValue(undefined);

      await optimizer.createFuturePartitions();

      expect(optimizer['createPartition']).toHaveBeenCalledTimes(3); // For each configured table
    });

    it('should skip existing partitions', async () => {
      jest.spyOn(optimizer as any, 'getPartitionDate').mockReturnValue(new Date());
      jest.spyOn(optimizer as any, 'getPartitionName').mockReturnValue('audit_logs_y2024m04');
      jest.spyOn(optimizer as any, 'partitionExists').mockResolvedValue(true);
      jest.spyOn(optimizer as any, 'createPartition').mockResolvedValue(undefined);

      await optimizer.createFuturePartitions();

      expect(optimizer['createPartition']).not.toHaveBeenCalled();
    });

    it('should handle errors for individual tables', async () => {
      jest.spyOn(optimizer as any, 'getPartitionDate').mockReturnValue(new Date());
      jest.spyOn(optimizer as any, 'getPartitionName').mockReturnValue('audit_logs_y2024m04');
      jest.spyOn(optimizer as any, 'partitionExists').mockRejectedValue(new Error('DB error'));

      // Should not throw
      await expect(optimizer.createFuturePartitions()).resolves.toBeUndefined();
    });
  });

  describe('cleanupOldPartitions', () => {
    it('should cleanup old partitions based on retention period', async () => {
      jest.spyOn(optimizer as any, 'findOldPartitions').mockResolvedValue(['audit_logs_y2021m01', 'audit_logs_y2021m02']);
      jest.spyOn(optimizer as any, 'dropPartition').mockResolvedValue(undefined);

      await optimizer.cleanupOldPartitions();

      expect(optimizer['dropPartition']).toHaveBeenCalledWith('audit_logs_y2021m01');
      expect(optimizer['dropPartition']).toHaveBeenCalledWith('audit_logs_y2021m02');
    });

    it('should handle no old partitions', async () => {
      jest.spyOn(optimizer as any, 'findOldPartitions').mockResolvedValue([]);
      jest.spyOn(optimizer as any, 'dropPartition').mockResolvedValue(undefined);

      await optimizer.cleanupOldPartitions();

      expect(optimizer['dropPartition']).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      jest.spyOn(optimizer as any, 'findOldPartitions').mockRejectedValue(new Error('DB error'));

      // Should not throw
      await expect(optimizer.cleanupOldPartitions()).resolves.toBeUndefined();
    });
  });

  describe('logQueryPerformance', () => {
    it('should log query performance metrics', async () => {
      mockPrisma.databasePerformanceLog.create.mockResolvedValue({} as any);

      await optimizer.logQueryPerformance('SELECT', 'users', 1500, 'query123');

      expect(mockPrisma.databasePerformanceLog.create).toHaveBeenCalledWith({
        data: {
          operation: 'SELECT',
          tableName: 'users',
          queryHash: 'query123',
          executionTime: 1500,
          rowsAffected: 0,
          timestamp: expect.any(Date),
          partitionDate: expect.any(Date),
        },
      });
    });

    it('should generate query hash when not provided', async () => {
      mockPrisma.databasePerformanceLog.create.mockResolvedValue({} as any);
      jest.spyOn(optimizer as any, 'generateQueryHash').mockReturnValue('generated_hash');

      await optimizer.logQueryPerformance('SELECT', 'users', 1500);

      expect(optimizer['generateQueryHash']).toHaveBeenCalledWith('SELECT', 'users');
      expect(mockPrisma.databasePerformanceLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          queryHash: 'generated_hash',
        }),
      });
    });

    it('should handle logging errors gracefully', async () => {
      mockPrisma.databasePerformanceLog.create.mockRejectedValue(new Error('DB error'));

      // Should not throw
      await expect(optimizer.logQueryPerformance('SELECT', 'users', 1500)).resolves.toBeUndefined();
    });
  });

  describe('getPerformanceMetrics', () => {
    it('should return performance metrics for all tables', async () => {
      const mockMetrics = [
        {
          tableName: 'users',
          operation: 'SELECT',
          _avg: { executionTime: 150 },
          _max: { executionTime: 500 },
          _min: { executionTime: 50 },
          _count: { executionTime: 100 },
        },
        {
          tableName: 'audit_logs',
          operation: 'INSERT',
          _avg: { executionTime: 75 },
          _max: { executionTime: 200 },
          _min: { executionTime: 25 },
          _count: { executionTime: 50 },
        },
      ];

      mockPrisma.databasePerformanceLog.groupBy.mockResolvedValue(mockMetrics as any);

      const result = await optimizer.getPerformanceMetrics();

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        tableName: 'users',
        operation: 'SELECT',
        avgExecutionTime: 150,
        maxExecutionTime: 500,
        minExecutionTime: 50,
        totalQueries: 100,
        slowQueries: 0,
        lastUpdated: expect.any(Date),
      });
    });

    it('should filter by table name when provided', async () => {
      mockPrisma.databasePerformanceLog.groupBy.mockResolvedValue([]);

      await optimizer.getPerformanceMetrics('users');

      expect(mockPrisma.databasePerformanceLog.groupBy).toHaveBeenCalledWith({
        by: ['tableName', 'operation'],
        where: { tableName: 'users' },
        _avg: { executionTime: true },
        _max: { executionTime: true },
        _min: { executionTime: true },
        _count: { executionTime: true },
      });
    });

    it('should filter by date range when provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      mockPrisma.databasePerformanceLog.groupBy.mockResolvedValue([]);

      await optimizer.getPerformanceMetrics(undefined, startDate, endDate);

      expect(mockPrisma.databasePerformanceLog.groupBy).toHaveBeenCalledWith({
        by: ['tableName', 'operation'],
        where: {
          timestamp: {
            gte: startDate,
            lte: endDate,
          },
        },
        _avg: { executionTime: true },
        _max: { executionTime: true },
        _min: { executionTime: true },
        _count: { executionTime: true },
      });
    });
  });

  describe('optimizeIndexes', () => {
    it('should analyze slow queries and suggest optimizations', async () => {
      const mockSlowQueries = [
        {
          tableName: 'users',
          operation: 'SELECT',
          executionTime: 2000,
          timestamp: new Date(),
        },
      ];

      jest.spyOn(optimizer as any, 'findSlowQueries').mockResolvedValue(mockSlowQueries);
      jest.spyOn(optimizer as any, 'suggestIndexOptimizations').mockResolvedValue(undefined);

      await optimizer.optimizeIndexes();

      expect(optimizer['findSlowQueries']).toHaveBeenCalled();
      expect(optimizer['suggestIndexOptimizations']).toHaveBeenCalledWith(mockSlowQueries[0]);
    });

    it('should handle no slow queries', async () => {
      jest.spyOn(optimizer as any, 'findSlowQueries').mockResolvedValue([]);
      jest.spyOn(optimizer as any, 'suggestIndexOptimizations').mockResolvedValue(undefined);

      await optimizer.optimizeIndexes();

      expect(optimizer['suggestIndexOptimizations']).not.toHaveBeenCalled();
    });
  });

  describe('findSlowQueries', () => {
    it('should find queries above threshold', async () => {
      const mockSlowQueries = [
        {
          tableName: 'users',
          operation: 'SELECT',
          executionTime: 2000,
          timestamp: new Date(),
        },
      ];

      mockPrisma.databasePerformanceLog.findMany.mockResolvedValue(mockSlowQueries as any);

      const result = await (optimizer as any).findSlowQueries();

      expect(mockPrisma.databasePerformanceLog.findMany).toHaveBeenCalledWith({
        where: {
          executionTime: { gte: 1000 },
          timestamp: {
            gte: expect.any(Date),
          },
        },
        orderBy: { executionTime: 'desc' },
        take: 50,
      });

      expect(result).toEqual(mockSlowQueries);
    });
  });

  describe('performMaintenance', () => {
    it('should perform all maintenance tasks', async () => {
      jest.spyOn(optimizer as any, 'updateTableStatistics').mockResolvedValue(undefined);
      jest.spyOn(optimizer as any, 'reindexTables').mockResolvedValue(undefined);
      jest.spyOn(optimizer as any, 'vacuumAnalyze').mockResolvedValue(undefined);

      await optimizer.performMaintenance();

      expect(optimizer['updateTableStatistics']).toHaveBeenCalled();
      expect(optimizer['reindexTables']).toHaveBeenCalled();
      expect(optimizer['vacuumAnalyze']).toHaveBeenCalled();
    });

    it('should handle maintenance errors', async () => {
      jest.spyOn(optimizer as any, 'updateTableStatistics').mockRejectedValue(new Error('Maintenance error'));

      // Should not throw
      await expect(optimizer.performMaintenance()).resolves.toBeUndefined();
    });
  });

  describe('updateTableStatistics', () => {
    it('should analyze all configured tables', async () => {
      mockPrisma.$executeRawUnsafe.mockResolvedValue(undefined);

      await (optimizer as any).updateTableStatistics();

      expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalledWith('ANALYZE audit_logs');
      expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalledWith('ANALYZE notifications');
      expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalledWith('ANALYZE users');
      expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalledWith('ANALYZE vendors');
      expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalledWith('ANALYZE procurements');
    });

    it('should handle individual table errors', async () => {
      mockPrisma.$executeRawUnsafe
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('Table error'))
        .mockResolvedValueOnce(undefined);

      // Should not throw
      await expect((optimizer as any).updateTableStatistics()).resolves.toBeUndefined();
    });
  });

  describe('reindexTables', () => {
    it('should reindex configured tables', async () => {
      mockPrisma.$executeRawUnsafe.mockResolvedValue(undefined);

      await (optimizer as any).reindexTables();

      expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalledWith('REINDEX TABLE audit_logs');
      expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalledWith('REINDEX TABLE notifications');
    });

    it('should handle reindex errors', async () => {
      mockPrisma.$executeRawUnsafe.mockRejectedValue(new Error('Reindex error'));

      // Should not throw
      await expect((optimizer as any).reindexTables()).resolves.toBeUndefined();
    });
  });

  describe('vacuumAnalyze', () => {
    it('should perform vacuum analyze', async () => {
      mockPrisma.$executeRawUnsafe.mockResolvedValue(undefined);

      await (optimizer as any).vacuumAnalyze();

      expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalledWith('VACUUM ANALYZE');
    });

    it('should handle vacuum errors', async () => {
      mockPrisma.$executeRawUnsafe.mockRejectedValue(new Error('Vacuum error'));

      // Should not throw
      await expect((optimizer as any).vacuumAnalyze()).resolves.toBeUndefined();
    });
  });

  describe('getDatabaseSizeInfo', () => {
    it('should return database size information', async () => {
      const mockSizeInfo = [
        {
          schemaname: 'public',
          tablename: 'audit_logs',
          size: '10 MB',
          size_bytes: 10485760,
        },
        {
          schemaname: 'public',
          tablename: 'users',
          size: '1 MB',
          size_bytes: 1048576,
        },
      ];

      mockPrisma.$queryRawUnsafe.mockResolvedValue(mockSizeInfo);

      const result = await optimizer.getDatabaseSizeInfo();

      expect(mockPrisma.$queryRawUnsafe).toHaveBeenCalledWith(
        expect.stringContaining('pg_size_pretty')
      );
      expect(result).toEqual(mockSizeInfo);
    });

    it('should handle database size query errors', async () => {
      mockPrisma.$queryRawUnsafe.mockRejectedValue(new Error('Size query error'));

      const result = await optimizer.getDatabaseSizeInfo();

      expect(result).toEqual([]);
    });
  });

  describe('Helper methods', () => {
    describe('getPartitionDate', () => {
      it('should calculate monthly partition date', () => {
        const baseDate = new Date('2024-01-15');
        const result = (optimizer as any).getPartitionDate(baseDate, 'MONTHLY', 1);
        
        expect(result.getFullYear()).toBe(2024);
        expect(result.getMonth()).toBe(1); // February (0-indexed)
        expect(result.getDate()).toBe(1);
      });

      it('should calculate quarterly partition date', () => {
        const baseDate = new Date('2024-01-15');
        const result = (optimizer as any).getPartitionDate(baseDate, 'QUARTERLY', 1);
        
        expect(result.getFullYear()).toBe(2024);
        expect(result.getMonth()).toBe(3); // April (0-indexed)
        expect(result.getDate()).toBe(1);
      });

      it('should calculate yearly partition date', () => {
        const baseDate = new Date('2024-01-15');
        const result = (optimizer as any).getPartitionDate(baseDate, 'YEARLY', 1);
        
        expect(result.getFullYear()).toBe(2025);
        expect(result.getMonth()).toBe(0); // January (0-indexed)
        expect(result.getDate()).toBe(1);
      });
    });

    describe('getPartitionName', () => {
      it('should generate monthly partition name', () => {
        const date = new Date('2024-03-15');
        const result = (optimizer as any).getPartitionName('audit_logs', date, 'MONTHLY');
        
        expect(result).toBe('audit_logs_y2024m03');
      });

      it('should generate quarterly partition name', () => {
        const date = new Date('2024-07-15');
        const result = (optimizer as any).getPartitionName('audit_logs', date, 'QUARTERLY');
        
        expect(result).toBe('audit_logs_y2024q3');
      });

      it('should generate yearly partition name', () => {
        const date = new Date('2024-07-15');
        const result = (optimizer as any).getPartitionName('audit_logs', date, 'YEARLY');
        
        expect(result).toBe('audit_logs_y2024');
      });
    });

    describe('generateQueryHash', () => {
      it('should generate consistent hash for same input', () => {
        const hash1 = (optimizer as any).generateQueryHash('SELECT', 'users');
        const hash2 = (optimizer as any).generateQueryHash('SELECT', 'users');
        
        expect(hash1).toBe(hash2);
        expect(hash1).toHaveLength(32); // MD5 hash length
      });

      it('should generate different hashes for different inputs', () => {
        const hash1 = (optimizer as any).generateQueryHash('SELECT', 'users');
        const hash2 = (optimizer as any).generateQueryHash('INSERT', 'users');
        
        expect(hash1).not.toBe(hash2);
      });
    });
  });
});
