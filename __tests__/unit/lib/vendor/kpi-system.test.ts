import { VendorKPISystem } from '@/lib/vendor/kpi-system';
import { prisma } from '@/lib/db';

jest.mock('@/lib/db');

describe('VendorKPISystem', () => {
  let kpiSystem: VendorKPISystem;
  const mockPrisma = prisma as jest.Mocked<typeof prisma>;

  beforeEach(() => {
    kpiSystem = new VendorKPISystem();
    jest.clearAllMocks();
  });

  describe('createKPITemplate', () => {
    const mockTemplateData = {
      name: 'Standard KPI Template',
      description: 'Standard KPI evaluation template',
      category: 'GENERAL' as const,
      metrics: [
        {
          name: 'Quality Score',
          description: 'Product/service quality assessment',
          category: 'QUALITY' as const,
          weight: 30,
          measurementType: 'PERCENTAGE' as const,
          targetValue: 95,
          minValue: 0,
          maxValue: 100,
          isRequired: true,
        },
        {
          name: 'Delivery Performance',
          description: 'On-time delivery performance',
          category: 'DELIVERY' as const,
          weight: 25,
          measurementType: 'PERCENTAGE' as const,
          targetValue: 98,
          minValue: 0,
          maxValue: 100,
          isRequired: true,
        },
      ],
      isActive: true,
      createdById: 'user123',
    };

    it('should create KPI template with metrics successfully', async () => {
      const mockTemplate = {
        id: 'template123',
        name: 'Standard KPI Template',
        category: 'GENERAL',
        isActive: true,
      };

      const mockMetric = {
        id: 'metric123',
        templateId: 'template123',
        name: 'Quality Score',
        category: 'QUALITY',
      };

      mockPrisma.vendorKpiTemplate.create.mockResolvedValue(mockTemplate as any);
      mockPrisma.vendorKpiMetric.create.mockResolvedValue(mockMetric as any);

      const result = await kpiSystem.createKPITemplate(mockTemplateData);

      expect(mockPrisma.vendorKpiTemplate.create).toHaveBeenCalledWith({
        data: {
          name: 'Standard KPI Template',
          description: 'Standard KPI evaluation template',
          category: 'GENERAL',
          isActive: true,
          createdById: 'user123',
        },
      });

      expect(mockPrisma.vendorKpiMetric.create).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockTemplate);
    });

    it('should validate metric weights sum to 100', async () => {
      const invalidTemplateData = {
        ...mockTemplateData,
        metrics: [
          { ...mockTemplateData.metrics[0], weight: 60 },
          { ...mockTemplateData.metrics[1], weight: 50 }, // Total: 110
        ],
      };

      await expect(kpiSystem.createKPITemplate(invalidTemplateData))
        .rejects.toThrow('Total metric weights must equal 100%');
    });

    it('should handle database errors', async () => {
      mockPrisma.vendorKpiTemplate.create.mockRejectedValue(new Error('Database error'));

      await expect(kpiSystem.createKPITemplate(mockTemplateData))
        .rejects.toThrow('Database error');
    });
  });

  describe('evaluateVendorKPI', () => {
    const mockEvaluationData = {
      vendorId: 'vendor123',
      templateId: 'template123',
      evaluationPeriod: {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-03-31'),
      },
      evaluatedById: 'user123',
      metricScores: [
        { metricId: 'metric1', actualValue: 92, score: 92, comments: 'Good quality' },
        { metricId: 'metric2', actualValue: 95, score: 95, comments: 'Excellent delivery' },
      ],
      overallComments: 'Good performance overall',
    };

    it('should create vendor KPI evaluation successfully', async () => {
      const mockTemplate = {
        id: 'template123',
        metrics: [
          { id: 'metric1', weight: 30, targetValue: 95 },
          { id: 'metric2', weight: 25, targetValue: 98 },
        ],
      };

      const mockEvaluation = {
        id: 'evaluation123',
        vendorId: 'vendor123',
        templateId: 'template123',
        overallScore: 93.2,
      };

      mockPrisma.vendorKpiTemplate.findUnique.mockResolvedValue(mockTemplate as any);
      mockPrisma.vendorKpiEvaluation.create.mockResolvedValue(mockEvaluation as any);
      mockPrisma.vendorKpiScore.create.mockResolvedValue({} as any);

      const result = await kpiSystem.evaluateVendorKPI(mockEvaluationData);

      expect(mockPrisma.vendorKpiEvaluation.create).toHaveBeenCalledWith({
        data: {
          vendorId: 'vendor123',
          templateId: 'template123',
          evaluationPeriodStart: new Date('2024-01-01'),
          evaluationPeriodEnd: new Date('2024-03-31'),
          overallScore: expect.any(Number),
          status: 'COMPLETED',
          evaluatedById: 'user123',
          evaluatedAt: expect.any(Date),
          comments: 'Good performance overall',
        },
      });

      expect(mockPrisma.vendorKpiScore.create).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockEvaluation);
    });

    it('should calculate weighted overall score correctly', async () => {
      const mockTemplate = {
        id: 'template123',
        metrics: [
          { id: 'metric1', weight: 40, targetValue: 95 },
          { id: 'metric2', weight: 60, targetValue: 98 },
        ],
      };

      mockPrisma.vendorKpiTemplate.findUnique.mockResolvedValue(mockTemplate as any);
      mockPrisma.vendorKpiEvaluation.create.mockResolvedValue({} as any);
      mockPrisma.vendorKpiScore.create.mockResolvedValue({} as any);

      const evaluationData = {
        ...mockEvaluationData,
        metricScores: [
          { metricId: 'metric1', actualValue: 90, score: 90 },
          { metricId: 'metric2', actualValue: 95, score: 95 },
        ],
      };

      await kpiSystem.evaluateVendorKPI(evaluationData);

      // Expected calculation: (90 * 40 + 95 * 60) / 100 = 93
      expect(mockPrisma.vendorKpiEvaluation.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          overallScore: 93,
        }),
      });
    });

    it('should throw error when template not found', async () => {
      mockPrisma.vendorKpiTemplate.findUnique.mockResolvedValue(null);

      await expect(kpiSystem.evaluateVendorKPI(mockEvaluationData))
        .rejects.toThrow('KPI template not found');
    });

    it('should validate all metrics are scored', async () => {
      const mockTemplate = {
        id: 'template123',
        metrics: [
          { id: 'metric1', weight: 50 },
          { id: 'metric2', weight: 50 },
        ],
      };

      const incompleteEvaluationData = {
        ...mockEvaluationData,
        metricScores: [
          { metricId: 'metric1', actualValue: 90, score: 90 },
          // Missing metric2
        ],
      };

      mockPrisma.vendorKpiTemplate.findUnique.mockResolvedValue(mockTemplate as any);

      await expect(kpiSystem.evaluateVendorKPI(incompleteEvaluationData))
        .rejects.toThrow('All template metrics must be scored');
    });
  });

  describe('getVendorKPIHistory', () => {
    it('should return vendor KPI evaluation history', async () => {
      const mockEvaluations = [
        {
          id: 'eval1',
          overallScore: 92.5,
          evaluationPeriodStart: new Date('2024-01-01'),
          evaluationPeriodEnd: new Date('2024-03-31'),
          template: { name: 'Q1 2024 Evaluation' },
          evaluatedBy: { name: 'John Doe' },
          scores: [
            { metric: { name: 'Quality', category: 'QUALITY' }, actualValue: 90, score: 90 },
            { metric: { name: 'Delivery', category: 'DELIVERY' }, actualValue: 95, score: 95 },
          ],
        },
      ];

      mockPrisma.vendorKpiEvaluation.findMany.mockResolvedValue(mockEvaluations as any);

      const result = await kpiSystem.getVendorKPIHistory('vendor123');

      expect(mockPrisma.vendorKpiEvaluation.findMany).toHaveBeenCalledWith({
        where: { vendorId: 'vendor123' },
        include: {
          template: { select: { name: true } },
          evaluatedBy: { select: { name: true, email: true } },
          scores: {
            include: {
              metric: { select: { name: true, category: true, weight: true } },
            },
          },
        },
        orderBy: { evaluatedAt: 'desc' },
      });

      expect(result).toEqual(mockEvaluations);
    });

    it('should filter by date range when provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      mockPrisma.vendorKpiEvaluation.findMany.mockResolvedValue([]);

      await kpiSystem.getVendorKPIHistory('vendor123', startDate, endDate);

      expect(mockPrisma.vendorKpiEvaluation.findMany).toHaveBeenCalledWith({
        where: {
          vendorId: 'vendor123',
          evaluatedAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: expect.any(Object),
        orderBy: { evaluatedAt: 'desc' },
      });
    });
  });

  describe('calculateVendorRanking', () => {
    it('should calculate vendor rankings based on latest KPI scores', async () => {
      const mockVendorScores = [
        { vendorId: 'vendor1', avgScore: 95.5 },
        { vendorId: 'vendor2', avgScore: 88.2 },
        { vendorId: 'vendor3', avgScore: 92.1 },
      ];

      const mockVendors = [
        { id: 'vendor1', companyName: 'Company A' },
        { id: 'vendor2', companyName: 'Company B' },
        { id: 'vendor3', companyName: 'Company C' },
      ];

      mockPrisma.$queryRaw.mockResolvedValue(mockVendorScores);
      mockPrisma.vendor.findMany.mockResolvedValue(mockVendors as any);

      const result = await kpiSystem.calculateVendorRanking();

      expect(result).toHaveLength(3);
      expect(result[0]).toEqual({
        rank: 1,
        vendorId: 'vendor1',
        companyName: 'Company A',
        averageScore: 95.5,
      });
      expect(result[1]).toEqual({
        rank: 2,
        vendorId: 'vendor3',
        companyName: 'Company C',
        averageScore: 92.1,
      });
      expect(result[2]).toEqual({
        rank: 3,
        vendorId: 'vendor2',
        companyName: 'Company B',
        averageScore: 88.2,
      });
    });

    it('should filter by category when provided', async () => {
      mockPrisma.$queryRaw.mockResolvedValue([]);
      mockPrisma.vendor.findMany.mockResolvedValue([]);

      await kpiSystem.calculateVendorRanking('QUALITY');

      expect(mockPrisma.$queryRaw).toHaveBeenCalledWith(
        expect.stringContaining('WHERE m.category = $1'),
        'QUALITY'
      );
    });

    it('should handle empty results', async () => {
      mockPrisma.$queryRaw.mockResolvedValue([]);
      mockPrisma.vendor.findMany.mockResolvedValue([]);

      const result = await kpiSystem.calculateVendorRanking();

      expect(result).toEqual([]);
    });
  });

  describe('getKPIAnalytics', () => {
    it('should return comprehensive KPI analytics', async () => {
      const mockAnalytics = {
        totalEvaluations: 150,
        averageScore: 87.5,
        categoryBreakdown: [
          { category: 'QUALITY', avgScore: 89.2, count: 50 },
          { category: 'DELIVERY', avgScore: 91.1, count: 45 },
          { category: 'COST', avgScore: 83.7, count: 35 },
        ],
        trendData: [
          { period: '2024-Q1', avgScore: 85.2 },
          { period: '2024-Q2', avgScore: 87.8 },
          { period: '2024-Q3', avgScore: 89.1 },
        ],
        topPerformers: [
          { vendorId: 'vendor1', companyName: 'Company A', avgScore: 95.5 },
          { vendorId: 'vendor2', companyName: 'Company B', avgScore: 93.2 },
        ],
        improvementAreas: [
          { category: 'COST', avgScore: 83.7, targetScore: 90 },
          { category: 'SERVICE', avgScore: 85.1, targetScore: 90 },
        ],
      };

      // Mock multiple database calls
      mockPrisma.vendorKpiEvaluation.count.mockResolvedValue(150);
      mockPrisma.vendorKpiEvaluation.aggregate.mockResolvedValue({ _avg: { overallScore: 87.5 } } as any);
      mockPrisma.$queryRaw
        .mockResolvedValueOnce(mockAnalytics.categoryBreakdown)
        .mockResolvedValueOnce(mockAnalytics.trendData)
        .mockResolvedValueOnce(mockAnalytics.topPerformers)
        .mockResolvedValueOnce(mockAnalytics.improvementAreas);

      const result = await kpiSystem.getKPIAnalytics();

      expect(result).toEqual({
        totalEvaluations: 150,
        averageScore: 87.5,
        categoryBreakdown: mockAnalytics.categoryBreakdown,
        trendData: mockAnalytics.trendData,
        topPerformers: mockAnalytics.topPerformers,
        improvementAreas: mockAnalytics.improvementAreas,
      });
    });

    it('should filter analytics by vendor when provided', async () => {
      mockPrisma.vendorKpiEvaluation.count.mockResolvedValue(10);
      mockPrisma.vendorKpiEvaluation.aggregate.mockResolvedValue({ _avg: { overallScore: 92.1 } } as any);
      mockPrisma.$queryRaw.mockResolvedValue([]);

      await kpiSystem.getKPIAnalytics('vendor123');

      expect(mockPrisma.vendorKpiEvaluation.count).toHaveBeenCalledWith({
        where: { vendorId: 'vendor123' },
      });
      expect(mockPrisma.vendorKpiEvaluation.aggregate).toHaveBeenCalledWith({
        where: { vendorId: 'vendor123' },
        _avg: { overallScore: true },
      });
    });

    it('should filter analytics by date range when provided', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      mockPrisma.vendorKpiEvaluation.count.mockResolvedValue(50);
      mockPrisma.vendorKpiEvaluation.aggregate.mockResolvedValue({ _avg: { overallScore: 88.5 } } as any);
      mockPrisma.$queryRaw.mockResolvedValue([]);

      await kpiSystem.getKPIAnalytics(undefined, startDate, endDate);

      expect(mockPrisma.vendorKpiEvaluation.count).toHaveBeenCalledWith({
        where: {
          evaluatedAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });
    });
  });

  describe('updateKPITemplate', () => {
    const mockUpdates = {
      name: 'Updated Template',
      description: 'Updated description',
      metrics: [
        {
          name: 'Updated Quality Score',
          description: 'Updated quality assessment',
          category: 'QUALITY' as const,
          weight: 50,
          measurementType: 'PERCENTAGE' as const,
          targetValue: 90,
          minValue: 0,
          maxValue: 100,
          isRequired: true,
        },
        {
          name: 'New Metric',
          description: 'New metric description',
          category: 'SERVICE' as const,
          weight: 50,
          measurementType: 'PERCENTAGE' as const,
          targetValue: 85,
          minValue: 0,
          maxValue: 100,
          isRequired: true,
        },
      ],
    };

    it('should update template and recreate metrics', async () => {
      const mockUpdatedTemplate = {
        id: 'template123',
        name: 'Updated Template',
        description: 'Updated description',
      };

      mockPrisma.vendorKpiTemplate.update.mockResolvedValue(mockUpdatedTemplate as any);
      mockPrisma.vendorKpiMetric.deleteMany.mockResolvedValue({ count: 2 } as any);
      mockPrisma.vendorKpiMetric.create.mockResolvedValue({} as any);

      const result = await kpiSystem.updateKPITemplate('template123', mockUpdates);

      expect(mockPrisma.vendorKpiTemplate.update).toHaveBeenCalledWith({
        where: { id: 'template123' },
        data: {
          name: 'Updated Template',
          description: 'Updated description',
          updatedAt: expect.any(Date),
        },
      });

      expect(mockPrisma.vendorKpiMetric.deleteMany).toHaveBeenCalledWith({
        where: { templateId: 'template123' },
      });

      expect(mockPrisma.vendorKpiMetric.create).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockUpdatedTemplate);
    });

    it('should validate metric weights when updating', async () => {
      const invalidUpdates = {
        ...mockUpdates,
        metrics: [
          { ...mockUpdates.metrics[0], weight: 60 },
          { ...mockUpdates.metrics[1], weight: 50 }, // Total: 110
        ],
      };

      await expect(kpiSystem.updateKPITemplate('template123', invalidUpdates))
        .rejects.toThrow('Total metric weights must equal 100%');
    });
  });

  describe('deleteKPITemplate', () => {
    it('should soft delete template when evaluations exist', async () => {
      mockPrisma.vendorKpiEvaluation.count.mockResolvedValue(5);
      mockPrisma.vendorKpiTemplate.update.mockResolvedValue({} as any);

      await kpiSystem.deleteKPITemplate('template123');

      expect(mockPrisma.vendorKpiTemplate.update).toHaveBeenCalledWith({
        where: { id: 'template123' },
        data: {
          isActive: false,
          deletedAt: expect.any(Date),
        },
      });
    });

    it('should hard delete template when no evaluations exist', async () => {
      mockPrisma.vendorKpiEvaluation.count.mockResolvedValue(0);
      mockPrisma.vendorKpiMetric.deleteMany.mockResolvedValue({ count: 3 } as any);
      mockPrisma.vendorKpiTemplate.delete.mockResolvedValue({} as any);

      await kpiSystem.deleteKPITemplate('template123');

      expect(mockPrisma.vendorKpiMetric.deleteMany).toHaveBeenCalledWith({
        where: { templateId: 'template123' },
      });
      expect(mockPrisma.vendorKpiTemplate.delete).toHaveBeenCalledWith({
        where: { id: 'template123' },
      });
    });
  });

  describe('Private helper methods', () => {
    describe('validateMetricWeights', () => {
      it('should return true for valid weights', () => {
        const metrics = [
          { weight: 30 },
          { weight: 40 },
          { weight: 30 },
        ];

        const result = (kpiSystem as any).validateMetricWeights(metrics);
        expect(result).toBe(true);
      });

      it('should return false for invalid weights', () => {
        const metrics = [
          { weight: 30 },
          { weight: 40 },
          { weight: 40 }, // Total: 110
        ];

        const result = (kpiSystem as any).validateMetricWeights(metrics);
        expect(result).toBe(false);
      });

      it('should handle empty metrics array', () => {
        const result = (kpiSystem as any).validateMetricWeights([]);
        expect(result).toBe(true);
      });
    });

    describe('calculateWeightedScore', () => {
      it('should calculate weighted score correctly', () => {
        const scores = [
          { score: 90, weight: 30 },
          { score: 85, weight: 40 },
          { score: 95, weight: 30 },
        ];

        const result = (kpiSystem as any).calculateWeightedScore(scores);
        expect(result).toBe(89); // (90*30 + 85*40 + 95*30) / 100
      });

      it('should handle empty scores array', () => {
        const result = (kpiSystem as any).calculateWeightedScore([]);
        expect(result).toBe(0);
      });

      it('should round to one decimal place', () => {
        const scores = [
          { score: 88.33, weight: 50 },
          { score: 91.67, weight: 50 },
        ];

        const result = (kpiSystem as any).calculateWeightedScore(scores);
        expect(result).toBe(90); // Should round 90.0 to 90
      });
    });
  });
});
