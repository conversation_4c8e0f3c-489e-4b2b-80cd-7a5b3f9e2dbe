import { EnhancedDocumentGenerator } from '@/lib/documents/enhanced-document-generator';
import { prisma } from '@/lib/db';
import puppeteer from 'puppeteer';
import { promises as fs } from 'fs';

jest.mock('@/lib/db');
jest.mock('puppeteer');
jest.mock('fs');

describe('EnhancedDocumentGenerator', () => {
  let documentGenerator: EnhancedDocumentGenerator;
  const mockPrisma = prisma as jest.Mocked<typeof prisma>;
  const mockPuppeteer = puppeteer as jest.Mocked<typeof puppeteer>;
  const mockFs = fs as jest.Mocked<typeof fs>;

  beforeEach(() => {
    documentGenerator = new EnhancedDocumentGenerator();
    jest.clearAllMocks();
  });

  describe('generateDocument', () => {
    const mockOptions = {
      templateId: 'template123',
      data: {
        poNumber: 'PO-2024-001',
        buyerName: 'Test Company',
        vendorName: 'Vendor Company',
        totalAmount: 1000000,
        items: [
          { name: 'Item 1', quantity: 10, unitPrice: 50000, totalPrice: 500000 },
          { name: 'Item 2', quantity: 5, unitPrice: 100000, totalPrice: 500000 },
        ],
      },
      outputFormat: 'pdf' as const,
      fileName: 'test-document.pdf',
    };

    const mockTemplate = {
      id: 'template123',
      name: 'Purchase Order Template',
      type: 'PURCHASE_ORDER',
      htmlTemplate: '<html><body>{{poNumber}} - {{buyerName}}</body></html>',
      cssStyles: 'body { font-family: Arial; }',
      variables: [
        { name: 'poNumber', type: 'text', required: true },
        { name: 'buyerName', type: 'text', required: true },
        { name: 'totalAmount', type: 'currency', required: true },
      ],
      signatureFields: [],
      isActive: true,
      version: 1,
    };

    beforeEach(() => {
      mockPrisma.documentTemplate.findUnique.mockResolvedValue({
        id: 'template123',
        name: 'Purchase Order Template',
        type: 'PURCHASE_ORDER',
        content: {
          htmlTemplate: mockTemplate.htmlTemplate,
          cssStyles: mockTemplate.cssStyles,
          signatureFields: [],
        },
        variables: mockTemplate.variables,
        isActive: true,
        version: 1,
      } as any);
    });

    it('should generate PDF document successfully', async () => {
      const mockBrowser = {
        newPage: jest.fn().mockResolvedValue({
          setContent: jest.fn(),
          pdf: jest.fn(),
          evaluate: jest.fn(),
        }),
        close: jest.fn(),
      };

      mockPuppeteer.launch.mockResolvedValue(mockBrowser as any);
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.stat.mockResolvedValue({ size: 1024 } as any);

      const result = await documentGenerator.generateDocument(mockOptions);

      expect(result.success).toBe(true);
      expect(result.fileName).toBe('test-document.pdf');
      expect(result.fileSize).toBe(1024);
      expect(mockPuppeteer.launch).toHaveBeenCalled();
      expect(mockBrowser.newPage).toHaveBeenCalled();
      expect(mockBrowser.close).toHaveBeenCalled();
    });

    it('should generate HTML document successfully', async () => {
      const htmlOptions = { ...mockOptions, outputFormat: 'html' as const, fileName: 'test-document.html' };

      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
      mockFs.stat.mockResolvedValue({ size: 512 } as any);

      const result = await documentGenerator.generateDocument(htmlOptions);

      expect(result.success).toBe(true);
      expect(result.fileName).toBe('test-document.html');
      expect(result.fileSize).toBe(512);
      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('should return error when template not found', async () => {
      mockPrisma.documentTemplate.findUnique.mockResolvedValue(null);

      const result = await documentGenerator.generateDocument(mockOptions);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Template not found');
    });

    it('should validate required variables', async () => {
      const invalidOptions = {
        ...mockOptions,
        data: { poNumber: 'PO-2024-001' }, // Missing required fields
      };

      const result = await documentGenerator.generateDocument(invalidOptions);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Missing required variables');
    });

    it('should handle approval workflow signatures', async () => {
      const optionsWithWorkflow = {
        ...mockOptions,
        approvalWorkflowId: 'workflow123',
        entityId: 'entity123',
        entityType: 'procurement',
      };

      const mockWorkflow = {
        id: 'workflow123',
        steps: [
          {
            id: 'step123',
            stepType: 'SIGNATURE',
            sequence: 1,
            name: 'Manager Signature',
            signatureConfig: {
              position: { x: 100, y: 200 },
              size: { width: 150, height: 80 },
              required: true,
            },
          },
        ],
      };

      mockPrisma.approvalWorkflow.findUnique.mockResolvedValue(mockWorkflow as any);
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
      mockFs.stat.mockResolvedValue({ size: 512 } as any);

      const result = await documentGenerator.generateDocument({
        ...optionsWithWorkflow,
        outputFormat: 'html',
      });

      expect(result.success).toBe(true);
      expect(result.signatureFields).toHaveLength(1);
      expect(result.pendingSignatures).toHaveLength(1);
    });

    it('should handle digital signatures', async () => {
      const optionsWithSignatures = {
        ...mockOptions,
        digitalSignatures: [
          {
            signerName: 'John Doe',
            signerTitle: 'Manager',
            timestamp: new Date(),
            stepSequence: 1,
            approved: true,
          },
        ],
        generateSignaturePlaceholders: true,
      };

      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue(undefined);
      mockFs.stat.mockResolvedValue({ size: 512 } as any);

      const result = await documentGenerator.generateDocument({
        ...optionsWithSignatures,
        outputFormat: 'html',
      });

      expect(result.success).toBe(true);
    });

    it('should handle PDF generation errors', async () => {
      mockPuppeteer.launch.mockRejectedValue(new Error('Puppeteer error'));

      const result = await documentGenerator.generateDocument(mockOptions);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Puppeteer error');
    });
  });

  describe('loadTemplateFromDB', () => {
    it('should load template successfully', async () => {
      const mockDbTemplate = {
        id: 'template123',
        name: 'Test Template',
        type: 'PURCHASE_ORDER',
        content: {
          htmlTemplate: '<html></html>',
          cssStyles: 'body {}',
          signatureFields: [],
        },
        variables: [],
        isActive: true,
        version: 1,
      };

      mockPrisma.documentTemplate.findUnique.mockResolvedValue(mockDbTemplate as any);

      const result = await (documentGenerator as any).loadTemplateFromDB('template123');

      expect(result).toEqual({
        id: 'template123',
        name: 'Test Template',
        type: 'PURCHASE_ORDER',
        htmlTemplate: '<html></html>',
        cssStyles: 'body {}',
        variables: [],
        signatureFields: [],
        isActive: true,
        version: 1,
      });
    });

    it('should return null when template not found', async () => {
      mockPrisma.documentTemplate.findUnique.mockResolvedValue(null);

      const result = await (documentGenerator as any).loadTemplateFromDB('nonexistent');

      expect(result).toBeNull();
    });

    it('should handle database errors', async () => {
      mockPrisma.documentTemplate.findUnique.mockRejectedValue(new Error('DB error'));

      const result = await (documentGenerator as any).loadTemplateFromDB('template123');

      expect(result).toBeNull();
    });
  });

  describe('getWorkflowSignatures', () => {
    it('should extract signature fields from workflow', async () => {
      const mockWorkflow = {
        id: 'workflow123',
        steps: [
          {
            id: 'step1',
            stepType: 'SIGNATURE',
            sequence: 1,
            name: 'Manager Signature',
            approverType: 'ROLE_BASED',
            isRequired: true,
            signatureConfig: {
              position: { x: 100, y: 200 },
              size: { width: 150, height: 80 },
              required: true,
            },
          },
          {
            id: 'step2',
            stepType: 'APPROVAL',
            sequence: 2,
            name: 'Regular Approval',
          },
        ],
      };

      mockPrisma.approvalWorkflow.findUnique.mockResolvedValue(mockWorkflow as any);

      const result = await (documentGenerator as any).getWorkflowSignatures('workflow123');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: 'workflow_step1',
        name: 'Manager Signature',
        stepSequence: 1,
        position: { x: 100, y: 200 },
        size: { width: 150, height: 80 },
        required: true,
        signerRole: 'ROLE_BASED',
        signerTitle: 'Manager Signature',
      });
    });

    it('should return empty array when workflow not found', async () => {
      mockPrisma.approvalWorkflow.findUnique.mockResolvedValue(null);

      const result = await (documentGenerator as any).getWorkflowSignatures('nonexistent');

      expect(result).toEqual([]);
    });
  });

  describe('validateTemplateData', () => {
    const mockTemplate = {
      variables: [
        { name: 'title', type: 'text', required: true },
        { name: 'amount', type: 'currency', required: true },
        { name: 'description', type: 'text', required: false },
      ],
    };

    it('should validate successfully with all required fields', () => {
      const data = { title: 'Test Title', amount: 1000000, description: 'Test' };

      const result = (documentGenerator as any).validateTemplateData(mockTemplate, data);

      expect(result.valid).toBe(true);
      expect(result.missing).toEqual([]);
    });

    it('should identify missing required fields', () => {
      const data = { title: 'Test Title' }; // Missing required 'amount'

      const result = (documentGenerator as any).validateTemplateData(mockTemplate, data);

      expect(result.valid).toBe(false);
      expect(result.missing).toEqual(['amount']);
    });

    it('should allow missing optional fields', () => {
      const data = { title: 'Test Title', amount: 1000000 }; // Missing optional 'description'

      const result = (documentGenerator as any).validateTemplateData(mockTemplate, data);

      expect(result.valid).toBe(true);
      expect(result.missing).toEqual([]);
    });
  });

  describe('formatValue', () => {
    it('should format currency values', () => {
      const result = (documentGenerator as any).formatValue(1000000, 'currency');
      expect(result).toMatch(/Rp/); // Should contain Indonesian Rupiah symbol
    });

    it('should format number values', () => {
      const result = (documentGenerator as any).formatValue(1000000, 'number');
      expect(result).toBe('1.000.000'); // Indonesian number format
    });

    it('should format date values', () => {
      const date = new Date('2024-01-15');
      const result = (documentGenerator as any).formatValue(date, 'date');
      expect(result).toContain('2024'); // Should contain year
    });

    it('should format boolean values', () => {
      expect((documentGenerator as any).formatValue(true, 'boolean')).toBe('Ya');
      expect((documentGenerator as any).formatValue(false, 'boolean')).toBe('Tidak');
    });

    it('should format array values', () => {
      const array = ['Item 1', 'Item 2', 'Item 3'];
      const result = (documentGenerator as any).formatValue(array, 'array');
      expect(result).toBe('Item 1, Item 2, Item 3');
    });

    it('should format text values', () => {
      const result = (documentGenerator as any).formatValue('Test String', 'text');
      expect(result).toBe('Test String');
    });
  });

  describe('processTemplateLogic', () => {
    it('should process each loops', () => {
      const html = '{{#each items}}<li>{{this.name}} - {{@index}}</li>{{/each}}';
      const data = {
        items: [
          { name: 'Item 1' },
          { name: 'Item 2' },
        ],
      };

      const result = (documentGenerator as any).processTemplateLogic(html, data);

      expect(result).toContain('<li>Item 1 - 1</li>');
      expect(result).toContain('<li>Item 2 - 2</li>');
    });

    it('should process if conditionals', () => {
      const html = '{{#if showDetails}}<div>Details shown</div>{{/if}}';
      const data = { showDetails: true };

      const result = (documentGenerator as any).processTemplateLogic(html, data);

      expect(result).toContain('<div>Details shown</div>');
    });

    it('should handle false conditionals', () => {
      const html = '{{#if showDetails}}<div>Details shown</div>{{/if}}';
      const data = { showDetails: false };

      const result = (documentGenerator as any).processTemplateLogic(html, data);

      expect(result).not.toContain('<div>Details shown</div>');
    });

    it('should handle empty arrays in loops', () => {
      const html = '{{#each items}}<li>{{this.name}}</li>{{/each}}';
      const data = { items: [] };

      const result = (documentGenerator as any).processTemplateLogic(html, data);

      expect(result).toBe('');
    });
  });

  describe('createTemplate', () => {
    const mockTemplate = {
      name: 'Test Template',
      type: 'PURCHASE_ORDER' as const,
      htmlTemplate: '<html></html>',
      cssStyles: 'body {}',
      variables: [],
      signatureFields: [],
      isActive: true,
    };

    it('should create template successfully', async () => {
      const mockCreated = { id: 'template123' };
      mockPrisma.documentTemplate.create.mockResolvedValue(mockCreated as any);

      const result = await documentGenerator.createTemplate(mockTemplate, 'user123');

      expect(mockPrisma.documentTemplate.create).toHaveBeenCalledWith({
        data: {
          name: 'Test Template',
          type: 'PURCHASE_ORDER',
          category: 'SYSTEM',
          content: {
            htmlTemplate: '<html></html>',
            cssStyles: 'body {}',
            signatureFields: [],
          },
          variables: [],
          status: 'APPROVED',
          isActive: true,
          createdBy: 'user123',
        },
      });

      expect(result).toBe('template123');
    });
  });

  describe('listTemplates', () => {
    it('should list all templates when no type specified', async () => {
      const mockTemplates = [
        {
          id: 'template1',
          name: 'Template 1',
          type: 'PURCHASE_ORDER',
          content: { htmlTemplate: '<html></html>' },
          variables: [],
          isActive: true,
          version: 1,
        },
      ];

      mockPrisma.documentTemplate.findMany.mockResolvedValue(mockTemplates as any);

      const result = await documentGenerator.listTemplates();

      expect(mockPrisma.documentTemplate.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
        },
        orderBy: { name: 'asc' },
      });

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Template 1');
    });

    it('should filter templates by type', async () => {
      mockPrisma.documentTemplate.findMany.mockResolvedValue([]);

      await documentGenerator.listTemplates('CONTRACT');

      expect(mockPrisma.documentTemplate.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          type: 'CONTRACT',
        },
        orderBy: { name: 'asc' },
      });
    });

    it('should handle database errors', async () => {
      mockPrisma.documentTemplate.findMany.mockRejectedValue(new Error('DB error'));

      const result = await documentGenerator.listTemplates();

      expect(result).toEqual([]);
    });
  });

  describe('addSignatureFields', () => {
    it('should add actual signatures for approved signatures', () => {
      const html = '<html><body>Content</body></html>';
      const signatureFields = [
        {
          id: 'sig1',
          name: 'Manager Signature',
          position: { x: 100, y: 200 },
          size: { width: 150, height: 80 },
          required: true,
          stepSequence: 1,
        },
      ];
      const digitalSignatures = [
        {
          signerName: 'John Doe',
          signerTitle: 'Manager',
          timestamp: new Date(),
          stepSequence: 1,
          approved: true,
        },
      ];

      const result = (documentGenerator as any).addSignatureFields(
        html,
        signatureFields,
        digitalSignatures,
        false
      );

      expect(result).toContain('John Doe');
      expect(result).toContain('Manager');
      expect(result).toContain('signature-field');
    });

    it('should add placeholders when generatePlaceholders is true', () => {
      const html = '<html><body>Content</body></html>';
      const signatureFields = [
        {
          id: 'sig1',
          name: 'Manager Signature',
          position: { x: 100, y: 200 },
          size: { width: 150, height: 80 },
          required: true,
        },
      ];

      const result = (documentGenerator as any).addSignatureFields(
        html,
        signatureFields,
        undefined,
        true
      );

      expect(result).toContain('signature-placeholder');
      expect(result).toContain('Manager Signature');
      expect(result).toContain('(Required)');
    });

    it('should not add signatures when not approved', () => {
      const html = '<html><body>Content</body></html>';
      const signatureFields = [
        {
          id: 'sig1',
          name: 'Manager Signature',
          position: { x: 100, y: 200 },
          size: { width: 150, height: 80 },
          required: true,
          stepSequence: 1,
        },
      ];
      const digitalSignatures = [
        {
          signerName: 'John Doe',
          signerTitle: 'Manager',
          timestamp: new Date(),
          stepSequence: 1,
          approved: false,
        },
      ];

      const result = (documentGenerator as any).addSignatureFields(
        html,
        signatureFields,
        digitalSignatures,
        false
      );

      expect(result).not.toContain('John Doe');
      expect(result).not.toContain('signature-field');
    });
  });

  describe('getPendingSignatures', () => {
    it('should identify pending signatures', () => {
      const signatureFields = [
        { id: 'sig1', stepSequence: 1, signerRole: 'MANAGER', position: { x: 100, y: 200 } },
        { id: 'sig2', stepSequence: 2, signerRole: 'DIRECTOR', position: { x: 300, y: 200 } },
      ];
      const providedSignatures = [
        { stepSequence: 1, approved: true },
      ];

      const result = (documentGenerator as any).getPendingSignatures(signatureFields, providedSignatures);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        stepSequence: 2,
        signerRole: 'DIRECTOR',
        position: { x: 300, y: 200 },
      });
    });

    it('should return all signatures when none provided', () => {
      const signatureFields = [
        { id: 'sig1', stepSequence: 1, signerRole: 'MANAGER', position: { x: 100, y: 200 } },
        { id: 'sig2', stepSequence: 2, signerRole: 'DIRECTOR', position: { x: 300, y: 200 } },
      ];

      const result = (documentGenerator as any).getPendingSignatures(signatureFields, undefined);

      expect(result).toHaveLength(2);
    });
  });
});
