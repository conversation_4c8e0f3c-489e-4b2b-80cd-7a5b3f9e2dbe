import { cn, formatCurrency, formatDate, formatNumber, generateId, validateEmail, validatePhone } from '@/lib/utils';

describe('Utility Functions', () => {
  describe('cn (className utility)', () => {
    it('should merge class names correctly', () => {
      const result = cn('base-class', 'additional-class');
      expect(result).toContain('base-class');
      expect(result).toContain('additional-class');
    });

    it('should handle conditional classes', () => {
      const result = cn('base-class', true && 'conditional-class', false && 'hidden-class');
      expect(result).toContain('base-class');
      expect(result).toContain('conditional-class');
      expect(result).not.toContain('hidden-class');
    });

    it('should handle undefined and null values', () => {
      const result = cn('base-class', undefined, null, 'valid-class');
      expect(result).toContain('base-class');
      expect(result).toContain('valid-class');
    });

    it('should handle empty strings', () => {
      const result = cn('base-class', '', 'valid-class');
      expect(result).toContain('base-class');
      expect(result).toContain('valid-class');
    });

    it('should handle arrays of classes', () => {
      const result = cn(['class1', 'class2'], 'class3');
      expect(result).toContain('class1');
      expect(result).toContain('class2');
      expect(result).toContain('class3');
    });
  });

  describe('formatCurrency', () => {
    it('should format Indonesian Rupiah correctly', () => {
      const result = formatCurrency(1000000);
      expect(result).toMatch(/Rp/);
      expect(result).toContain('1.000.000');
    });

    it('should handle zero values', () => {
      const result = formatCurrency(0);
      expect(result).toMatch(/Rp/);
      expect(result).toContain('0');
    });

    it('should handle negative values', () => {
      const result = formatCurrency(-1000000);
      expect(result).toMatch(/Rp/);
      expect(result).toContain('-');
    });

    it('should handle decimal values', () => {
      const result = formatCurrency(1000000.50);
      expect(result).toMatch(/Rp/);
      expect(result).toContain('1.000.001'); // Rounds to nearest integer
    });

    it('should handle very large numbers', () => {
      const result = formatCurrency(1000000000000);
      expect(result).toMatch(/Rp/);
      expect(result).toContain('1.000.000.000.000');
    });

    it('should handle custom currency code', () => {
      const result = formatCurrency(1000000, 'USD');
      expect(result).toMatch(/\$|USD/);
    });

    it('should handle custom locale', () => {
      const result = formatCurrency(1000000, 'IDR', 'en-US');
      expect(result).toBeDefined();
    });
  });

  describe('formatDate', () => {
    const testDate = new Date('2024-01-15T10:30:00Z');

    it('should format date with default Indonesian format', () => {
      const result = formatDate(testDate);
      expect(result).toContain('2024');
      expect(result).toContain('15');
    });

    it('should format date with custom format', () => {
      const result = formatDate(testDate, 'yyyy-MM-dd');
      expect(result).toBe('2024-01-15');
    });

    it('should format date with time', () => {
      const result = formatDate(testDate, 'dd/MM/yyyy HH:mm');
      expect(result).toContain('15/01/2024');
      expect(result).toContain(':');
    });

    it('should handle string date input', () => {
      const result = formatDate('2024-01-15');
      expect(result).toContain('2024');
      expect(result).toContain('15');
    });

    it('should handle invalid date input', () => {
      const result = formatDate('invalid-date');
      expect(result).toBe('Invalid Date');
    });

    it('should handle null/undefined input', () => {
      expect(formatDate(null as any)).toBe('');
      expect(formatDate(undefined as any)).toBe('');
    });

    it('should use Indonesian locale by default', () => {
      const result = formatDate(testDate, 'EEEE, dd MMMM yyyy');
      // Should contain Indonesian day/month names
      expect(result).toBeDefined();
    });
  });

  describe('formatNumber', () => {
    it('should format numbers with Indonesian locale', () => {
      const result = formatNumber(1000000);
      expect(result).toBe('1.000.000');
    });

    it('should handle zero', () => {
      const result = formatNumber(0);
      expect(result).toBe('0');
    });

    it('should handle negative numbers', () => {
      const result = formatNumber(-1000000);
      expect(result).toBe('-1.000.000');
    });

    it('should handle decimal numbers', () => {
      const result = formatNumber(1000.50);
      expect(result).toContain('1.000');
      expect(result).toContain('5');
    });

    it('should handle very small numbers', () => {
      const result = formatNumber(0.001);
      expect(result).toContain('0');
    });

    it('should handle very large numbers', () => {
      const result = formatNumber(1e12);
      expect(result).toContain('1.000.000.000.000');
    });

    it('should handle custom decimal places', () => {
      const result = formatNumber(1000.123456, 2);
      expect(result).toBe('1.000,12');
    });

    it('should handle custom locale', () => {
      const result = formatNumber(1000000, undefined, 'en-US');
      expect(result).toBe('1,000,000');
    });
  });

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
    });

    it('should generate IDs of consistent length', () => {
      const id1 = generateId();
      const id2 = generateId();
      
      expect(id1.length).toBe(id2.length);
      expect(id1.length).toBeGreaterThan(10);
    });

    it('should generate IDs with custom prefix', () => {
      const id = generateId('user');
      expect(id).toMatch(/^user/);
    });

    it('should generate IDs with custom length', () => {
      const id = generateId(undefined, 20);
      expect(id.length).toBe(20);
    });

    it('should generate alphanumeric IDs', () => {
      const id = generateId();
      expect(id).toMatch(/^[a-zA-Z0-9]+$/);
    });

    it('should handle empty prefix', () => {
      const id = generateId('');
      expect(typeof id).toBe('string');
      expect(id.length).toBeGreaterThan(0);
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@domain.com')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(true); // Current implementation allows this
    });

    it('should handle edge cases', () => {
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(' ')).toBe(false);
      expect(validateEmail('test@domain')).toBe(false);
      expect(validateEmail('test@domain.')).toBe(false);
    });

    it('should handle international domains', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should handle special characters', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should handle case sensitivity', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should handle null/undefined input', () => {
      expect(validateEmail(null as any)).toBe(false);
      expect(validateEmail(undefined as any)).toBe(false);
    });
  });

  describe('validatePhone', () => {
    it('should validate Indonesian phone numbers', () => {
      expect(validatePhone('+62812345678')).toBe(true);
      expect(validatePhone('08123456789')).toBe(true);
      expect(validatePhone('021-1234567')).toBe(true);
    });

    it('should validate international phone numbers', () => {
      expect(validatePhone('+1234567890')).toBe(false); // Doesn't start with +62
      expect(validatePhone('+44123456789')).toBe(false); // Doesn't start with +62
      expect(validatePhone('+6212345678')).toBe(true); // Indonesian international format
    });

    it('should reject invalid phone numbers', () => {
      expect(validatePhone('123')).toBe(false);
      expect(validatePhone('abc123')).toBe(false);
      expect(validatePhone('++123456789')).toBe(false);
    });

    it('should handle different formats', () => {
      expect(validatePhone('(021) 1234-567')).toBe(false); // Doesn't start with valid prefix
      expect(validatePhone('021 1234 567')).toBe(true); // Starts with 0, valid format
      expect(validatePhone('02112345678')).toBe(true); // Valid Indonesian format
    });

    it('should handle edge cases', () => {
      expect(validatePhone('')).toBe(false);
      expect(validatePhone(' ')).toBe(false);
      expect(validatePhone('+')).toBe(false);
    });

    it('should handle null/undefined input', () => {
      expect(validatePhone(null as any)).toBe(false);
      expect(validatePhone(undefined as any)).toBe(false);
    });

    it('should validate minimum and maximum length', () => {
      expect(validatePhone('123')).toBe(false); // Too short
      expect(validatePhone('12345678901234567890')).toBe(false); // Too long
      expect(validatePhone('081234567890')).toBe(true); // Valid Indonesian mobile number
    });
  });

  describe('Error handling in utility functions', () => {
    it('should handle formatCurrency with invalid input', () => {
      expect(() => formatCurrency(NaN)).not.toThrow();
      expect(() => formatCurrency(Infinity)).not.toThrow();
      expect(() => formatCurrency(-Infinity)).not.toThrow();
    });

    it('should handle formatNumber with invalid input', () => {
      expect(() => formatNumber(NaN)).not.toThrow();
      expect(() => formatNumber(Infinity)).not.toThrow();
      expect(() => formatNumber(-Infinity)).not.toThrow();
    });

    it('should handle generateId with invalid parameters', () => {
      expect(() => generateId(undefined, -1)).not.toThrow();
      expect(() => generateId(undefined, 0)).not.toThrow();
      expect(() => generateId(undefined, 1000)).not.toThrow();
    });
  });

  describe('Performance considerations', () => {
    it('should generate IDs quickly', () => {
      const start = Date.now();
      for (let i = 0; i < 1000; i++) {
        generateId();
      }
      const end = Date.now();
      
      expect(end - start).toBeLessThan(1000); // Should complete in less than 1 second
    });

    it('should validate emails quickly', () => {
      const emails = Array(1000).fill('<EMAIL>');
      
      const start = Date.now();
      emails.forEach(email => validateEmail(email));
      const end = Date.now();
      
      expect(end - start).toBeLessThan(100); // Should complete quickly
    });

    it('should format numbers quickly', () => {
      const numbers = Array(1000).fill(1000000);
      
      const start = Date.now();
      numbers.forEach(num => formatNumber(num));
      const end = Date.now();
      
      expect(end - start).toBeLessThan(500); // Should complete reasonably quickly
    });
  });
});
