import { hasPermission, getUserPermissions, hasAllPermissions, hasAnyPermission } from '@/lib/security/rbac';
import { prisma } from '@/lib/db';

jest.mock('@/lib/db');

describe('RBAC Security Functions', () => {
  const mockPrisma = prisma as jest.Mocked<typeof prisma>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkPermission', () => {
    const mockUser = {
      id: 'user123',
      email: '<EMAIL>',
      roles: ['ADMIN', 'PROCUREMENT_USER'],
    };

    it('should return true for admin users', async () => {
      const result = await checkPermission(mockUser, 'user.delete');
      expect(result).toBe(true);
    });

    it('should check user permissions from database', async () => {
      const userWithoutAdmin = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      const mockUserRoles = [
        {
          role: {
            permissions: ['procurement.create', 'procurement.read', 'vendor.read'],
          },
        },
      ];

      mockPrisma.userRole.findMany.mockResolvedValue(mockUserRoles as any);

      const result = await checkPermission(userWithoutAdmin, 'procurement.create');
      
      expect(result).toBe(true);
      expect(mockPrisma.userRole.findMany).toHaveBeenCalledWith({
        where: { userId: 'user123' },
        include: {
          role: {
            select: { permissions: true },
          },
        },
      });
    });

    it('should return false for insufficient permissions', async () => {
      const userWithoutAdmin = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['VENDOR'],
      };

      const mockUserRoles = [
        {
          role: {
            permissions: ['offer.create', 'offer.read'],
          },
        },
      ];

      mockPrisma.userRole.findMany.mockResolvedValue(mockUserRoles as any);

      const result = await checkPermission(userWithoutAdmin, 'user.delete');
      
      expect(result).toBe(false);
    });

    it('should check resource-specific permissions', async () => {
      const userWithoutAdmin = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      const mockUserRoles = [
        {
          role: {
            permissions: ['procurement.read'],
          },
        },
      ];

      const mockResourcePermissions = [
        {
          permission: 'procurement.update',
          granted: true,
        },
      ];

      mockPrisma.userRole.findMany.mockResolvedValue(mockUserRoles as any);
      mockPrisma.resourcePermission.findMany.mockResolvedValue(mockResourcePermissions as any);

      const result = await checkPermission(userWithoutAdmin, 'procurement.update', 'proc123');
      
      expect(result).toBe(true);
      expect(mockPrisma.resourcePermission.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user123',
          resourceType: 'procurement',
          resourceId: 'proc123',
          permission: 'procurement.update',
          granted: true,
        },
      });
    });

    it('should handle database errors gracefully', async () => {
      const userWithoutAdmin = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      mockPrisma.userRole.findMany.mockRejectedValue(new Error('Database error'));

      const result = await checkPermission(userWithoutAdmin, 'procurement.create');
      
      expect(result).toBe(false);
    });

    it('should return false for users without roles', async () => {
      const userWithoutRoles = {
        id: 'user123',
        email: '<EMAIL>',
        roles: [],
      };

      const result = await checkPermission(userWithoutRoles, 'procurement.create');
      
      expect(result).toBe(false);
    });
  });

  describe('getUserPermissions', () => {
    it('should return all permissions for admin users', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['ADMIN'],
      };

      const result = await getUserPermissions(mockUser);
      
      expect(result).toContain('*'); // Admin has all permissions
    });

    it('should aggregate permissions from all user roles', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER', 'APPROVER'],
      };

      const mockUserRoles = [
        {
          role: {
            permissions: ['procurement.create', 'procurement.read'],
          },
        },
        {
          role: {
            permissions: ['approval.approve', 'approval.reject'],
          },
        },
      ];

      mockPrisma.userRole.findMany.mockResolvedValue(mockUserRoles as any);

      const result = await getUserPermissions(mockUser);
      
      expect(result).toEqual([
        'procurement.create',
        'procurement.read',
        'approval.approve',
        'approval.reject',
      ]);
    });

    it('should remove duplicate permissions', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      const mockUserRoles = [
        {
          role: {
            permissions: ['procurement.read', 'vendor.read', 'procurement.read'], // Duplicate
          },
        },
      ];

      mockPrisma.userRole.findMany.mockResolvedValue(mockUserRoles as any);

      const result = await getUserPermissions(mockUser);
      
      expect(result).toEqual(['procurement.read', 'vendor.read']);
    });

    it('should handle database errors', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      mockPrisma.userRole.findMany.mockRejectedValue(new Error('Database error'));

      const result = await getUserPermissions(mockUser);
      
      expect(result).toEqual([]);
    });
  });

  describe('hasRole', () => {
    it('should return true when user has the specified role', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['ADMIN', 'PROCUREMENT_USER'],
      };

      const result = hasRole(mockUser, 'ADMIN');
      
      expect(result).toBe(true);
    });

    it('should return false when user does not have the specified role', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      const result = hasRole(mockUser, 'ADMIN');
      
      expect(result).toBe(false);
    });

    it('should handle users without roles', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: [],
      };

      const result = hasRole(mockUser, 'ADMIN');
      
      expect(result).toBe(false);
    });

    it('should handle undefined roles', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
      } as any;

      const result = hasRole(mockUser, 'ADMIN');
      
      expect(result).toBe(false);
    });
  });

  describe('hasAnyRole', () => {
    it('should return true when user has any of the specified roles', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER', 'VENDOR'],
      };

      const result = hasAnyRole(mockUser, ['ADMIN', 'PROCUREMENT_USER']);
      
      expect(result).toBe(true);
    });

    it('should return false when user has none of the specified roles', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['VENDOR'],
      };

      const result = hasAnyRole(mockUser, ['ADMIN', 'PROCUREMENT_USER']);
      
      expect(result).toBe(false);
    });

    it('should handle empty role arrays', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['ADMIN'],
      };

      const result = hasAnyRole(mockUser, []);
      
      expect(result).toBe(false);
    });

    it('should handle users without roles', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: [],
      };

      const result = hasAnyRole(mockUser, ['ADMIN', 'PROCUREMENT_USER']);
      
      expect(result).toBe(false);
    });
  });

  describe('Edge cases and security considerations', () => {
    it('should be case-sensitive for role names', () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['admin'], // lowercase
      };

      const result = hasRole(mockUser, 'ADMIN'); // uppercase
      
      expect(result).toBe(false);
    });

    it('should be case-sensitive for permission names', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      const mockUserRoles = [
        {
          role: {
            permissions: ['procurement.create'], // lowercase
          },
        },
      ];

      mockPrisma.userRole.findMany.mockResolvedValue(mockUserRoles as any);

      const result = await checkPermission(mockUser, 'PROCUREMENT.CREATE'); // uppercase
      
      expect(result).toBe(false);
    });

    it('should handle null/undefined user gracefully', async () => {
      const result1 = await checkPermission(null as any, 'procurement.create');
      const result2 = await checkPermission(undefined as any, 'procurement.create');
      
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });

    it('should handle empty permission strings', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['ADMIN'],
      };

      const result = await checkPermission(mockUser, '');
      
      expect(result).toBe(false);
    });

    it('should validate permission format', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      const mockUserRoles = [
        {
          role: {
            permissions: ['procurement.create'],
          },
        },
      ];

      mockPrisma.userRole.findMany.mockResolvedValue(mockUserRoles as any);

      // Invalid permission format (missing action)
      const result = await checkPermission(mockUser, 'procurement');
      
      expect(result).toBe(false);
    });

    it('should handle resource permissions with invalid resource IDs', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      mockPrisma.userRole.findMany.mockResolvedValue([]);
      mockPrisma.resourcePermission.findMany.mockResolvedValue([]);

      const result = await checkPermission(mockUser, 'procurement.update', '');
      
      expect(result).toBe(false);
    });
  });

  describe('Performance considerations', () => {
    it('should cache admin check to avoid database calls', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['ADMIN'],
      };

      await checkPermission(mockUser, 'user.delete');
      await checkPermission(mockUser, 'procurement.create');
      
      // Should not call database for admin users
      expect(mockPrisma.userRole.findMany).not.toHaveBeenCalled();
    });

    it('should minimize database calls for permission checks', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['PROCUREMENT_USER'],
      };

      const mockUserRoles = [
        {
          role: {
            permissions: ['procurement.create', 'procurement.read'],
          },
        },
      ];

      mockPrisma.userRole.findMany.mockResolvedValue(mockUserRoles as any);

      await checkPermission(mockUser, 'procurement.create');
      
      // Should only call database once per user
      expect(mockPrisma.userRole.findMany).toHaveBeenCalledTimes(1);
    });
  });
});
