import { getCurrentUser, verifyToken, hashPassword, verifyPassword } from '@/lib/auth';
import { prisma } from '@/lib/db';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';

jest.mock('@/lib/db');
jest.mock('jsonwebtoken');
jest.mock('bcryptjs');
jest.mock('next/headers');

describe('Authentication Functions', () => {
  const mockPrisma = prisma as jest.Mocked<typeof prisma>;
  const mockJwt = jwt as jest.Mocked<typeof jwt>;
  const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;
  const mockCookies = cookies as jest.MockedFunction<typeof cookies>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCurrentUser', () => {
    it('should return user when valid token provided', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        roles: ['ADMIN'],
        isActive: true,
      };

      const mockRequest = {} as NextRequest;

      mockCookies.mockResolvedValue({
        get: jest.fn().mockReturnValue({ value: 'valid_token' }),
      } as any);

      mockJwt.verify.mockReturnValue({ userId: 'user123' } as any);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      const result = await getCurrentUser(mockRequest);

      expect(result).toEqual(mockUser);
      expect(mockJwt.verify).toHaveBeenCalledWith('valid_token', 'fallback-secret-key');
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user123' },
        include: {
          vendor: true,
        },
      });
    });

    it('should throw error when no authentication token', async () => {
      const mockRequest = {} as NextRequest;

      mockCookies.mockResolvedValue({
        get: jest.fn().mockReturnValue(undefined),
      } as any);

      await expect(getCurrentUser(mockRequest)).rejects.toThrow('No authentication token found');
    });

    it('should throw error when token verification fails', async () => {
      const mockRequest = {} as NextRequest;

      mockCookies.mockResolvedValue({
        get: jest.fn().mockReturnValue({ value: 'invalid_token' }),
      } as any);

      mockJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(getCurrentUser(mockRequest)).rejects.toThrow('Invalid token');
    });

    it('should throw error when token verification fails', async () => {
      const mockRequest = {
        headers: {
          get: jest.fn().mockReturnValue('Bearer invalid_token'),
        },
      } as unknown as NextRequest;

      mockJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(getCurrentUser(mockRequest)).rejects.toThrow('Invalid token');
    });

    it('should throw error when user not found', async () => {
      const mockRequest = {
        headers: {
          get: jest.fn().mockReturnValue('Bearer valid_token'),
        },
      } as unknown as NextRequest;

      mockJwt.verify.mockReturnValue({ userId: 'nonexistent' } as any);
      mockPrisma.user.findUnique.mockResolvedValue(null);

      await expect(getCurrentUser(mockRequest)).rejects.toThrow('User not found');
    });

    it('should throw error when user is inactive', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        roles: ['ADMIN'],
        isActive: false,
      };

      const mockRequest = {
        headers: {
          get: jest.fn().mockReturnValue('Bearer valid_token'),
        },
      } as unknown as NextRequest;

      mockJwt.verify.mockReturnValue({ userId: 'user123' } as any);
      mockPrisma.user.findUnique.mockResolvedValue(null); // Inactive users return null

      await expect(getCurrentUser(mockRequest)).rejects.toThrow('User not found');
    });
  });

  describe('verifyToken', () => {
    it('should verify valid token', () => {
      const mockPayload = { userId: 'user123', email: '<EMAIL>' };
      mockJwt.verify.mockReturnValue(mockPayload as any);

      const result = verifyToken('valid_token');

      expect(result).toEqual(mockPayload);
      expect(mockJwt.verify).toHaveBeenCalledWith('valid_token', 'fallback-secret-key');
    });

    it('should throw error for invalid token', () => {
      mockJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      expect(() => verifyToken('invalid_token')).toThrow('Invalid token');
    });

    it('should throw error for expired token', () => {
      mockJwt.verify.mockImplementation(() => {
        const error = new Error('Token expired');
        (error as any).name = 'TokenExpiredError';
        throw error;
      });

      expect(() => verifyToken('expired_token')).toThrow('Invalid token');
    });
  });

  describe('hashPassword', () => {
    it('should hash password successfully', async () => {
      mockBcrypt.hash.mockResolvedValue('hashed_password');

      const result = await hashPassword('plain_password');

      expect(result).toBe('hashed_password');
      expect(mockBcrypt.hash).toHaveBeenCalledWith('plain_password', 12);
    });

    it('should handle hashing errors', async () => {
      mockBcrypt.hash.mockRejectedValue(new Error('Hashing failed'));

      await expect(hashPassword('plain_password')).rejects.toThrow('Hashing failed');
    });

    it('should use default salt rounds', async () => {
      mockBcrypt.hash.mockResolvedValue('hashed_password');

      await hashPassword('plain_password');

      expect(mockBcrypt.hash).toHaveBeenCalledWith('plain_password', 12);
    });
  });

  describe('verifyPassword', () => {
    it('should return true for matching passwords', async () => {
      mockBcrypt.compare.mockResolvedValue(true);

      const result = await verifyPassword('plain_password', 'hashed_password');

      expect(result).toBe(true);
      expect(mockBcrypt.compare).toHaveBeenCalledWith('plain_password', 'hashed_password');
    });

    it('should return false for non-matching passwords', async () => {
      mockBcrypt.compare.mockResolvedValue(false);

      const result = await verifyPassword('wrong_password', 'hashed_password');

      expect(result).toBe(false);
    });

    it('should handle comparison errors', async () => {
      mockBcrypt.compare.mockRejectedValue(new Error('Comparison failed'));

      await expect(verifyPassword('plain_password', 'hashed_password')).rejects.toThrow('Comparison failed');
    });
  });

  describe('Edge cases and security', () => {
    it('should handle empty password', async () => {
      await expect(hashPassword('')).resolves.toBeDefined();
      expect(mockBcrypt.hash).toHaveBeenCalledWith('', 12);
    });

    it('should handle very long passwords', async () => {
      const longPassword = 'a'.repeat(1000);
      mockBcrypt.hash.mockResolvedValue('hashed_long_password');

      const result = await hashPassword(longPassword);

      expect(result).toBe('hashed_long_password');
      expect(mockBcrypt.hash).toHaveBeenCalledWith(longPassword, 12);
    });

    it('should handle special characters in passwords', async () => {
      const specialPassword = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      mockBcrypt.hash.mockResolvedValue('hashed_special_password');

      const result = await hashPassword(specialPassword);

      expect(result).toBe('hashed_special_password');
    });

    it('should handle unicode characters in passwords', async () => {
      const unicodePassword = '密码测试🔒';
      mockBcrypt.hash.mockResolvedValue('hashed_unicode_password');

      const result = await hashPassword(unicodePassword);

      expect(result).toBe('hashed_unicode_password');
    });

    it('should handle malformed JWT tokens', () => {
      mockJwt.verify.mockImplementation(() => {
        const error = new Error('Malformed token');
        (error as any).name = 'JsonWebTokenError';
        throw error;
      });

      expect(() => verifyToken('malformed.token')).toThrow('Invalid token');
    });

    it('should handle missing JWT secret', () => {
      const originalSecret = process.env.JWT_SECRET;
      delete process.env.JWT_SECRET;

      mockJwt.verify.mockImplementation(() => {
        throw new Error('Secret not provided');
      });

      expect(() => verifyToken('token')).toThrow('Invalid token');

      // Restore original secret
      process.env.JWT_SECRET = originalSecret;
    });
  });

  describe('Cookie-based authentication', () => {
    it('should handle valid cookie token', async () => {
      const mockRequest = {} as NextRequest;

      mockCookies.mockResolvedValue({
        get: jest.fn().mockReturnValue({ value: 'valid_token' }),
      } as any);

      mockJwt.verify.mockReturnValue({ userId: 'user123' } as any);
      mockPrisma.user.findUnique.mockResolvedValue(global.mockUser as any);

      await getCurrentUser(mockRequest);

      expect(mockJwt.verify).toHaveBeenCalledWith('valid_token', 'fallback-secret-key');
    });

    it('should handle missing cookie', async () => {
      const mockRequest = {} as NextRequest;

      mockCookies.mockResolvedValue({
        get: jest.fn().mockReturnValue(undefined),
      } as any);

      await expect(getCurrentUser(mockRequest)).rejects.toThrow('No authentication token found');
    });
  });

  describe('Database integration', () => {
    it('should handle database connection errors', async () => {
      const mockRequest = {} as NextRequest;

      mockCookies.mockResolvedValue({
        get: jest.fn().mockReturnValue({ value: 'valid_token' }),
      } as any);

      mockJwt.verify.mockReturnValue({ userId: 'user123' } as any);
      mockPrisma.user.findUnique.mockRejectedValue(new Error('Database connection failed'));

      await expect(getCurrentUser(mockRequest)).rejects.toThrow('Database connection failed');
    });

    it('should handle user with missing roles', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        roles: null,
        isActive: true,
      };

      const mockRequest = {} as NextRequest;

      mockCookies.mockResolvedValue({
        get: jest.fn().mockReturnValue({ value: 'valid_token' }),
      } as any);

      mockJwt.verify.mockReturnValue({ userId: 'user123' } as any);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      const result = await getCurrentUser(mockRequest);

      expect(result.roles).toBeNull();
    });
  });
});
