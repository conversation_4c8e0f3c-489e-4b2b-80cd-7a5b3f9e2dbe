import { jest } from '@jest/globals';

// Mock nodemailer
const mockSendMail = jest.fn();
const mockCreateTransport = jest.fn(() => ({
  sendMail: mockSendMail,
}));

jest.mock('nodemailer', () => ({
  createTransport: mockCreateTransport,
}));

// Mock handlebars
const mockCompile = jest.fn();
jest.mock('handlebars', () => ({
  compile: mockCompile,
}));

// Mock prisma
const mockPrismaCreate = jest.fn();
const mockPrismaFindMany = jest.fn();
const mockPrismaUpdate = jest.fn();

jest.mock('@/lib/db', () => ({
  prisma: {
    notification: {
      create: mockPrismaCreate,
      findMany: mockPrismaFindMany,
      update: mockPrismaUpdate,
    },
  },
}));

// Import after mocking

// Import after mocking
import {
  createNotification,
  sendEmailNotification,
  sendCombinedNotification,
} from '@/lib/notifications/notification-service';

describe('Notification Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set up default environment
    process.env.NODE_ENV = 'test';
    process.env.SMTP_HOST = 'localhost';
    process.env.SMTP_PORT = '587';
    process.env.FROM_EMAIL = '<EMAIL>';
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.SMTP_HOST;
    delete process.env.SMTP_PORT;
    delete process.env.FROM_EMAIL;
  });

  describe('createNotification', () => {
    it('should create a notification successfully', async () => {
      const notificationData = {
        userId: 'user-123',
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'INFO' as const,
      };

      const mockCreatedNotification = {
        id: 'notification-123',
        ...notificationData,
        read: false,
        createdAt: new Date(),
      };

      mockPrismaCreate.mockResolvedValue(mockCreatedNotification);

      const result = await createNotification(notificationData);

      expect(mockPrismaCreate).toHaveBeenCalledWith({
        data: {
          userId: notificationData.userId,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type,
          read: false,
        },
      });

      expect(result).toEqual(mockCreatedNotification);
    });

    it('should handle optional metadata', async () => {
      const notificationData = {
        userId: 'user-123',
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'SUCCESS' as const,
        metadata: { procurementId: 'proc-123' },
      };

      mockPrismaCreate.mockResolvedValue({ id: 'notification-123' });

      await createNotification(notificationData);

      expect(mockPrismaCreate).toHaveBeenCalledWith({
        data: {
          userId: notificationData.userId,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type,
          metadata: notificationData.metadata,
          read: false,
        },
      });
    });
  });

  describe('sendEmailNotification', () => {
    it('should send email successfully in production mode', async () => {
      process.env.NODE_ENV = 'production';
      
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        template: 'vendor-status-change',
        data: {
          vendorName: 'Test Vendor',
          status: 'VERIFIED',
        },
      };

      const mockCompiledTemplate = jest.fn(() => '<html>Test Email</html>');
      mockCompile.mockReturnValue(mockCompiledTemplate);

      mockSendMail.mockResolvedValue({
        messageId: 'test-message-id',
      });

      await sendEmailNotification(emailData);

      expect(mockCreateTransport).toHaveBeenCalledWith({
        host: 'localhost',
        port: 587,
        secure: false,
        auth: undefined,
      });

      expect(mockSendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<html>Test Email</html>',
        text: expect.any(String),
      });
    });

    it('should handle email sending failure in production', async () => {
      process.env.NODE_ENV = 'production';
      
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        template: 'vendor-status-change',
        data: { vendorName: 'Test Vendor', status: 'VERIFIED' },
      };

      mockSendMail.mockRejectedValue(new Error('SMTP Error'));

      await expect(sendEmailNotification(emailData)).rejects.toThrow('Email sending failed');
    });

    it('should log email content in development mode', async () => {
      process.env.NODE_ENV = 'development';
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        template: 'vendor-status-change',
        data: { vendorName: 'Test Vendor', status: 'VERIFIED' },
      };

      mockSendMail.mockRejectedValue(new Error('SMTP Error'));

      await sendEmailNotification(emailData);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Email notification (dev mode):',
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Test Email',
          template: 'vendor-status-change',
        })
      );

      consoleSpy.mockRestore();
    });

    it('should handle array of recipients', async () => {
      const emailData = {
        to: ['<EMAIL>', '<EMAIL>'],
        subject: 'Test Email',
        template: 'vendor-status-change',
        data: { vendorName: 'Test Vendor', status: 'VERIFIED' },
      };

      const mockCompiledTemplate = jest.fn(() => '<html>Test Email</html>');
      mockCompile.mockReturnValue(mockCompiledTemplate);

      mockSendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await sendEmailNotification(emailData);

      expect(mockSendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>, <EMAIL>',
        })
      );
    });
  });

  describe('sendCombinedNotification', () => {
    it('should send both in-app and email notifications', async () => {
      const notificationData = {
        userId: 'user-123',
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'INFO' as const,
      };

      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        template: 'vendor-status-change',
        data: { vendorName: 'Test Vendor', status: 'VERIFIED' },
      };

      mockPrismaCreate.mockResolvedValue({ id: 'notification-123' });
      mockSendMail.mockResolvedValue({ messageId: 'test-message-id' });

      const mockCompiledTemplate = jest.fn(() => '<html>Test Email</html>');
      mockCompile.mockReturnValue(mockCompiledTemplate);

      await sendCombinedNotification(notificationData, emailData);

      expect(mockPrismaCreate).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: notificationData.userId,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type,
        }),
      });

      expect(mockSendMail).toHaveBeenCalled();
    });

    it('should send only in-app notification when email data is not provided', async () => {
      const notificationData = {
        userId: 'user-123',
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'INFO' as const,
      };

      mockPrismaCreate.mockResolvedValue({ id: 'notification-123' });

      await sendCombinedNotification(notificationData);

      expect(mockPrismaCreate).toHaveBeenCalled();
      expect(mockSendMail).not.toHaveBeenCalled();
    });
  });

  describe('Email Templates', () => {
    it('should throw error for unknown template', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        template: 'unknown-template',
        data: {},
      };

      await expect(sendEmailNotification(emailData)).rejects.toThrow(
        "Email template 'unknown-template' not found"
      );
    });
  });
});
