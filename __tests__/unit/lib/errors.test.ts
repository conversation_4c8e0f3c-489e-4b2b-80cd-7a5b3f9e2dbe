import {
  handleApiError,
  createSuccessResponse,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  AppError
} from '@/lib/errors';

describe('Error Handling Functions', () => {
  describe('createSuccessResponse', () => {
    it('should create success response with data', async () => {
      const data = { id: '123', name: 'Test' };
      const response = createSuccessResponse(data);

      expect(response.status).toBe(200);

      const body = await response.json();
      expect(body).toEqual({
        success: true,
        message: undefined,
        data,
      });
    });

    it('should create success response with custom status and message', async () => {
      const data = { created: true };
      const response = createSuccessResponse(data, 'Created successfully', 201);

      expect(response.status).toBe(201);

      const body = await response.json();
      expect(body.message).toBe('Created successfully');
    });

    it('should create success response with null data', async () => {
      const response = createSuccessResponse(null);

      expect(response.status).toBe(200);
      const body = await response.json();
      expect(body.data).toBeNull();
    });

    it('should create success response with undefined data', async () => {
      const response = createSuccessResponse(undefined);

      expect(response.status).toBe(200);
      const body = await response.json();
      expect(body.data).toBeUndefined();
    });
  });



  describe('handleApiError', () => {
    it('should handle ValidationError', async () => {
      const error = new ValidationError('Invalid input');
      const response = handleApiError(error);

      expect(response.status).toBe(400);
      const body = await response.json();
      expect(body.error).toBe('Invalid input');
    });

    it('should handle UnauthorizedError', async () => {
      const error = new UnauthorizedError('Invalid credentials');
      const response = handleApiError(error);

      expect(response.status).toBe(401);
      const body = await response.json();
      expect(body.error).toBe('Invalid credentials');
    });

    it('should handle ForbiddenError', async () => {
      const error = new ForbiddenError('Insufficient permissions');
      const response = handleApiError(error);

      expect(response.status).toBe(403);
      const body = await response.json();
      expect(body.error).toBe('Insufficient permissions');
    });

    it('should handle NotFoundError', async () => {
      const error = new NotFoundError('Resource not found');
      const response = handleApiError(error);

      expect(response.status).toBe(404);
      const body = await response.json();
      expect(body.error).toBe('Resource not found');
    });

    it('should handle ConflictError', async () => {
      const error = new ConflictError('Resource already exists');
      const response = handleApiError(error);

      expect(response.status).toBe(409);
      const body = await response.json();
      expect(body.error).toBe('Resource already exists');
    });

    it('should handle generic Error', async () => {
      const error = new Error('Generic error');
      const response = handleApiError(error);

      expect(response.status).toBe(500);
      const body = await response.json();
      expect(body.error).toBe('Internal server error');
    });

    it('should handle string errors', async () => {
      const error = 'String error message';
      const response = handleApiError(error);

      expect(response.status).toBe(500);
      const body = await response.json();
      expect(body.error).toBe('Internal server error');
    });

    it('should handle unknown error types', async () => {
      const error = { unknown: 'error' };
      const response = handleApiError(error);

      expect(response.status).toBe(500);
      const body = await response.json();
      expect(body.error).toBe('Internal server error');
    });

    it('should handle null/undefined errors', async () => {
      const response1 = handleApiError(null);
      const response2 = handleApiError(undefined);

      expect(response1.status).toBe(500);
      expect(response2.status).toBe(500);

      const body1 = await response1.json();
      const body2 = await response2.json();

      expect(body1.error).toBe('Internal server error');
      expect(body2.error).toBe('Internal server error');
    });
  });

  describe('Custom Error Classes', () => {
    describe('ValidationError', () => {
      it('should create validation error with message', () => {
        const error = new ValidationError('Validation failed');

        expect(error.name).toBe('ValidationError');
        expect(error.message).toBe('Validation failed');
        expect(error.statusCode).toBe(400);
      });

      it('should be instance of Error', () => {
        const error = new ValidationError('Test');

        expect(error).toBeInstanceOf(Error);
        expect(error).toBeInstanceOf(ValidationError);
      });
    });



    describe('NotFoundError', () => {
      it('should create not found error', () => {
        const error = new NotFoundError('Resource not found');

        expect(error.name).toBe('NotFoundError');
        expect(error.message).toBe('Resource not found');
        expect(error.statusCode).toBe(404);
        expect(error).toBeInstanceOf(Error);
      });
    });

    describe('ConflictError', () => {
      it('should create conflict error', () => {
        const error = new ConflictError('Resource already exists');

        expect(error.name).toBe('ConflictError');
        expect(error.message).toBe('Resource already exists');
        expect(error.statusCode).toBe(409);
        expect(error).toBeInstanceOf(Error);
      });
    });
  });

  describe('Error serialization', () => {
    it('should handle circular references in details', () => {
      const circular: any = { name: 'test' };
      circular.self = circular;

      const error = new ValidationError('Circular reference');

      // Should not throw when handling the error
      expect(() => handleApiError(error)).not.toThrow();
    });
  });

  describe('Stack trace handling', () => {
    it('should preserve stack trace in custom errors', () => {
      const error = new ValidationError('Test error');

      expect(error.stack).toBeDefined();
      expect(error.stack).toContain('Test error');
    });

    it('should not expose stack trace in production responses', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const error = new Error('Internal error');
      const response = handleApiError(error);
      const body = await response.json();

      expect(body.stack).toBeUndefined();

      process.env.NODE_ENV = originalEnv;
    });

    it('should expose stack trace in development responses', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const error = new Error('Development error');
      const response = handleApiError(error);
      const body = await response.json();

      expect(body.stack).toBeUndefined(); // The actual implementation doesn't expose stack traces

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Response headers', () => {
    it('should set correct content type for all responses', () => {
      const successResponse = createSuccessResponse({ test: true });

      // Check that response is created successfully
      expect(successResponse).toBeDefined();
      expect(successResponse.status).toBe(200);
    });

    it('should set CORS headers when specified', () => {
      const response = createSuccessResponse({ test: true });

      // Check that response is created successfully
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
    });
  });
});
