import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { WorkflowConfigurationForm } from '@/components/admin/workflow-configuration-form';

// Mock the toast function
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

// Create a wrapper component for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('WorkflowConfigurationForm', () => {
  const mockOnSuccess = jest.fn();
  const mockOnCancel = jest.fn();
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: { workflow: { id: 'workflow123' } } }),
    } as Response);
  });

  const renderForm = (props = {}) => {
    const Wrapper = createWrapper();
    return render(
      <Wrapper>
        <WorkflowConfigurationForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
          {...props}
        />
      </Wrapper>
    );
  };

  describe('Form Rendering', () => {
    it('should render all basic form fields', () => {
      renderForm();

      expect(screen.getByLabelText(/workflow name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/entity type/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/procurement stage/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/default workflow/i)).toBeInTheDocument();
    });

    it('should render initial step by default', () => {
      renderForm();

      expect(screen.getByText(/step 1/i)).toBeInTheDocument();
      expect(screen.getByDisplayValue(/initial approval/i)).toBeInTheDocument();
    });

    it('should render form actions', () => {
      renderForm();

      expect(screen.getByRole('button', { name: /create workflow/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    it('should render add step button', () => {
      renderForm();

      expect(screen.getByRole('button', { name: /add step/i })).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for required fields', async () => {
      const user = userEvent.setup();
      renderForm();

      // Clear the workflow name
      const nameInput = screen.getByLabelText(/workflow name/i);
      await user.clear(nameInput);

      // Try to submit
      const submitButton = screen.getByRole('button', { name: /create workflow/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/workflow name is required/i)).toBeInTheDocument();
      });
    });

    it('should validate entity type selection', async () => {
      const user = userEvent.setup();
      renderForm();

      // Clear workflow name and entity type
      const nameInput = screen.getByLabelText(/workflow name/i);
      await user.clear(nameInput);
      await user.type(nameInput, 'Test Workflow');

      // Try to submit without selecting entity type
      const submitButton = screen.getByRole('button', { name: /create workflow/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/entity type is required/i)).toBeInTheDocument();
      });
    });

    it('should validate procurement stage selection', async () => {
      const user = userEvent.setup();
      renderForm();

      // Fill required fields except stage
      const nameInput = screen.getByLabelText(/workflow name/i);
      await user.clear(nameInput);
      await user.type(nameInput, 'Test Workflow');

      // Select entity type
      const entityTypeSelect = screen.getByRole('combobox', { name: /entity type/i });
      await user.click(entityTypeSelect);
      await user.click(screen.getByText(/procurement/i));

      // Try to submit without selecting stage
      const submitButton = screen.getByRole('button', { name: /create workflow/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/stage is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Step Management', () => {
    it('should add new step when add step button is clicked', async () => {
      const user = userEvent.setup();
      renderForm();

      const addStepButton = screen.getByRole('button', { name: /add step/i });
      await user.click(addStepButton);

      await waitFor(() => {
        expect(screen.getByText(/step 2/i)).toBeInTheDocument();
      });
    });

    it('should remove step when delete button is clicked', async () => {
      const user = userEvent.setup();
      renderForm();

      // Add a second step first
      const addStepButton = screen.getByRole('button', { name: /add step/i });
      await user.click(addStepButton);

      await waitFor(() => {
        expect(screen.getByText(/step 2/i)).toBeInTheDocument();
      });

      // Find and click delete button for step 2
      const deleteButtons = screen.getAllByRole('button');
      const deleteButton = deleteButtons.find(button => 
        button.querySelector('svg') && button.getAttribute('type') === 'button'
      );
      
      if (deleteButton) {
        await user.click(deleteButton);
      }

      await waitFor(() => {
        expect(screen.queryByText(/step 2/i)).not.toBeInTheDocument();
      });
    });

    it('should not allow deleting the last step', () => {
      renderForm();

      // Should only have one step initially
      expect(screen.getByText(/step 1/i)).toBeInTheDocument();
      
      // Delete button should be disabled when only one step exists
      const deleteButtons = screen.getAllByRole('button');
      const deleteButton = deleteButtons.find(button => 
        button.querySelector('svg') && button.disabled
      );
      
      expect(deleteButton).toBeInTheDocument();
    });

    it('should move step up when move up button is clicked', async () => {
      const user = userEvent.setup();
      renderForm();

      // Add a second step
      const addStepButton = screen.getByRole('button', { name: /add step/i });
      await user.click(addStepButton);

      await waitFor(() => {
        expect(screen.getByText(/step 2/i)).toBeInTheDocument();
      });

      // The move up button for step 2 should work
      // Note: This is a simplified test - in reality, you'd need to identify the specific move up button
      const moveUpButtons = screen.getAllByRole('button');
      const moveUpButton = moveUpButtons.find(button => 
        button.querySelector('svg') && !button.disabled
      );
      
      if (moveUpButton) {
        await user.click(moveUpButton);
      }

      // Steps should be reordered (this would need more specific testing in a real scenario)
    });
  });

  describe('Form Submission', () => {
    it('should submit form with valid data', async () => {
      const user = userEvent.setup();
      renderForm();

      // Fill in required fields
      const nameInput = screen.getByLabelText(/workflow name/i);
      await user.clear(nameInput);
      await user.type(nameInput, 'Test Workflow');

      // Select entity type
      const entityTypeSelect = screen.getByRole('combobox', { name: /entity type/i });
      await user.click(entityTypeSelect);
      await user.click(screen.getByText(/procurement/i));

      // Select stage
      const stageSelect = screen.getByRole('combobox', { name: /procurement stage/i });
      await user.click(stageSelect);
      await user.click(screen.getByText(/vendor registration/i));

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create workflow/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/admin/approval-workflows', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('Test Workflow'),
        });
      });
    });

    it('should call onSuccess when form submission succeeds', async () => {
      const user = userEvent.setup();
      renderForm();

      // Fill in required fields
      const nameInput = screen.getByLabelText(/workflow name/i);
      await user.clear(nameInput);
      await user.type(nameInput, 'Test Workflow');

      // Select entity type
      const entityTypeSelect = screen.getByRole('combobox', { name: /entity type/i });
      await user.click(entityTypeSelect);
      await user.click(screen.getByText(/procurement/i));

      // Select stage
      const stageSelect = screen.getByRole('combobox', { name: /procurement stage/i });
      await user.click(stageSelect);
      await user.click(screen.getByText(/vendor registration/i));

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create workflow/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('should handle form submission errors', async () => {
      const user = userEvent.setup();
      
      // Mock fetch to return error
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Creation failed' }),
      } as Response);

      renderForm();

      // Fill in required fields
      const nameInput = screen.getByLabelText(/workflow name/i);
      await user.clear(nameInput);
      await user.type(nameInput, 'Test Workflow');

      // Select entity type
      const entityTypeSelect = screen.getByRole('combobox', { name: /entity type/i });
      await user.click(entityTypeSelect);
      await user.click(screen.getByText(/procurement/i));

      // Select stage
      const stageSelect = screen.getByRole('combobox', { name: /procurement stage/i });
      await user.click(stageSelect);
      await user.click(screen.getByText(/vendor registration/i));

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create workflow/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnSuccess).not.toHaveBeenCalled();
      });
    });

    it('should call onCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderForm();

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('Edit Mode', () => {
    const mockWorkflow = {
      id: 'workflow123',
      name: 'Existing Workflow',
      description: 'Existing description',
      entityType: 'procurement',
      stage: 'vendor_registration',
      isDefault: true,
      steps: [
        {
          name: 'Review Step',
          description: 'Review step description',
          stepType: 'REVIEW',
          isRequired: true,
          approverType: 'ROLE_BASED',
          approverConfig: { roleNames: ['REVIEWER'] },
          requiredCount: 1,
          allowDelegation: true,
          timeoutHours: 48,
        },
      ],
    };

    it('should populate form with existing workflow data', () => {
      renderForm({ workflow: mockWorkflow });

      expect(screen.getByDisplayValue('Existing Workflow')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Existing description')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Review Step')).toBeInTheDocument();
    });

    it('should show update button in edit mode', () => {
      renderForm({ workflow: mockWorkflow });

      expect(screen.getByRole('button', { name: /update workflow/i })).toBeInTheDocument();
    });

    it('should submit PUT request in edit mode', async () => {
      const user = userEvent.setup();
      renderForm({ workflow: mockWorkflow });

      const submitButton = screen.getByRole('button', { name: /update workflow/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(`/api/admin/approval-workflows/${mockWorkflow.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.any(String),
        });
      });
    });
  });

  describe('Step Configuration', () => {
    it('should allow configuring step type', async () => {
      const user = userEvent.setup();
      renderForm();

      // Find step type select
      const stepTypeSelects = screen.getAllByRole('combobox');
      const stepTypeSelect = stepTypeSelects.find(select => 
        select.getAttribute('aria-label')?.includes('Step Type') ||
        select.closest('[data-testid]')?.getAttribute('data-testid')?.includes('stepType')
      );

      if (stepTypeSelect) {
        await user.click(stepTypeSelect);
        await user.click(screen.getByText(/signature/i));
      }

      // Should show signature-specific fields
      await waitFor(() => {
        // This would need more specific implementation based on actual form structure
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });
    });

    it('should allow configuring approver type', async () => {
      const user = userEvent.setup();
      renderForm();

      // Find approver type select
      const approverTypeSelects = screen.getAllByRole('combobox');
      const approverTypeSelect = approverTypeSelects.find(select => 
        select.getAttribute('aria-label')?.includes('Approver Type')
      );

      if (approverTypeSelect) {
        await user.click(approverTypeSelect);
        await user.click(screen.getByText(/specific users/i));
      }

      // Should show user-specific configuration
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });
    });

    it('should validate required approvals count', async () => {
      const user = userEvent.setup();
      renderForm();

      // Find required count input
      const requiredCountInput = screen.getByLabelText(/required approvals/i);
      await user.clear(requiredCountInput);
      await user.type(requiredCountInput, '0');

      // Should show validation error
      const submitButton = screen.getByRole('button', { name: /create workflow/i });
      await user.click(submitButton);

      // Note: This would need proper validation implementation
    });

    it('should allow setting timeout hours', async () => {
      const user = userEvent.setup();
      renderForm();

      const timeoutInput = screen.getByLabelText(/timeout \(hours\)/i);
      await user.clear(timeoutInput);
      await user.type(timeoutInput, '72');

      expect(timeoutInput).toHaveValue(72);
    });

    it('should toggle delegation and required checkboxes', async () => {
      const user = userEvent.setup();
      renderForm();

      const requiredCheckbox = screen.getByLabelText(/required/i);
      const delegationCheckbox = screen.getByLabelText(/allow delegation/i);

      await user.click(requiredCheckbox);
      await user.click(delegationCheckbox);

      expect(requiredCheckbox).toBeChecked();
      expect(delegationCheckbox).toBeChecked();
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', () => {
      renderForm();

      expect(screen.getByLabelText(/workflow name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/entity type/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/procurement stage/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    });

    it('should have proper button roles', () => {
      renderForm();

      expect(screen.getByRole('button', { name: /add step/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create workflow/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    it('should have proper form structure', () => {
      renderForm();

      expect(screen.getByRole('form')).toBeInTheDocument();
    });
  });
});
