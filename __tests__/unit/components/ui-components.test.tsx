import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

describe('UI Components', () => {
  describe('Button Component', () => {
    it('should render button with text', () => {
      render(<Button>Click me</Button>);
      expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
    });

    it('should handle click events', async () => {
      const user = userEvent.setup();
      const handleClick = jest.fn();
      
      render(<Button onClick={handleClick}>Click me</Button>);
      
      const button = screen.getByRole('button', { name: /click me/i });
      await user.click(button);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('should apply variant classes', () => {
      render(<Button variant="destructive">Delete</Button>);
      const button = screen.getByRole('button', { name: /delete/i });
      expect(button).toHaveClass('bg-destructive');
    });

    it('should apply size classes', () => {
      render(<Button size="sm">Small</Button>);
      const button = screen.getByRole('button', { name: /small/i });
      expect(button).toHaveClass('h-9');
    });

    it('should be disabled when disabled prop is true', () => {
      render(<Button disabled>Disabled</Button>);
      const button = screen.getByRole('button', { name: /disabled/i });
      expect(button).toBeDisabled();
    });

    it('should render as child component when asChild is true', () => {
      render(
        <Button asChild>
          <a href="/test">Link Button</a>
        </Button>
      );
      expect(screen.getByRole('link', { name: /link button/i })).toBeInTheDocument();
    });

    it('should handle loading state', () => {
      render(<Button disabled>Loading...</Button>);
      const button = screen.getByRole('button', { name: /loading/i });
      expect(button).toBeDisabled();
    });
  });

  describe('Input Component', () => {
    it('should render input field', () => {
      render(<Input placeholder="Enter text" />);
      expect(screen.getByPlaceholderText(/enter text/i)).toBeInTheDocument();
    });

    it('should handle value changes', async () => {
      const user = userEvent.setup();
      const handleChange = jest.fn();
      
      render(<Input onChange={handleChange} />);
      
      const input = screen.getByRole('textbox');
      await user.type(input, 'test value');
      
      expect(handleChange).toHaveBeenCalled();
      expect(input).toHaveValue('test value');
    });

    it('should apply type attribute', () => {
      render(<Input type="email" />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'email');
    });

    it('should be disabled when disabled prop is true', () => {
      render(<Input disabled />);
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('should apply custom className', () => {
      render(<Input className="custom-class" />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('custom-class');
    });

    it('should handle password type', () => {
      render(<Input type="password" />);
      const input = screen.getByLabelText(/password/i) || screen.getByDisplayValue('');
      expect(input).toHaveAttribute('type', 'password');
    });

    it('should handle number type', () => {
      render(<Input type="number" />);
      const input = screen.getByRole('spinbutton');
      expect(input).toHaveAttribute('type', 'number');
    });
  });

  describe('Card Components', () => {
    it('should render card with all sub-components', () => {
      render(
        <Card>
          <CardHeader>
            <CardTitle>Card Title</CardTitle>
            <CardDescription>Card Description</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Card content goes here</p>
          </CardContent>
          <CardFooter>
            <Button>Action</Button>
          </CardFooter>
        </Card>
      );

      expect(screen.getByText(/card title/i)).toBeInTheDocument();
      expect(screen.getByText(/card description/i)).toBeInTheDocument();
      expect(screen.getByText(/card content goes here/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /action/i })).toBeInTheDocument();
    });

    it('should apply custom className to card', () => {
      render(<Card className="custom-card">Content</Card>);
      const card = screen.getByText(/content/i).closest('div');
      expect(card).toHaveClass('custom-card');
    });

    it('should render card header with proper structure', () => {
      render(
        <CardHeader>
          <CardTitle>Title</CardTitle>
        </CardHeader>
      );
      
      const title = screen.getByText(/title/i);
      expect(title.tagName).toBe('H3');
    });

    it('should render card description with muted styling', () => {
      render(<CardDescription>Description text</CardDescription>);
      const description = screen.getByText(/description text/i);
      expect(description).toHaveClass('text-muted-foreground');
    });
  });

  describe('Badge Component', () => {
    it('should render badge with text', () => {
      render(<Badge>New</Badge>);
      expect(screen.getByText(/new/i)).toBeInTheDocument();
    });

    it('should apply variant classes', () => {
      render(<Badge variant="destructive">Error</Badge>);
      const badge = screen.getByText(/error/i);
      expect(badge).toHaveClass('bg-destructive');
    });

    it('should apply secondary variant', () => {
      render(<Badge variant="secondary">Info</Badge>);
      const badge = screen.getByText(/info/i);
      expect(badge).toHaveClass('bg-secondary');
    });

    it('should apply outline variant', () => {
      render(<Badge variant="outline">Outline</Badge>);
      const badge = screen.getByText(/outline/i);
      expect(badge).toHaveClass('border');
    });

    it('should apply custom className', () => {
      render(<Badge className="custom-badge">Custom</Badge>);
      const badge = screen.getByText(/custom/i);
      expect(badge).toHaveClass('custom-badge');
    });
  });

  describe('Alert Components', () => {
    it('should render alert with title and description', () => {
      render(
        <Alert>
          <AlertTitle>Alert Title</AlertTitle>
          <AlertDescription>Alert description text</AlertDescription>
        </Alert>
      );

      expect(screen.getByText(/alert title/i)).toBeInTheDocument();
      expect(screen.getByText(/alert description text/i)).toBeInTheDocument();
    });

    it('should apply variant classes', () => {
      render(
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
        </Alert>
      );
      
      const alert = screen.getByText(/error/i).closest('div');
      expect(alert).toHaveClass('border-destructive');
    });

    it('should render with icon', () => {
      const AlertIcon = () => <span data-testid="alert-icon">!</span>;
      
      render(
        <Alert>
          <AlertIcon />
          <AlertTitle>Alert with Icon</AlertTitle>
        </Alert>
      );

      expect(screen.getByTestId('alert-icon')).toBeInTheDocument();
      expect(screen.getByText(/alert with icon/i)).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      render(
        <Alert className="custom-alert">
          <AlertTitle>Custom Alert</AlertTitle>
        </Alert>
      );
      
      const alert = screen.getByText(/custom alert/i).closest('div');
      expect(alert).toHaveClass('custom-alert');
    });
  });

  describe('Component Accessibility', () => {
    it('should have proper button accessibility', () => {
      render(<Button aria-label="Close dialog">×</Button>);
      const button = screen.getByRole('button', { name: /close dialog/i });
      expect(button).toBeInTheDocument();
    });

    it('should have proper input accessibility', () => {
      render(
        <div>
          <label htmlFor="email-input">Email</label>
          <Input id="email-input" type="email" />
        </div>
      );
      
      const input = screen.getByLabelText(/email/i);
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'email');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      const handleClick = jest.fn();
      
      render(<Button onClick={handleClick}>Keyboard Test</Button>);
      
      const button = screen.getByRole('button', { name: /keyboard test/i });
      button.focus();
      
      await user.keyboard('{Enter}');
      expect(handleClick).toHaveBeenCalled();
    });

    it('should support screen reader attributes', () => {
      render(
        <Alert role="alert" aria-live="polite">
          <AlertTitle>Important Message</AlertTitle>
          <AlertDescription>This is an important alert</AlertDescription>
        </Alert>
      );
      
      const alert = screen.getByRole('alert');
      expect(alert).toHaveAttribute('aria-live', 'polite');
    });
  });

  describe('Component Styling', () => {
    it('should apply focus styles to button', async () => {
      const user = userEvent.setup();
      render(<Button>Focus Test</Button>);
      
      const button = screen.getByRole('button', { name: /focus test/i });
      await user.tab();
      
      expect(button).toHaveFocus();
      expect(button).toHaveClass('focus-visible:ring-2');
    });

    it('should apply hover styles to button', () => {
      render(<Button>Hover Test</Button>);
      const button = screen.getByRole('button', { name: /hover test/i });
      expect(button).toHaveClass('hover:bg-primary/90');
    });

    it('should apply disabled styles', () => {
      render(<Button disabled>Disabled Button</Button>);
      const button = screen.getByRole('button', { name: /disabled button/i });
      expect(button).toHaveClass('disabled:pointer-events-none');
    });
  });

  describe('Component Props Forwarding', () => {
    it('should forward ref to button', () => {
      const ref = React.createRef<HTMLButtonElement>();
      render(<Button ref={ref}>Ref Test</Button>);
      
      expect(ref.current).toBeInstanceOf(HTMLButtonElement);
    });

    it('should forward ref to input', () => {
      const ref = React.createRef<HTMLInputElement>();
      render(<Input ref={ref} />);
      
      expect(ref.current).toBeInstanceOf(HTMLInputElement);
    });

    it('should forward data attributes', () => {
      render(<Button data-testid="custom-button">Test</Button>);
      expect(screen.getByTestId('custom-button')).toBeInTheDocument();
    });

    it('should forward aria attributes', () => {
      render(<Input aria-describedby="help-text" />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-describedby', 'help-text');
    });
  });

  describe('Component Edge Cases', () => {
    it('should handle empty children', () => {
      render(<Button></Button>);
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toBeEmptyDOMElement();
    });

    it('should handle null children', () => {
      render(<Card>{null}</Card>);
      const card = screen.getByRole('generic');
      expect(card).toBeInTheDocument();
    });

    it('should handle undefined props', () => {
      render(<Button variant={undefined}>Test</Button>);
      const button = screen.getByRole('button', { name: /test/i });
      expect(button).toBeInTheDocument();
    });

    it('should handle multiple class names', () => {
      render(<Button className="class1 class2 class3">Multi Class</Button>);
      const button = screen.getByRole('button', { name: /multi class/i });
      expect(button).toHaveClass('class1', 'class2', 'class3');
    });
  });
});
