import { NextRequest } from "next/server";
import { jest } from "@jest/globals";

// Mock the dependencies
jest.mock("@/lib/db", () => ({
  prisma: {
    purchaseRequisition: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    purchaseRequisitionItem: {
      create: jest.fn(),
      deleteMany: jest.fn(),
    },
    contract: {
      findUnique: jest.fn(),
    },
    notification: {
      create: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

jest.mock("@/lib/auth", () => ({
  getCurrentUser: jest.fn(),
}));

jest.mock("@/lib/approval/enhanced-workflow-engine", () => ({
  enhancedWorkflowEngine: {
    startStageApproval: jest.fn(),
  },
}));

import { prisma } from "@/lib/db";
import { getCurrentUser } from "@/lib/auth";
import { enhancedWorkflowEngine } from "@/lib/approval/enhanced-workflow-engine";

// Import the handlers
import { GET as listHandler, POST as createHandler } from "@/app/api/purchase-requisitions/route";
import { GET as getHandler } from "@/app/api/purchase-requisitions/[id]/route";
import { POST as releaseHandler } from "@/app/api/purchase-requisitions/[id]/release/route";

describe("Purchase Requisitions API", () => {
  const mockUser = {
    id: "user-1",
    name: "Test User",
    email: "<EMAIL>",
    roles: ["PROCUREMENT_USER"],
  };

  const mockPR = {
    id: "pr-1",
    prNumber: "***********-0001",
    title: "Test PR",
    type: "INTERNAL",
    status: "DRAFT",
    requesterId: "user-1",
    createdAt: new Date(),
    items: [
      {
        id: "item-1",
        name: "Test Item",
        quantity: 10,
        unit: "pcs",
        estimatedPrice: 100000,
      },
    ],
    requester: {
      id: "user-1",
      name: "Test User",
      email: "<EMAIL>",
    },
    _count: {
      items: 1,
      documents: 0,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (getCurrentUser as jest.Mock).mockResolvedValue(mockUser);
  });

  describe("GET /api/purchase-requisitions", () => {
    it("should list purchase requisitions successfully", async () => {
      const mockPRs = [mockPR];
      (prisma.purchaseRequisition.findMany as jest.Mock).mockResolvedValue(mockPRs);
      (prisma.purchaseRequisition.count as jest.Mock).mockResolvedValue(1);

      const request = new NextRequest("http://localhost/api/purchase-requisitions");
      const response = await listHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(1);
      expect(data.data[0].totalAmount).toBe(1000000); // 10 * 100000
    });

    it("should filter by status", async () => {
      const mockPRs = [{ ...mockPR, status: "APPROVED" }];
      (prisma.purchaseRequisition.findMany as jest.Mock).mockResolvedValue(mockPRs);
      (prisma.purchaseRequisition.count as jest.Mock).mockResolvedValue(1);

      const request = new NextRequest("http://localhost/api/purchase-requisitions?status=APPROVED");
      const response = await listHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(prisma.purchaseRequisition.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: "APPROVED",
          }),
        })
      );
    });

    it("should filter by user role", async () => {
      const procurementUser = { ...mockUser, roles: ["PROCUREMENT_USER"] };
      (getCurrentUser as jest.Mock).mockResolvedValue(procurementUser);
      (prisma.purchaseRequisition.findMany as jest.Mock).mockResolvedValue([mockPR]);
      (prisma.purchaseRequisition.count as jest.Mock).mockResolvedValue(1);

      const request = new NextRequest("http://localhost/api/purchase-requisitions");
      const response = await listHandler(request);

      expect(response.status).toBe(200);
      expect(prisma.purchaseRequisition.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            requesterId: "user-1",
          }),
        })
      );
    });
  });

  describe("POST /api/purchase-requisitions", () => {
    const validPRData = {
      title: "Test PR",
      type: "INTERNAL",
      items: [
        {
          name: "Test Item",
          quantity: 10,
          unit: "pcs",
          estimatedPrice: 100000,
        },
      ],
    };

    it("should create purchase requisition successfully", async () => {
      const mockTransaction = jest.fn().mockResolvedValue({
        ...mockPR,
        items: [
          {
            id: "item-1",
            prId: "pr-1",
            name: "Test Item",
            quantity: 10,
            unit: "pcs",
            estimatedPrice: 100000,
          },
        ],
      });
      (prisma.$transaction as jest.Mock).mockImplementation(mockTransaction);

      const request = new NextRequest("http://localhost/api/purchase-requisitions", {
        method: "POST",
        body: JSON.stringify(validPRData),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.title).toBe("Test PR");
    });

    it("should validate required fields", async () => {
      const invalidData = {
        title: "",
        type: "INVALID_TYPE",
        items: [],
      };

      const request = new NextRequest("http://localhost/api/purchase-requisitions", {
        method: "POST",
        body: JSON.stringify(invalidData),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });

    it("should validate source contract for external routine", async () => {
      const dataWithContract = {
        ...validPRData,
        type: "EXTERNAL_ROUTINE",
        sourceContractId: "contract-1",
      };

      (prisma.contract.findUnique as jest.Mock).mockResolvedValue({
        id: "contract-1",
        status: "ACTIVE",
      });

      const mockTransaction = jest.fn().mockResolvedValue(mockPR);
      (prisma.$transaction as jest.Mock).mockImplementation(mockTransaction);

      const request = new NextRequest("http://localhost/api/purchase-requisitions", {
        method: "POST",
        body: JSON.stringify(dataWithContract),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(prisma.contract.findUnique).toHaveBeenCalledWith({
        where: { id: "contract-1" },
      });
    });
  });

  describe("GET /api/purchase-requisitions/[id]", () => {
    it("should get purchase requisition by ID", async () => {
      (prisma.purchaseRequisition.findUnique as jest.Mock).mockResolvedValue({
        ...mockPR,
        totalAmount: 1000000,
      });

      const request = new NextRequest("http://localhost/api/purchase-requisitions/pr-1");
      const response = await getHandler(request, { params: { id: "pr-1" } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.id).toBe("pr-1");
      expect(data.data.totalAmount).toBe(1000000);
    });

    it("should return 404 for non-existent PR", async () => {
      (prisma.purchaseRequisition.findUnique as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest("http://localhost/api/purchase-requisitions/non-existent");
      const response = await getHandler(request, { params: { id: "non-existent" } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
    });

    it("should check access permissions", async () => {
      const otherUserPR = { ...mockPR, requesterId: "other-user" };
      (prisma.purchaseRequisition.findUnique as jest.Mock).mockResolvedValue(otherUserPR);

      const request = new NextRequest("http://localhost/api/purchase-requisitions/pr-1");
      const response = await getHandler(request, { params: { id: "pr-1" } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain("Access denied");
    });
  });

  describe("POST /api/purchase-requisitions/[id]/release", () => {
    it("should release PR for approval successfully", async () => {
      const draftPR = { ...mockPR, status: "DRAFT" };
      (prisma.purchaseRequisition.findUnique as jest.Mock).mockResolvedValue(draftPR);

      const mockApprovalInstance = {
        id: "approval-1",
        status: "PENDING",
      };
      (enhancedWorkflowEngine.startStageApproval as jest.Mock).mockResolvedValue(mockApprovalInstance);

      const mockTransaction = jest.fn().mockResolvedValue({
        purchaseRequisition: { ...draftPR, status: "PENDING_APPROVAL" },
        approvalInstance: mockApprovalInstance,
      });
      (prisma.$transaction as jest.Mock).mockImplementation(mockTransaction);

      const request = new NextRequest("http://localhost/api/purchase-requisitions/pr-1/release", {
        method: "POST",
      });

      const response = await releaseHandler(request, { params: { id: "pr-1" } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.status).toBe("PENDING_APPROVAL");
      expect(enhancedWorkflowEngine.startStageApproval).toHaveBeenCalled();
    });

    it("should validate PR status before release", async () => {
      const approvedPR = { ...mockPR, status: "APPROVED" };
      (prisma.purchaseRequisition.findUnique as jest.Mock).mockResolvedValue(approvedPR);

      const request = new NextRequest("http://localhost/api/purchase-requisitions/pr-1/release", {
        method: "POST",
      });

      const response = await releaseHandler(request, { params: { id: "pr-1" } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain("cannot be released");
    });

    it("should validate PR has items before release", async () => {
      const emptyPR = { ...mockPR, items: [] };
      (prisma.purchaseRequisition.findUnique as jest.Mock).mockResolvedValue(emptyPR);

      const request = new NextRequest("http://localhost/api/purchase-requisitions/pr-1/release", {
        method: "POST",
      });

      const response = await releaseHandler(request, { params: { id: "pr-1" } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain("must have at least one item");
    });
  });
});
