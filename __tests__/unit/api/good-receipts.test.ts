import { NextRequest } from "next/server";
import { jest } from "@jest/globals";

// Mock the dependencies
jest.mock("@/lib/db", () => ({
  prisma: {
    goodReceipt: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    goodReceiptItem: {
      create: jest.fn(),
      createMany: jest.fn(),
    },
    purchaseOrder: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    purchaseOrderItem: {
      update: jest.fn(),
    },
    notification: {
      create: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

jest.mock("@/lib/auth", () => ({
  getCurrentUser: jest.fn(),
}));

import { prisma } from "@/lib/db";
import { getCurrentUser } from "@/lib/auth";

// Import the handlers
import { GET as listHandler, POST as create<PERSON>andler } from "@/app/api/good-receipts/route";
import { GET as getHandler } from "@/app/api/good-receipts/[id]/route";

describe("Good Receipts API", () => {
  const mockUser = {
    id: "user-1",
    name: "Test User",
    email: "<EMAIL>",
    roles: ["PROCUREMENT_USER"],
  };

  const mockPO = {
    id: "po-1",
    poNumber: "***********-0001",
    status: "APPROVED",
    vendorId: "vendor-1",
    vendor: {
      id: "vendor-1",
      companyName: "Test Vendor",
    },
    items: [
      {
        id: "po-item-1",
        name: "Test Item",
        quantity: 10,
        unit: "pcs",
        price: 100000,
        receivedQuantity: 0,
      },
    ],
  };

  const mockGR = {
    id: "gr-1",
    grNumber: "***********-0001",
    poId: "po-1",
    receivedDate: new Date(),
    status: "DRAFT",
    createdById: "user-1",
    purchaseOrder: mockPO,
    items: [
      {
        id: "gr-item-1",
        purchaseOrderItemId: "po-item-1",
        receivedQuantity: 8,
        notes: "Partial delivery",
      },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (getCurrentUser as jest.Mock).mockResolvedValue(mockUser);
  });

  describe("GET /api/good-receipts", () => {
    it("should list good receipts successfully", async () => {
      const mockGRs = [mockGR];
      (prisma.goodReceipt.findMany as jest.Mock).mockResolvedValue(mockGRs);

      const request = new NextRequest("http://localhost/api/good-receipts");
      const response = await listHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(1);
      expect(data.data[0].grNumber).toBe("***********-0001");
    });

    it("should filter by status", async () => {
      const completedGRs = [{ ...mockGR, status: "COMPLETED" }];
      (prisma.goodReceipt.findMany as jest.Mock).mockResolvedValue(completedGRs);

      const request = new NextRequest("http://localhost/api/good-receipts?status=COMPLETED");
      const response = await listHandler(request);

      expect(response.status).toBe(200);
      expect(prisma.goodReceipt.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: "COMPLETED",
          }),
        })
      );
    });

    it("should filter by PO", async () => {
      (prisma.goodReceipt.findMany as jest.Mock).mockResolvedValue([mockGR]);

      const request = new NextRequest("http://localhost/api/good-receipts?poId=po-1");
      const response = await listHandler(request);

      expect(response.status).toBe(200);
      expect(prisma.goodReceipt.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            poId: "po-1",
          }),
        })
      );
    });
  });

  describe("POST /api/good-receipts", () => {
    const validGRData = {
      poId: "po-1",
      receivedDate: new Date().toISOString(),
      items: [
        {
          purchaseOrderItemId: "po-item-1",
          receivedQuantity: 8,
          notes: "Partial delivery",
        },
      ],
    };

    it("should create good receipt successfully", async () => {
      (prisma.purchaseOrder.findUnique as jest.Mock).mockResolvedValue(mockPO);

      const mockTransaction = jest.fn().mockResolvedValue({
        goodReceipt: mockGR,
        items: mockGR.items,
      });
      (prisma.$transaction as jest.Mock).mockImplementation(mockTransaction);

      const request = new NextRequest("http://localhost/api/good-receipts", {
        method: "POST",
        body: JSON.stringify(validGRData),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.grNumber).toBe("***********-0001");
    });

    it("should validate PO exists and is approved", async () => {
      (prisma.purchaseOrder.findUnique as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest("http://localhost/api/good-receipts", {
        method: "POST",
        body: JSON.stringify(validGRData),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.message).toContain("Purchase order not found");
    });

    it("should validate PO status", async () => {
      const draftPO = { ...mockPO, status: "DRAFT" };
      (prisma.purchaseOrder.findUnique as jest.Mock).mockResolvedValue(draftPO);

      const request = new NextRequest("http://localhost/api/good-receipts", {
        method: "POST",
        body: JSON.stringify(validGRData),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain("must be approved");
    });

    it("should validate received quantities", async () => {
      (prisma.purchaseOrder.findUnique as jest.Mock).mockResolvedValue(mockPO);

      const invalidData = {
        ...validGRData,
        items: [
          {
            purchaseOrderItemId: "po-item-1",
            receivedQuantity: 15, // More than ordered (10)
            notes: "Over delivery",
          },
        ],
      };

      const request = new NextRequest("http://localhost/api/good-receipts", {
        method: "POST",
        body: JSON.stringify(invalidData),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain("exceeds ordered quantity");
    });

    it("should validate required fields", async () => {
      const invalidData = {
        poId: "",
        receivedDate: "invalid-date",
        items: [],
      };

      const request = new NextRequest("http://localhost/api/good-receipts", {
        method: "POST",
        body: JSON.stringify(invalidData),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });

    it("should update PO item received quantities", async () => {
      (prisma.purchaseOrder.findUnique as jest.Mock).mockResolvedValue(mockPO);

      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const tx = {
          goodReceipt: {
            create: jest.fn().mockResolvedValue(mockGR),
          },
          goodReceiptItem: {
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
          purchaseOrderItem: {
            update: jest.fn().mockResolvedValue({}),
          },
          purchaseOrder: {
            update: jest.fn().mockResolvedValue({}),
          },
        };
        return await callback(tx);
      });
      (prisma.$transaction as jest.Mock).mockImplementation(mockTransaction);

      const request = new NextRequest("http://localhost/api/good-receipts", {
        method: "POST",
        body: JSON.stringify(validGRData),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const response = await createHandler(request);

      expect(response.status).toBe(200);
      expect(prisma.$transaction).toHaveBeenCalled();
    });
  });

  describe("GET /api/good-receipts/[id]", () => {
    it("should get good receipt by ID", async () => {
      (prisma.goodReceipt.findUnique as jest.Mock).mockResolvedValue(mockGR);

      const request = new NextRequest("http://localhost/api/good-receipts/gr-1");
      const response = await getHandler(request, { params: { id: "gr-1" } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.id).toBe("gr-1");
      expect(data.data.grNumber).toBe("***********-0001");
    });

    it("should return 404 for non-existent GR", async () => {
      (prisma.goodReceipt.findUnique as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest("http://localhost/api/good-receipts/non-existent");
      const response = await getHandler(request, { params: { id: "non-existent" } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.message).toContain("not found");
    });

    it("should include related data", async () => {
      const grWithRelations = {
        ...mockGR,
        purchaseOrder: mockPO,
        items: mockGR.items,
        createdBy: {
          id: "user-1",
          name: "Test User",
          email: "<EMAIL>",
        },
      };
      (prisma.goodReceipt.findUnique as jest.Mock).mockResolvedValue(grWithRelations);

      const request = new NextRequest("http://localhost/api/good-receipts/gr-1");
      const response = await getHandler(request, { params: { id: "gr-1" } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.purchaseOrder).toBeDefined();
      expect(data.data.items).toBeDefined();
      expect(data.data.createdBy).toBeDefined();
    });
  });
});
