import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/db';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/lib/db');

const mockGetCurrentUser = getCurrentUser as jest.MockedFunction<typeof getCurrentUser>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('Approval Workflows System', () => {

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Approval Workflow Configuration', () => {
    const mockUser = {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
      roles: ['ADMIN'],
    };

    beforeEach(() => {
      mockGetCurrentUser.mockResolvedValue(mockUser);
    });

    it('should create workflow configuration', async () => {
      const mockWorkflow = {
        id: 'workflow-1',
        name: 'Test Workflow',
        entityType: 'PURCHASE_REQUISITION',
        enabled: true,
        priority: 1,
        createdBy: { id: 'user-1', name: 'Test User', email: '<EMAIL>' },
      };

      mockPrisma.approvalWorkflowConfig.create.mockResolvedValue(mockWorkflow as any);

      const workflowData = {
        name: 'Test Workflow',
        entityType: 'PURCHASE_REQUISITION',
        steps: [
          {
            name: 'Supervisor Approval',
            type: 'SINGLE',
            approvers: [{ type: 'ROLE', value: 'SUPERVISOR' }],
          },
        ],
      };

      // Test that the mock is properly configured
      const result = await mockPrisma.approvalWorkflowConfig.create({
        data: { ...workflowData, createdBy: 'user-1' },
        include: { createdBy: { select: { id: true, name: true, email: true } } },
      });

      expect(result.name).toBe('Test Workflow');
      expect(result.entityType).toBe('PURCHASE_REQUISITION');
      expect(mockPrisma.approvalWorkflowConfig.create).toHaveBeenCalledWith({
        data: { ...workflowData, createdBy: 'user-1' },
        include: { createdBy: { select: { id: true, name: true, email: true } } },
      });
    });

    it('should retrieve workflow configurations', async () => {
      const mockWorkflows = [
        {
          id: 'workflow1',
          name: 'Vendor Registration Workflow',
          entityType: 'VENDOR',
          enabled: true,
          priority: 1,
          createdBy: { id: 'user-1', name: 'Test User', email: '<EMAIL>' },
          _count: { instances: 5 },
        },
      ];

      mockPrisma.approvalWorkflowConfig.findMany.mockResolvedValue(mockWorkflows as any);

      const result = await mockPrisma.approvalWorkflowConfig.findMany({
        where: { entityType: 'VENDOR' },
        include: { createdBy: { select: { id: true, name: true, email: true } } },
      });

      expect(result).toEqual(mockWorkflows);
      expect(result[0].entityType).toBe('VENDOR');
      expect(mockPrisma.approvalWorkflowConfig.findMany).toHaveBeenCalledWith({
        where: { entityType: 'VENDOR' },
        include: { createdBy: { select: { id: true, name: true, email: true } } },
      });
    });

    it('should handle errors gracefully', async () => {
      mockPrisma.approvalWorkflowConfig.findMany.mockRejectedValue(new Error('Database error'));

      try {
        await mockPrisma.approvalWorkflowConfig.findMany();
      } catch (error) {
        expect(error.message).toBe('Database error');
      }

      expect(mockPrisma.approvalWorkflowConfig.findMany).toHaveBeenCalled();
    });
  });
});
