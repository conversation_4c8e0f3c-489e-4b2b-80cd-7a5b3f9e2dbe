import { auditLogger } from '@/lib/audit/comprehensive-audit-logger';
import { prisma } from '@/lib/db';

// Mock dependencies
jest.mock('@/lib/audit/comprehensive-audit-logger');
jest.mock('@/lib/db');

const mockAuditLogger = auditLogger as jest.Mocked<typeof auditLogger>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('Audit Logs System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Audit Logger', () => {
    it('should retrieve audit logs with filtering', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          userId: 'user-1',
          action: 'CREATE',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
          createdAt: new Date(),
          user: { id: 'user-1', name: 'Test User', email: '<EMAIL>' },
        },
      ];

      mockAuditLogger.getAuditLogs.mockResolvedValue({
        logs: mockLogs,
        total: 1,
        page: 1,
        totalPages: 1,
      });

      const filters = {
        userId: 'user-1',
        action: 'CREATE',
        entityType: 'PURCHASE_REQUISITION',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        page: 1,
        limit: 50,
      };

      const result = await auditLogger.getAuditLogs(filters);

      expect(result).toEqual({
        logs: mockLogs,
        total: 1,
        page: 1,
        totalPages: 1,
      });

      expect(mockAuditLogger.getAuditLogs).toHaveBeenCalledWith(filters);
    });

    it('should log CRUD operations', async () => {
      const mockRequest = {
        headers: {
          get: jest.fn()
            .mockReturnValueOnce('192.168.1.1') // x-forwarded-for
            .mockReturnValueOnce('Mozilla/5.0'), // user-agent
        },
      };

      mockPrisma.auditLog.create.mockResolvedValue({
        id: 'audit-1',
        userId: 'user-1',
        action: 'CREATE',
        entityType: 'PURCHASE_REQUISITION',
        entityId: 'pr-1',
      } as any);

      await auditLogger.logCrudOperation(
        'user-1',
        'CREATE',
        'PURCHASE_REQUISITION',
        'pr-1',
        null,
        { title: 'New PR', amount: 1000 },
        mockRequest as any
      );

      expect(mockAuditLogger.logCrudOperation).toHaveBeenCalledWith(
        'user-1',
        'CREATE',
        'PURCHASE_REQUISITION',
        'pr-1',
        null,
        { title: 'New PR', amount: 1000 },
        mockRequest
      );
    });

    it('should handle batch logging', async () => {
      const batchLogs = [
        {
          userId: 'user-1',
          action: 'VIEW',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-1',
        },
        {
          userId: 'user-1',
          action: 'VIEW',
          entityType: 'PURCHASE_REQUISITION',
          entityId: 'pr-2',
        },
      ];

      mockPrisma.auditLog.createMany.mockResolvedValue({ count: 2 });

      // Test that the mock is properly configured
      const result = await mockPrisma.auditLog.createMany({ data: batchLogs });

      expect(result.count).toBe(2);
      expect(mockPrisma.auditLog.createMany).toHaveBeenCalledWith({ data: batchLogs });
    });

    it('should handle errors gracefully', async () => {
      mockAuditLogger.getAuditLogs.mockRejectedValue(new Error('Database error'));

      const filters = { page: 1, limit: 50 };

      try {
        await auditLogger.getAuditLogs(filters);
      } catch (error) {
        expect(error.message).toBe('Database error');
      }

      expect(mockAuditLogger.getAuditLogs).toHaveBeenCalledWith(filters);
    });
  });
});
