# =============================================================================
# E-PROCUREMENT SYSTEM - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and configure the values according to your environment
# All sensitive values should be replaced with actual credentials

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Node.js Environment (development, production, test)
NODE_ENV=development

# Application Port (default: 3000)
PORT=3000

# Application URLs
APP_URL=http://localhost:3000
APP_NAME="E-Procurement System"

# =============================================================================
# DATABASE CONFIGURATION (REQUIRED)
# =============================================================================

# PostgreSQL Database Connection
# Format: postgresql://username:password@host:port/database?schema=public
DATABASE_URL="postgresql://username:password@localhost:5432/eproc_db?schema=public"

# Database Connection Pool Settings (optional)
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# =============================================================================
# AUTHENTICATION & SECURITY (REQUIRED)
# =============================================================================

# JWT Secret Key - MUST be a strong, random string in production
# Generate with: openssl rand -base64 32
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Bcrypt Rounds for Password Hashing (default: 12)
# Higher values = more secure but slower
BCRYPT_ROUNDS=12

# CSRF Protection Secret Key
# Generate with: openssl rand -base64 32
CSRF_SECRET=your-csrf-secret-key-change-this-in-production

# =============================================================================
# EMAIL/SMTP CONFIGURATION (REQUIRED FOR NOTIFICATIONS)
# =============================================================================

# SMTP Server Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email Settings
FROM_EMAIL=<EMAIL>
FROM_NAME="E-Procurement System"

# Email Service Provider (optional alternatives)
# EMAIL_PROVIDER=sendgrid  # or 'ses', 'postmark', 'resend'
# SENDGRID_API_KEY=your-sendgrid-api-key
# AWS_SES_REGION=us-east-1
# POSTMARK_API_KEY=your-postmark-api-key
# RESEND_API_KEY=your-resend-api-key

# =============================================================================
# REDIS CONFIGURATION (OPTIONAL - FOR CACHING & RATE LIMITING)
# =============================================================================

# Redis Connection URL
REDIS_URL=redis://localhost:6379

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Cache Settings
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================

# Storage Provider: local, aws-s3, gcs, cloudflare-r2
STORAGE_PROVIDER=local

# --- LOCAL STORAGE (DEFAULT) ---
LOCAL_STORAGE_PATH=./uploads
LOCAL_STORAGE_URL=http://localhost:3000/uploads

# --- AWS S3 STORAGE ---
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=your-s3-bucket-name
# AWS_S3_CDN_URL=https://your-cloudfront-domain.com

# --- GOOGLE CLOUD STORAGE ---
# GCS_PROJECT_ID=your-gcs-project-id
# GCS_KEY_FILE=path/to/service-account-key.json
# GCS_BUCKET=your-gcs-bucket-name
# GCS_CDN_URL=https://your-cdn-domain.com

# --- CLOUDFLARE R2 STORAGE ---
# R2_ACCOUNT_ID=your-cloudflare-account-id
# R2_ACCESS_KEY_ID=your-r2-access-key
# R2_SECRET_ACCESS_KEY=your-r2-secret-key
# R2_BUCKET=your-r2-bucket-name
# R2_CDN_URL=https://your-r2-cdn-domain.com

# =============================================================================
# PUBLIC ASSETS CONFIGURATION (SEPARATE FROM PRIVATE FILES)
# =============================================================================

# Public Assets Storage (for logos, banners, etc.)
PUBLIC_ASSETS_PROVIDER=local
PUBLIC_ASSETS_LOCAL_PATH=./public/assets
PUBLIC_ASSETS_LOCAL_URL=/assets

# Public Assets Cloud Storage (optional)
# PUBLIC_ASSETS_BUCKET=eproc-public-assets
# PUBLIC_ASSETS_REGION=us-east-1
# PUBLIC_ASSETS_CDN_URL=https://cdn.yourcompany.com
# PUBLIC_ASSETS_ACCESS_KEY_ID=your-public-assets-key
# PUBLIC_ASSETS_SECRET_ACCESS_KEY=your-public-assets-secret

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================

# Rate Limiting (requests per window)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# API Rate Limits
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX_REQUESTS=60

# Authentication Rate Limits
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# =============================================================================
# MONITORING & PERFORMANCE (OPTIONAL)
# =============================================================================

# Database Performance Monitoring
DB_PERFORMANCE_LOGGING=true
DB_SLOW_QUERY_THRESHOLD=1000

# Application Monitoring
ENABLE_AUDIT_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# Error Tracking (optional)
# SENTRY_DSN=your-sentry-dsn
# SENTRY_ENVIRONMENT=development

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Prisma Configuration
PRISMA_GENERATE_DATAPROXY=false

# Development Tools
NEXT_TELEMETRY_DISABLED=1

# Testing Configuration (for test environment)
# TEST_DATABASE_URL="postgresql://username:password@localhost:5432/eproc_test_db"

# =============================================================================
# WEBHOOK CONFIGURATION (OPTIONAL)
# =============================================================================

# Webhook Settings for External Integrations
# WEBHOOK_SECRET=your-webhook-secret
# WEBHOOK_TIMEOUT=30000

# =============================================================================
# BACKUP & MAINTENANCE (PRODUCTION)
# =============================================================================

# Database Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE="0 2 * * *"
# BACKUP_RETENTION_DAYS=30
# BACKUP_STORAGE_PATH=./backups

# Maintenance Mode
# MAINTENANCE_MODE=false
# MAINTENANCE_MESSAGE="System is under maintenance. Please try again later."

# =============================================================================
# THIRD-PARTY INTEGRATIONS (OPTIONAL)
# =============================================================================

# Payment Gateway (if needed)
# PAYMENT_GATEWAY_API_KEY=your-payment-gateway-key
# PAYMENT_GATEWAY_SECRET=your-payment-gateway-secret

# Document Signing Service (if needed)
# DOCUSIGN_CLIENT_ID=your-docusign-client-id
# DOCUSIGN_CLIENT_SECRET=your-docusign-client-secret

# SMS Service (if needed)
# SMS_PROVIDER=twilio
# TWILIO_ACCOUNT_SID=your-twilio-account-sid
# TWILIO_AUTH_TOKEN=your-twilio-auth-token
# TWILIO_PHONE_NUMBER=your-twilio-phone-number

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Level (error, warn, info, debug)
LOG_LEVEL=info

# Log Format (json, simple)
LOG_FORMAT=json

# Log File Path (optional)
# LOG_FILE_PATH=./logs/app.log

# =============================================================================
# FEATURE FLAGS (OPTIONAL)
# =============================================================================

# Enable/Disable Features
FEATURE_VENDOR_REGISTRATION=true
FEATURE_PROCUREMENT_DISCUSSIONS=true
FEATURE_DOCUMENT_TEMPLATES=true
FEATURE_KPI_EVALUATIONS=true
FEATURE_NEGOTIATION_SESSIONS=true

# =============================================================================
# NOTES FOR DEVELOPERS
# =============================================================================
# 
# REQUIRED VARIABLES (must be configured):
# - DATABASE_URL
# - JWT_SECRET
# - CSRF_SECRET
# - SMTP_* (for email notifications)
# 
# OPTIONAL VARIABLES:
# - REDIS_URL (enables caching and rate limiting)
# - Cloud storage credentials (if not using local storage)
# - Monitoring and logging configurations
# 
# SECURITY NOTES:
# - Change all default secrets in production
# - Use strong, randomly generated keys
# - Keep sensitive credentials secure
# - Enable HTTPS in production
# - Configure proper CORS settings
# 
# PERFORMANCE NOTES:
# - Configure Redis for better performance
# - Use cloud storage for production
# - Enable database connection pooling
# - Configure CDN for static assets
# 
# =============================================================================
