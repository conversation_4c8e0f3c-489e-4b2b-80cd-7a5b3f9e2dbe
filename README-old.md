# E-Procurement System

This is an e-procurement system built with [Next.js 15](https://nextjs.org) and bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app). The project aims to streamline procurement processes through a digital platform.

## Tech Stack

- **Bun**: Fast JavaScript runtime for development and dependency management.
- **Next.js 15**: Framework for full-stack web applications with server-side rendering and API routes.
- **TypeScript**: Static typing for improved code safety and developer experience.
- **Prisma**: ORM for database interactions with PostgreSQL.
- **PostgreSQL**: Relational database for storing procurement data.
- **TanStack Query**: Library for efficient data fetching and state management.
- **Shadcn UI**: Reusable UI components styled with Tailwind CSS.
- **Tailwind CSS**: Utility-first CSS framework for rapid UI development.

## Getting Started

### Prerequisites

- Ensure [Bun](https://bun.sh) is installed on your system for managing dependencies and running the application.

### Installation

1. Clone or navigate to the project directory:
   ```bash
   cd /Users/<USER>/Documents/e-proc
   ```
2. Install dependencies:
   ```bash
   bun install
   ```

### Database Setup

1. Configure your PostgreSQL database connection by setting the `DATABASE_URL` in the `.env` file:
   ```bash
   echo "DATABASE_URL=postgresql://user:password@localhost:5432/eproc_db" > .env
   ```
   Replace `user`, `password`, and database details as per your setup.
2. Migrate the database schema using Prisma:
   ```bash
   bun run prisma migrate dev --name init
   ```
   This applies any schema changes defined in `prisma/schema.prisma` to your database.

### Running the Development Server

1. Start the development server:
   ```bash
   bun run dev
   ```
2. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

You can start editing the page by modifying `src/app/page.tsx`. The page auto-updates as you edit the file.

## Project Structure

- **Source Code**: Located in `src/` directory, following Next.js App Router conventions.
- **Prisma Schema**: Defined in `prisma/schema.prisma` for database models.
- **Memory Bank**: Documentation in `memory-bank/` to maintain project context across sessions.
- **Documentation**: Additional design documents in `docs/` for system and database design.

## Learn More

To learn more about the technologies used:

- [Next.js Documentation](https://nextjs.org/docs) - Features and API details.
- [Prisma Documentation](https://www.prisma.io/docs) - Database ORM guide.
- [Bun Documentation](https://bun.sh/docs) - Runtime and package manager details.

## Contributing

Feedback and contributions are welcome. Please follow the project's coding standards and submit pull requests for review.

## Deployment

For deployment, consider using platforms like [Vercel](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) which offers seamless integration with Next.js. Refer to [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for detailed instructions.
